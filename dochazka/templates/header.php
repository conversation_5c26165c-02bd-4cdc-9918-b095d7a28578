<?php
session_start();
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../classes/User.php';
require_once __DIR__ . '/../classes/Attendance.php';

checkIpAddress();

$db = getDbConnection();
$user = new User($db);

if (!$user->isLoggedIn()) {
    header("Location: login.php");
    exit();
}

$currentUser = $user->getById($_SESSION['user_id']);
?>
<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Docházkový systém</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="public/css/style.css" rel="stylesheet">
</head>
<body>
    <?php include 'templates/navigation.php'; ?>
    <div class="container mt-4">
