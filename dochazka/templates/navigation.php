<nav class="navbar navbar-expand-lg navbar-dark bg-dark">
    <div class="container-fluid">
        <a class="navbar-brand" href="index.php">Docházkový systém</a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav ms-auto d-flex align-items-center">
                <li class="nav-item">
                    <span class="navbar-text">
                        <?php
                            $name = $currentUser['full_name'];
                            // Jednoduchá kontrola pro ženská jména končící na 'á'
                            $lastChar = mb_substr(trim($name), -1);
                            $label = (in_array($lastChar, ['á', 'a'])) ? 'P<PERSON>ihl<PERSON>šena' : 'P<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>';
                            echo htmlspecialchars($label . ': ' . $name);
                        ?>
                    </span>
                </li>
                <?php if ($user->hasRole('admin')): ?>
                    <li class="nav-item ms-3">
                        <a class="admin-btn" href="admin/index.php">Administrace</a>
                    </li>
                <?php endif; ?>

                <li class="nav-item ms-3">
                    <a href="logout.php" class="logout-btn">Odhlásit se</a>
                </li>
            </ul>
        </div>
    </div>
</nav>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>