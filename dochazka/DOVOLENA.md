# Funkcionalita: Evidence dovolené

Tento dokument popisuje plán pro rozšíření docházkového systému o možnost zadávání a sledování dovolené.

---

## Krok 1: Rozš<PERSON>ření databáze

Potřebujeme upravit tabulku `event_types` a přidat novou tabulku pro žádosti o dovolenou.

**1. Upravit `event_types`**
- Přidat nový typ události:
  - `event_name`: "Dovolená"
  - `event_code`: "VACATION"

**2. Nová tabulka `vacation_requests`**
- `id` (INT, primární klíč, auto-increment)
- `user_id` (INT, cizí klíč odkazující na `users.id`)
- `start_date` (DATE) - datum začátku dovolené
- `end_date` (DATE) - datum konce dovolené
- `status` (ENUM('pending', 'approved', 'rejected')) - stav ž<PERSON><PERSON>
- `reason` (TEXT, může být NULL) - důvod <PERSON>
- `approved_by` (INT, cizí klíč odkazující na `users.id`, může být NULL) - ID admina, který žádost schválil/zamítl
- `created_at` (TIMESTAMP, výchozí CURRENT_TIMESTAMP)

---

## Krok 2: Úprava Backendu

Rozšíříme třídu `Attendance.php` o nové metody.

1.  **`classes/Attendance.php`**:
    - `requestVacation($userId, $startDate, $endDate, $reason)`: Vytvoří novou žádost o dovolenou se statusem 'pending'.
    - `getVacationRequests($status = 'pending')`: Načte žádosti o dovolenou (pro admina).
    - `approveVacation($requestId, $adminId)`: Schválí žádost. Po schválení automaticky vytvoří záznamy v `attendance_records` pro každý den dovolené s typem "VACATION".
    - `rejectVacation($requestId, $adminId)`: Zamítne žádost.
    - `getUserVacationDays($userId, $year)`: Spočítá počet dní dovolené pro daného uživatele a rok.

---

## Krok 3: Uživatelské rozhraní

Přidáme novou sekci pro uživatele, kde mohou žádat o dovolenou.

1.  **`vacation.php`**: Nová stránka, která bude obsahovat:
    - Formulář pro zadání žádosti o dovolenou (výběr data od-do, důvod).
    - Přehled vlastních žádostí a jejich stav.
2.  **Úprava `index.php`**: V měsíčním přehledu se dny dovolené zobrazí podobně jako svátky (např. podbarvené jinou barvou).

---

## Krok 4: Administrační rozhraní

Rozšíříme administraci o správu žádostí o dovolenou.

1.  **`admin/vacations.php`**: Nová stránka v administraci:
    - Zobrazí seznam žádostí o dovolenou, které čekají na schválení.
    - U každé žádosti budou tlačítka "Schválit" a "Zamítnout".
    - Zobrazí také historii všech žádostí.
2.  **Úprava `admin/index.php`**: V celkovém přehledu docházky se dny dovolené propíší jako jakýkoli jiný záznam.

---
Tento plán poskytuje základní rámec pro implementaci. Každý krok bude vyžadovat pečlivé testování, zejména logika pro automatické vytváření záznamů po schválení žádosti.
