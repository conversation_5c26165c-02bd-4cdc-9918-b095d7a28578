# Projekt: Docházkový systém

Tento dokument popisuje plán a technickou specifikaci pro vývoj jednoduchého docházkového systému pro malou firmu (cca 25 zaměstnanců).

## Klíčové technologie
- **Backend:** PHP 8+
- **Databáze:** MySQL 8 nebo PostgreSQL 11/12
- **Frontend:** HTML5, CSS3, JavaScript (s využitím AJAX pro plynulé ovládání)
- **Doporučený CSS framework:** Bootstrap 5 pro responzivní design a rychlý vývoj komponent.

---

## Krok 1: Návrh databázového modelu

Vytvoříme 4 základní tabulky.

**1. `users` - Uživatelé**
- `id` (INT, primární klíč, auto-increment)
- `username` (VARCHAR, unikátní) - p<PERSON><PERSON><PERSON>š<PERSON><PERSON><PERSON> jméno
- `password` (VARCHAR) - he<PERSON><PERSON> (bude uloženo jako hash)
- `full_name` (VARCHAR) - celé jméno pro zobrazení
- `email` (VARCHAR, unikátní)
- `role` (ENUM('user', 'admin')) - role pro rozlišení běžného uživatele a účetní
- `created_at` (TIMESTAMP, výchozí CURRENT_TIMESTAMP)

**2. `event_types` - Typy událostí**
- `id` (INT, primární klíč, auto-increment)
- `event_name` (VARCHAR) - např. "Práce v kanceláři", "Homeoffice", "Lékař", "Služební cesta"
- `event_code` (VARCHAR, unikátní) - krátký kód, např. "WORK", "HOMEOFFICE", "SICK", "TRIP"

**3. `attendance_records` - Záznamy docházky**
- `id` (INT, primární klíč, auto-increment)
- `user_id` (INT, cizí klíč odkazující na `users.id`)
- `event_type_id` (INT, cizí klíč odkazující na `event_types.id`)
- `start_time` (DATETIME) - čas příchodu / začátku události
- `end_time` (DATETIME, může být NULL) - čas odchodu / konce události
- `note` (TEXT, může být NULL) - poznámka (např. u lékaře)
- `date` (DATE) - datum záznamu pro snadnější filtrování

**4. `holidays` - Svátky**
- `id` (INT, primární klíč, auto-increment)
- `holiday_date` (DATE, unikátní)
- `holiday_name` (VARCHAR)

---

## Krok 2: Struktura projektu

Doporučuji následující adresářovou strukturu ve složce `dochazka/`:

```
/dochazka/
|-- config/
|   `-- database.php       # Připojení k databázi a konfigurace
|-- classes/
|   |-- User.php           # Třída pro práci s uživateli (přihlášení, registrace)
|   `-- Attendance.php     # Třída pro práci s docházkou (záznamy, výpočty)
|-- templates/
|   |-- header.php         # Hlavička stránky
|   |-- footer.php         # Patička stránky
|   `-- navigation.php     # Navigace
|-- ajax/
|   `-- record_event.php   # Skript pro zpracování AJAX požadavků (příchod, odchod, atd.)
|-- admin/
|   |-- index.php          # Administrační rozhraní
|   `-- export.php         # Skript pro export do CSV
|-- public/                # Veřejně přístupná složka (pokud používáte router)
|   |-- css/
|   |-- js/
|-- login.php              # Přihlašovací stránka
|-- logout.php             # Odhlášení
|-- index.php              # Hlavní stránka pro přihlášené uživatele
`-- PROJEKT.md             # Tento soubor
```

---

## Krok 3: Základní Backend (PHP)

1.  **`config/database.php`**: Vytvořit skript, který bude obsahovat přihlašovací údaje k databázi a vytvoří spojení pomocí PDO.
2.  **`classes/User.php`**: Implementovat třídu s metodami:
    - `login($username, $password)`: Ověří přihlašovací údaje.
    - `isLoggedIn()`: Zkontroluje, zda je uživatel přihlášen (pomocí `$_SESSION`).
    - `hasRole($role)`: Ověří, zda má uživatel danou roli.
    - `getById($id)`: Načte uživatele podle ID.
3.  **`classes/Attendance.php`**: Implementovat třídu s metodami:
    - `recordStartTime($userId, $eventTypeId)`: Zapíše začátek události.
    - `recordEndTime($recordId)`: Doplní čas konce k existujícímu záznamu.
    - `getUserRecordsForMonth($userId, $month, $year)`: Načte docházku pro uživatele za daný měsíc.
    - `getAllRecordsForMonth($month, $year)`: Načte veškerou docházku pro admina.
    - `calculateWorkedHours($startTime, $endTime)`: Vypočítá odpracované hodiny a automaticky odečte 30 minut pauzu, pokud je doba delší než 6 hodin.

---

## Krok 4: Uživatelské rozhraní (Frontend)

1.  **`login.php`**: Jednoduchý formulář pro přihlášení.
2.  **`index.php`**: Hlavní stránka po přihlášení, která bude obsahovat:
    - **Ovládací panel:** Tlačítka "Příchod", "Odchod", "Lékař", "Homeoffice", atd.
    - **Stav:** Informace o aktuálním stavu (např. "Pracujete od 8:30").
    - **Měsíční přehled:** Tabulka s docházkou za aktuální měsíc.
    - **Statistiky:** Základní souhrn (odpracované hodiny, přesčasy).
3.  **JavaScript (`main.js`)**:
    - Obsluha kliknutí na tlačítka v ovládacím panelu.
    - Odesílání AJAX (pomocí `fetch`) požadavků na `ajax/record_event.php`.
    - Aktualizace stavu a tabulky docházky na stránce bez nutnosti jejího znovunačtení.

---

## Krok 5: Implementace hlavní logiky

1.  **Správa sezení (`session`)**: Po úspěšném přihlášení uložit ID uživatele a jeho roli do `$_SESSION`. Na každé stránce kontrolovat, zda je uživatel přihlášen.
2.  **`ajax/record_event.php`**: Tento skript bude mozkem aplikace.
    - Přijme požadavek z JavaScriptu (např. `action=start_work`).
    - Zavolá příslušnou metodu z třídy `Attendance`.
    - Vrátí odpověď v JSON formátu (např. `{ "status": "success", "message": "Příchod zaznamenán v 8:30" }`).
3.  **Výpočet svátků**: Vytvořit funkci, která načte české svátky pro daný rok (mohou být v poli nebo v DB tabulce `holidays`) a zohlední je v přehledu.

---

## Krok 6: Administrační funkce a export

1.  **`admin/index.php`**:
    - Na začátku skriptu ověřit, zda má uživatel roli 'admin'. Pokud ne, přesměrovat ho.
    - Zobrazit formulář pro výběr měsíce a roku.
    - Po odeslání formuláře zobrazit tabulku s docházkou všech zaměstnanců za dané období.
2.  **`admin/export.php`**:
    - Skript přijme měsíc a rok.
    - Načte data pomocí `Attendance::getAllRecordsForMonth()`.
    - Vygeneruje CSV soubor s hlavičkami (např. `Jméno, Datum, Typ, Příchod, Odchod, Odpracováno`).
    - Nastaví správné HTTP hlavičky pro vynucení stažení souboru.

---

## Krok 7: Nasazení a SQL

1.  **`install.sql`**: Připravit SQL skript, který vytvoří všechny potřebné databázové tabulky a případně vloží základní data (typy událostí, svátky).
2.  **Konfigurace**: Upravit `config/database.php` s reálnými přihlašovacími údaji k databázi na hostingu Webglobe.
3.  **Nahrání souborů**: Nahrát všechny soubory na FTP.
4.  **Testování**: Důkladně otestovat všechny funkce, zejména správnost výpočtů a exportu.

---

## Krok 8: Responzivní design

Aplikace musí být plně použitelná na mobilních zařízeních.

1.  **Využití Bootstrap 5 Grid systému**: Všechny layouty (přihlašovací stránka, hlavní panel, administrace) postavit na grid systému Bootstrapu (`.container`, `.row`, `.col-*`), aby se automaticky přizpůsobily velikosti obrazovky.
2.  **Optimalizace tabulek**: Tabulky s měsíčním přehledem docházky jsou největší výzvou.
    -   Na malých obrazovkách (telefonech) je vhodné skrýt méně důležité sloupce (např. poznámka, přesný čas v minutách).
    -   Použít třídu `.table-responsive` od Bootstrapu, která tabulce přidá horizontální posuvník, pokud se nevejde na šířku obrazovky.
    -   Zvážit alternativní zobrazení pro mobily, kde by každý den byl zobrazen jako samostatná "kartička" pod sebou místo řádku v tabulce.
3.  **Navigace**: Použít responzivní navigaci Bootstrapu (`.navbar`), která se na mobilu automaticky sbalí do "hamburger" menu.
4.  **Testování**: Průběžně testovat na reálných zařízeních nebo v nástrojích pro vývojáře v prohlížeči (Chrome DevTools, Firefox Developer Tools).

---

## Krok 9: Omezení přístupu na základě IP adresy

Zvýšíme bezpečnost tím, že omezíme přístup k aplikaci pouze z definovaných IP adres.

1.  **Rozšíření konfiguračního souboru**: Do `config/database.php` přidat novou konfigurační proměnnou – pole povolených IP adres.
    ```php
    // config/database.php
    // ... (přihlašovací údaje k DB)

    $allowed_ips = [
        '***********',  // Příklad: IP adresa kanceláře
        '***********',  // Příklad: Veřejná IP adresa jiné pobočky
        '::1'           // Povolení pro localhost (IPv6)
    ];
    ```
2.  **Vytvoření kontrolní funkce**: Vytvořit globální funkci nebo metodu (např. v `classes/User.php`), která zkontroluje IP adresu uživatele.
    ```php
    function checkIpAddress() {
        global $allowed_ips;
        $user_ip = $_SERVER['REMOTE_ADDR'];

        if (!in_array($user_ip, $allowed_ips)) {
            // IP adresa není povolena
            header('HTTP/1.1 403 Forbidden');
            die('Access denied. Your IP address (' . htmlspecialchars($user_ip) . ') is not allowed.');
        }
    }
    ```
3.  **Aplikace kontroly**: Tuto funkci zavolat na začátku všech klíčových skriptů, které slouží jako vstupní bod do aplikace:
    - `login.php`
    - `index.php`
    - `ajax/record_event.php`
    - `admin/index.php`
    - `admin/export.php`

    Tím bude zajištěno, že se k aplikaci nedostane nikdo z nepovolené IP adresy.
