-- SQL skript pro vytvoření nových uživatelů
-- <PERSON><PERSON> jsou náhodně vygenerovaná a měla by b<PERSON><PERSON> změn<PERSON>.
-- Heslo pro všechny je 'heslo123'

INSERT INTO `users` (`username`, `password`, `full_name`, `email`, `role`) VALUES
('ales.seidl', '$2y$10$9.pG1Z.y5Y/1a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a', '<PERSON><PERSON><PERSON>', '<EMAIL>', 'user'),
('dagmar.dejral<PERSON>', '$2y$10$9.pG1Z.y5Y/1a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a', '<PERSON><PERSON><PERSON>', 'dagmar.dej<PERSON><PERSON>@example.com', 'user'),
('daniela.pavlova', '$2y$10$9.pG1Z.y5Y/1a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a', 'Daniela Pavlová', '<EMAIL>', 'user'),
('hana.gajarska', '$2y$10$9.pG1Z.y5Y/1a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a', 'Hana Gajarská', '<EMAIL>', 'user'),
('iveta.zivelova', '$2y$10$9.pG1Z.y5Y/1a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a', 'Iveta Živělová', '<EMAIL>', 'user'),
('jiri.belajev', '$2y$10$9.pG1Z.y5Y/1a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a', 'Jiří Belajev', '<EMAIL>', 'user'),
('lenka.pelajova', '$2y$10$9.pG1Z.y5Y/1a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a', 'Lenka Pelajová', '<EMAIL>', 'user'),
('marketa.stodulkova', '$2y$10$9.pG1Z.y5Y/1a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a', 'Markéta Stodůlková', '<EMAIL>', 'user'),
('michal.venhuda', '$2y$10$9.pG1Z.y5Y/1a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a', 'Michal Venhuda', '<EMAIL>', 'user'),
('milos.sturza', '$2y$10$9.pG1Z.y5Y/1a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a', 'Miloš Šturza', '<EMAIL>', 'user'),
('mirka', '$2y$10$9.pG1Z.y5Y/1a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a', 'Mirka', '<EMAIL>', 'user'),
('miroslav.urbanek', '$2y$10$9.pG1Z.y5Y/1a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a', 'Miroslav Urbánek', '<EMAIL>', 'user'),
('monika.janderkova', '$2y$10$9.pG1Z.y5Y/1a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a', 'Monika Janderková', '<EMAIL>', 'user'),
('monika.martinkova', '$2y$10$9.pG1Z.y5Y/1a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a', 'Monika Martínková', '<EMAIL>', 'user'),
('nikol.krasova', '$2y$10$9.pG1Z.y5Y/1a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a', 'Nikol Krásová', '<EMAIL>', 'user'),
('pavel.skolar', '$2y$10$9.pG1Z.y5Y/1a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a', 'Pavel Skolar', '<EMAIL>', 'user'),
('petr.joch', '$2y$10$9.pG1Z.y5Y/1a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a', 'Petr Joch', '<EMAIL>', 'user'),
('petr.vesely', '$2y$10$9.pG1Z.y5Y/1a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a', 'Petr Veselý', '<EMAIL>', 'user'),
('rudolf.trencansky', '$2y$10$9.pG1Z.y5Y/1a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a', 'Rudolf Trencansky', '<EMAIL>', 'user'),
('sylvie.swierczkova', '$2y$10$9.pG1Z.y5Y/1a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a', 'Sylvie Swierczková', '<EMAIL>', 'user'),
('tereza.nedelova', '$2y$10$9.pG1Z.y5Y/1a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a', 'Tereza Nedělová', '<EMAIL>', 'user'),
('veronika.spirikova', '$2y$10$9.pG1Z.y5Y/1a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a', 'Veronika Špiříková', '<EMAIL>', 'user'),
('vladimir.peska', '$2y$10$9.pG1Z.y5Y/1a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a.a', 'Vladimír Peška', '<EMAIL>', 'user');
