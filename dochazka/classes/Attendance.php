<?php
// classes/Attendance.php

class Attendance {
    private $db;

    public function __construct($db) {
        $this->db = $db;
    }

    public function recordStartTime($userId, $eventTypeId, $note = null) {
        $date = date('Y-m-d');
        $startTime = date('Y-m-d H:i:s');

        $stmt = $this->db->prepare(
            "INSERT INTO attendance_records (user_id, event_type_id, start_time, date, note) 
             VALUES (:user_id, :event_type_id, :start_time, :date, :note)"
        );
        $stmt->execute([
            'user_id' => $userId,
            'event_type_id' => $eventTypeId,
            'start_time' => $startTime,
            'date' => $date,
            'note' => $note
        ]);
        return $this->db->lastInsertId();
    }

    public function getActiveRecord($userId) {
        $stmt = $this->db->prepare(
            "SELECT ar.id, ar.start_time, et.event_code
             FROM attendance_records ar
             JOIN event_types et ON ar.event_type_id = et.id
             WHERE ar.user_id = :user_id AND ar.end_time IS NULL 
             ORDER BY ar.start_time DESC LIMIT 1"
        );
        $stmt->execute(['user_id' => $userId]);
        return $stmt->fetch();
    }

    public function recordEndTime($userId) {
        $activeRecord = $this->getActiveRecord($userId);
        if ($activeRecord) {
            $endTime = date('Y-m-d H:i:s');
            $stmt = $this->db->prepare("UPDATE attendance_records SET end_time = :end_time WHERE id = :id");
            return $stmt->execute(['end_time' => $endTime, 'id' => $activeRecord['id']]);
        }
        return false;
    }

    public function updateNote($recordId, $note) {
        $stmt = $this->db->prepare("UPDATE attendance_records SET note = :note WHERE id = :id");
        return $stmt->execute(['note' => $note, 'id' => $recordId]);
    }

    public function getUserRecordsForMonth($userId, $month, $year) {
        $stmt = $this->db->prepare(
            "SELECT ar.*, et.event_name 
             FROM attendance_records ar
             JOIN event_types et ON ar.event_type_id = et.id
             WHERE ar.user_id = :user_id 
               AND MONTH(ar.date) = :month 
               AND YEAR(ar.date) = :year
             ORDER BY ar.date ASC, ar.start_time ASC"
        );
        $stmt->execute(['user_id' => $userId, 'month' => $month, 'year' => $year]);
        return $stmt->fetchAll();
    }

    public function getAllRecordsForMonth($month, $year) {
        $stmt = $this->db->prepare(
            "SELECT ar.*, et.event_name, u.full_name
             FROM attendance_records ar
             JOIN event_types et ON ar.event_type_id = et.id
             JOIN users u ON ar.user_id = u.id
             WHERE MONTH(ar.date) = :month 
               AND YEAR(ar.date) = :year
             ORDER BY u.full_name, ar.date ASC, ar.start_time ASC"
        );
        $stmt->execute(['month' => $month, 'year' => $year]);
        return $stmt->fetchAll();
    }

    public function calculateWorkedHours($startTime, $endTime) {
        if (!$startTime || !$endTime) {
            return 0;
        }
        $start = new DateTime($startTime);
        $end = new DateTime($endTime);
        $diff = $end->getTimestamp() - $start->getTimestamp();

        // Odečtení 30 minut pauzy, pokud je odpracováno více než 6 hodin
        if ($diff > 6 * 3600) {
            $diff -= 30 * 60;
        }

        return $diff / 3600; // Převedení na hodiny
    }

    public function getWorkedHoursForDay($userId, $date) {
        $stmt = $this->db->prepare(
            "SELECT start_time, end_time 
             FROM attendance_records 
             WHERE user_id = :user_id AND date = :date AND end_time IS NOT NULL"
        );
        $stmt->execute(['user_id' => $userId, 'date' => $date]);
        $records = $stmt->fetchAll();

        $totalHours = 0;
        foreach ($records as $record) {
            $totalHours += $this->calculateWorkedHours($record['start_time'], $record['end_time']);
        }
        return $totalHours;
    }

    public function getWorkedHoursForWeek($userId, $date) {
        $startOfWeek = date('Y-m-d', strtotime('monday this week', strtotime($date)));
        $endOfWeek = date('Y-m-d', strtotime('sunday this week', strtotime($date)));

        $stmt = $this->db->prepare(
            "SELECT start_time, end_time 
             FROM attendance_records 
             WHERE user_id = :user_id AND date BETWEEN :start_of_week AND :end_of_week AND end_time IS NOT NULL"
        );
        $stmt->execute(['user_id' => $userId, 'start_of_week' => $startOfWeek, 'end_of_week' => $endOfWeek]);
        $records = $stmt->fetchAll();

        $totalHours = 0;
        foreach ($records as $record) {
            $totalHours += $this->calculateWorkedHours($record['start_time'], $record['end_time']);
        }
        return $totalHours;
    }
    
    public function getCzechHolidays($year) {
        $stmt = $this->db->prepare("SELECT holiday_date, holiday_name FROM holidays WHERE YEAR(holiday_date) = :year");
        $stmt->execute(['year' => $year]);
        return $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
    }

    public function getAllUsersCurrentStatus() {
        $today = date('Y-m-d');
        $stmt = $this->db->prepare(
            "SELECT
                u.id,
                u.full_name,
                et.event_name,
                et.event_code
            FROM
                users u
            LEFT JOIN
                attendance_records ar ON u.id = ar.user_id AND ar.end_time IS NULL AND ar.date = :today
            LEFT JOIN
                event_types et ON ar.event_type_id = et.id
            ORDER BY
                u.full_name ASC"
        );
        $stmt->execute(['today' => $today]);
        return $stmt->fetchAll();
    }
}
