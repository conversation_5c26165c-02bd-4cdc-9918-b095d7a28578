<?php
// config/database.php

// --- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> k databázi ---
define('DB_HOST', 'db.dw189.webglobe.com');
define('DB_NAME', 'dochaz_myrec_cz');
define('DB_USER', 'dochaz_myrec_cz');
define('DB_PASS', 'XqdWVNl0');

// --- Omezení přístupu na základě IP adresy ---
$allowed_ips = [
    '127.0.0.1', // Povolení pro localhost (IPv4)
    '::1',        // Povolení pro localhost (IPv6)
    '************'// Zde přidejte další povolené IP adresy
    // '***********',
];

// --- Funkce pro připojení k databázi ---
function getDbConnection() {
    $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4";
    $options = [
        PDO::ATTR_ERRMODE            => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES   => false,
    ];
    try {
        return new PDO($dsn, DB_USER, DB_PASS, $options);
    } catch (\PDOException $e) {
        throw new \PDOException($e->getMessage(), (int)$e->getCode());
    }
}

// --- Funkce pro kontrolu IP adresy ---
function checkIpAddress() {
    global $allowed_ips;
    $user_ip = $_SERVER['REMOTE_ADDR'];

    if (!in_array($user_ip, $allowed_ips)) {
        header('HTTP/1.1 403 Forbidden');
        die('Access denied. Your IP address (' . htmlspecialchars($user_ip) . ') is not allowed.');
    }
}
