/* public/css/style.css */

:root {
    --prichod: #B2E5BF;
    --odchod: #F7B2B2;
    --homeoffice: #AED9E0;
    --lekar: #D6C2F0;
    --sluzebni-cesta: #FFF3B0;

    --prichod-hover: #9ADDA7;
    --odchod-hover: #F59F9F;
    --homeoffice-hover: #95C5D1;
    --lekar-hover: #C3AEE8;
    --sluzebni-cesta-hover: #FDEE98;

    --pastel-green: #a8e6cf;
    --pastel-red: #ff8a80;
    --pastel-blue: #d7e5f0;
    --pastel-yellow: #ffd3b6;
    --pastel-gray: #e2e2e2;
    --pastel-purple: #d1c4e9;
}

body {
    background-color: #f8f9fa;
}

#attendance-controls .btn {
    width: 150px;
    height: 150px;
    margin: 10px;
    font-size: 1.2rem;
    font-weight: bold;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    transition: all 0.2s ease-in-out;
}

#attendance-controls .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 8px rgba(0,0,0,0.15);
}

.btn-green { background-color: var(--prichod); border-color: var(--prichod); color: #333; }
.btn-green:hover { background-color: var(--prichod-hover); border-color: var(--prichod-hover); }

.btn-red { background-color: var(--odchod); border-color: var(--odchod); color: #333; }
.btn-red:hover { background-color: var(--odchod-hover); border-color: var(--odchod-hover); }

.btn-blue { background-color: var(--homeoffice); border-color: var(--homeoffice); color: #333; }
.btn-blue:hover { background-color: var(--homeoffice-hover); border-color: var(--homeoffice-hover); }

.btn-purple { background-color: var(--lekar); border-color: var(--lekar); color: #333; }
.btn-purple:hover { background-color: var(--lekar-hover); border-color: var(--lekar-hover); }

.btn-yellow { background-color: var(--sluzebni-cesta); border-color: var(--sluzebni-cesta); color: #333; }
.btn-yellow:hover { background-color: var(--sluzebni-cesta-hover); border-color: var(--sluzebni-cesta-hover); }

.btn-gray { background-color: var(--pastel-gray); border-color: var(--pastel-gray); color: #333; }


#timer {
    font-size: 2rem;
    font-weight: bold;
    color: #333;
}

table tbody tr.weekend-day td {
    background-color: #ffc9c9 !important; /* Světle šedá pro víkendy */
}

table tbody tr.holiday-day td {
    background-color: var(--pastel-blue) !important; /* Pastelově modrá pro svátky */
}

.note-icon {
    cursor: pointer;
    margin-left: 8px;
}

.logout-btn {
    background-color: #dc3545;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    text-decoration: none;
    font-weight: bold;
    transition: background-color 0.3s;
}

.logout-btn:hover {
    background-color: #c82333;
    color: white;
}

.admin-btn {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    text-decoration: none;
    font-weight: bold;
    transition: background-color 0.3s;
    margin-left: 10px;
}

.admin-btn:hover {
    background-color: #0056b3;
    color: white;
}

.back-to-dashboard-btn {
    background-color: #6c757d;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    text-decoration: none;
    font-weight: bold;
    transition: background-color 0.3s;
    float: right;
}

.back-to-dashboard-btn:hover {
    background-color: #5a6268;
    color: white;
}

.monthly-overview {
    display: flex;
    justify-content: space-around;
    margin-bottom: 20px;
}

.overview-block {
    background-color: #fff;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    text-align: center;
    width: 30%;
}

.progress-circle {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background: conic-gradient(var(--prichod) 0% 75%, #eee 75% 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto 10px;
    font-size: 1.5rem;
    font-weight: bold;
}
