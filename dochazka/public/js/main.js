document.addEventListener('DOMContentLoaded', function () {
    const btnStartWork = document.getElementById('btn-start-work');
    const btnEndWork = document.getElementById('btn-end-work');
    const btnStartHomeoffice = document.getElementById('btn-start-homeoffice');
    const btnStartTrip = document.getElementById('btn-start-trip');
    const btnStartSick = document.getElementById('btn-start-sick');

    const statusMessage = document.getElementById('status-message');
    const workingTimeDisplay = document.getElementById('working-time-display');
    const noteInputArea = document.getElementById('note-input-area');
    const noteTextarea = document.getElementById('note-text');

    let timerInterval;
    let currentStatus = null; // 'WORK', 'HOMEOFFICE', 'TRIP', 'SICK', 'none'
    let currentStartTime = null; // Timestamp when work started

    // Function to update UI based on current status
    function updateUI() {
        // Reset all buttons and displays
        [btnStartWork, btnEndWork, btnStartHomeoffice, btnStartTrip, btnStartSick].forEach(btn => {
            btn.style.display = 'none';
            btn.disabled = false; // Ensure buttons are enabled by default
        });
        workingTimeDisplay.style.display = 'none';
        noteInputArea.style.display = 'none';
        stopTimer();

        switch (currentStatus) {
            case 'WORK':
                btnEndWork.style.display = 'block';
                btnStartTrip.style.display = 'block';
                btnStartSick.style.display = 'block';
                workingTimeDisplay.style.display = 'block';
                noteInputArea.style.display = 'block';
                startTimer(currentStartTime);
                btnStartWork.disabled = true;
                break;
            case 'HOMEOFFICE':
                btnEndWork.style.display = 'block';
                btnStartTrip.style.display = 'block';
                btnStartSick.style.display = 'block';
                noteInputArea.style.display = 'block';
                workingTimeDisplay.style.display = 'block';
                workingTimeDisplay.textContent = `Jste na homeoffice.`;
                btnStartHomeoffice.disabled = true;
                break;
            case 'TRIP':
                btnEndWork.style.display = 'block';
                btnStartWork.style.display = 'block';
                btnStartHomeoffice.style.display = 'block';
                noteInputArea.style.display = 'block';
                workingTimeDisplay.style.display = 'block';
                workingTimeDisplay.textContent = `Jste na služební cestě.`;
                btnStartTrip.disabled = true;
                break;
            case 'SICK':
                btnStartWork.style.display = 'block';
                btnEndWork.style.display = 'block';
                btnStartHomeoffice.style.display = 'block';
                noteInputArea.style.display = 'block'; // Note input area visible for sick as well
                workingTimeDisplay.style.display = 'block';
                workingTimeDisplay.textContent = `Jste u lékaře.`;
                btnStartSick.disabled = true;
                break;
            case 'none': // No active attendance
            default:
                btnStartWork.style.display = 'block';
                btnStartHomeoffice.style.display = 'block';
                btnStartTrip.style.display = 'block';
                btnStartSick.style.display = 'block';
                btnEndWork.style.display = 'none'; // Odchod is not available initially
                break;
        }
    }

    // Timer functions
    function startTimer(startTime) {
        if (!startTime) return;
        stopTimer(); // Clear any existing timer

        timerInterval = setInterval(() => {
            const now = new Date().getTime();
            const elapsed = now - startTime;

            const hours = Math.floor(elapsed / (1000 * 60 * 60));
            const minutes = Math.floor((elapsed % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((elapsed % (1000 * 60)) / 1000);

            workingTimeDisplay.textContent = `Pracujete: ${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
        }, 1000);
    }

    function stopTimer() {
        if (timerInterval) {
            clearInterval(timerInterval);
            timerInterval = null;
        }
    }

    // Function to send AJAX request
    function sendRequest(action) {
        statusMessage.innerHTML = ''; // Clear previous messages
        const formData = new FormData();
        formData.append('action', action);

        // Add note if input area is visible and not for 'sick'
        if (noteInputArea.style.display === 'block' && noteTextarea.value.trim() !== '') {
            formData.append('note', noteTextarea.value.trim());
        }

        fetch('ajax/record_event.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                statusMessage.innerHTML = `<div class="alert alert-success">${data.message}</div>`;
                currentStatus = data.current_status;
                currentStartTime = data.current_start_time;
                noteTextarea.value = ''; // Clear note after sending
                updateUI(); // Update UI immediately
            } else {
                statusMessage.innerHTML = `<div class="alert alert-danger">${data.message}</div>`;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            statusMessage.innerHTML = `<div class="alert alert-danger">Došlo k chybě při komunikaci se serverem.</div>`;
        });
    }

    // Function to fetch current status from server
    function fetchCurrentStatus() {
        const formData = new FormData();
        formData.append('action', 'get_current_status');

        fetch('ajax/record_event.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                currentStatus = data.current_status;
                currentStartTime = data.current_start_time;
                updateUI();
            } else {
                console.error('Failed to fetch current status:', data.message);
            }
        })
        .catch(error => {
            console.error('Error fetching current status:', error);
        });
    }

    // Event listeners for buttons
    btnStartWork.addEventListener('click', () => sendRequest('start_work'));
    btnEndWork.addEventListener('click', () => sendRequest('end_work'));
    btnStartHomeoffice.addEventListener('click', () => sendRequest('start_homeoffice'));
    btnStartTrip.addEventListener('click', () => sendRequest('start_trip'));
    btnStartSick.addEventListener('click', () => sendRequest('start_sick'));

    // Initialize UI by fetching current status from server
    fetchCurrentStatus();
});