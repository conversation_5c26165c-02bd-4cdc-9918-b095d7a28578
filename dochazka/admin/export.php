<?php
session_start();
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../classes/User.php';
require_once __DIR__ . '/../classes/Attendance.php';

$db = getDbConnection();
$user = new User($db);

if (!$user->isLoggedIn() || !$user->hasRole('admin')) {
    header("Location: ../login.php");
    exit();
}

$attendance = new Attendance($db);

$year = isset($_GET['year']) ? (int)$_GET['year'] : date('Y');
$month = isset($_GET['month']) ? (int)$_GET['month'] : date('m');
$selectedUserId = isset($_GET['user_id']) ? (int)$_GET['user_id'] : 0;

$records = [];
$filename = "dochazka";
$selectedUserName = "V<PERSON>ichni";

if ($selectedUserId > 0) {
    $records = $attendance->getUserRecordsForMonth($selectedUserId, $month, $year);
    $selectedUser = $user->getById($selectedUserId);
    $selectedUserName = $selectedUser['full_name'];
    $filename .= "_" . str_replace(' ', '_', $selectedUserName);
} else {
    $records = $attendance->getAllRecordsForMonth($month, $year);
}

$filename .= "_{$month}_{$year}.csv";

header('Content-Type: text/csv; charset=utf-8');
header('Content-Disposition: attachment; filename="' . $filename . '"');

$output = fopen('php://output', 'w');

// Přidání BOM pro správné zobrazení UTF-8 v Excelu
fwrite($output, "\xEF\xBB\xBF");

// --- Měsíční přehled (pouze pokud je vybrán jeden zaměstnanec) ---
if ($selectedUserId > 0) {
    $holidays = $attendance->getCzechHolidays($year);
    $daysInMonth = cal_days_in_month(CAL_GREGORIAN, $month, $year);
    
    $totalWorkedHours = 0;
    foreach ($records as $record) {
        $totalWorkedHours += $attendance->calculateWorkedHours($record['start_time'], $record['end_time']);
    }

    $workingDaysCount = 0;
    for ($day = 1; $day <= $daysInMonth; $day++) {
        $currentDateForFund = sprintf('%s-%s-%02d', $year, $month, $day);
        $isHolidayForFund = isset($holidays[$currentDateForFund]);
        $isWeekendForFund = in_array(date('N', strtotime($currentDateForFund)), [6, 7]);
        if (!$isHolidayForFund && !$isWeekendForFund) {
            $workingDaysCount++;
        }
    }
    $monthlyHourFund = $workingDaysCount * 8;
    $difference = $totalWorkedHours - $monthlyHourFund;

    fputcsv($output, ['Zaměstnanec:', $selectedUserName]);
    fputcsv($output, ['Měsíc:', "$month/$year"]);
    fputcsv($output, []); // Prázdný řádek
    fputcsv($output, ['Celkem odpracováno:', number_format($totalWorkedHours, 2)]);
    fputcsv($output, ['Měsíční fond hodin:', number_format($monthlyHourFund, 2)]);
    fputcsv($output, ['Rozdíl:', number_format($difference, 2)]);
    fputcsv($output, []); // Prázdný řádek pro oddělení
}


// --- Hlavička CSV pro záznamy ---
fputcsv($output, ['Jméno', 'Datum', 'Typ', 'Příchod', 'Odchod', 'Odpracováno (h)', 'Poznámka']);

// --- Data záznamů ---
foreach ($records as $record) {
    $workedHours = $attendance->calculateWorkedHours($record['start_time'], $record['end_time']);
    fputcsv($output, [
        $record['full_name'] ?? $selectedUserName,
        date('d.m.Y', strtotime($record['date'])),
        $record['event_name'],
        date('H:i', strtotime($record['start_time'])),
        $record['end_time'] ? date('H:i', strtotime($record['end_time'])) : '',
        $record['end_time'] ? number_format($workedHours, 2) : '',
        $record['note']
    ]);
}

fclose($output);
exit();
