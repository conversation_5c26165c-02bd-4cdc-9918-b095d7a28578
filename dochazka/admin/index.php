<?php
session_start(); // Ensure session is started
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../classes/User.php';
require_once __DIR__ . '/../classes/Attendance.php';
require_once __DIR__ . '/../templates/header.php'; // Použijeme stejnou hlavičku

$db = getDbConnection();
$user = new User($db);

if (!$user->isLoggedIn() || !$user->hasRole('admin')) {
    // Pokud uživatel není admin, přesměrujeme ho pryč
    header("Location: ../login.php");
    exit();
}

$attendance = new Attendance($db);

// Získání měsíce a roku z formuláře, výchozí je aktuální měsíc a rok
$year = isset($_GET['year']) ? (int)$_GET['year'] : date('Y');
$month = isset($_GET['month']) ? (int)$_GET['month'] : date('m');
$selectedUserId = isset($_GET['user_id']) ? (int)$_GET['user_id'] : 0;

// Načtení všech uživatelů pro select box
$allUsers = $user->getAll();

// Načtení záznamů podle filtru
if ($selectedUserId > 0) {
    $records = $attendance->getUserRecordsForMonth($selectedUserId, $month, $year);
} else {
    $records = $attendance->getAllRecordsForMonth($month, $year);
}
?>

<div class="d-flex justify-content-between align-items-center">
    <h2>Administrace docházky</h2>
    <a href="../index.php" class="back-to-dashboard-btn">Zpět na Dashboard</a>
</div>
<p>Zde můžete vidět přehled docházky všech zaměstnanců a exportovat data.</p>

<div class="card bg-light mb-4">
    <div class="card-body">
        <form class="row g-3 align-items-center" method="GET" action="">
            <div class="col-auto">
                <label for="month" class="form-label">Měsíc:</label>
                <select name="month" id="month" class="form-select">
                    <?php for ($m = 1; $m <= 12; $m++): ?>
                        <option value="<?php echo $m; ?>" <?php if ($m == $month) echo 'selected'; ?>>
                            <?php echo str_pad($m, 2, '0', STR_PAD_LEFT); ?>
                        </option>
                    <?php endfor; ?>
                </select>
            </div>
            <div class="col-auto">
                <label for="year" class="form-label">Rok:</label>
                <input type="number" name="year" id="year" class="form-control" value="<?php echo $year; ?>">
            </div>
            <div class="col-auto">
                <label for="user_id" class="form-label">Zaměstnanec:</label>
                <select name="user_id" id="user_id" class="form-select">
                    <option value="0">Všichni zaměstnanci</option>
                    <?php foreach ($allUsers as $u): ?>
                        <option value="<?php echo $u['id']; ?>" <?php if ($u['id'] == $selectedUserId) echo 'selected'; ?>>
                            <?php echo htmlspecialchars($u['full_name']); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-auto">
                <button type="submit" class="btn btn-primary mt-4">Zobrazit</button>
            </div>
            <div class="col-auto">
                 <a href="export.php?month=<?php echo $month; ?>&year=<?php echo $year; ?>&user_id=<?php echo $selectedUserId; ?>" class="btn btn-success mt-4">Exportovat do CSV</a>
            </div>
        </form>
    </div>
</div>


<h3>Přehled za <?php echo "$month/$year"; ?> <?php echo ($selectedUserId > 0 && !empty($records)) ? 'pro ' . htmlspecialchars($records[0]['full_name']) : ''; ?></h3>
<div class="table-responsive">
    <table class="table table-striped table-bordered">
        <thead>
            <tr>
                <th>Jméno</th>
                <th>Datum</th>
                <th>Typ</th>
                <th>Příchod</th>
                <th>Odchod</th>
                <th>Odpracováno (h)</th>
                <th>Poznámka</th>
            </tr>
        </thead>
        <tbody>
            <?php if (empty($records)): ?>
                <tr>
                    <td colspan="7" class="text-center">Pro zadané období nebyly nalezeny žádné záznamy.</td>
                </tr>
            <?php else: ?>
                <?php foreach ($records as $record):
                    $workedHours = $attendance->calculateWorkedHours($record['start_time'], $record['end_time']);
                ?>
                    <tr>
                        <td><?php echo htmlspecialchars($record['full_name']); ?></td>
                        <td><?php echo date('d.m.Y', strtotime($record['date'])); ?></td>
                        <td><?php echo htmlspecialchars($record['event_name']); ?></td>
                        <td><?php echo date('H:i', strtotime($record['start_time'])); ?></td>
                        <td><?php echo $record['end_time'] ? date('H:i', strtotime($record['end_time'])) : '---'; ?></td>
                        <td><?php echo $record['end_time'] ? number_format($workedHours, 2) : '---'; ?></td>
                        <td><?php echo htmlspecialchars($record['note']); ?></td>
                    </tr>
                <?php endforeach; ?>
            <?php endif; ?>
        </tbody>
    </table>
</div>

<?php require_once __DIR__ . '/../templates/footer.php'; ?>