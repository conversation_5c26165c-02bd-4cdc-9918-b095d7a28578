# Funkcionalita: Implementace evidence dovolené

Tento dokument podrobně popisuje kroky potřebné k implementaci funkcionality pro evidenci dovolené v docházkovém systému.

## 1. Rozšíření databáze

Je nutné upravit stávající tabulku `event_types` a přidat novou tabulku `vacation_requests`.

### 1.1. Úprava `event_types`
Přidejte nový typ události do tabulky `event_types`. To lze provést SQL dotazem:

```sql
INSERT INTO event_types (event_name, event_code) VALUES ('Dovolená', 'VACATION');
```

### 1.2. <PERSON><PERSON> tabulka `vacation_requests`
Vytvořte novou tabulku pro ukládání žádostí o dovolenou.

```sql
CREATE TABLE vacation_requests (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    reason TEXT,
    approved_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (approved_by) REFERENCES users(id)
);
```

## 2. Úprava Backendu (PHP)

Rozšíříme třídu `classes/Attendance.php` o nové metody pro správu dovolené.

### 2.1. Úprava `classes/Attendance.php`

Přidejte následující metody do třídy `Attendance`:

```php
// V classes/Attendance.php

// ... (existující kód) ...

/**
 * Vytvoří novou žádost o dovolenou.
 * @param int $userId ID uživatele
 * @param string $startDate Datum začátku dovolené (YYYY-MM-DD)
 * @param string $endDate Datum konce dovolené (YYYY-MM-DD)
 * @param string|null $reason Důvod žádosti
 * @return int ID nové žádosti
 */
public function requestVacation($userId, $startDate, $endDate, $reason = null) {
    $stmt = $this->db->prepare(
        "INSERT INTO vacation_requests (user_id, start_date, end_date, reason)
         VALUES (:user_id, :start_date, :end_date, :reason)"
    );
    $stmt->execute([
        'user_id' => $userId,
        'start_date' => $startDate,
        'end_date' => $endDate,
        'reason' => $reason
    ]);
    return $this->db->lastInsertId();
}

/**
 * Načte žádosti o dovolenou.
 * @param string $status Stav žádosti ('pending', 'approved', 'rejected', nebo prázdný pro všechny)
 * @return array Seznam žádostí
 */
public function getVacationRequests($status = '') {
    $sql = "SELECT vr.*, u.full_name AS user_name, au.full_name AS approver_name
            FROM vacation_requests vr
            JOIN users u ON vr.user_id = u.id
            LEFT JOIN users au ON vr.approved_by = au.id";
    $params = [];
    if (!empty($status)) {
        $sql .= " WHERE vr.status = :status";
        $params['status'] = $status;
    }
    $sql .= " ORDER BY vr.created_at DESC";

    $stmt = $this->db->prepare($sql);
    $stmt->execute($params);
    return $stmt->fetchAll();
}

/**
 * Schválí žádost o dovolenou a vytvoří záznamy v docházce.
 * @param int $requestId ID žádosti
 * @param int $adminId ID admina, který schvaluje
 * @return bool True při úspěchu, false jinak
 */
public function approveVacation($requestId, $adminId) {
    $this->db->beginTransaction();
    try {
        // 1. Získat detaily žádosti
        $stmt = $this->db->prepare("SELECT user_id, start_date, end_date FROM vacation_requests WHERE id = :id");
        $stmt->execute(['id' => $requestId]);
        $request = $stmt->fetch();

        if (!$request) {
            $this->db->rollBack();
            return false;
        }

        // 2. Aktualizovat status žádosti
        $stmt = $this->db->prepare("UPDATE vacation_requests SET status = 'approved', approved_by = :admin_id WHERE id = :id");
        $stmt->execute(['admin_id' => $adminId, 'id' => $requestId]);

        // 3. Získat event_type_id pro 'VACATION'
        $stmt = $this->db->prepare("SELECT id FROM event_types WHERE event_code = 'VACATION'");
        $stmt->execute();
        $vacationEventTypeId = $stmt->fetchColumn();

        if (!$vacationEventTypeId) {
            throw new Exception("Event type 'VACATION' not found.");
        }

        // 4. Vytvořit záznamy v attendance_records pro každý den dovolené
        $startDate = new DateTime($request['start_date']);
        $endDate = new DateTime($request['end_date']);
        $interval = new DateInterval('P1D'); // 1 day
        $period = new DatePeriod($startDate, $interval, $endDate->modify('+1 day')); // Include end date

        foreach ($period as $date) {
            // Zkontrolovat, zda den není víkend nebo svátek (volitelné, záleží na pravidlech firmy)
            // Pro jednoduchost zde nebudeme kontrolovat víkendy/svátky, dovolená se počítá i přes ně.
            $this->recordStartTime($request['user_id'], $vacationEventTypeId, 'Dovolená', $date->format('Y-m-d H:i:s'), $date->format('Y-m-d H:i:s'));
            // Poznámka: Pro dovolenou nastavíme start_time a end_time na stejný čas, aby se nepočítaly odpracované hodiny.
            // Nebo můžete nastavit end_time na NULL a pak ji aktualizovat, pokud by se dovolená měla "ukončovat".
            // Pro jednoduchost zde předpokládáme, že dovolená je celodenní a nemá odpracované hodiny.
        }

        $this->db->commit();
        return true;
    } catch (Exception $e) {
        $this->db->rollBack();
        error_log("Error approving vacation: " . $e->getMessage());
        return false;
    }
}

/**
 * Zamítne žádost o dovolenou.
 * @param int $requestId ID žádosti
 * @param int $adminId ID admina, který zamítá
 * @return bool True při úspěchu, false jinak
 */
public function rejectVacation($requestId, $adminId) {
    $stmt = $this->db->prepare("UPDATE vacation_requests SET status = 'rejected', approved_by = :admin_id WHERE id = :id");
    return $stmt->execute(['admin_id' => $adminId, 'id' => $requestId]);
}

/**
 * Spočítá počet dní dovolené pro daného uživatele a rok.
 * @param int $userId ID uživatele
 * @param int $year Rok
 * @return int Počet dní dovolené
 */
public function getUserVacationDays($userId, $year) {
    $stmt = $this->db->prepare(
        "SELECT SUM(DATEDIFF(end_date, start_date) + 1) AS total_days
         FROM vacation_requests
         WHERE user_id = :user_id AND YEAR(start_date) = :year AND status = 'approved'"
    );
    $stmt->execute(['user_id' => $userId, 'year' => $year]);
    return (int) $stmt->fetchColumn();
}

// Upravte metodu recordStartTime, aby mohla přijímat volitelný čas a datum
public function recordStartTime($userId, $eventTypeId, $note = null, $startTime = null, $date = null) {
    $date = $date ?? date('Y-m-d');
    $startTime = $startTime ?? date('Y-m-d H:i:s');

    $stmt = $this->db->prepare(
        "INSERT INTO attendance_records (user_id, event_type_id, start_time, date, note)
         VALUES (:user_id, :event_type_id, :start_time, :date, :note)"
    );
    $stmt->execute([
        'user_id' => $userId,
        'event_type_id' => $eventTypeId,
        'start_time' => $startTime,
        'date' => $date,
        'note' => $note
    ]);
    return $this->db->lastInsertId();
}
```

## 3. Uživatelské rozhraní

### 3.1. Nová stránka `vacation.php`

Vytvořte nový soubor `dochazka/vacation.php` pro uživatele, kde mohou žádat o dovolenou a vidět své žádosti.

```php
<?php
session_start();
require_once __DIR__ . '/config/database.php';
require_once __DIR__ . '/classes/User.php';
require_once __DIR__ . '/classes/Attendance.php';
require_once 'templates/header.php';

$db = getDbConnection();
$user = new User($db);

if (!$user->isLoggedIn()) {
    header('Location: login.php');
    exit();
}

$attendance = new Attendance($db);
$userId = $_SESSION['user_id'];
$message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['request_vacation'])) {
    $startDate = $_POST['start_date'] ?? '';
    $endDate = $_POST['end_date'] ?? '';
    $reason = $_POST['reason'] ?? null;

    if (!empty($startDate) && !empty($endDate)) {
        if ($attendance->requestVacation($userId, $startDate, $endDate, $reason)) {
            $message = '<div class="alert alert-success">Žádost o dovolenou byla úspěšně odeslána.</div>';
        } else {
            $message = '<div class="alert alert-danger">Chyba při odesílání žádosti o dovolenou.</div>';
        }
    } else {
        $message = '<div class="alert alert-warning">Vyplňte prosím datum začátku i konce dovolené.</div>';
    }
}

$userVacationRequests = $attendance->getVacationRequestsByUserId($userId); // Bude potřeba přidat tuto metodu do Attendance.php
?>

<div class="container mt-4">
    <h2>Žádost o dovolenou</h2>
    <?php echo $message; ?>

    <form method="POST">
        <div class="mb-3">
            <label for="start_date" class="form-label">Datum začátku:</label>
            <input type="date" class="form-control" id="start_date" name="start_date" required>
        </div>
        <div class="mb-3">
            <label for="end_date" class="form-label">Datum konce:</label>
            <input type="date" class="form-control" id="end_date" name="end_date" required>
        </div>
        <div class="mb-3">
            <label for="reason" class="form-label">Důvod (volitelné):</label>
            <textarea class="form-control" id="reason" name="reason" rows="3"></textarea>
        </div>
        <button type="submit" name="request_vacation" class="btn btn-primary">Odeslat žádost</button>
    </form>

    <h3 class="mt-5">Moje žádosti o dovolenou</h3>
    <div class="table-responsive">
        <table class="table table-striped table-bordered">
            <thead>
                <tr>
                    <th>Od</th>
                    <th>Do</th>
                    <th>Důvod</th>
                    <th>Stav</th>
                    <th>Schválil/a</th>
                    <th>Odesláno</th>
                </tr>
            </thead>
            <tbody>
                <?php if (!empty($userVacationRequests)): ?>
                    <?php foreach ($userVacationRequests as $request): ?>
                        <tr>
                            <td><?php echo htmlspecialchars($request['start_date']); ?></td>
                            <td><?php echo htmlspecialchars($request['end_date']); ?></td>
                            <td><?php echo htmlspecialchars($request['reason'] ?? '-'); ?></td>
                            <td><?php echo htmlspecialchars($request['status']); ?></td>
                            <td><?php echo htmlspecialchars($request['approver_name'] ?? '-'); ?></td>
                            <td><?php echo htmlspecialchars($request['created_at']); ?></td>
                        </tr>
                    <?php endforeach; ?>
                <?php else: ?>
                    <tr>
                        <td colspan="6">Žádné žádosti o dovolenou.</td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>

<?php require_once 'templates/footer.php'; ?>
```

**Poznámka:** Bude potřeba přidat metodu `getVacationRequestsByUserId($userId)` do `Attendance.php`, která načte žádosti pouze pro konkrétního uživatele.

### 3.2. Úprava `index.php` pro zobrazení dovolené

V `index.php` bude potřeba načíst schválené dovolené uživatele a zohlednit je v tabulce docházky.

1.  **Načtení dovolených:**
    ```php
    // V index.php, po načtení $holidays
    $approvedVacations = $attendance->getApprovedVacationsForUserMonth($currentUser['id'], $month, $year);
    // Bude potřeba přidat metodu getApprovedVacationsForUserMonth do Attendance.php
    ```
2.  **Zobrazení v tabulce:**
    Upravte smyčku pro dny v měsíci tak, aby kontrolovala i dovolenou.

    ```php
    // V index.php, uvnitř smyčky for ($day = 1; ...)
    $isVacation = false;
    foreach ($approvedVacations as $vacation) {
        if ($currentDate >= $vacation['start_date'] && $currentDate <= $vacation['end_date']) {
            $isVacation = true;
            break;
        }
    }

    $rowClass = '';
    if ($isHoliday) {
        $rowClass = 'holiday-day';
    } elseif ($isWeekend) {
        $rowClass = 'weekend-day';
    } elseif ($isVacation) {
        $rowClass = 'vacation-day'; // Nová CSS třída pro dovolenou
    }
    ```
    A v řádku tabulky pro daný den přidejte zobrazení "Dovolená":
    ```php
    <tr class="<?php echo $rowClass; ?>">
        <td><?php echo date('d.m.Y', strtotime($currentDate)); ?></td>
        <td>
            <?php if ($isHoliday) echo htmlspecialchars($holidays[$currentDate]); ?>
            <?php if ($isVacation) echo 'Dovolená'; ?>
        </td>
        <td colspan="4"></td>
    </tr>
    ```

### 3.3. Nová CSS třída pro dovolenou

Přidejte do `public/css/style.css` novou třídu pro podbarvení dnů dovolené:

```css
/* V public/css/style.css */
.vacation-day {
    background-color: var(--pastel-yellow) !important; /* Např. pastelově žlutá pro dovolenou */
}
```

## 4. Administrační rozhraní

### 4.1. Nová stránka `admin/vacations.php`

Vytvořte nový soubor `dochazka/admin/vacations.php` pro správu žádostí o dovolenou.

```php
<?php
session_start();
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../classes/User.php';
require_once __DIR__ . '/../classes/Attendance.php';
require_once __DIR__ . '/../templates/header.php'; // Předpokládá se, že admin má vlastní header nebo sdílí

$db = getDbConnection();
$user = new User($db);

if (!$user->isLoggedIn() || !$user->hasRole('admin')) {
    header('Location: ../login.php');
    exit();
}

$attendance = new Attendance($db);
$adminId = $_SESSION['user_id'];
$message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $requestId = $_POST['request_id'] ?? null;
    $action = $_POST['action'] ?? '';

    if ($requestId) {
        if ($action === 'approve') {
            if ($attendance->approveVacation($requestId, $adminId)) {
                $message = '<div class="alert alert-success">Žádost o dovolenou byla schválena.</div>';
            } else {
                $message = '<div class="alert alert-danger">Chyba při schvalování žádosti.</div>';
            }
        } elseif ($action === 'reject') {
            if ($attendance->rejectVacation($requestId, $adminId)) {
                $message = '<div class="alert alert-success">Žádost o dovolenou byla zamítnuta.</div>';
            } else {
                $message = '<div class="alert alert-danger">Chyba při zamítání žádosti.</div>';
            }
        }
    }
}

$pendingRequests = $attendance->getVacationRequests('pending');
$allRequests = $attendance->getVacationRequests(); // Pro zobrazení historie
?>

<div class="container mt-4">
    <h2>Správa žádostí o dovolenou</h2>
    <?php echo $message; ?>

    <h3 class="mt-4">Čekající žádosti</h3>
    <?php if (!empty($pendingRequests)): ?>
        <div class="table-responsive">
            <table class="table table-striped table-bordered">
                <thead>
                    <tr>
                        <th>Uživatel</th>
                        <th>Od</th>
                        <th>Do</th>
                        <th>Důvod</th>
                        <th>Odesláno</th>
                        <th>Akce</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($pendingRequests as $request): ?>
                        <tr>
                            <td><?php echo htmlspecialchars($request['user_name']); ?></td>
                            <td><?php echo htmlspecialchars($request['start_date']); ?></td>
                            <td><?php echo htmlspecialchars($request['end_date']); ?></td>
                            <td><?php echo htmlspecialchars($request['reason'] ?? '-'); ?></td>
                            <td><?php echo htmlspecialchars($request['created_at']); ?></td>
                            <td>
                                <form method="POST" style="display:inline-block;">
                                    <input type="hidden" name="request_id" value="<?php echo $request['id']; ?>">
                                    <button type="submit" name="action" value="approve" class="btn btn-success btn-sm">Schválit</button>
                                </form>
                                <form method="POST" style="display:inline-block;">
                                    <input type="hidden" name="request_id" value="<?php echo $request['id']; ?>">
                                    <button type="submit" name="action" value="reject" class="btn btn-danger btn-sm">Zamítnout</button>
                                </form>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php else: ?>
        <p>Žádné čekající žádosti o dovolenou.</p>
    <?php endif; ?>

    <h3 class="mt-5">Historie žádostí</h3>
    <?php if (!empty($allRequests)): ?>
        <div class="table-responsive">
            <table class="table table-striped table-bordered">
                <thead>
                    <tr>
                        <th>Uživatel</th>
                        <th>Od</th>
                        <th>Do</th>
                        <th>Důvod</th>
                        <th>Stav</th>
                        <th>Schválil/a</th>
                        <th>Odesláno</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($allRequests as $request): ?>
                        <tr>
                            <td><?php echo htmlspecialchars($request['user_name']); ?></td>
                            <td><?php echo htmlspecialchars($request['start_date']); ?></td>
                            <td><?php echo htmlspecialchars($request['end_date']); ?></td>
                            <td><?php echo htmlspecialchars($request['reason'] ?? '-'); ?></td>
                            <td><?php echo htmlspecialchars($request['status']); ?></td>
                            <td><?php echo htmlspecialchars($request['approver_name'] ?? '-'); ?></td>
                            <td><?php echo htmlspecialchars($request['created_at']); ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php else: ?>
        <p>Žádná historie ��ádostí o dovolenou.</p>
    <?php endif; ?>
</div>

<?php require_once __DIR__ . '/../templates/footer.php'; ?>
```

## 5. Další úpravy a testování

*   **Navigace:** Přidejte odkaz na `vacation.php` do uživatelské navigace a na `admin/vacations.php` do administrace.
*   **Testování:** Důkladně otestujte všechny nové funkce:
    *   Odesílání žádostí o dovolenou uživatelem.
    *   Schvalování/zamítání žádostí adminem.
    *   Správné zobrazení schválených dovolených v docházce uživatele.
    *   Správné vytvoření záznamů v `attendance_records` po schválení.
*   **Chybové stavy:** Zvažte ošetření chybových stavů (např. pokus o schválení již schválené žádosti).
*   **Uživatelská role:** Ujistěte se, že pouze admini mají přístup k `admin/vacations.php`.
*   **Časový fond dovolené:** Pokud je potřeba sledovat zbývající dny dovolené pro každého zaměstnance, bude nutné rozšířit tabulku `users` o pole `vacation_days_total` a implementovat logiku pro jeho aktualizaci a odečítání schválených dní.
