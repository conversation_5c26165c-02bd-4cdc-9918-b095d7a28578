<?php
// hash_password.php
$hashed_password = '';
$plain_password = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && !empty($_POST['password'])) {
    $plain_password = $_POST['password'];
    // Použijeme standardní a bezpečný algoritmus BCRYPT
    $hashed_password = password_hash($plain_password, PASSWORD_DEFAULT);
}
?>
<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gener<PERSON>tor hesel</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <h1 class="text-center mt-5"><PERSON><PERSON><PERSON><PERSON> hesel</h1>
                <div class="card mt-4">
                    <div class="card-body">
                        <form method="POST">
                            <div class="mb-3">
                                <label for="password" class="form-label">Zadejte heslo:</label>
                                <input type="text" class="form-control" id="password" name="password" required>
                            </div>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">Vygenerovat hash</button>
                            </div>
                        </form>

                        <?php if ($hashed_password): ?>
                            <div class="mt-4">
                                <h3>Vygenerovaný hash pro heslo "<?php echo htmlspecialchars($plain_password); ?>" je:</h3>
                                <pre class="alert alert-success"><?php echo htmlspecialchars($hashed_password); ?></pre>
                                <p><strong>Instrukce:</strong> Zkopírujte tento hash a vložte ho do sloupce <code>password</code> v tabulce <code>users</code> pro vašeho administrátora.</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                 <p class="text-center mt-3 text-muted">PHP verze na serveru: <?php echo phpversion(); ?></p>
            </div>
        </div>
    </div>
</body>
</html>
