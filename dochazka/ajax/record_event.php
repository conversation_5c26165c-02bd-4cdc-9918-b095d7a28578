<?php
session_start();
ob_start(); // Start output buffering

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../classes/User.php';
require_once __DIR__ . '/../classes/Attendance.php';

$response = [];

try {
    $db = getDbConnection();
    $user = new User($db);

    if (!$user->isLoggedIn()) {
        throw new Exception('Nejste přihlášeni.', 403);
    }

    $attendance = new Attendance($db);
    $userId = $_SESSION['user_id'];
    $action = $_POST['action'] ?? '';
    $note = $_POST['note'] ?? null;

    $response = ['status' => 'error', 'message' => 'Neznámá akce.'];
    $activeRecord = $attendance->getActiveRecord($userId);

    switch ($action) {
        case 'start_work':
        case 'start_homeoffice':
        case 'start_sick':
        case 'start_trip':
            $eventMap = [
                'start_work' => ['id' => 1, 'msg' => 'Příchod zaznamenán.'],
                'start_homeoffice' => ['id' => 2, 'msg' => 'Začátek homeoffice zaznamenán.'],
                'start_sick' => ['id' => 3, 'msg' => 'Návštěva lékaře zaznamenána.'],
                'start_trip' => ['id' => 4, 'msg' => 'Začátek služební cesty zaznamenán.'],
            ];
            $eventInfo = $eventMap[$action];

            if ($activeRecord) {
                $attendance->recordEndTime($userId);
            }
            $attendance->recordStartTime($userId, $eventInfo['id'], $note);
            $response = ['status' => 'success', 'message' => $eventInfo['msg']];
            break;

        case 'end_work':
            if ($activeRecord) {
                if ($note !== null && $note !== '') {
                    $attendance->updateNote($activeRecord['id'], $note);
                }
                $attendance->recordEndTime($userId);
                $response = ['status' => 'success', 'message' => 'Odchod zaznamenán.'];
            } else {
                $response = ['status' => 'error', 'message' => 'Nemáte žádný aktivní záznam k ukončení.'];
            }
            break;

        case 'get_current_status':
            $response = ['status' => 'success'];
            break;

        case 'update_active_note':
            if ($activeRecord) {
                if ($note !== null) {
                    $attendance->updateNote($activeRecord['id'], $note);
                    $response = ['status' => 'success', 'message' => 'Poznámka uložena.'];
                } else {
                    $response = ['status' => 'error', 'message' => 'Poznámka nemůže být prázdná.'];
                }
            } else {
                $response = ['status' => 'error', 'message' => 'Nemáte žádný aktivní záznam k aktualizaci poznámky.'];
            }
            break;
    }

    $finalActiveRecord = $attendance->getActiveRecord($userId);
    if ($finalActiveRecord) {
        $response['current_status'] = $finalActiveRecord['event_code'];
        $response['current_start_time'] = strtotime($finalActiveRecord['start_time']) * 1000;
    } else {
        $response['current_status'] = 'none';
        $response['current_start_time'] = null;
    }

} catch (Exception $e) {
    // If an error occurs, set a generic error message
    $response = ['status' => 'error', 'message' => 'Došlo k serverové chybě: ' . $e->getMessage()];
    if ($e->getCode()) {
        http_response_code($e->getCode());
    } else {
        http_response_code(500);
    }
}

ob_end_clean(); // Clean (erase) the output buffer and turn off output buffering
header('Content-Type: application/json');
echo json_encode($response);
exit();
