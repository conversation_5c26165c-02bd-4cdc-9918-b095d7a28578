<?php
session_start();
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../classes/User.php';
require_once __DIR__ . '/../classes/Attendance.php';

header('Content-Type: application/json');

$db = getDbConnection();
$user = new User($db);

if (!$user->isLoggedIn()) {
    echo json_encode(['status' => 'error', 'message' => 'Nejste přihlášeni.']);
    exit();
}

$attendance = new Attendance($db);
$userId = $_SESSION['user_id'];
$year = date('Y');
$month = date('m');

$records = $attendance->getUserRecordsForMonth($userId, $month, $year);
$holidays = $attendance->getCzechHolidays($year);

$daysInMonth = cal_days_in_month(CAL_GREGORIAN, $month, $year);
$totalWorkedHours = 0;
foreach ($records as $record) {
    $totalWorkedHours += $attendance->calculateWorkedHours($record['start_time'], $record['end_time']);
}


// Calculate monthly hour fund
$workingDaysCount = 0;
for ($day = 1; $day <= $daysInMonth; $day++) {
    $currentDateForFund = sprintf('%s-%s-%02d', $year, $month, $day);
    $isHolidayForFund = isset($holidays[$currentDateForFund]);
    $isWeekendForFund = in_array(date('N', strtotime($currentDateForFund)), [6, 7]);
    if (!$isHolidayForFund && !$isWeekendForFund) {
        $workingDaysCount++;
    }
}
$monthlyHourFund = $workingDaysCount * 8; // Assuming 8 hours per working day

$tableRows = [];
foreach ($records as $record) {
    $workedHours = $attendance->calculateWorkedHours($record['start_time'], $record['end_time']);
    $tableRows[] = [
        'date' => date('d.m.Y', strtotime($record['date'])),
        'event_name' => htmlspecialchars($record['event_name']),
        'start_time' => date('H:i', strtotime($record['start_time'])),
        'end_time' => $record['end_time'] ? date('H:i', strtotime($record['end_time'])) : '---',
        'worked_hours' => $record['end_time'] ? number_format($workedHours, 2) : '---',
        'row_class' => '' // This will be determined on the client side for holidays/weekends
    ];
}

$today = date('Y-m-d');
$workedHoursToday = $attendance->getWorkedHoursForDay($userId, $today);
$workedHoursWeek = $attendance->getWorkedHoursForWeek($userId, $today);

// Prepare data for the monthly overview card
$monthlyOverview = [
    'totalWorkedHours' => number_format($totalWorkedHours, 2),
    'monthlyHourFund' => number_format($monthlyHourFund, 2),
    'difference' => number_format($totalWorkedHours - $monthlyHourFund, 2),
    'workedHoursToday' => number_format($workedHoursToday, 2),
    'workedHoursWeek' => number_format($workedHoursWeek, 2)
];

echo json_encode([
    'status' => 'success',
    'records' => $tableRows,
    'monthlyOverview' => $monthlyOverview,
    'holidays' => $holidays, // Pass holidays to client for row class determination
    'daysInMonth' => $daysInMonth,
    'year' => $year,
    'month' => $month
]);

?>
