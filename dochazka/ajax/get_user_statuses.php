<?php
session_start();
ob_start(); // Start output buffering to catch any stray output

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../classes/User.php';
require_once __DIR__ . '/../classes/Attendance.php';

try {
    $db = getDbConnection();
    $user = new User($db);

    if (!$user->isLoggedIn()) {
        // Don't output anything if not logged in, the fetch will fail and stop.
        http_response_code(403);
        exit();
    }

    $attendance = new Attendance($db);
    $allUsersStatus = $attendance->getAllUsersCurrentStatus();

    ob_end_clean(); // Clear buffer before outputting the list

    foreach ($allUsersStatus as $status):
        $statusText = 'Nepřítomen';
        $statusClass = 'bg-secondary';
        if ($status['event_name']) {
            $statusText = $status['event_name'];
            switch ($status['event_code']) {
                case 'WORK':
                    $statusClass = 'bg-success';
                    break;
                case 'HOMEOFFICE':
                    $statusClass = 'bg-info';
                    break;
                case 'SICK':
                    $statusClass = 'bg-warning text-dark';
                    break;
                case 'TRIP':
                    $statusClass = 'bg-primary';
                    break;
                case 'VACATION':
                    $statusClass = 'bg-info';
                    break;
                default:
                    $statusClass = 'bg-dark';
            }
        }
    ?>
        <li class="list-group-item d-flex justify-content-between align-items-center">
            <?php echo htmlspecialchars($status['full_name']); ?>
            <span class="badge <?php echo $statusClass; ?>"><?php echo htmlspecialchars($statusText); ?></span>
        </li>
    <?php endforeach;

} catch (Exception $e) {
    ob_end_clean(); // Clean buffer in case of error
    // Output a user-friendly error message within the list structure
    echo '<li class="list-group-item text-danger">Došlo k chybě při načítání stavů.</li>';
    // Log the actual error for debugging
    error_log('Error in get_user_statuses.php: ' . $e->getMessage());
}
