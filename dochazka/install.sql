-- Struktura pro tabulku `users`
CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL,
  `full_name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `role` enum('user','admin') NOT NULL DEFAULT 'user',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Struktura pro tabulku `event_types`
CREATE TABLE `event_types` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `event_name` varchar(255) NOT NULL,
  `event_code` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `event_code` (`event_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- <PERSON><PERSON>ž<PERSON><PERSON>ladních typů událostí
INSERT INTO `event_types` (`id`, `event_name`, `event_code`) VALUES
(1, 'Práce v kanceláři', 'WORK'),
(2, 'Homeoffice', 'HOMEOFFICE'),
(3, 'Lékař', 'SICK'),
(4, 'Služební cesta', 'TRIP');

-- Struktura pro tabulku `attendance_records`
CREATE TABLE `attendance_records` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `event_type_id` int(11) NOT NULL,
  `start_time` datetime NOT NULL,
  `end_time` datetime DEFAULT NULL,
  `note` text,
  `date` date NOT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `event_type_id` (`event_type_id`),
  CONSTRAINT `attendance_records_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `attendance_records_ibfk_2` FOREIGN KEY (`event_type_id`) REFERENCES `event_types` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Struktura pro tabulku `holidays`
CREATE TABLE `holidays` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `holiday_date` date NOT NULL,
  `holiday_name` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `holiday_date` (`holiday_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Vložení českých svátků pro rok 2025 (jako příklad)
INSERT INTO `holidays` (`holiday_date`, `holiday_name`) VALUES
('2025-01-01', 'Nový rok'),
('2025-04-18', 'Velký pátek'),
('2025-04-21', 'Velikonoční pondělí'),
('2025-05-01', 'Svátek práce'),
('2025-05-08', 'Den vítězství'),
('2025-07-05', 'Den slovanských věrozvěstů Cyrila a Metoděje'),
('2025-07-06', 'Den upálení mistra Jana Husa'),
('2025-09-28', 'Den české státnosti'),
('2025-10-28', 'Den vzniku samostatného československého státu'),
('2025-11-17', 'Den boje za svobodu a demokracii'),
('2025-12-24', 'Štědrý den'),
('2025-12-25', '1. svátek vánoční'),
('2025-12-26', '2. svátek vánoční');
