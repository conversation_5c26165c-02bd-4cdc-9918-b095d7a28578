<?php
require_once 'templates/header.php';

$attendance = new Attendance($db);
$year = date('Y');
$month = date('m');
$today = date('Y-m-d');

$records = $attendance->getUserRecordsForMonth($currentUser['id'], $month, $year);
$holidays = $attendance->getCzechHolidays($year);
$allUsersStatus = $attendance->getAllUsersCurrentStatus();

$daysInMonth = cal_days_in_month(CAL_GREGORIAN, $month, $year);
$totalWorkedHours = 0;
foreach ($records as $record) {
    $totalWorkedHours += $attendance->calculateWorkedHours($record['start_time'], $record['end_time']);
}

// Calculate monthly hour fund
$workingDaysCount = 0;
for ($day = 1; $day <= $daysInMonth; $day++) {
    $currentDateForFund = sprintf('%s-%s-%02d', $year, $month, $day);
    $isHolidayForFund = isset($holidays[$currentDateForFund]);
    $isWeekendForFund = in_array(date('N', strtotime($currentDateForFund)), [6, 7]);
    if (!$isHolidayForFund && !$isWeekendForFund) {
        $workingDaysCount++;
    }
}
$monthlyHourFund = $workingDaysCount * 8; // Assuming 8 hours per working day

$workedHoursToday = $attendance->getWorkedHoursForDay($currentUser['id'], $today);
$workedHoursWeek = $attendance->getWorkedHoursForWeek($currentUser['id'], $today);
?>

<div class="row">
    <div class="col-md-4">
        <h2>Ovládací panel</h2>
        <div class="d-flex flex-wrap justify-content-around align-items-center attendance-controls-grid" id="attendance-controls">
            <button class="btn btn-green attendance-btn" id="btn-start-work" data-action="start_work" style="display: none;">Příchod</button>
            <button class="btn btn-red attendance-btn" id="btn-end-work" data-action="end_work" style="display: none;">Odchod</button>
            <button class="btn btn-blue attendance-btn" id="btn-start-homeoffice" data-action="start_homeoffice" style="display: none;">Homeoffice</button>
            <button class="btn btn-yellow attendance-btn" id="btn-start-trip" data-action="start_trip" style="display: none;">Služební cesta</button>
            <button class="btn btn-purple attendance-btn" id="btn-start-sick" data-action="start_sick" style="display: none;">Lékař</button>
        </div>
        <div id="status-message" class="mt-3"></div>
        <div id="working-time-display" class="mt-3 h4 text-center" style="display: none;"></div>
        <div id="note-input-area" class="mt-3" style="display: none;">
            <label for="note-text" class="form-label">Poznámka:</label>
            <textarea class="form-control" id="note-text" rows="3"></textarea>
            <button class="btn btn-secondary mt-2" id="btn-save-note">Uložit poznámku</button>
        </div>

        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">Přehled stavu uživatelů</h5>
            </div>
            <ul class="list-group list-group-flush" id="user-status-list">
                <?php foreach ($allUsersStatus as $status):
                    $statusText = 'Nepřítomen';
                    $statusClass = 'bg-secondary';
                    if ($status['event_name']) {
                        $statusText = $status['event_name'];
                        switch ($status['event_code']) {
                            case 'WORK':
                                $statusClass = 'bg-success';
                                break;
                            case 'HOMEOFFICE':
                                $statusClass = 'bg-info';
                                break;
                            case 'SICK':
                                $statusClass = 'bg-warning text-dark';
                                break;
                            case 'TRIP':
                                $statusClass = 'bg-primary';
                                break;
                            case 'VACATION':
                                $statusClass = 'bg-info';
                                break;
                            default:
                                $statusClass = 'bg-dark';
                        }
                    }
                ?>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <?php echo htmlspecialchars($status['full_name']); ?>
                        <span class="badge <?php echo $statusClass; ?>"><?php echo htmlspecialchars($statusText); ?></span>
                    </li>
                <?php endforeach; ?>
            </ul>
        </div>
    </div>
    <div class="col-md-8">
        <h2>Docházka za <?php echo "$month/$year"; ?></h2>
        <div class="monthly-overview">
            <div class="overview-block">
                <h5>Celkem</h5>
                <div class="progress-circle" style="background: conic-gradient(var(--prichod) 0% <?php echo ($totalWorkedHours / $monthlyHourFund) * 100; ?>%, #eee <?php echo ($totalWorkedHours / $monthlyHourFund) * 100; ?>% 100%);">
                    <span id="overview-total"><?php echo number_format($totalWorkedHours, 2); ?> h</span>
                </div>
            </div>
            <div class="overview-block">
                <h5>Fond</h5>
                <p class="h4" id="overview-fund"><?php echo number_format($monthlyHourFund, 2); ?> h</p>
                <h5>Zbývá</h5>
                <p class="h4" id="overview-remaining"><?php echo number_format($monthlyHourFund - $totalWorkedHours, 2); ?> h</p>
            </div>
            <div class="overview-block">
                <h5>Dnes</h5>
                <p class="h4" id="overview-today"><?php echo number_format($workedHoursToday, 2); ?> h</p>
                <h5>Týden</h5>
                <p class="h4" id="overview-week"><?php echo number_format($workedHoursWeek, 2); ?> h</p>
            </div>
        </div>
        <div class="table-responsive">
            <table class="table table-striped table-bordered">
                <thead>
                    <tr>
                        <th>Datum</th>
                        <th>Typ</th>
                        <th>Příchod</th>
                        <th>Odchod</th>
                        <th>Odpracováno (h)</th>
                    </tr>
                </thead>
                <tbody id="attendance-table-body">
                    <!-- Records will be loaded here by JavaScript -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const statusMessage = document.getElementById('status-message');
    const workingTimeDisplay = document.getElementById('working-time-display');
    const noteInputArea = document.getElementById('note-input-area');
    const noteText = document.getElementById('note-text');
    const attendanceTableBody = document.getElementById('attendance-table-body');
    const userStatusList = document.getElementById('user-status-list');

    // Overview elements
    const overviewTotal = document.getElementById('overview-total');
    const overviewFund = document.getElementById('overview-fund');
    const overviewRemaining = document.getElementById('overview-remaining');
    const overviewToday = document.getElementById('overview-today');
    const overviewWeek = document.getElementById('overview-week');
    const progressCircle = document.querySelector('.progress-circle');


    let timerInterval;
    let startTime;
    let currentEventType = 'none';

    function showStatus(message, type = 'info') {
        if (!message) return;
        statusMessage.textContent = message;
        statusMessage.className = `mt-3 alert alert-${type}`;
        statusMessage.style.display = 'block';
        setTimeout(() => {
            if (statusMessage.textContent === message) {
                hideStatus();
            }
        }, 3000);
    }

    function hideStatus() {
        statusMessage.style.display = 'none';
    }

    function getTimerPrefix(eventType) {
        switch (eventType) {
            case 'WORK': return 'Pracovní doba:';
            case 'HOMEOFFICE': return 'Pracovní doba:';
            case 'SICK': return 'Doba u lékaře:';
            case 'TRIP': return 'Doba na služební cestě:';
            default: return 'Čas:';
        }
    }

    function updateWorkingTimeDisplay() {
        if (startTime) {
            const now = Date.now();
            const elapsedMilliseconds = now - startTime;
            const hours = Math.floor(elapsedMilliseconds / 3600000);
            const minutes = Math.floor((elapsedMilliseconds % 3600000) / 60000);
            const seconds = Math.floor((elapsedMilliseconds % 60000) / 1000);
            const prefix = getTimerPrefix(currentEventType);
            workingTimeDisplay.textContent = `${prefix} ${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
            workingTimeDisplay.style.display = 'block';
        } else {
            workingTimeDisplay.style.display = 'none';
        }
    }

    function startTimer(startMs, eventType) {
        startTime = startMs;
        currentEventType = eventType;
        if (timerInterval) clearInterval(timerInterval);
        timerInterval = setInterval(updateWorkingTimeDisplay, 1000);
        updateWorkingTimeDisplay();
    }

    function stopTimer() {
        clearInterval(timerInterval);
        timerInterval = null;
        startTime = null;
        currentEventType = 'none';
        workingTimeDisplay.style.display = 'none';
    }

    function updateUI(status, start_time) {
        status = String(status || 'none');
        const buttonVisibilityMap = {
            'none': ['btn-start-work', 'btn-start-homeoffice', 'btn-start-trip', 'btn-start-sick'],
            'WORK': ['btn-end-work', 'btn-start-homeoffice', 'btn-start-trip', 'btn-start-sick'],
            'HOMEOFFICE': ['btn-end-work', 'btn-start-work', 'btn-start-trip', 'btn-start-sick'],
            'TRIP': ['btn-end-work', 'btn-start-work', 'btn-start-homeoffice', 'btn-start-sick'],
            'SICK': ['btn-end-work', 'btn-start-work', 'btn-start-homeoffice', 'btn-start-trip']
        };
        const visibleButtons = buttonVisibilityMap[status] || [];
        document.querySelectorAll('.attendance-btn').forEach(button => {
            button.style.display = visibleButtons.includes(button.id) ? 'block' : 'none';
        });

        if (status !== 'none') {
            noteInputArea.style.display = 'block';
            startTimer(start_time, status);
        } else {
            noteInputArea.style.display = 'none';
            stopTimer();
        }
    }

    function fetchAndRenderMonthlyRecords() {
        fetch('ajax/get_monthly_records.php')
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    attendanceTableBody.innerHTML = '';
                    const recordsByDate = {};
                    data.records.forEach(record => {
                        if (!recordsByDate[record.date]) recordsByDate[record.date] = [];
                        recordsByDate[record.date].push(record);
                    });

                    for (let day = 1; day <= data.daysInMonth; day++) {
                        const dateString = `${String(day).padStart(2, '0')}.${String(data.month).padStart(2, '0')}.${data.year}`;
                        const fullDateISO = `${data.year}-${String(data.month).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
                        const isHoliday = data.holidays[fullDateISO];
                        const dayOfWeek = new Date(fullDateISO).getDay();
                        const isWeekend = (dayOfWeek === 0 || dayOfWeek === 6);
                        let rowClass = isHoliday ? 'holiday-day' : (isWeekend ? 'weekend-day' : '');

                        if (recordsByDate[dateString]) {
                            recordsByDate[dateString].forEach(record => {
                                attendanceTableBody.insertAdjacentHTML('beforeend', `<tr class="${rowClass}"><td>${dateString}</td><td>${record.event_name}</td><td>${record.start_time}</td><td>${record.end_time}</td><td>${record.worked_hours}</td></tr>`);
                            });
                        } else {
                             attendanceTableBody.insertAdjacentHTML('beforeend', `<tr class="${rowClass}"><td>${dateString}</td><td>${isHoliday ? isHoliday : ''}</td><td colspan="3"></td></tr>`);
                        }
                    }
                    
                    // Update overview
                    const totalHours = parseFloat(data.monthlyOverview.totalWorkedHours);
                    const fundHours = parseFloat(data.monthlyOverview.monthlyHourFund);
                    const todayHours = parseFloat(data.monthlyOverview.workedHoursToday);
                    const weekHours = parseFloat(data.monthlyOverview.workedHoursWeek);
                    const remainingHours = fundHours - totalHours;
                    const progressPercent = (totalHours / fundHours) * 100;

                    overviewTotal.textContent = totalHours.toFixed(2) + ' h';
                    overviewFund.textContent = fundHours.toFixed(2) + ' h';
                    overviewRemaining.textContent = remainingHours.toFixed(2) + ' h';
                    overviewToday.textContent = todayHours.toFixed(2) + ' h';
                    overviewWeek.textContent = weekHours.toFixed(2) + ' h';
                    progressCircle.style.background = `conic-gradient(var(--prichod) 0% ${progressPercent}%, #eee ${progressPercent}% 100%)`;
                    if(progressCircle.querySelector('span')) {
                        progressCircle.querySelector('span').textContent = totalHours.toFixed(2) + ' h';
                    }


                } else {
                    console.error('Error fetching monthly records:', data.message);
                }
            })
            .catch(error => console.error('Error fetching monthly records:', error));
    }

    function handleAction(action) {
        let postData = `action=${action}`;
        if (action === 'end_work' && noteText.value.trim() !== '') {
            postData += `&note=${encodeURIComponent(noteText.value.trim())}`;
        }
        fetch('ajax/record_event.php', {
            method: 'POST',
            headers: {'Content-Type': 'application/x-www-form-urlencoded'},
            body: postData
        })
        .then(response => response.json())
        .then(data => {
            showStatus(data.message, data.status);
            noteText.value = '';
            updateUI(data.current_status, data.current_start_time);
            fetchAndRenderMonthlyRecords();
            // Po úspěšné akci obnovíme i seznam stavů uživatelů
            updateUserStatusesPeriodically();
        })
        .catch(error => {
            console.error('Error:', error);
            showStatus('Došlo k chybě při zpracování požadavku.', 'danger');
        });
    }
    
    function fetchCurrentStatus() {
        fetch('ajax/record_event.php', {
            method: 'POST',
            headers: {'Content-Type': 'application/x-www-form-urlencoded'},
            body: 'action=get_current_status'
        })
        .then(response => response.json())
        .then(data => {
            updateUI(data.current_status, data.current_start_time);
            fetchAndRenderMonthlyRecords();
        })
        .catch(error => {
            console.error('Error fetching current status:', error);
            showStatus('Chyba při načítání stavu docházky.', 'danger');
        });
    }

    function updateUserStatusesPeriodically() {
        fetch('ajax/get_user_statuses.php')
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.text();
            })
            .then(html => {
                userStatusList.innerHTML = html;
            })
            .catch(error => {
                console.error('Error fetching user statuses:', error);
                // Optionally, display an error message to the user in the list
                userStatusList.innerHTML = '<li class="list-group-item text-danger">Chyba při aktualizaci stavů.</li>';
            });
    }

    document.querySelectorAll('.attendance-btn').forEach(button => {
        button.addEventListener('click', () => handleAction(button.dataset.action));
    });

    const btnSaveNote = document.getElementById('btn-save-note');
    if (btnSaveNote) {
        btnSaveNote.addEventListener('click', function() {
            const note = noteText.value.trim();
            if (note === '') {
                showStatus('Poznámka nemůže být prázdná.', 'warning');
                return;
            }
            fetch('ajax/record_event.php', {
                method: 'POST',
                headers: {'Content-Type': 'application/x-www-form-urlencoded'},
                body: `action=update_active_note&note=${encodeURIComponent(note)}`
            })
            .then(response => response.json())
            .then(data => {
                showStatus(data.message, data.status);
            })
            .catch(error => {
                console.error('Error saving note:', error);
                showStatus('Došlo k chybě při ukládání poznámky.', 'danger');
            });
        });
    }
    
    fetchCurrentStatus();
    // Start periodic updates of the user status list every 60 seconds
    setInterval(updateUserStatusesPeriodically, 60000);
});
</script>
<?php require_once 'templates/footer.php'; ?>