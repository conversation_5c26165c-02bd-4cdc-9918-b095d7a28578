.rating-container {
  max-width: 800px;
  margin: 40px auto;
  padding: 30px;
  text-align: center;
  background-color: #ffffff;
  border-radius: 12px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.rating-container h2 {
  margin-bottom: 25px;
  font-size: 28px;
  color: #333;
  font-weight: 600;
}

.stars {
  font-size: 50px;
  margin: 30px 0;
  display: flex;
  justify-content: center;
  gap: 15px;
}

.stars span {
  color: #ccc;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-block;
  transform: scale(1);
}

.stars span:hover {
  color: #FFD700;
  transform: scale(1.1);
}

#positive, #negative {
  display: none;
  margin-top: 30px;
  padding: 25px;
  border-radius: 10px;
  background-color: #f9f9f9;
  transition: all 0.5s ease;
  animation: fadeIn 0.5s;
}

#positive h3, #negative h3 {
  margin-bottom: 15px;
  font-size: 22px;
  color: #333;
}

#positive p, #negative p {
  margin-bottom: 20px;
  font-size: 16px;
  color: #555;
  line-height: 1.5;
}

#positive a, #negative a {
  display: inline-block;
  margin: 10px;
  padding: 12px 24px;
  background: #f8f8f8;
  color: #333;
  text-decoration: none;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
  font-weight: 500;
  border: 1px solid #eee;
}

#positive a:hover, #negative a:hover {
  background: #f0f0f0;
  box-shadow: 0 4px 8px rgba(0,0,0,0.15);
  transform: translateY(-2px);
}

/* Barevné rozlišení */
#positive {
  background-color: #f0f9f0;
  border-left: 5px solid #4CAF50;
}

#negative {
  background-color: #fff8f8;
  border-left: 5px solid #ff9800;
}

#positive a {
  color: #2E7D32;
  border: 1px solid #C8E6C9;
  background: #E8F5E9;
}

#positive a:hover {
  background: #C8E6C9;
}

#negative a {
  color: #D84315;
  border: 1px solid #FFCCBC;
  background: #FBE9E7;
  display: block;
  width: 80%;
  margin: 10px auto;
  text-align: center;
}

#negative a:hover {
  background: #FFCCBC;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}