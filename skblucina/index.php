<?php
// Pokud je to API požadavek, zpracovat ho
if (isset($_GET['api']) || strpos($_SERVER['REQUEST_URI'], '/api') !== false) {
    header('Content-Type: application/json');
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type');

    if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
        http_response_code(200);
        exit();
    }

    $dataFile = 'data/team_data.json';

    if (!file_exists('data')) {
        mkdir('data', 0755, true);
    }

    $defaultData = [
        'players' => [
            [
                'id' => 1,
                'firstName' => 'Jan',
                'lastName' => 'Novák',
                'positions' => ['Brankář'],
                'capacity' => 100,
                'status' => 'hraje',
                'number' => 1,
                'isInjured' => false
            ],
            [
                'id' => 2,
                'firstName' => 'Petr',
                'lastName' => 'Svoboda',
                'positions' => ['Obránce'],
                'capacity' => 85,
                'status' => 'hraje',
                'number' => 5,
                'isInjured' => false
            ],
            [
                'id' => 3,
                'firstName' => 'Tomáš',
                'lastName' => 'Dvořák',
                'positions' => ['Záložník', 'Útočník'],
                'capacity' => 75,
                'status' => 'zjišťujeme',
                'number' => 10,
                'isInjured' => false
            ],
            [
                'id' => 4,
                'firstName' => 'Martin',
                'lastName' => 'Zraněný',
                'positions' => ['Obránce'],
                'capacity' => 0,
                'status' => 'zraněn',
                'number' => 15,
                'isInjured' => true,
                'expectedReturn' => '2025-07-01'
            ]
        ],
        'staff' => [
            [
                'id' => 1,
                'firstName' => 'Milan',
                'lastName' => 'Kouč',
                'role' => 'Hlavní trenér'
            ],
            [
                'id' => 2,
                'firstName' => 'Pavel',
                'lastName' => 'Asistent',
                'role' => 'Asistent trenéra'
            ]
        ],
        'injured' => [],
        'formation' => [],
        'lastUpdated' => date('Y-m-d H:i:s')
    ];

    function loadData($file, $default) {
        if (file_exists($file)) {
            $content = file_get_contents($file);
            if ($content !== false) {
                $data = json_decode($content, true);
                if ($data !== null) {
                    return $data;
                }
            }
        }
        return $default;
    }

    function saveData($file, $data) {
        $data['lastUpdated'] = date('Y-m-d H:i:s');
        $jsonData = json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        return file_put_contents($file, $jsonData, LOCK_EX) !== false;
    }

    $method = $_SERVER['REQUEST_METHOD'];
    $input = json_decode(file_get_contents('php://input'), true);

    try {
        switch ($method) {
            case 'GET':
                $data = loadData($dataFile, $defaultData);
                echo json_encode(['success' => true, 'data' => $data]);
                break;

            case 'POST':
                if (!$input) {
                    throw new Exception('Neplatná data');
                }
                
                $currentData = loadData($dataFile, $defaultData);
                
                if (isset($input['players'])) {
                    $currentData['players'] = $input['players'];
                }
                if (isset($input['staff'])) {
                    $currentData['staff'] = $input['staff'];
                }
                if (isset($input['injured'])) {
                    $currentData['injured'] = $input['injured'];
                }
                if (isset($input['formation'])) {
                    $currentData['formation'] = $input['formation'];
                }
                
                if (saveData($dataFile, $currentData)) {
                    echo json_encode(['success' => true, 'message' => 'Data uložena']);
                } else {
                    throw new Exception('Chyba při ukládání dat');
                }
                break;

            case 'PUT':
                if (!$input || !isset($input['section']) || !isset($input['data'])) {
                    throw new Exception('Neplatný formát dat');
                }
                
                $currentData = loadData($dataFile, $defaultData);
                $section = $input['section'];
                
                if (!in_array($section, ['players', 'staff', 'injured', 'formation'])) {
                    throw new Exception('Neplatná sekce');
                }
                
                $currentData[$section] = $input['data'];
                
                if (saveData($dataFile, $currentData)) {
                    echo json_encode(['success' => true, 'message' => "Sekce $section aktualizována"]);
                } else {
                    throw new Exception('Chyba při ukládání dat');
                }
                break;

            default:
                throw new Exception('Neplatná HTTP metoda');
        }

    } catch (Exception $e) {
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => $e->getMessage()]);
    }
    exit();
}
?>
<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SK Blučina - Správa týmu</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <style>
        /* Fallback styly pro případ, že se Tailwind nenačte */
        .min-h-screen { min-height: 100vh; }
        .bg-gradient-to-br { background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%); }
        .p-4 { padding: 1rem; }
        .max-w-7xl { max-width: 80rem; }
        .mx-auto { margin-left: auto; margin-right: auto; }
        .bg-white { background-color: white; }
        .rounded-lg { border-radius: 0.5rem; }
        .shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1); }
        .p-6 { padding: 1.5rem; }
        .mb-6 { margin-bottom: 1.5rem; }
        .text-3xl { font-size: 1.875rem; }
        .font-bold { font-weight: 700; }
        .text-green-800 { color: #166534; }
        .mb-2 { margin-bottom: 0.5rem; }
        .text-gray-600 { color: #4b5563; }
        .flex { display: flex; }
        .justify-between { justify-content: space-between; }
        .items-center { align-items: center; }
        .space-x-2 > * + * { margin-left: 0.5rem; }
        .px-3 { padding-left: 0.75rem; padding-right: 0.75rem; }
        .py-1 { padding-top: 0.25rem; padding-bottom: 0.25rem; }
        .bg-blue-600 { background-color: #2563eb; }
        .text-white { color: white; }
        .rounded { border-radius: 0.25rem; }
        .hover\:bg-blue-700:hover { background-color: #1d4ed8; }
        .disabled\:opacity-50:disabled { opacity: 0.5; }
        .w-4 { width: 1rem; }
        .h-4 { height: 1rem; }
        .mr-1 { margin-right: 0.25rem; }
        .text-xs { font-size: 0.75rem; }
        .text-gray-500 { color: #6b7280; }
        .text-red-500 { color: #ef4444; }
        .border-b { border-bottom-width: 1px; }
        .px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
        .py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }
        .font-medium { font-weight: 500; }
        .text-green-600 { color: #16a34a; }
        .border-b-2 { border-bottom-width: 2px; }
        .border-green-600 { border-color: #16a34a; }
        .text-gray-500 { color: #6b7280; }
        .hover\:text-gray-700:hover { color: #374151; }
        .text-xl { font-size: 1.25rem; }
        .font-semibold { font-weight: 600; }
        .w-5 { width: 1.25rem; }
        .h-5 { height: 1.25rem; }
        .mr-2 { margin-right: 0.5rem; }
        .px-4 { padding-left: 1rem; padding-right: 1rem; }
        .py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
        .hover\:bg-green-700:hover { background-color: #15803d; }
        .grid { display: grid; }
        .gap-4 { gap: 1rem; }
        .bg-gray-50 { background-color: #f9fafb; }
        .border { border-width: 1px; }
        .cursor-move { cursor: move; }
        .hover\:shadow-md:hover { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1); }
        .transition-shadow { transition: box-shadow 0.15s ease-in-out; }
        .flex-1 { flex: 1 1 0%; }
        .text-lg { font-size: 1.125rem; }
        .ml-2 { margin-left: 0.5rem; }
        .text-sm { font-size: 0.875rem; }
        .space-x-4 > * + * { margin-left: 1rem; }
        .px-2 { padding-left: 0.5rem; padding-right: 0.5rem; }
        .py-1 { padding-top: 0.25rem; padding-bottom: 0.25rem; }
        .bg-green-100 { background-color: #dcfce7; }
        .text-green-800 { color: #166534; }
        .bg-yellow-100 { background-color: #fef3c7; }
        .text-yellow-800 { color: #92400e; }
        .bg-red-100 { background-color: #fef2f2; }
        .text-red-800 { color: #991b1b; }
        .bg-gray-100 { background-color: #f3f4f6; }
        .text-gray-800 { color: #1f2937; }
        .text-green-600 { color: #16a34a; }
        .text-yellow-600 { color: #d97706; }
        .text-red-600 { color: #dc2626; }
        .space-x-2 > * + * { margin-left: 0.5rem; }
        .p-2 { padding: 0.5rem; }
        .text-blue-600 { color: #2563eb; }
        .hover\:bg-blue-100:hover { background-color: #dbeafe; }
        .hover\:bg-red-100:hover { background-color: #fef2f2; }
        .p-8 { padding: 2rem; }
        .relative { position: relative; }
        .w-full { width: 100%; }
        .h-96 { height: 24rem; }
        .bg-green-200 { background-color: #bbf7d0; }
        .border-2 { border-width: 2px; }
        .border-white { border-color: white; }
        .absolute { position: absolute; }
        .w-16 { width: 4rem; }
        .h-16 { height: 4rem; }
        .rounded-full { border-radius: 9999px; }
        .justify-center { justify-content: center; }
        .cursor-pointer { cursor: pointer; }
        .text-center { text-align: center; }
        .mt-4 { margin-top: 1rem; }
        .bg-blue-50 { background-color: #eff6ff; }
        .bg-red-50 { background-color: #fef2f2; }
        .fixed { position: fixed; }
        .inset-0 { top: 0; right: 0; bottom: 0; left: 0; }
        .bg-black { background-color: black; }
        .bg-opacity-50 { background-color: rgba(0, 0, 0, 0.5); }
        .z-50 { z-index: 50; }
        .max-w-md { max-width: 28rem; }
        .mb-4 { margin-bottom: 1rem; }
        .space-y-4 > * + * { margin-top: 1rem; }
        .block { display: block; }
        .mb-1 { margin-bottom: 0.25rem; }
        .border-gray-300 { border-color: #d1d5db; }
        .focus\:ring-2:focus { box-shadow: 0 0 0 2px #16a34a; }
        .focus\:ring-green-500:focus { box-shadow: 0 0 0 2px #16a34a; }
        .focus\:border-transparent:focus { border-color: transparent; }
        .justify-end { justify-content: flex-end; }
        .space-x-3 > * + * { margin-left: 0.75rem; }
        .mt-6 { margin-top: 1.5rem; }
        .border-gray-300 { border-color: #d1d5db; }
        .hover\:bg-gray-50:hover { background-color: #f9fafb; }
        .animate-spin { animation: spin 1s linear infinite; }
        .border-b-2 { border-bottom-width: 2px; }
        .border-green-600 { border-color: #16a34a; }
        @keyframes spin { to { transform: rotate(360deg); } }
    </style>
</head>
<body>
    <div id="root"></div>

    <script>
        // Vanilla JavaScript aplikace pro správu fotbalového týmu
        console.log('Načítám aplikaci...');

        // API funkce - používá stejný soubor s ?api parametrem
        const API = {
            baseUrl: window.location.pathname + '?api=1',

            async loadData() {
                try {
                    const response = await fetch(this.baseUrl);
                    const result = await response.json();
                    if (result.success) {
                        return result.data;
                    } else {
                        throw new Error(result.error || 'Chyba při načítání dat');
                    }
                } catch (error) {
                    console.error('Chyba při načítání dat:', error);
                    throw error;
                }
            },

            async saveData(data) {
                try {
                    const response = await fetch(this.baseUrl, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(data)
                    });
                    const result = await response.json();
                    if (!result.success) {
                        throw new Error(result.error || 'Chyba při ukládání dat');
                    }
                    return result;
                } catch (error) {
                    console.error('Chyba při ukládání dat:', error);
                    throw error;
                }
            },

            async saveSection(section, data) {
                try {
                    const response = await fetch(this.baseUrl, {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ section, data })
                    });
                    const result = await response.json();
                    if (!result.success) {
                        throw new Error(result.error || 'Chyba při ukládání sekce');
                    }
                    return result;
                } catch (error) {
                    console.error('Chyba při ukládání sekce:', error);
                    throw error;
                }
            }
        };

        // Globální proměnné pro stav aplikace
        let players = [];
        let staff = [];
        let injured = [];
        let formation = [];
        let activeTab = 'dashboard';
        let isLoading = true;
        let isSaving = false;
        let lastSaved = null;
        let editingPlayer = null;
        let editingStaff = null;

        // Výchozí data
        const defaultData = {
            players: [
                { id: 1, firstName: 'Jan', lastName: 'Novák', positions: ['Brankář'], capacity: 100, status: 'hraje', number: 1, isInjured: false },
                { id: 2, firstName: 'Petr', lastName: 'Svoboda', positions: ['Obránce'], capacity: 85, status: 'hraje', number: 5, isInjured: false },
                { id: 3, firstName: 'Tomáš', lastName: 'Dvořák', positions: ['Záložník', 'Útočník'], capacity: 75, status: 'zjišťujeme', number: 10, isInjured: false },
                { id: 4, firstName: 'Martin', lastName: 'Zraněný', positions: ['Obránce'], capacity: 0, status: 'zraněn', number: 15, isInjured: true, expectedReturn: '2025-07-01' }
            ],
            staff: [
                { id: 1, firstName: 'Milan', lastName: 'Kouč', role: 'Hlavní trenér' },
                { id: 2, firstName: 'Pavel', lastName: 'Asistent', role: 'Asistent trenéra' }
            ],
            injured: [],
            formation: []
        };

        // Pomocné funkce
        function getStatusColor(status) {
            switch (status) {
                case 'hraje': return 'bg-green-100 text-green-800';
                case 'zjišťujeme': return 'bg-yellow-100 text-yellow-800';
                case 'odmítl': return 'bg-red-100 text-red-800';
                default: return 'bg-gray-100 text-gray-800';
            }
        }

        function getCapacityColor(capacity) {
            if (capacity >= 90) return 'text-green-600';
            if (capacity >= 70) return 'text-yellow-600';
            return 'text-red-600';
        }

        // Načtení dat při startu
        async function loadInitialData() {
            try {
                isLoading = true;
                showLoadingScreen();
                const data = await API.loadData();
                players = data.players || defaultData.players;
                staff = data.staff || defaultData.staff;
                injured = data.injured || defaultData.injured;
                formation = data.formation || defaultData.formation;
                lastSaved = data.lastUpdated ? new Date(data.lastUpdated) : null;
            } catch (error) {
                console.error('Chyba při načítání:', error);
                // Použít výchozí data při chybě
                players = defaultData.players;
                staff = defaultData.staff;
                injured = defaultData.injured;
                formation = defaultData.formation;
            } finally {
                isLoading = false;
                renderApp();
            }
        }

        // Automatické ukládání při změnách
        async function saveToServer(section, data) {
            try {
                isSaving = true;
                updateSaveStatus();
                await API.saveSection(section, data);
                lastSaved = new Date();
                updateSaveStatus();
            } catch (error) {
                console.error('Chyba při ukládání:', error);
                alert('Chyba při ukládání: ' + error.message);
            } finally {
                isSaving = false;
                updateSaveStatus();
            }
        }

        // Manuální uložení
        async function manualSave() {
            try {
                isSaving = true;
                updateSaveStatus();
                await API.saveData({ players, staff, injured, formation });
                lastSaved = new Date();
                alert('Data úspěšně uložena!');
            } catch (error) {
                alert('Chyba při ukládání: ' + error.message);
            } finally {
                isSaving = false;
                updateSaveStatus();
            }
        }

        // Pozice na hřišti
        const fieldPositions = [
            { id: 'gk', x: 50, y: 85, label: 'GK' },
            { id: 'lb', x: 15, y: 65, label: 'LB' },
            { id: 'cb1', x: 35, y: 65, label: 'CB' },
            { id: 'cb2', x: 65, y: 65, label: 'CB' },
            { id: 'rb', x: 85, y: 65, label: 'RB' },
            { id: 'lm', x: 15, y: 35, label: 'LM' },
            { id: 'cm1', x: 35, y: 35, label: 'CM' },
            { id: 'cm2', x: 65, y: 35, label: 'CM' },
            { id: 'rm', x: 85, y: 35, label: 'RM' },
            { id: 'st1', x: 35, y: 15, label: 'ST' },
            { id: 'st2', x: 65, y: 15, label: 'ST' }
        ];

        // Funkce pro práci s hráči
        function addPlayer() {
            const firstName = document.getElementById('firstName').value.trim();
            const lastName = document.getElementById('lastName').value.trim();
            const number = document.getElementById('number').value;
            const capacity = document.getElementById('capacity').value;
            const status = document.getElementById('status').value;
            const isInjured = document.getElementById('isInjured').checked;
            const expectedReturn = document.getElementById('expectedReturn').value;

            if (!firstName || !lastName) {
                alert('Vyplňte prosím jméno a příjmení');
                return;
            }

            const positions = Array.from(document.getElementById('positions').selectedOptions).map(opt => opt.value);

            if (editingPlayer) {
                const index = players.findIndex(p => p.id === editingPlayer.id);
                if (index !== -1) {
                    players[index] = {
                        ...players[index],
                        firstName,
                        lastName,
                        number: number || null,
                        positions,
                        capacity: parseInt(capacity),
                        status: isInjured ? 'zraněn' : status,
                        isInjured,
                        expectedReturn: isInjured ? expectedReturn : null
                    };
                }
                editingPlayer = null;
            } else {
                const newPlayer = {
                    id: Date.now(),
                    firstName,
                    lastName,
                    number: number || null,
                    positions,
                    capacity: parseInt(capacity),
                    status: isInjured ? 'zraněn' : status,
                    isInjured,
                    expectedReturn: isInjured ? expectedReturn : null
                };
                players.push(newPlayer);
            }

            hideForm();
            renderApp();
            saveToServer('players', players);
        }

        function editPlayer(playerId) {
            const player = players.find(p => p.id === playerId);
            if (!player) return;

            editingPlayer = player;
            document.getElementById('firstName').value = player.firstName;
            document.getElementById('lastName').value = player.lastName;
            document.getElementById('number').value = player.number || '';
            document.getElementById('capacity').value = player.capacity;
            document.getElementById('status').value = player.status;
            document.getElementById('isInjured').checked = player.isInjured || false;
            document.getElementById('expectedReturn').value = player.expectedReturn || '';

            // Zobrazit/skrýt pole pro očekávaný návrat
            toggleInjuryFields();

            // Nastavit pozice
            const positionsSelect = document.getElementById('positions');
            Array.from(positionsSelect.options).forEach(option => {
                option.selected = player.positions.includes(option.value);
            });

            showForm();
        }

        function deletePlayer(playerId) {
            if (confirm('Opravdu chcete odstranit tohoto hráče?')) {
                players = players.filter(p => p.id !== playerId);
                renderApp();
                saveToServer('players', players);
            }
        }

        function getPlayerAtPosition(positionId) {
            return formation.find(p => p.position === positionId);
        }

        // Funkce pro práci s realizačním týmem
        function addStaff() {
            const firstName = document.getElementById('staffFirstName').value.trim();
            const lastName = document.getElementById('staffLastName').value.trim();
            const role = document.getElementById('staffRole').value.trim();

            if (!firstName || !lastName || !role) {
                alert('Vyplňte prosím všechna pole');
                return;
            }

            if (editingStaff) {
                const index = staff.findIndex(s => s.id === editingStaff.id);
                if (index !== -1) {
                    staff[index] = {
                        ...staff[index],
                        firstName,
                        lastName,
                        role
                    };
                }
                editingStaff = null;
            } else {
                const newStaff = {
                    id: Date.now(),
                    firstName,
                    lastName,
                    role
                };
                staff.push(newStaff);
            }

            hideStaffForm();
            renderApp();
            saveToServer('staff', staff);
        }

        function editStaff(staffId) {
            const member = staff.find(s => s.id === staffId);
            if (!member) return;

            editingStaff = member;
            document.getElementById('staffFirstName').value = member.firstName;
            document.getElementById('staffLastName').value = member.lastName;
            document.getElementById('staffRole').value = member.role;

            showStaffForm();
        }

        function deleteStaff(staffId) {
            if (confirm('Opravdu chcete odstranit tohoto člena realizačního týmu?')) {
                staff = staff.filter(s => s.id !== staffId);
                renderApp();
                saveToServer('staff', staff);
            }
        }

        // Funkce pro zobrazení/skrytí formuláře realizačního týmu
        function showStaffForm() {
            document.getElementById('staffForm').style.display = 'flex';
        }

        function hideStaffForm() {
            document.getElementById('staffForm').style.display = 'none';
            document.getElementById('staffFirstName').value = '';
            document.getElementById('staffLastName').value = '';
            document.getElementById('staffRole').value = '';
            editingStaff = null;
        }

        // Funkce pro přepínání polí zranění
        function toggleInjuryFields() {
            const isInjured = document.getElementById('isInjured').checked;
            const expectedReturnField = document.getElementById('expectedReturnField');
            expectedReturnField.style.display = isInjured ? 'block' : 'none';
        }

        // Drag & Drop funkce pro sestavu
        function dragPlayer(event) {
            event.dataTransfer.setData("text/plain", event.target.dataset.playerId);
        }

        function allowDrop(event) {
            event.preventDefault();
        }

        function dropPlayer(event) {
            event.preventDefault();
            const playerId = parseInt(event.dataTransfer.getData("text/plain"));
            const positionId = event.currentTarget.dataset.position;

            // Najít hráče
            const player = players.find(p => p.id === playerId);
            if (!player) return;

            // Odstranit hráče z jiné pozice, pokud tam už je
            formation = formation.filter(f => f.id !== playerId);

            // Odstranit jiného hráče z této pozice
            formation = formation.filter(f => f.position !== positionId);

            // Přidat hráče na novou pozici
            formation.push({
                id: player.id,
                firstName: player.firstName,
                lastName: player.lastName,
                position: positionId
            });

            renderApp();
            saveToServer('formation', formation);
        }

        function removePlayerFromPosition(positionId) {
            formation = formation.filter(f => f.position !== positionId);
            renderApp();
            saveToServer('formation', formation);
        }

        // Funkce pro zobrazení loading obrazovky
        function showLoadingScreen() {
            const root = document.getElementById('root');
            root.innerHTML = `
                <div class="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex items-center justify-center">
                    <div class="text-center">
                        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"></div>
                        <p class="text-gray-600">Načítám data...</p>
                    </div>
                </div>
            `;
        }

        // Funkce pro zobrazení/skrytí formuláře
        function showForm() {
            document.getElementById('playerForm').style.display = 'flex';
        }

        function hideForm() {
            document.getElementById('playerForm').style.display = 'none';
            document.getElementById('firstName').value = '';
            document.getElementById('lastName').value = '';
            document.getElementById('number').value = '';
            document.getElementById('capacity').value = '100';
            document.getElementById('status').value = 'hraje';
            document.getElementById('isInjured').checked = false;
            document.getElementById('expectedReturn').value = '';
            document.getElementById('positions').selectedIndex = -1;
            toggleInjuryFields();
            editingPlayer = null;
        }

        // Funkce pro přepínání záložek
        function setActiveTab(tab) {
            activeTab = tab;
            renderApp();
        }

        // Funkce pro aktualizaci statusu ukládání
        function updateSaveStatus() {
            const saveBtn = document.getElementById('saveBtn');
            const saveStatus = document.getElementById('saveStatus');

            if (saveBtn) {
                saveBtn.disabled = isSaving;
                saveBtn.textContent = isSaving ? 'Ukládám...' : 'Uložit';
            }

            if (saveStatus && lastSaved) {
                saveStatus.textContent = `Naposledy uloženo: ${lastSaved.toLocaleString('cs-CZ')}`;
            }
        }

        // Hlavní funkce pro renderování aplikace
        function renderApp() {
            const root = document.getElementById('root');
            root.innerHTML = `
                <div class="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 p-4">
                    <div class="max-w-7xl mx-auto">
                        <!-- Hlavička -->
                        <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
                            <div class="flex justify-between items-center">
                                <div>
                                    <h1 class="text-3xl font-bold text-green-800 mb-2">SK Blučina</h1>
                                    <p class="text-gray-600">Správa fotbalového týmu</p>
                                </div>
                                <div class="text-right">
                                    <p id="saveStatus" class="text-xs text-gray-500"></p>
                                </div>
                            </div>
                        </div>

                        <!-- Navigační záložky -->
                        <div class="bg-white rounded-lg shadow-lg mb-6">
                            <div class="flex border-b">
                                <button onclick="setActiveTab('dashboard')" class="px-6 py-3 font-medium ${activeTab === 'dashboard' ? 'text-green-600 border-b-2 border-green-600' : 'text-gray-500 hover:text-gray-700'}">
                                    Dashboard
                                </button>
                                <button onclick="setActiveTab('players')" class="px-6 py-3 font-medium ${activeTab === 'players' ? 'text-green-600 border-b-2 border-green-600' : 'text-gray-500 hover:text-gray-700'}">
                                    Hráči
                                </button>
                                <button onclick="setActiveTab('formation')" class="px-6 py-3 font-medium ${activeTab === 'formation' ? 'text-green-600 border-b-2 border-green-600' : 'text-gray-500 hover:text-gray-700'}">
                                    Sestava
                                </button>
                                <button onclick="setActiveTab('staff')" class="px-6 py-3 font-medium ${activeTab === 'staff' ? 'text-green-600 border-b-2 border-green-600' : 'text-gray-500 hover:text-gray-700'}">
                                    Realizační tým
                                </button>
                            </div>

                            <!-- Obsah záložek -->
                            <div class="p-6">
                                ${renderTabContent()}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Formulář pro přidání/úpravu hráče -->
                <div id="playerForm" style="display: none;" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div class="bg-white rounded-lg p-6 w-full max-w-md">
                        <h3 class="text-lg font-semibold mb-4">
                            ${editingPlayer ? 'Upravit hráče' : 'Přidat nového hráče'}
                        </h3>
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Jméno</label>
                                <input type="text" id="firstName" class="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Příjmení</label>
                                <input type="text" id="lastName" class="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Číslo dresu</label>
                                <input type="number" id="number" class="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Pozice</label>
                                <select multiple id="positions" class="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent">
                                    <option value="Brankář">Brankář</option>
                                    <option value="Obránce">Obránce</option>
                                    <option value="Záložník">Záložník</option>
                                    <option value="Útočník">Útočník</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Kondice (%)</label>
                                <input type="range" id="capacity" min="0" max="100" value="100" class="w-full" oninput="document.getElementById('capacityValue').textContent = this.value + '%'">
                                <div id="capacityValue" class="text-center text-sm text-gray-600">100%</div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Stav</label>
                                <select id="status" class="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent">
                                    <option value="hraje">Hraje</option>
                                    <option value="zjišťujeme">Zjišťujeme</option>
                                    <option value="odmítl">Odmítl</option>
                                </select>
                            </div>
                            <div>
                                <label class="flex items-center">
                                    <input type="checkbox" id="isInjured" onchange="toggleInjuryFields()" class="mr-2">
                                    <span class="text-sm font-medium text-gray-700">Zraněný hráč</span>
                                </label>
                            </div>
                            <div id="expectedReturnField" style="display: none;">
                                <label class="block text-sm font-medium text-gray-700 mb-1">Očekávaný návrat</label>
                                <input type="date" id="expectedReturn" class="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent">
                            </div>
                        </div>
                        <div class="flex justify-end space-x-3 mt-6">
                            <button onclick="hideForm()" class="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50">
                                Zrušit
                            </button>
                            <button onclick="addPlayer()" class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700">
                                ${editingPlayer ? 'Uložit změny' : 'Přidat hráče'}
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Formulář pro přidání/úpravu člena realizačního týmu -->
                <div id="staffForm" style="display: none;" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div class="bg-white rounded-lg p-6 w-full max-w-md">
                        <h3 class="text-lg font-semibold mb-4">
                            ${editingStaff ? 'Upravit člena realizačního týmu' : 'Přidat nového člena realizačního týmu'}
                        </h3>
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Jméno</label>
                                <input type="text" id="staffFirstName" class="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Příjmení</label>
                                <input type="text" id="staffLastName" class="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Role</label>
                                <input type="text" id="staffRole" placeholder="např. Hlavní trenér, Asistent trenéra, Masér..." class="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent">
                            </div>
                        </div>
                        <div class="flex justify-end space-x-3 mt-6">
                            <button onclick="hideStaffForm()" class="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50">
                                Zrušit
                            </button>
                            <button onclick="addStaff()" class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700">
                                ${editingStaff ? 'Uložit změny' : 'Přidat člena'}
                            </button>
                        </div>
                    </div>
                </div>
            `;

            updateSaveStatus();
        }
        // Funkce pro renderování obsahu záložek
        function renderTabContent() {
            switch (activeTab) {
                case 'dashboard':
                    const healthyPlayers = players.filter(p => !p.isInjured);
                    const injuredPlayers = players.filter(p => p.isInjured);
                    const activePlayers = players.filter(p => p.status === 'hraje' && !p.isInjured);

                    return `
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                            <div class="bg-green-100 p-6 rounded-lg">
                                <h3 class="text-lg font-semibold text-green-800 mb-2">Celkem hráčů</h3>
                                <p class="text-3xl font-bold text-green-600">${players.length}</p>
                            </div>
                            <div class="bg-blue-100 p-6 rounded-lg">
                                <h3 class="text-lg font-semibold text-blue-800 mb-2">Zdraví hráči</h3>
                                <p class="text-3xl font-bold text-blue-600">${healthyPlayers.length}</p>
                            </div>
                            <div class="bg-red-100 p-6 rounded-lg">
                                <h3 class="text-lg font-semibold text-red-800 mb-2">Zranění hráči</h3>
                                <p class="text-3xl font-bold text-red-600">${injuredPlayers.length}</p>
                            </div>
                            <div class="bg-purple-100 p-6 rounded-lg">
                                <h3 class="text-lg font-semibold text-purple-800 mb-2">Realizační tým</h3>
                                <p class="text-3xl font-bold text-purple-600">${staff.length}</p>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div class="bg-white p-6 rounded-lg shadow">
                                <h3 class="text-lg font-semibold mb-4">Poslední přidaní hráči</h3>
                                <div class="space-y-2">
                                    ${players.slice(-3).map(player => `
                                        <div class="flex justify-between items-center p-2 bg-gray-50 rounded">
                                            <span>${player.firstName} ${player.lastName}</span>
                                            <span class="text-sm text-gray-500">${player.positions.join(', ')}</span>
                                        </div>
                                    `).join('')}
                                </div>
                            </div>

                            <div class="bg-white p-6 rounded-lg shadow">
                                <h3 class="text-lg font-semibold mb-4">Zranění hráči</h3>
                                <div class="space-y-2">
                                    ${injuredPlayers.length > 0 ? injuredPlayers.map(player => `
                                        <div class="flex justify-between items-center p-2 bg-red-50 rounded">
                                            <span>${player.firstName} ${player.lastName}</span>
                                            <span class="text-sm text-red-600">${player.expectedReturn ? new Date(player.expectedReturn).toLocaleDateString('cs-CZ') : 'Neurčeno'}</span>
                                        </div>
                                    `).join('') : '<p class="text-gray-500">Žádní zranění hráči</p>'}
                                </div>
                            </div>
                        </div>
                    `;

                case 'players':
                    return `
                        <div class="flex justify-between items-center mb-6">
                            <h2 class="text-xl font-semibold flex items-center">
                                👥 Hráči
                            </h2>
                            <button onclick="showForm()" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center">
                                ➕ Přidat hráče
                            </button>
                        </div>
                        <div class="grid gap-4">
                            ${players.map(player => `
                                <div class="p-4 rounded-lg border hover:shadow-md transition-shadow ${player.isInjured ? 'bg-red-50 border-red-200' : 'bg-gray-50'}">
                                    <div class="flex justify-between items-start">
                                        <div class="flex-1">
                                            <div class="flex items-center mb-2">
                                                <h3 class="font-semibold text-lg">
                                                    ${player.firstName} ${player.lastName}
                                                </h3>
                                                ${player.number ? `<span class="ml-2 bg-green-600 text-white px-2 py-1 rounded text-sm">#${player.number}</span>` : ''}
                                                ${player.isInjured ? `<span class="ml-2 bg-red-600 text-white px-2 py-1 rounded text-sm">🤕 Zraněn</span>` : ''}
                                            </div>
                                            <p class="text-gray-600 mb-2">
                                                Pozice: ${player.positions.join(', ')}
                                            </p>
                                            <div class="flex items-center space-x-4">
                                                <span class="px-2 py-1 rounded text-sm ${getStatusColor(player.status)}">
                                                    ${player.status}
                                                </span>
                                                <span class="font-semibold ${getCapacityColor(player.capacity)}">
                                                    ${player.capacity}%
                                                </span>
                                                ${player.isInjured && player.expectedReturn ? `<span class="text-sm text-red-600">Návrat: ${new Date(player.expectedReturn).toLocaleDateString('cs-CZ')}</span>` : ''}
                                            </div>
                                        </div>
                                        <div class="flex space-x-2">
                                            <button onclick="editPlayer(${player.id})" class="p-2 text-blue-600 hover:bg-blue-100 rounded">
                                                ✏️
                                            </button>
                                            <button onclick="deletePlayer(${player.id})" class="p-2 text-red-600 hover:bg-red-100 rounded">
                                                🗑️
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    `;

                case 'formation':
                    const availablePlayers = players.filter(p => !p.isInjured && p.status === 'hraje');
                    return `
                        <h2 class="text-xl font-semibold mb-6">Sestava týmu</h2>
                        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                            <!-- Hřiště -->
                            <div class="lg:col-span-2">
                                <div class="bg-green-100 rounded-lg p-8 relative">
                                    <div class="relative w-full h-96 bg-green-200 rounded border-2 border-white" id="field">
                                        ${fieldPositions.map(position => {
                                            const playerAtPosition = getPlayerAtPosition(position.id);
                                            return `
                                                <div class="absolute w-16 h-16 rounded-full border-2 border-white flex items-center justify-center text-xs font-bold cursor-pointer drop-zone"
                                                     data-position="${position.id}"
                                                     style="left: ${position.x}%; top: ${position.y}%; transform: translate(-50%, -50%); background-color: ${playerAtPosition ? '#10b981' : '#6b7280'}; color: white;"
                                                     ondrop="dropPlayer(event)" ondragover="allowDrop(event)" onclick="removePlayerFromPosition('${position.id}')">
                                                    ${playerAtPosition ?
                                                        `<div class="text-center" data-player-id="${playerAtPosition.id}">
                                                            <div class="text-xs">${playerAtPosition.firstName}</div>
                                                            <div class="text-xs">${playerAtPosition.lastName}</div>
                                                        </div>` :
                                                        position.label
                                                    }
                                                </div>
                                            `;
                                        }).join('')}
                                    </div>
                                </div>
                            </div>

                            <!-- Seznam dostupných hráčů -->
                            <div class="bg-white p-4 rounded-lg shadow">
                                <h3 class="text-lg font-semibold mb-4">Dostupní hráči</h3>
                                <div class="space-y-2 max-h-96 overflow-y-auto">
                                    ${availablePlayers.filter(p => !formation.some(f => f.id === p.id)).map(player => `
                                        <div class="p-2 bg-gray-100 rounded cursor-move player-item"
                                             draggable="true"
                                             data-player-id="${player.id}"
                                             ondragstart="dragPlayer(event)">
                                            <div class="font-medium">${player.firstName} ${player.lastName}</div>
                                            <div class="text-sm text-gray-600">${player.positions.join(', ')}</div>
                                            ${player.number ? `<div class="text-xs text-green-600">#${player.number}</div>` : ''}
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                        </div>
                    `;

                case 'staff':
                    return `
                        <div class="flex justify-between items-center mb-6">
                            <h2 class="text-xl font-semibold flex items-center">
                                🛡️ Realizační tým
                            </h2>
                            <button onclick="showStaffForm()" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center">
                                ➕ Přidat člena
                            </button>
                        </div>
                        <div class="grid gap-4">
                            ${staff.map(member => `
                                <div class="bg-blue-50 p-4 rounded-lg border hover:shadow-md transition-shadow">
                                    <div class="flex justify-between items-start">
                                        <div class="flex-1">
                                            <h3 class="font-semibold text-lg">
                                                ${member.firstName} ${member.lastName}
                                            </h3>
                                            <p class="text-gray-600">${member.role}</p>
                                        </div>
                                        <div class="flex space-x-2">
                                            <button onclick="editStaff(${member.id})" class="p-2 text-blue-600 hover:bg-blue-100 rounded">
                                                ✏️
                                            </button>
                                            <button onclick="deleteStaff(${member.id})" class="p-2 text-red-600 hover:bg-red-100 rounded">
                                                🗑️
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    `;

                default:
                    return '<p>Neznámá záložka</p>';
            }
        }

        // Inicializace aplikace
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Inicializuji aplikaci...');
            showLoadingScreen();
            loadInitialData();
        });
    </script>
</body>
</html>