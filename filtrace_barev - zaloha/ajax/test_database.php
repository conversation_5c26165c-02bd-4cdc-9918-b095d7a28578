<?php
/**
 * Test databázové struktury pro varianty a obrázky
 */

// Načtení PrestaShop frameworku
require_once dirname(__FILE__) . '/../../../config/config.inc.php';
require_once dirname(__FILE__) . '/../../../init.php';

header('Content-Type: text/html; charset=utf-8');

?>
<!DOCTYPE html>
<html>
<head>
    <title>Test databázové struktury</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border: 1px solid #ddd; }
        .success { background-color: #d4edda; }
        .error { background-color: #f8d7da; }
        .warning { background-color: #fff3cd; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Test databázové struktury pro varianty a obrázky</h1>
    
    <?php
    
    function testTable($table_name, $description) {
        echo "<h3>Test tabulky: $table_name ($description)</h3>";
        
        $full_table_name = _DB_PREFIX_ . $table_name;
        
        // Test existence tabulky
        $sql = "SHOW TABLES LIKE '$full_table_name'";
        $result = Db::getInstance()->executeS($sql);
        
        if ($result) {
            echo "<div class='test-result success'>✅ Tabulka $full_table_name existuje</div>";
            
            // Zobrazíme strukturu tabulky
            $sql = "DESCRIBE `$full_table_name`";
            $structure = Db::getInstance()->executeS($sql);
            
            if ($structure) {
                echo "<h4>Struktura tabulky:</h4>";
                echo "<table>";
                echo "<tr><th>Sloupec</th><th>Typ</th><th>Null</th><th>Klíč</th><th>Default</th></tr>";
                
                foreach ($structure as $column) {
                    echo "<tr>";
                    echo "<td>" . $column['Field'] . "</td>";
                    echo "<td>" . $column['Type'] . "</td>";
                    echo "<td>" . $column['Null'] . "</td>";
                    echo "<td>" . $column['Key'] . "</td>";
                    echo "<td>" . $column['Default'] . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
                
                // Počet záznamů
                $sql = "SELECT COUNT(*) as count FROM `$full_table_name`";
                $count = Db::getInstance()->getValue($sql);
                echo "<p><strong>Počet záznamů:</strong> $count</p>";
                
                // Ukázka dat (prvních 5 záznamů)
                if ($count > 0) {
                    $sql = "SELECT * FROM `$full_table_name` LIMIT 5";
                    $sample_data = Db::getInstance()->executeS($sql);
                    
                    if ($sample_data) {
                        echo "<h4>Ukázka dat (prvních 5 záznamů):</h4>";
                        echo "<table>";
                        
                        // Hlavička
                        echo "<tr>";
                        foreach (array_keys($sample_data[0]) as $column) {
                            echo "<th>$column</th>";
                        }
                        echo "</tr>";
                        
                        // Data
                        foreach ($sample_data as $row) {
                            echo "<tr>";
                            foreach ($row as $value) {
                                echo "<td>" . htmlspecialchars($value) . "</td>";
                            }
                            echo "</tr>";
                        }
                        echo "</table>";
                    }
                }
            }
        } else {
            echo "<div class='test-result error'>❌ Tabulka $full_table_name NEEXISTUJE</div>";
        }
    }
    
    // Test hlavních tabulek
    testTable('product', 'Produkty');
    testTable('product_attribute', 'Varianty produktů');
    testTable('product_attribute_image', 'Přiřazení obrázků k variantám');
    testTable('image', 'Obrázky');
    testTable('attribute', 'Atributy (barvy, velikosti, atd.)');
    testTable('attribute_lang', 'Názvy atributů v jazycích');
    
    // Test konkrétního produktu s variantami
    echo "<h2>Test konkrétního produktu s variantami</h2>";
    
    $test_product_id = 2821; // ID z vašeho testu
    $test_variant_id = 7605;
    
    echo "<h3>Produkt ID: $test_product_id, Varianta ID: $test_variant_id</h3>";
    
    // Test existence produktu
    $sql = "SELECT * FROM `" . _DB_PREFIX_ . "product` WHERE id_product = $test_product_id";
    $product_data = Db::getInstance()->getRow($sql);
    
    if ($product_data) {
        echo "<div class='test-result success'>✅ Produkt $test_product_id existuje</div>";
        
        // Test existence varianty
        $sql = "SELECT * FROM `" . _DB_PREFIX_ . "product_attribute` WHERE id_product_attribute = $test_variant_id AND id_product = $test_product_id";
        $variant_data = Db::getInstance()->getRow($sql);
        
        if ($variant_data) {
            echo "<div class='test-result success'>✅ Varianta $test_variant_id existuje pro produkt $test_product_id</div>";
            
            // Test přiřazených obrázků
            $sql = "SELECT pai.*, i.position, i.cover 
                    FROM `" . _DB_PREFIX_ . "product_attribute_image` pai
                    LEFT JOIN `" . _DB_PREFIX_ . "image` i ON pai.id_image = i.id_image
                    WHERE pai.id_product_attribute = $test_variant_id";
            $variant_images = Db::getInstance()->executeS($sql);
            
            if ($variant_images) {
                echo "<div class='test-result success'>✅ Varianta má " . count($variant_images) . " přiřazených obrázků</div>";
                
                echo "<h4>Přiřazené obrázky:</h4>";
                echo "<table>";
                echo "<tr><th>ID obrázku</th><th>Pozice</th><th>Cover</th></tr>";
                
                foreach ($variant_images as $img) {
                    echo "<tr>";
                    echo "<td>" . $img['id_image'] . "</td>";
                    echo "<td>" . $img['position'] . "</td>";
                    echo "<td>" . ($img['cover'] ? 'Ano' : 'Ne') . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
            } else {
                echo "<div class='test-result warning'>⚠️ Varianta nemá přiřazené žádné specifické obrázky</div>";
                
                // Zkusíme najít všechny obrázky produktu
                $sql = "SELECT * FROM `" . _DB_PREFIX_ . "image` WHERE id_product = $test_product_id ORDER BY position";
                $all_images = Db::getInstance()->executeS($sql);
                
                if ($all_images) {
                    echo "<div class='test-result'>ℹ️ Produkt má " . count($all_images) . " obrázků celkem</div>";
                    
                    echo "<h4>Všechny obrázky produktu:</h4>";
                    echo "<table>";
                    echo "<tr><th>ID obrázku</th><th>Pozice</th><th>Cover</th></tr>";
                    
                    foreach ($all_images as $img) {
                        echo "<tr>";
                        echo "<td>" . $img['id_image'] . "</td>";
                        echo "<td>" . $img['position'] . "</td>";
                        echo "<td>" . ($img['cover'] ? 'Ano' : 'Ne') . "</td>";
                        echo "</tr>";
                    }
                    echo "</table>";
                }
            }
        } else {
            echo "<div class='test-result error'>❌ Varianta $test_variant_id neexistuje pro produkt $test_product_id</div>";
        }
    } else {
        echo "<div class='test-result error'>❌ Produkt $test_product_id neexistuje</div>";
    }
    
    ?>
    
    <h2>Test nového AJAX endpointu</h2>
    <p><a href="get_variant_image_simple.php?id_product=<?php echo $test_product_id; ?>&id_product_attribute=<?php echo $test_variant_id; ?>" target="_blank">Test jednoduchého AJAX endpointu</a></p>
    
    <h2>Další nástroje</h2>
    <ul>
        <li><a href="diagnostika.php?test=all">Kompletní diagnostika</a></li>
        <li><a href="find_red_variants.php">Najít červené varianty</a></li>
        <li><a href="test_ajax.html">AJAX tester</a></li>
    </ul>
</body>
</html>
