<?php
/**
 * <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> a spolehlivější AJAX endpoint pro získání obrázku varianty produktu.
 */

// Dočasně vypneme hlášení chyb pro diagnostiku, aby <PERSON>vlivňovaly JSON výstup
error_reporting(0);
ini_set('display_errors', 0);

// Načtení PrestaShop frameworku
require_once dirname(__FILE__) . '/../../../config/config.inc.php';
require_once dirname(__FILE__) . '/../../../init.php';

// Získání a validace parametrů
$id_product = (int) Tools::getValue('id_product');
$id_product_attribute = (int) Tools::getValue('id_product_attribute');

if (!$id_product || !$id_product_attribute) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'error' => 'Missing parameters']);
    exit;
}

try {
    $context = Context::getContext();
    $product = new Product($id_product, false, $context->language->id);

    // Zkontrolujeme, zda produkt existuje
    if (!Validate::isLoadedObject($product)) {
        throw new Exception('Product not found');
    }

    $variant_image_id = 0;
    $debug_info = [
        'id_product' => $id_product,
        'id_product_attribute' => $id_product_attribute,
        'product_name' => $product->name,
        'method_used' => 'unknown'
    ];

    // Metoda 1: Přímý SQL dotaz pro nalezení obrázku varianty (nejspolehlivější)
    try {
        $sql = "SELECT pai.id_image, i.position FROM `" . _DB_PREFIX_ . "product_attribute_image` pai LEFT JOIN `" . _DB_PREFIX_ . "image` i ON pai.id_image = i.id_image WHERE pai.id_product_attribute = " . (int)$id_product_attribute . " ORDER BY i.position ASC LIMIT 1";

        $result = Db::getInstance()->getRow($sql);
        if ($result && isset($result['id_image'])) {
            $variant_image_id = $result['id_image'];
            $debug_info['method_used'] = 'direct_sql';
            $debug_info['found_image_id'] = $result['id_image'];
            $debug_info['image_position'] = $result['position'];
        }
    } catch (Exception $e) {
        $debug_info['direct_sql_error'] = $e->getMessage();
    }

    // Metoda 2: Pokud přímý SQL selhal, zkusíme přes ProductAttribute
    if (!$variant_image_id) {
        try {
            $productAttribute = new ProductAttribute($id_product_attribute);
            if (Validate::isLoadedObject($productAttribute)) {
                // Získáme všechny obrázky produktu
                $images = Image::getImages($context->language->id, $id_product);
                $debug_info['total_images'] = count($images);

                // Projdeme všechny obrázky a najdeme ten, který patří k této variantě
                foreach ($images as $image) {
                    // Zkusíme různé způsoby, jak zjistit přiřazení obrázku k variantě

                    // Způsob 1: Přímý SQL dotaz (nejspolehlivější)
                    $sql = "SELECT COUNT(*) as count FROM `" . _DB_PREFIX_ . "product_attribute_image`
                            WHERE id_product_attribute = " . (int)$id_product_attribute . "
                            AND id_image = " . (int)$image['id_image'];

                    try {
                        $result = Db::getInstance()->getValue($sql);
                        if ($result > 0) {
                            $variant_image_id = $image['id_image'];
                            $debug_info['method_used'] = 'sql_via_images';
                            $debug_info['found_in_image_id'] = $image['id_image'];
                            break;
                        }
                    } catch (Exception $e) {
                        $debug_info['sql_error'] = $e->getMessage();
                        // Pokračujeme k dalšímu způsobu
                    }
                }
            }
        } catch (Exception $e) {
            $debug_info['productattribute_error'] = $e->getMessage();
        }
    }

    // Metoda 2: Pokud jsme nenašli specifický obrázek, použijeme cover obrázek
    if (!$variant_image_id) {
        $cover = Product::getCover($id_product);
        if ($cover && isset($cover['id_image'])) {
            $variant_image_id = $cover['id_image'];
            $debug_info['method_used'] = 'cover_fallback';
        }
    }

    // Metoda 3: Pokud ani cover není, použijeme první dostupný obrázek
    if (!$variant_image_id) {
        $images = Image::getImages($context->language->id, $id_product);
        if ($images && count($images) > 0) {
            $variant_image_id = $images[0]['id_image'];
            $debug_info['method_used'] = 'first_image_fallback';
        }
    }

    if ($variant_image_id) {
        $link = new Link();
        
        // Získáme URL pro náhledový obrázek
        $image_url = $link->getImageLink(
            $product->link_rewrite,
            $variant_image_id,
            ImageType::getFormattedName('home_default')
        );

        // Získáme URL pro velký obrázek
        $large_image_url = $link->getImageLink(
            $product->link_rewrite,
            $variant_image_id,
            ImageType::getFormattedName('large_default')
        );

        // Zajistíme správné HTTPS URL
        $image_url = (strpos($image_url, 'http') === 0) ? $image_url : 'https:' . $image_url;
        $large_image_url = (strpos($large_image_url, 'http') === 0) ? $large_image_url : 'https:' . $large_image_url;

        $debug_info['variant_image_id'] = $variant_image_id;

        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'image_url' => $image_url,
            'large_image_url' => $large_image_url,
            'debug' => $debug_info
        ]);
        exit;
    } else {
        throw new Exception('No image found for this variant or product');
    }

} catch (Exception $e) {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false, 
        'error' => $e->getMessage(),
        'debug' => isset($debug_info) ? $debug_info : []
    ]);
    exit;
}
?>
