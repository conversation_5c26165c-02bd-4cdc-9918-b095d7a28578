<?php
/**
 * Finální test celé funkcionality filtrace barev
 */

// Načtení PrestaShop frameworku
require_once dirname(__FILE__) . '/../../../config/config.inc.php';
require_once dirname(__FILE__) . '/../../../init.php';

header('Content-Type: text/html; charset=utf-8');

?>
<!DOCTYPE html>
<html>
<head>
    <title>Finální test filtrace barev</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border: 1px solid #ddd; }
        .success { background-color: #d4edda; }
        .error { background-color: #f8d7da; }
        .warning { background-color: #fff3cd; }
        .info { background-color: #d1ecf1; }
        .celebration { background-color: #d4edda; border: 3px solid #28a745; text-align: center; padding: 20px; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; font-size: 12px; }
        .image-comparison { display: flex; gap: 20px; margin: 20px 0; }
        .image-box { text-align: center; border: 1px solid #ddd; padding: 10px; }
        .image-box img { max-width: 200px; }
        .correct { border-color: #28a745; background-color: #f8fff9; }
        .incorrect { border-color: #dc3545; background-color: #fff8f8; }
    </style>
</head>
<body>
    <h1>🎯 Finální test filtrace barev</h1>
    
    <?php
    
    $test_product_id = 2821;
    $test_variant_id = 7605;
    
    echo "<div class='info test-result'>";
    echo "<strong>Testovací scénář:</strong><br>";
    echo "1. Zákazník jde na kategorii s produkty<br>";
    echo "2. Aplikuje filtr 'Červená barva'<br>";
    echo "3. Měl by vidět červené obrázky produktů<br>";
    echo "4. Konkrétně produkt 'Dering stínítko' by měl zobrazit červený obrázek (ID 31794) místo výchozího (ID 31792)";
    echo "</div>";
    
    // Test 1: Ověření dat
    echo "<h2>1. Ověření dat v databázi</h2>";
    
    try {
        $sql = "SELECT id_image FROM `" . _DB_PREFIX_ . "product_attribute_image` WHERE id_product_attribute = " . (int)$test_variant_id;
        $variant_images = Db::getInstance()->executeS($sql);
        
        if ($variant_images && count($variant_images) > 0) {
            echo "<div class='test-result success'>";
            echo "✅ Varianta má " . count($variant_images) . " přiřazených obrázků: ";
            foreach ($variant_images as $img) {
                echo $img['id_image'] . " ";
            }
            echo "</div>";
            $expected_image_id = $variant_images[0]['id_image'];
        } else {
            echo "<div class='test-result error'>❌ Varianta nemá přiřazené obrázky</div>";
            $expected_image_id = null;
        }
    } catch (Exception $e) {
        echo "<div class='test-result error'>❌ Chyba databáze: " . $e->getMessage() . "</div>";
        $expected_image_id = null;
    }
    
    // Test 2: AJAX endpoint
    echo "<h2>2. Test AJAX endpointu</h2>";
    
    $_GET['id_product'] = $test_product_id;
    $_GET['id_product_attribute'] = $test_variant_id;
    
    ob_start();
    include dirname(__FILE__) . '/get_variant_image_basic.php';
    $ajax_output = ob_get_clean();
    
    echo "<h4>AJAX výstup:</h4>";
    echo "<pre>" . htmlspecialchars($ajax_output) . "</pre>";

    $ajax_data = json_decode($ajax_output, true);
    if ($ajax_data && $ajax_data['success']) {
        $returned_image_id = $ajax_data['debug']['variant_image_id'];
        $method_used = $ajax_data['debug']['method_used'];

        if ($expected_image_id && $returned_image_id == $expected_image_id) {
            echo "<div class='test-result success'>";
            echo "✅ AJAX endpoint vrací správný obrázek: $returned_image_id (metoda: $method_used)";
            echo "</div>";
            $ajax_success = true;
        } else {
            echo "<div class='test-result error'>";
            echo "❌ AJAX endpoint vrací nesprávný obrázek: $returned_image_id (očekáváno: $expected_image_id)";
            echo "</div>";
            $ajax_success = false;
        }
    } else {
        echo "<div class='test-result error'>❌ AJAX endpoint selhal</div>";
        $ajax_success = false;
    }
    
    // Test 3: Vizuální porovnání
    echo "<h2>3. Vizuální porovnání obrázků</h2>";
    
    if ($ajax_data && $ajax_data['success']) {
        $context = Context::getContext();
        $product = new Product($test_product_id, false, $context->language->id);
        $link = new Link();
        
        // Cover obrázek (nesprávný)
        $cover = Product::getCover($test_product_id);
        $cover_url = '';
        if ($cover) {
            $cover_url = 'https:' . $link->getImageLink(
                $product->link_rewrite,
                $cover['id_image'],
                ImageType::getFormattedName('home_default')
            );
        }
        
        // Vrácený obrázek z AJAX
        $ajax_image_url = $ajax_data['image_url'];
        
        echo "<div class='image-comparison'>";
        
        echo "<div class='image-box incorrect'>";
        echo "<h4>❌ Původní (nesprávný)</h4>";
        echo "<img src='$cover_url' alt='Cover obrázek'>";
        echo "<p>Cover obrázek (ID: " . $cover['id_image'] . ")</p>";
        echo "<p>Toto se zobrazovalo dříve</p>";
        echo "</div>";
        
        echo "<div class='image-box correct'>";
        echo "<h4>✅ Nový (správný)</h4>";
        echo "<img src='$ajax_image_url' alt='Obrázek varianty'>";
        echo "<p>Obrázek varianty (ID: $returned_image_id)</p>";
        echo "<p>Toto se zobrazuje nyní</p>";
        echo "</div>";
        
        echo "</div>";
    }
    
    // Test 4: Soubory
    echo "<h2>4. Kontrola souborů</h2>";
    
    $files_to_check = [
        'AJAX endpoint' => dirname(__FILE__) . '/get_variant_image_basic.php',
        'Product template' => dirname(__FILE__) . '/../product.tpl',
        'Variant links template' => dirname(__FILE__) . '/../variant-links.tpl'
    ];
    
    $all_files_exist = true;
    foreach ($files_to_check as $name => $path) {
        if (file_exists($path)) {
            echo "<div class='test-result success'>✅ $name existuje</div>";
        } else {
            echo "<div class='test-result error'>❌ $name NEEXISTUJE: $path</div>";
            $all_files_exist = false;
        }
    }
    
    // Finální vyhodnocení
    echo "<h2>🏁 Finální vyhodnocení</h2>";
    
    if ($expected_image_id && $ajax_success && $all_files_exist) {
        echo "<div class='celebration'>";
        echo "<h2>🎉 GRATULUJEME! 🎉</h2>";
        echo "<h3>Filtrace barev je plně funkční!</h3>";
        echo "<p><strong>Co bylo dosaženo:</strong></p>";
        echo "<ul style='text-align: left; display: inline-block;'>";
        echo "<li>✅ AJAX endpoint správně vrací obrázky variant</li>";
        echo "<li>✅ JavaScript je aktualizován pro použití fungujícího endpointu</li>";
        echo "<li>✅ Všechny potřebné soubory jsou na místě</li>";
        echo "<li>✅ Databázová struktura je v pořádku</li>";
        echo "</ul>";
        echo "<p><strong>Výsledek:</strong> Když zákazník vyfiltruje červenou barvu, uvidí červené obrázky produktů!</p>";
        echo "</div>";
        
        echo "<div class='info test-result'>";
        echo "<h4>📋 Další kroky:</h4>";
        echo "<ol>";
        echo "<li><strong>Nahrajte upravený product.tpl na server</strong> do složky tématu</li>";
        echo "<li><strong>Otestujte na produkci:</strong> Jděte na kategorii → aplikujte barevný filtr → zkontrolujte obrázky</li>";
        echo "<li><strong>Ověřte další barvy:</strong> Zkuste i jiné barvy než červenou</li>";
        echo "</ol>";
        echo "</div>";
        
    } else {
        echo "<div class='test-result error'>";
        echo "<h3>❌ Ještě nejsme hotovi</h3>";
        echo "<p>Některé testy selhaly. Zkontrolujte výše uvedené chyby.</p>";
        echo "</div>";
    }
    
    ?>
    
    <h2>🔧 Diagnostické nástroje</h2>
    <ul>
        <li><a href="test_all_endpoints.php">Test všech AJAX endpointů</a></li>
        <li><a href="find_red_variants.php">Najít červené varianty</a></li>
        <li><a href="diagnostika.php?test=all">Kompletní diagnostika</a></li>
        <li><a href="test_ajax.html">Interaktivní AJAX tester</a></li>
    </ul>
    
    <h2>📁 Soubory k nahrání na server</h2>
    <div class="info test-result">
        <p><strong>Tyto soubory nahrajte na produkční server:</strong></p>
        <ul>
            <li><code>/modules/filtrace_barev/ajax/get_variant_image_basic.php</code></li>
            <li><code>/themes/Electech/templates/catalog/_partials/miniatures/product.tpl</code> (aktualizovaný)</li>
            <li><code>/themes/Electech/templates/catalog/_partials/variant-links.tpl</code></li>
        </ul>
    </div>
</body>
</html>
