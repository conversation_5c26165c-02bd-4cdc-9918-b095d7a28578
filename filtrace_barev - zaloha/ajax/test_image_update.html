<!DOCTYPE html>
<html>
<head>
    <title>Test aktualizace <PERSON></title>
    <meta charset="utf-8">
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            line-height: 1.6; 
            background: #f8f9fa;
        }
        .container { 
            max-width: 1000px; 
            margin: 0 auto; 
            background: white; 
            padding: 30px; 
            border-radius: 10px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .step { 
            margin: 20px 0; 
            padding: 20px; 
            border: 2px solid #ddd; 
            border-radius: 8px; 
            background: #fafafa;
        }
        .success { border-color: #28a745; background: #f8fff9; }
        .error { border-color: #dc3545; background: #fff5f5; }
        .info { border-color: #17a2b8; background: #f0f9ff; }
        .warning { border-color: #ffc107; background: #fffef0; }
        
        .image-comparison { 
            display: flex; 
            gap: 20px; 
            margin: 20px 0; 
            flex-wrap: wrap;
        }
        .image-box { 
            text-align: center; 
            padding: 15px; 
            border: 2px solid #ddd; 
            border-radius: 8px; 
            flex: 1;
            min-width: 200px;
        }
        .image-box img { 
            max-width: 150px; 
            height: auto; 
            border: 1px solid #ccc;
            border-radius: 5px;
        }
        .correct { border-color: #28a745; background: #f8fff9; }
        .incorrect { border-color: #dc3545; background: #fff5f5; }
        
        .code { 
            background: #f1f3f4; 
            padding: 15px; 
            border-radius: 5px; 
            font-family: monospace; 
            margin: 10px 0;
            border-left: 4px solid #007bff;
            font-size: 14px;
        }
        
        button { 
            padding: 10px 20px; 
            background: #007bff; 
            color: white; 
            border: none; 
            border-radius: 3px; 
            cursor: pointer; 
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        
        h1 { color: #333; text-align: center; }
        h2 { color: #007bff; }
        
        .highlight { background: #fff3cd; padding: 2px 5px; border-radius: 3px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Test aktualizace obrázků</h1>
        
        <div class="info step">
            <h2>📋 Zjištěný problém</h2>
            <p>Z analýzy HTML a konzole jsme zjistili:</p>
            <ul>
                <li>✅ JavaScript správně detekuje filtry a varianty</li>
                <li>✅ PrestaShop už zobrazuje správné varianty (ID 7605)</li>
                <li>❌ Ale obrázky zůstávají staré (31792 místo 31794)</li>
                <li>🔧 JavaScript říká "už má správnou variantu" a nepřepisuje obrázek</li>
            </ul>
        </div>
        
        <div class="warning step">
            <h2>🎯 Řešení</h2>
            <p>Upravili jsme JavaScript logiku:</p>
            <ol>
                <li><strong>Kontrola obrázku i u správných variant</strong> - i když má správnou variantu, zkontrolujeme obrázek</li>
                <li><strong>Aktualizace všech obrázků</strong> - hlavní i fliper obrázek</li>
                <li><strong>Porovnání URL obrázků</strong> - ověříme, zda se liší od aktuálního</li>
            </ol>
        </div>
        
        <div class="step">
            <h2>🔧 Co bylo změněno v JavaScript</h2>
            
            <h3>1. Kontrola obrázku i u správných variant:</h3>
            <div class="code">
// PŘED (přeskakoval produkty se správnou variantou):
if (currentAttributeId === targetAttributeId) {
    debugLog(`Produkt už má správnou variantu`);
    continue; // ❌ Přeskočí bez kontroly obrázku
}

// PO (kontroluje i obrázek):
if (currentAttributeId === targetAttributeId) {
    debugLog(`Produkt už má správnou variantu`);
    
    // ✅ Ale zkontrolujeme obrázek
    const imageData = await loadVariantImage(productId, targetAttributeId);
    if (imageData && imageData.imageUrl !== currentImageSrc) {
        updateProductImage(...); // 🔄 Aktualizuje obrázek
    }
}
            </div>
            
            <h3>2. Aktualizace fliper obrázku:</h3>
            <div class="code">
// Přidáno do updateProductImage():
const fliperImgElement = productElement.querySelector('.fliper_image');

if (fliperImgElement && imageUrl) {
    fliperImgElement.src = imageUrl;
    fliperImgElement.setAttribute('data-src', imageUrl);
}
            </div>
        </div>
        
        <div class="step">
            <h2>🧪 Očekávané výsledky po opravě</h2>
            
            <p>Po nahrání opraveného JavaScript souboru byste měli v konzoli vidět:</p>
            
            <div class="code">
[Filtrace Barev] Produkt 2821 už má správnou variantu 7605
[Filtrace Barev] Aktuální obrázek: https://czimg-dev1.www2.peterman.cz/31792-home_default/dering-stinitko.jpg
[Filtrace Barev] AJAX volání: .../get_variant_image_basic.php?id_product=2821&id_product_attribute=7605
[Filtrace Barev] 🔄 Aktualizuji obrázek i přes správnou variantu
[Filtrace Barev] Aktualizuji hlavní obrázek: https://czimg-dev1.www2.peterman.cz/31794-home_default/dering-stinitko.jpg
[Filtrace Barev] Aktualizuji fliper obrázek: https://czimg-dev1.www2.peterman.cz/31794-home_default/dering-stinitko.jpg
            </div>
        </div>
        
        <div class="image-comparison">
            <div class="image-box incorrect">
                <h4>❌ Před opravou</h4>
                <img src="https://czimg-dev1.www2.peterman.cz/31792-home_default/dering-stinitko.jpg" alt="Starý obrázek">
                <p><strong>ID:</strong> 31792 (cover)</p>
                <p>Zobrazoval se i při červeném filtru</p>
            </div>
            
            <div class="image-box correct">
                <h4>✅ Po opravě</h4>
                <img src="https://czimg-dev1.www2.peterman.cz/31794-home_default/dering-stinitko.jpg" alt="Nový obrázek">
                <p><strong>ID:</strong> 31794 (červená varianta)</p>
                <p>Měl by se zobrazit při červeném filtru</p>
            </div>
        </div>
        
        <div class="success step">
            <h2>🚀 Kroky k dokončení</h2>
            <ol>
                <li><strong>Nahrajte opravený soubor:</strong> <span class="highlight">/modules/filtrace_barev/views/js/filtrace_barev.js</span></li>
                <li><strong>Vymažte cache prohlížeče:</strong> Ctrl+F5</li>
                <li><strong>Jděte na kategorii slunečníků</strong></li>
                <li><strong>Aplikujte červený filtr</strong></li>
                <li><strong>Sledujte konzoli</strong> - měli byste vidět aktualizaci obrázků</li>
                <li><strong>Ověřte vizuálně</strong> - obrázky by se měly změnit na červené</li>
            </ol>
        </div>
        
        <div class="step">
            <h2>🔗 Užitečné odkazy</h2>
            <div style="text-align: center;">
                <a href="../views/js/filtrace_barev.js" target="_blank" style="display: inline-block; padding: 10px 20px; background: #28a745; color: white; text-decoration: none; border-radius: 3px; margin: 5px;">📁 Stáhnout opravený JS</a>
                <a href="production_test.php" style="display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 3px; margin: 5px;">🧪 Test produkce</a>
                <a href="quick_test.html" style="display: inline-block; padding: 10px 20px; background: #17a2b8; color: white; text-decoration: none; border-radius: 3px; margin: 5px;">⚡ Rychlý test</a>
            </div>
        </div>
        
        <div class="info step">
            <h2>💡 Debug tipy</h2>
            <ul>
                <li><strong>Konzole:</strong> Hledejte zprávy "🔄 Aktualizuji obrázek i přes správnou variantu"</li>
                <li><strong>Network tab:</strong> Sledujte AJAX volání na get_variant_image_basic.php</li>
                <li><strong>Elements tab:</strong> Zkontrolujte, zda se mění src atribut obrázků</li>
                <li><strong>Porovnání ID:</strong> 31792 (špatně) vs 31794 (správně)</li>
            </ul>
        </div>
    </div>
    
    <script>
    // Automatická detekce, zda je na stránce s filtry
    if (window.location.search.includes('q=')) {
        const banner = document.createElement('div');
        banner.style.cssText = 'position: fixed; top: 0; left: 0; right: 0; background: #ffc107; color: #212529; padding: 10px; text-align: center; z-index: 9999; font-weight: bold;';
        banner.innerHTML = '⚠️ Detekován filtr v URL! Po nahrání opraveného JS by se měly obrázky aktualizovat.';
        document.body.appendChild(banner);
        
        setTimeout(() => banner.remove(), 8000);
    }
    </script>
</body>
</html>
