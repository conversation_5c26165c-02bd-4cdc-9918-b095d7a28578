<?php
/**
 * Diagnostický nástroj pro testování filtrace barev a zobrazování variant
 * Použití: /modules/filtrace_barev/ajax/diagnostika.php?test=all
 */

// Načtení PrestaShop frameworku
require_once dirname(__FILE__) . '/../../../config/config.inc.php';
require_once dirname(__FILE__) . '/../../../init.php';

// Získání parametrů
$test = Tools::getValue('test', 'all');
$id_product = (int) Tools::getValue('id_product', 0);
$id_category = (int) Tools::getValue('id_category', 0);

// Nastavení výstupu
header('Content-Type: text/html; charset=utf-8');

?>
<!DOCTYPE html>
<html>
<head>
    <title>Diagnostika filtrace barev</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .info { color: blue; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>Diagnostika filtrace barev - PrestaShop</h1>
    
    <?php
    
    function logMessage($message, $type = 'info') {
        $class = $type;
        echo "<p class='$class'>[$type] $message</p>";
    }
    
    function testBasicSetup() {
        echo "<div class='test-section'>";
        echo "<h2>1. Základní nastavení</h2>";
        
        // Test PrestaShop verze
        logMessage("PrestaShop verze: " . _PS_VERSION_, 'info');
        
        // Test existence souborů
        $context = Context::getContext();
        $theme_dir = _PS_THEME_DIR_;
        $electech_theme_dir = _PS_ROOT_DIR_ . '/themes/Electech/';

        $files = [
            'get_variant_image.php' => dirname(__FILE__) . '/get_variant_image.php',
            'product.tpl (lokální)' => dirname(__FILE__) . '/../product.tpl',
            'variant-links.tpl (lokální)' => dirname(__FILE__) . '/../variant-links.tpl',
            'product.tpl (aktuální téma)' => $theme_dir . 'templates/catalog/_partials/miniatures/product.tpl',
            'variant-links.tpl (aktuální téma)' => $theme_dir . 'templates/catalog/_partials/variant-links.tpl',
            'product.tpl (Electech)' => $electech_theme_dir . 'templates/catalog/_partials/miniatures/product.tpl',
            'variant-links.tpl (Electech)' => $electech_theme_dir . 'templates/catalog/_partials/variant-links.tpl'
        ];
        
        foreach ($files as $name => $path) {
            if (file_exists($path)) {
                logMessage("Soubor $name existuje", 'success');
            } else {
                logMessage("Soubor $name NEEXISTUJE na cestě: $path", 'error');
            }
        }
        
        // Test práv k souborům
        if (is_readable(dirname(__FILE__) . '/get_variant_image.php')) {
            logMessage("AJAX endpoint je čitelný", 'success');
        } else {
            logMessage("AJAX endpoint NENÍ čitelný", 'error');
        }
        
        echo "</div>";
    }
    
    function testProductsWithVariants() {
        echo "<div class='test-section'>";
        echo "<h2>2. Produkty s variantami</h2>";
        
        $context = Context::getContext();
        
        // Najdeme produkty s variantami (kombinacemi)
        $sql = "SELECT DISTINCT p.id_product, pl.name, COUNT(pa.id_product_attribute) as variant_count
                FROM " . _DB_PREFIX_ . "product p
                LEFT JOIN " . _DB_PREFIX_ . "product_lang pl ON (p.id_product = pl.id_product AND pl.id_lang = " . (int)$context->language->id . ")
                LEFT JOIN " . _DB_PREFIX_ . "product_attribute pa ON p.id_product = pa.id_product
                WHERE p.active = 1 AND pa.id_product_attribute IS NOT NULL
                GROUP BY p.id_product
                HAVING variant_count > 1
                ORDER BY variant_count DESC
                LIMIT 10";
        
        $products = Db::getInstance()->executeS($sql);
        
        if ($products) {
            logMessage("Nalezeno " . count($products) . " produktů s variantami", 'success');
            
            echo "<table>";
            echo "<tr><th>ID produktu</th><th>Název</th><th>Počet variant</th><th>Akce</th></tr>";
            
            foreach ($products as $product) {
                echo "<tr>";
                echo "<td>" . $product['id_product'] . "</td>";
                echo "<td>" . htmlspecialchars($product['name']) . "</td>";
                echo "<td>" . $product['variant_count'] . "</td>";
                echo "<td><a href='?test=product&id_product=" . $product['id_product'] . "'>Testovat</a></td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            logMessage("Nebyly nalezeny žádné produkty s variantami", 'warning');
        }
        
        echo "</div>";
    }
    
    function testSpecificProduct($id_product) {
        echo "<div class='test-section'>";
        echo "<h2>3. Test konkrétního produktu (ID: $id_product)</h2>";
        
        $context = Context::getContext();
        $product = new Product($id_product, false, $context->language->id);
        
        if (!Validate::isLoadedObject($product)) {
            logMessage("Produkt s ID $id_product neexistuje", 'error');
            echo "</div>";
            return;
        }
        
        logMessage("Produkt: " . $product->name, 'info');
        
        // Získáme varianty produktu
        $combinations = $product->getAttributeCombinations($context->language->id);
        
        if ($combinations) {
            logMessage("Nalezeno " . count($combinations) . " kombinací atributů", 'success');
            
            // Seskupíme kombinace podle id_product_attribute
            $grouped_combinations = [];
            foreach ($combinations as $combination) {
                $id_attr = $combination['id_product_attribute'];
                if (!isset($grouped_combinations[$id_attr])) {
                    $grouped_combinations[$id_attr] = [
                        'id_product_attribute' => $id_attr,
                        'attributes' => [],
                        'reference' => $combination['reference'],
                        'price' => $combination['price']
                    ];
                }
                $grouped_combinations[$id_attr]['attributes'][] = [
                    'name' => $combination['attribute_name'],
                    'group' => $combination['group_name']
                ];
            }
            
            echo "<table>";
            echo "<tr><th>ID varianty</th><th>Atributy</th><th>Reference</th><th>Test AJAX</th></tr>";
            
            foreach ($grouped_combinations as $combination) {
                echo "<tr>";
                echo "<td>" . $combination['id_product_attribute'] . "</td>";
                echo "<td>";
                foreach ($combination['attributes'] as $attr) {
                    echo $attr['group'] . ": " . $attr['name'] . "<br>";
                }
                echo "</td>";
                echo "<td>" . $combination['reference'] . "</td>";
                echo "<td><a href='?test=ajax&id_product=$id_product&id_product_attribute=" . $combination['id_product_attribute'] . "'>Test</a></td>";
                echo "</tr>";
            }
            echo "</table>";
            
        } else {
            logMessage("Produkt nemá žádné varianty", 'warning');
        }
        
        // Test obrázků produktu
        $images = $product->getImages($context->language->id);
        if ($images) {
            logMessage("Nalezeno " . count($images) . " obrázků", 'success');

            echo "<h3>Obrázky produktu:</h3>";
            echo "<table>";
            echo "<tr><th>ID obrázku</th><th>Pozice</th><th>Cover</th><th>Přiřazené varianty</th></tr>";

            foreach ($images as $image) {
                // Najdeme varianty přiřazené k tomuto obrázku
                $sql = "SELECT pai.id_product_attribute
                        FROM " . _DB_PREFIX_ . "product_attribute_image pai
                        WHERE pai.id_image = " . (int)$image['id_image'];
                $assigned_variants = Db::getInstance()->executeS($sql);

                $variant_ids = [];
                if ($assigned_variants) {
                    foreach ($assigned_variants as $variant) {
                        $variant_ids[] = $variant['id_product_attribute'];
                    }
                }

                echo "<tr>";
                echo "<td>" . $image['id_image'] . "</td>";
                echo "<td>" . $image['position'] . "</td>";
                echo "<td>" . ($image['cover'] ? 'Ano' : 'Ne') . "</td>";
                echo "<td>" . (empty($variant_ids) ? 'Všechny varianty' : implode(', ', $variant_ids)) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }

        // Test přiřazení obrázků k variantám
        echo "<h3>Přiřazení obrázků k variantám:</h3>";
        $sql = "SELECT pa.id_product_attribute, pa.reference, pai.id_image, i.position
                FROM " . _DB_PREFIX_ . "product_attribute pa
                LEFT JOIN " . _DB_PREFIX_ . "product_attribute_image pai ON pa.id_product_attribute = pai.id_product_attribute
                LEFT JOIN " . _DB_PREFIX_ . "image i ON pai.id_image = i.id_image
                WHERE pa.id_product = " . (int)$id_product . "
                ORDER BY pa.id_product_attribute, i.position";

        $variant_images = Db::getInstance()->executeS($sql);
        if ($variant_images) {
            echo "<table>";
            echo "<tr><th>ID varianty</th><th>Reference</th><th>ID obrázku</th><th>Pozice obrázku</th></tr>";

            foreach ($variant_images as $vi) {
                echo "<tr>";
                echo "<td>" . $vi['id_product_attribute'] . "</td>";
                echo "<td>" . $vi['reference'] . "</td>";
                echo "<td>" . ($vi['id_image'] ?: 'Žádný') . "</td>";
                echo "<td>" . ($vi['position'] ?: '-') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
        echo "</div>";
    }
    
    function testAjaxEndpoint($id_product, $id_product_attribute) {
        echo "<div class='test-section'>";
        echo "<h2>4. Test AJAX endpointu</h2>";
        
        logMessage("Testování: ID produktu = $id_product, ID varianty = $id_product_attribute", 'info');
        
        // Simulujeme volání AJAX endpointu
        $_GET['id_product'] = $id_product;
        $_GET['id_product_attribute'] = $id_product_attribute;
        
        ob_start();
        include dirname(__FILE__) . '/get_variant_image.php';
        $output = ob_get_clean();
        
        echo "<h3>Výstup AJAX endpointu:</h3>";
        echo "<pre>" . htmlspecialchars($output) . "</pre>";
        
        // Pokusíme se dekódovat JSON
        $data = json_decode($output, true);
        if ($data) {
            if ($data['success']) {
                logMessage("AJAX endpoint vrátil úspěšnou odpověď", 'success');
                logMessage("URL obrázku: " . $data['image_url'], 'info');
                if (isset($data['large_image_url'])) {
                    logMessage("URL velkého obrázku: " . $data['large_image_url'], 'info');
                }
            } else {
                logMessage("AJAX endpoint vrátil chybu: " . $data['error'], 'error');
            }
        } else {
            logMessage("AJAX endpoint nevrátil platný JSON", 'error');
        }
        
        echo "</div>";
    }
    
    function testColorFilters() {
        echo "<div class='test-section'>";
        echo "<h2>5. Test barevných filtrů</h2>";
        
        $context = Context::getContext();
        
        // Najdeme atributy typu "barva"
        $sql = "SELECT ag.id_attribute_group, agl.name as group_name, a.id_attribute, al.name as attribute_name, a.color
                FROM " . _DB_PREFIX_ . "attribute_group ag
                LEFT JOIN " . _DB_PREFIX_ . "attribute_group_lang agl ON (ag.id_attribute_group = agl.id_attribute_group AND agl.id_lang = " . (int)$context->language->id . ")
                LEFT JOIN " . _DB_PREFIX_ . "attribute a ON ag.id_attribute_group = a.id_attribute_group
                LEFT JOIN " . _DB_PREFIX_ . "attribute_lang al ON (a.id_attribute = al.id_attribute AND al.id_lang = " . (int)$context->language->id . ")
                WHERE agl.name LIKE '%barv%' OR agl.name LIKE '%color%' OR a.color != ''
                ORDER BY ag.id_attribute_group, a.id_attribute";
        
        $attributes = Db::getInstance()->executeS($sql);
        
        if ($attributes) {
            logMessage("Nalezeno " . count($attributes) . " barevných atributů", 'success');
            
            echo "<table>";
            echo "<tr><th>Skupina</th><th>Atribut</th><th>Barva</th><th>Normalizovaný název</th></tr>";
            
            foreach ($attributes as $attr) {
                $normalized = strtolower($attr['attribute_name']);
                $normalized = iconv('UTF-8', 'ASCII//TRANSLIT', $normalized);
                $normalized = preg_replace('/[^a-z0-9]/', '-', $normalized);
                $normalized = preg_replace('/-+/', '-', $normalized);
                $normalized = trim($normalized, '-');
                
                echo "<tr>";
                echo "<td>" . htmlspecialchars($attr['group_name']) . "</td>";
                echo "<td>" . htmlspecialchars($attr['attribute_name']) . "</td>";
                echo "<td style='background-color: " . $attr['color'] . "'>" . $attr['color'] . "</td>";
                echo "<td>" . $normalized . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            logMessage("Nebyly nalezeny žádné barevné atributy", 'warning');
        }
        
        echo "</div>";
    }
    
    function testJavaScriptLogic() {
        echo "<div class='test-section'>";
        echo "<h2>6. Test JavaScript logiky</h2>";

        echo "<p>Tento test ověří, zda JavaScript logika správně funguje na stránce s produkty.</p>";

        echo "<h3>Test normalizace textu:</h3>";
        echo "<script>";
        echo "
        function normalizeText(text) {
            if (!text) return '';
            return text.toLowerCase()
                .normalize('NFD').replace(/[\\u0300-\\u036f]/g, '')
                .replace(/\\s+/g, '-');
        }

        const testCases = [
            'Červená',
            'Modrá',
            'Žlutá',
            'Zelená',
            'Černá',
            'Bílá',
            'Růžová',
            'Fialová'
        ];

        console.log('=== Test normalizace barev ===');
        testCases.forEach(color => {
            const normalized = normalizeText(color);
            console.log(color + ' -> ' + normalized);
        });
        ";
        echo "</script>";

        echo "<p>Výsledky normalizace jsou v konzoli prohlížeče (F12).</p>";

        echo "<h3>Test detekce URL parametrů:</h3>";
        echo "<p>Aktuální URL: <code>" . htmlspecialchars($_SERVER['REQUEST_URI']) . "</code></p>";

        if (isset($_GET['q'])) {
            $qParam = $_GET['q'];
            echo "<p>Parametr 'q': <code>" . htmlspecialchars($qParam) . "</code></p>";

            $qParts = explode('/', $qParam);
            $colorFilters = [];
            foreach ($qParts as $part) {
                if (stripos($part, 'barva-') === 0) {
                    $colorFilters[] = substr($part, 6);
                }
            }

            if ($colorFilters) {
                echo "<p>Detekované barevné filtry: " . implode(', ', $colorFilters) . "</p>";
            } else {
                echo "<p>Žádné barevné filtry nebyly detekovány.</p>";
            }
        } else {
            echo "<p>Parametr 'q' není v URL přítomen.</p>";
            echo "<p>Příklad URL s filtrem: <code>?q=barva-cervena/kategorie-123</code></p>";
        }

        echo "</div>";
    }

    function testImageUrls() {
        echo "<div class='test-section'>";
        echo "<h2>7. Test URL obrázků</h2>";

        $context = Context::getContext();
        $link = new Link();

        // Test základních URL
        logMessage("Base URL: " . $link->getBaseLink(), 'info');
        logMessage("Image URL: " . $context->link->getImageLink('test', 1, 'home_default'), 'info');

        // Test existence image typů
        $imageTypes = ImageType::getImagesTypes('products');
        if ($imageTypes) {
            echo "<h3>Dostupné typy obrázků:</h3>";
            echo "<table>";
            echo "<tr><th>Název</th><th>Šířka</th><th>Výška</th></tr>";

            foreach ($imageTypes as $type) {
                echo "<tr>";
                echo "<td>" . $type['name'] . "</td>";
                echo "<td>" . $type['width'] . "</td>";
                echo "<td>" . $type['height'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }

        echo "</div>";
    }

    function testVariantImages($id_product) {
        echo "<div class='test-section'>";
        echo "<h2>8. Detailní test variant a obrázků (ID: $id_product)</h2>";

        $context = Context::getContext();
        $product = new Product($id_product, false, $context->language->id);

        if (!Validate::isLoadedObject($product)) {
            logMessage("Produkt s ID $id_product neexistuje", 'error');
            echo "</div>";
            return;
        }

        logMessage("Produkt: " . $product->name, 'info');

        // Test všech variant s jejich atributy a obrázky
        $sql = "SELECT
                    pa.id_product_attribute,
                    pa.reference,
                    pa.price,
                    GROUP_CONCAT(CONCAT(agl.name, ': ', al.name) SEPARATOR ', ') as attributes,
                    GROUP_CONCAT(pai.id_image SEPARATOR ', ') as image_ids
                FROM " . _DB_PREFIX_ . "product_attribute pa
                LEFT JOIN " . _DB_PREFIX_ . "product_attribute_combination pac ON pa.id_product_attribute = pac.id_product_attribute
                LEFT JOIN " . _DB_PREFIX_ . "attribute a ON pac.id_attribute = a.id_attribute
                LEFT JOIN " . _DB_PREFIX_ . "attribute_lang al ON (a.id_attribute = al.id_attribute AND al.id_lang = " . (int)$context->language->id . ")
                LEFT JOIN " . _DB_PREFIX_ . "attribute_group_lang agl ON (a.id_attribute_group = agl.id_attribute_group AND agl.id_lang = " . (int)$context->language->id . ")
                LEFT JOIN " . _DB_PREFIX_ . "product_attribute_image pai ON pa.id_product_attribute = pai.id_product_attribute
                WHERE pa.id_product = " . (int)$id_product . "
                GROUP BY pa.id_product_attribute
                ORDER BY pa.id_product_attribute";

        $variants = Db::getInstance()->executeS($sql);

        if ($variants) {
            logMessage("Nalezeno " . count($variants) . " variant", 'success');

            echo "<table>";
            echo "<tr><th>ID varianty</th><th>Atributy</th><th>Reference</th><th>Cena</th><th>ID obrázků</th><th>Test AJAX</th></tr>";

            foreach ($variants as $variant) {
                echo "<tr>";
                echo "<td>" . $variant['id_product_attribute'] . "</td>";
                echo "<td>" . ($variant['attributes'] ?: 'Žádné') . "</td>";
                echo "<td>" . $variant['reference'] . "</td>";
                echo "<td>" . $variant['price'] . "</td>";
                echo "<td>" . ($variant['image_ids'] ?: 'Žádné') . "</td>";
                echo "<td><a href='?test=ajax&id_product=$id_product&id_product_attribute=" . $variant['id_product_attribute'] . "'>Test</a></td>";
                echo "</tr>";
            }
            echo "</table>";

            // Test konkrétních barevných variant
            echo "<h3>Barevné varianty:</h3>";
            $color_variants = [];
            foreach ($variants as $variant) {
                if (stripos($variant['attributes'], 'barva:') !== false || stripos($variant['attributes'], 'color:') !== false) {
                    $color_variants[] = $variant;
                }
            }

            if ($color_variants) {
                logMessage("Nalezeno " . count($color_variants) . " barevných variant", 'success');

                echo "<table>";
                echo "<tr><th>ID varianty</th><th>Barva</th><th>Obrázky</th><th>Test</th></tr>";

                foreach ($color_variants as $variant) {
                    // Extrahujeme název barvy
                    preg_match('/barva:\s*([^,]+)/i', $variant['attributes'], $matches);
                    $color_name = isset($matches[1]) ? trim($matches[1]) : 'Neznámá';

                    echo "<tr>";
                    echo "<td>" . $variant['id_product_attribute'] . "</td>";
                    echo "<td>" . $color_name . "</td>";
                    echo "<td>" . ($variant['image_ids'] ?: 'Žádné') . "</td>";
                    echo "<td><a href='test_ajax.html?id_product=$id_product&id_product_attribute=" . $variant['id_product_attribute'] . "' target='_blank'>AJAX test</a></td>";
                    echo "</tr>";
                }
                echo "</table>";
            } else {
                logMessage("Nebyly nalezeny žádné barevné varianty", 'warning');
            }
        } else {
            logMessage("Produkt nemá žádné varianty", 'warning');
        }

        echo "</div>";
    }

    // Spuštění testů podle parametru
    switch ($test) {
        case 'all':
            testBasicSetup();
            testProductsWithVariants();
            testColorFilters();
            testJavaScriptLogic();
            testImageUrls();
            break;

        case 'product':
            if ($id_product) {
                testSpecificProduct($id_product);
            } else {
                logMessage("Chybí parametr id_product", 'error');
            }
            break;

        case 'ajax':
            $id_product_attribute = (int) Tools::getValue('id_product_attribute', 0);
            if ($id_product && $id_product_attribute) {
                testAjaxEndpoint($id_product, $id_product_attribute);
            } else {
                logMessage("Chybí parametry id_product nebo id_product_attribute", 'error');
            }
            break;

        case 'js':
            testJavaScriptLogic();
            break;

        case 'images':
            testImageUrls();
            break;

        case 'variant_images':
            if ($id_product) {
                testVariantImages($id_product);
            } else {
                logMessage("Chybí parametr id_product", 'error');
            }
            break;

        default:
            logMessage("Neznámý test: $test", 'error');
    }
    
    ?>
    
    <div class='test-section'>
        <h2>Navigace</h2>
        <p>
            <a href="?test=all">Všechny testy</a> |
            <a href="?test=js">JavaScript logika</a> |
            <a href="?test=images">URL obrázků</a> |
            <a href="test_ajax.html">AJAX tester</a> |
            <a href="javascript:void(0)" onclick="testCurrentPage()">Test aktuální stránky</a>
        </p>

        <p><strong>Testy konkrétních produktů:</strong></p>
        <p>
            <a href="?test=product&id_product=16352">Test produktu 16352</a> |
            <a href="?test=variant_images&id_product=16352">Varianty a obrázky 16352</a>
        </p>

        <h3>Rychlé odkazy:</h3>
        <ul>
            <li><strong>Kompletní diagnostika:</strong> <code>?test=all</code></li>
            <li><strong>Test konkrétního produktu:</strong> <code>?test=product&id_product=123</code></li>
            <li><strong>Test AJAX endpointu:</strong> <code>?test=ajax&id_product=123&id_product_attribute=456</code></li>
            <li><strong>AJAX tester (HTML):</strong> <a href="test_ajax.html">test_ajax.html</a></li>
        </ul>
    </div>
    
    <script>
    function testCurrentPage() {
        // Jednoduchý test JavaScript logiky na aktuální stránce
        console.log('=== Test filtrace barev ===');
        
        // Test detekce filtrů z URL
        const urlParams = new URLSearchParams(window.location.search);
        const qParam = urlParams.get('q');
        console.log('URL parametr q:', qParam);
        
        if (qParam) {
            const qParts = qParam.split('/');
            const colorFilters = [];
            qParts.forEach(part => {
                if (part.toLowerCase().startsWith('barva-')) {
                    colorFilters.push(part.substring(6));
                }
            });
            console.log('Detekované barevné filtry:', colorFilters);
        }
        
        // Test existence produktových miniatur
        const miniatures = document.querySelectorAll('.js-product-miniature');
        console.log('Nalezeno miniatur produktů:', miniatures.length);
        
        // Test existence variant
        const variants = document.querySelectorAll('.variant-links a[data-variant-name]');
        console.log('Nalezeno variant:', variants.length);
        
        alert('Výsledky testu jsou v konzoli (F12)');
    }
    </script>
</body>
</html>
