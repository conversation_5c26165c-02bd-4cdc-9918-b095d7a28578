<?php
/**
 * Test opravy URL formátování
 */

// Načtení PrestaShop frameworku
require_once dirname(__FILE__) . '/../../../config/config.inc.php';
require_once dirname(__FILE__) . '/../../../init.php';

header('Content-Type: text/html; charset=utf-8');

$test_product_id = 2821;
$test_variant_id = 7605;

?>
<!DOCTYPE html>
<html>
<head>
    <title>Test opravy URL</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .test-section { margin: 20px 0; padding: 15px; border: 2px solid #ddd; border-radius: 5px; }
        .success { border-color: #28a745; background-color: #f8fff9; }
        .error { border-color: #dc3545; background-color: #fff8f8; }
        .warning { border-color: #ffc107; background-color: #fffef0; }
        .info { border-color: #17a2b8; background-color: #f0f9ff; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
        .url-test { margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 3px; }
        .url-good { border-left: 4px solid #28a745; }
        .url-bad { border-left: 4px solid #dc3545; }
        .image-test { margin: 10px 0; text-align: center; }
        .image-test img { max-width: 150px; border: 1px solid #ddd; margin: 5px; }
    </style>
</head>
<body>
    <h1>🔧 Test opravy URL formátování</h1>
    
    <div class="warning test-section">
        <h2>⚠️ Identifikovaný problém</h2>
        <p>Z konzole jsme zjistili, že AJAX endpoint vrací URL s chybějícími <code>//</code> po <code>https:</code></p>
        <ul>
            <li>❌ <strong>Špatně:</strong> <code>https:czimg-dev1.www2.peterman.cz/...</code></li>
            <li>✅ <strong>Správně:</strong> <code>https://czimg-dev1.www2.peterman.cz/...</code></li>
        </ul>
    </div>
    
    <?php
    
    echo "<div class='test-section'>";
    echo "<h2>🧪 Test AJAX endpointu před a po opravě</h2>";
    
    // Test původního endpointu
    $_GET['id_product'] = $test_product_id;
    $_GET['id_product_attribute'] = $test_variant_id;
    
    ob_start();
    include dirname(__FILE__) . '/get_variant_image_basic.php';
    $ajax_output = ob_get_clean();
    
    echo "<h3>Výstup opraveného AJAX endpointu:</h3>";
    echo "<pre>" . htmlspecialchars($ajax_output) . "</pre>";
    
    $ajax_data = json_decode($ajax_output, true);
    if ($ajax_data && $ajax_data['success']) {
        $image_url = $ajax_data['image_url'];
        $large_image_url = $ajax_data['large_image_url'];
        
        echo "<h3>Analýza URL:</h3>";
        
        // Test image_url
        $is_valid_image = (strpos($image_url, 'https://') === 0 || strpos($image_url, 'http://') === 0);
        $class_image = $is_valid_image ? 'url-good' : 'url-bad';
        $status_image = $is_valid_image ? '✅ Správně' : '❌ Chybně';
        
        echo "<div class='url-test $class_image'>";
        echo "<strong>Image URL:</strong> $status_image<br>";
        echo "<code>$image_url</code>";
        echo "</div>";
        
        // Test large_image_url
        $is_valid_large = (strpos($large_image_url, 'https://') === 0 || strpos($large_image_url, 'http://') === 0);
        $class_large = $is_valid_large ? 'url-good' : 'url-bad';
        $status_large = $is_valid_large ? '✅ Správně' : '❌ Chybně';
        
        echo "<div class='url-test $class_large'>";
        echo "<strong>Large Image URL:</strong> $status_large<br>";
        echo "<code>$large_image_url</code>";
        echo "</div>";
        
        // Test načítání obrázků
        if ($is_valid_image && $is_valid_large) {
            echo "<div class='success test-section'>";
            echo "<h3>🎉 URL jsou správně formátované!</h3>";
            echo "<p>Obrázky by se nyní měly načítat správně.</p>";
            
            echo "<div class='image-test'>";
            echo "<h4>Test načítání obrázků:</h4>";
            echo "<img src='$image_url' alt='Test obrázku' onload=\"console.log('✅ Obrázek se načetl:', this.src)\" onerror=\"console.log('❌ Obrázek se nenačetl:', this.src)\">";
            echo "<br><small>Zkontrolujte konzoli pro výsledky načítání</small>";
            echo "</div>";
            echo "</div>";
        } else {
            echo "<div class='error test-section'>";
            echo "<h3>❌ URL stále nejsou správně formátované</h3>";
            echo "<p>Je potřeba další oprava AJAX endpointu.</p>";
            echo "</div>";
        }
        
    } else {
        echo "<div class='error test-section'>";
        echo "<h3>❌ AJAX endpoint selhal</h3>";
        echo "<p>Chyba: " . (isset($ajax_data['error']) ? $ajax_data['error'] : 'Neznámá chyba') . "</p>";
        echo "</div>";
    }
    
    echo "</div>";
    
    ?>
    
    <div class="info test-section">
        <h2>🔧 Co bylo opraveno</h2>
        <ol>
            <li><strong>AJAX endpoint:</strong> Opraveno formátování URL z <code>https:</code> na <code>https://</code></li>
            <li><strong>JavaScript:</strong> Přidána validace URL před nastavením do img elementů</li>
            <li><strong>Error handling:</strong> Lepší zpracování chybných URL</li>
        </ol>
    </div>
    
    <div class="test-section">
        <h2>🚀 Kroky k dokončení</h2>
        <ol>
            <li><strong>Nahrajte opravené soubory:</strong>
                <ul>
                    <li><code>/modules/filtrace_barev/ajax/get_variant_image_basic.php</code></li>
                    <li><code>/modules/filtrace_barev/views/js/filtrace_barev.js</code></li>
                </ul>
            </li>
            <li><strong>Vymažte cache prohlížeče:</strong> Ctrl+F5</li>
            <li><strong>Otestujte na kategorii:</strong> Aplikujte červený filtr</li>
            <li><strong>Sledujte konzoli:</strong> Měli byste vidět správné URL</li>
        </ol>
    </div>
    
    <div class="success test-section">
        <h2>✅ Očekávané výsledky</h2>
        <p>Po opravě byste měli vidět:</p>
        <ul>
            <li>✅ Správně formátované URL v AJAX odpovědích</li>
            <li>✅ Obrázky se načítají bez chyb</li>
            <li>✅ Vizuální změna obrázků při filtraci</li>
            <li>✅ Žádné chyby v konzoli</li>
        </ul>
    </div>
    
    <div style="text-align: center; margin-top: 30px;">
        <a href="production_test.php" style="display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 3px; margin: 5px;">🧪 Test produkce</a>
        <a href="quick_test.html" style="display: inline-block; padding: 10px 20px; background: #28a745; color: white; text-decoration: none; border-radius: 3px; margin: 5px;">⚡ Rychlý test</a>
    </div>
    
    <script>
    // Automatický test URL
    window.addEventListener('load', function() {
        const images = document.querySelectorAll('img[src*="czimg-dev1"]');
        images.forEach(img => {
            img.addEventListener('load', function() {
                console.log('✅ Obrázek se úspěšně načetl:', this.src);
            });
            img.addEventListener('error', function() {
                console.error('❌ Obrázek se nepodařilo načíst:', this.src);
            });
        });
    });
    </script>
</body>
</html>
