<?php
/**
 * Nejjednodušší AJAX endpoint pro získání obrázku varianty produktu.
 * Používá pouze základní SQL dotazy bez JOIN.
 */

// Dočasně vypneme hlášení chyb pro diagnostiku, aby <PERSON>vlivňovaly JSON výstup
error_reporting(0);
ini_set('display_errors', 0);

// Načtení PrestaShop frameworku
require_once dirname(__FILE__) . '/../../../config/config.inc.php';
require_once dirname(__FILE__) . '/../../../init.php';

// Získání a validace parametrů
$id_product = (int) Tools::getValue('id_product');
$id_product_attribute = (int) Tools::getValue('id_product_attribute');

if (!$id_product || !$id_product_attribute) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'error' => 'Missing parameters']);
    exit;
}

try {
    $context = Context::getContext();
    $product = new Product($id_product, false, $context->language->id);

    // Zkontrolujeme, zda produkt existuje
    if (!Validate::isLoadedObject($product)) {
        throw new Exception('Product not found');
    }

    $variant_image_id = 0;
    $debug_info = [
        'id_product' => $id_product,
        'id_product_attribute' => $id_product_attribute,
        'product_name' => $product->name,
        'method_used' => 'unknown'
    ];

    // Metoda 1: Nejjednodušší SQL dotaz - pouze z product_attribute_image
    try {
        $sql = "SELECT id_image FROM `" . _DB_PREFIX_ . "product_attribute_image` WHERE id_product_attribute = " . (int)$id_product_attribute . " LIMIT 1";
        
        $result = Db::getInstance()->getValue($sql);
        if ($result) {
            $variant_image_id = $result;
            $debug_info['method_used'] = 'basic_sql';
            $debug_info['found_image_id'] = $result;
        }
    } catch (Exception $e) {
        $debug_info['basic_sql_error'] = $e->getMessage();
    }

    // Metoda 2: Pokud základní SQL selhal, zkusíme přes Db::getInstance()->executeS
    if (!$variant_image_id) {
        try {
            $sql = "SELECT id_image FROM `" . _DB_PREFIX_ . "product_attribute_image` WHERE id_product_attribute = " . (int)$id_product_attribute;
            
            $results = Db::getInstance()->executeS($sql);
            if ($results && count($results) > 0) {
                $variant_image_id = $results[0]['id_image'];
                $debug_info['method_used'] = 'executeS_sql';
                $debug_info['found_image_id'] = $variant_image_id;
                $debug_info['total_variant_images'] = count($results);
            }
        } catch (Exception $e) {
            $debug_info['executeS_sql_error'] = $e->getMessage();
        }
    }

    // Metoda 3: Pokud SQL selhaly, použijeme cover obrázek
    if (!$variant_image_id) {
        $cover = Product::getCover($id_product);
        if ($cover && isset($cover['id_image'])) {
            $variant_image_id = $cover['id_image'];
            $debug_info['method_used'] = 'cover_fallback';
        }
    }

    // Metoda 4: Pokud ani cover není, použijeme první dostupný obrázek
    if (!$variant_image_id) {
        $images = Image::getImages($context->language->id, $id_product);
        if ($images && count($images) > 0) {
            $variant_image_id = $images[0]['id_image'];
            $debug_info['method_used'] = 'first_image_fallback';
        }
    }

    if ($variant_image_id) {
        $context = Context::getContext();

        // Získáme správné URL obrázků pomocí Image třídy
        $image = new Image($variant_image_id);

        if (Validate::isLoadedObject($image)) {
            // Použijeme správnou metodu pro generování URL
            $base_url = $context->shop->getBaseURL(true);

            // Generujeme cestu k obrázku podle PrestaShop struktury
            $image_path = $image->getExistingImgPath();

            // URL pro home_default
            $image_url = $base_url . 'img/p/' . $image_path . '-home_default.jpg';

            // URL pro large_default
            $large_image_url = $base_url . 'img/p/' . $image_path . '-large_default.jpg';

            // Fallback - pokud getExistingImgPath nefunguje, použijeme Link třídu
            if (!$image_path) {
                $link = new Link();
                $image_url = $link->getImageLink(
                    $product->link_rewrite,
                    $variant_image_id,
                    ImageType::getFormattedName('home_default')
                );
                $large_image_url = $link->getImageLink(
                    $product->link_rewrite,
                    $variant_image_id,
                    ImageType::getFormattedName('large_default')
                );

                // Opravíme URL pokud je potřeba
                if (strpos($image_url, 'http') !== 0) {
                    $image_url = $base_url . ltrim($image_url, '/');
                }
                if (strpos($large_image_url, 'http') !== 0) {
                    $large_image_url = $base_url . ltrim($large_image_url, '/');
                }
            }
        } else {
            throw new Exception('Image object not found for ID: ' . $variant_image_id);
        }

        $debug_info['variant_image_id'] = $variant_image_id;

        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'image_url' => $image_url,
            'large_image_url' => $large_image_url,
            'debug' => $debug_info
        ]);
        exit;
    } else {
        throw new Exception('No image found for this variant or product');
    }

} catch (Exception $e) {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false, 
        'error' => $e->getMessage(),
        'debug' => isset($debug_info) ? $debug_info : []
    ]);
    exit;
}
?>
