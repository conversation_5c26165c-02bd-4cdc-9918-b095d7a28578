<?php
/**
 * <PERSON><PERSON><PERSON><PERSON> test pro ověření opravy AJAX endpointu
 */

// Načtení PrestaShop frameworku
require_once dirname(__FILE__) . '/../../../config/config.inc.php';
require_once dirname(__FILE__) . '/../../../init.php';

header('Content-Type: text/html; charset=utf-8');

$id_product = (int) Tools::getValue('id_product', 1);
$id_product_attribute = (int) Tools::getValue('id_product_attribute', 1);

?>
<!DOCTYPE html>
<html>
<head>
    <title>Test opravy variant</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border: 1px solid #ddd; }
        .success { background-color: #d4edda; }
        .error { background-color: #f8d7da; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Test opravy AJAX endpointu pro varianty</h1>
    
    <form method="get">
        <label>ID produktu: <input type="number" name="id_product" value="<?php echo $id_product; ?>"></label>
        <label>ID varianty: <input type="number" name="id_product_attribute" value="<?php echo $id_product_attribute; ?>"></label>
        <button type="submit">Testovat</button>
    </form>
    
    <?php
    
    if ($id_product && $id_product_attribute) {
        echo "<h2>Test pro produkt $id_product, varianta $id_product_attribute</h2>";
        
        try {
            $context = Context::getContext();
            $product = new Product($id_product, false, $context->language->id);
            
            if (!Validate::isLoadedObject($product)) {
                throw new Exception('Produkt neexistuje');
            }
            
            echo "<div class='test-result success'>";
            echo "<h3>✅ Produkt nalezen: " . htmlspecialchars($product->name) . "</h3>";
            echo "</div>";
            
            // Test staré metody (původní problém)
            echo "<h3>1. Stará metoda (getImages):</h3>";
            $old_images = $product->getImages($context->language->id);
            $old_variant_image_id = 0;
            
            foreach ($old_images as $image) {
                if (isset($image['id_product_attribute']) && $image['id_product_attribute'] == $id_product_attribute) {
                    $old_variant_image_id = $image['id_image'];
                    break;
                }
            }
            
            echo "<div class='test-result " . ($old_variant_image_id ? 'success' : 'error') . "'>";
            echo "Stará metoda našla obrázek ID: " . ($old_variant_image_id ?: 'Žádný');
            echo "</div>";
            
            // Test nové metody (oprava)
            echo "<h3>2. Nová metoda (SQL dotaz):</h3>";
            $sql = "SELECT pai.id_image 
                    FROM " . _DB_PREFIX_ . "product_attribute_image pai
                    WHERE pai.id_product_attribute = " . (int)$id_product_attribute . "
                    ORDER BY pai.id_image ASC
                    LIMIT 1";
            
            $result = Db::getInstance()->getRow($sql);
            $new_variant_image_id = 0;
            if ($result && isset($result['id_image'])) {
                $new_variant_image_id = $result['id_image'];
            }
            
            echo "<div class='test-result " . ($new_variant_image_id ? 'success' : 'error') . "'>";
            echo "Nová metoda našla obrázek ID: " . ($new_variant_image_id ?: 'Žádný');
            echo "</div>";
            
            // Porovnání
            if ($old_variant_image_id != $new_variant_image_id) {
                echo "<div class='test-result error'>";
                echo "<strong>⚠️ ROZDÍL DETEKOVÁN!</strong><br>";
                echo "Stará metoda: $old_variant_image_id<br>";
                echo "Nová metoda: $new_variant_image_id<br>";
                echo "Nová metoda by měla být správnější.";
                echo "</div>";
            } else {
                echo "<div class='test-result success'>";
                echo "✅ Obě metody vrátily stejný výsledek: $new_variant_image_id";
                echo "</div>";
            }
            
            // Test všech obrázků přiřazených k variantě
            echo "<h3>3. Všechny obrázky přiřazené k variantě:</h3>";
            $sql_all = "SELECT pai.id_image, i.position, i.cover
                        FROM " . _DB_PREFIX_ . "product_attribute_image pai
                        LEFT JOIN " . _DB_PREFIX_ . "image i ON pai.id_image = i.id_image
                        WHERE pai.id_product_attribute = " . (int)$id_product_attribute . "
                        ORDER BY i.position ASC";
            
            $all_images = Db::getInstance()->executeS($sql_all);
            
            if ($all_images) {
                echo "<table>";
                echo "<tr><th>ID obrázku</th><th>Pozice</th><th>Cover</th><th>URL náhledu</th></tr>";
                
                $link = new Link();
                foreach ($all_images as $img) {
                    $image_url = $link->getImageLink(
                        $product->link_rewrite,
                        $img['id_image'],
                        ImageType::getFormattedName('home_default')
                    );
                    
                    echo "<tr>";
                    echo "<td>" . $img['id_image'] . "</td>";
                    echo "<td>" . $img['position'] . "</td>";
                    echo "<td>" . ($img['cover'] ? 'Ano' : 'Ne') . "</td>";
                    echo "<td><a href='https:" . $image_url . "' target='_blank'>Zobrazit</a></td>";
                    echo "</tr>";
                }
                echo "</table>";
            } else {
                echo "<div class='test-result error'>";
                echo "❌ Varianta nemá přiřazené žádné obrázky";
                echo "</div>";
            }
            
            // Test AJAX endpointu
            echo "<h3>4. Test AJAX endpointu:</h3>";
            
            // Simulujeme volání
            $_GET['id_product'] = $id_product;
            $_GET['id_product_attribute'] = $id_product_attribute;
            
            ob_start();
            include dirname(__FILE__) . '/get_variant_image.php';
            $ajax_output = ob_get_clean();
            
            echo "<h4>Výstup AJAX:</h4>";
            echo "<pre>" . htmlspecialchars($ajax_output) . "</pre>";
            
            $ajax_data = json_decode($ajax_output, true);
            if ($ajax_data && $ajax_data['success']) {
                echo "<div class='test-result success'>";
                echo "✅ AJAX endpoint funguje správně<br>";
                echo "Obrázek: <a href='" . $ajax_data['image_url'] . "' target='_blank'>" . $ajax_data['image_url'] . "</a>";
                if (isset($ajax_data['debug'])) {
                    echo "<br>Debug info: ID obrázku = " . $ajax_data['debug']['variant_image_id'];
                }
                echo "</div>";
            } else {
                echo "<div class='test-result error'>";
                echo "❌ AJAX endpoint vrátil chybu";
                echo "</div>";
            }
            
        } catch (Exception $e) {
            echo "<div class='test-result error'>";
            echo "❌ Chyba: " . $e->getMessage();
            echo "</div>";
        }
    }
    
    ?>
    
    <h2>Rychlé odkazy na testování:</h2>
    <ul>
        <li><a href="?id_product=16352&id_product_attribute=1">Test produktu 16352, varianta 1</a></li>
        <li><a href="diagnostika.php?test=variant_images&id_product=16352">Detailní analýza variant produktu 16352</a></li>
        <li><a href="test_ajax.html">Interaktivní AJAX tester</a></li>
    </ul>
</body>
</html>
