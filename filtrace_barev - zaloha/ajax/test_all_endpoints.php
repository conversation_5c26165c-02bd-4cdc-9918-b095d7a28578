<?php
/**
 * Test všech tří verzí <PERSON>AX endpointů
 */

// Načtení PrestaShop frameworku
require_once dirname(__FILE__) . '/../../../config/config.inc.php';
require_once dirname(__FILE__) . '/../../../init.php';

header('Content-Type: text/html; charset=utf-8');

$test_product_id = 2821;
$test_variant_id = 7605;

?>
<!DOCTYPE html>
<html>
<head>
    <title>Test všech AJAX endpointů</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border: 1px solid #ddd; }
        .success { background-color: #d4edda; }
        .error { background-color: #f8d7da; }
        .warning { background-color: #fff3cd; }
        .info { background-color: #d1ecf1; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; font-size: 12px; }
        .endpoint-test { margin: 20px 0; padding: 15px; border: 2px solid #ddd; }
        .image-preview { margin: 10px 0; }
        .image-preview img { max-width: 150px; border: 1px solid #ddd; margin: 5px; }
    </style>
</head>
<body>
    <h1>Test všech AJAX endpointů</h1>
    
    <div class="info test-result">
        <strong>Testovací data:</strong><br>
        Produkt: <?php echo $test_product_id; ?> (Dering stínítko)<br>
        Varianta: <?php echo $test_variant_id; ?><br>
        Očekávaný obrázek: 31794 (červená varianta)<br>
        Nesprávný obrázek: 31792 (cover)
    </div>
    
    <?php
    
    // Nejdříve ověříme, jaké obrázky má varianta
    echo "<h2>Ověření dat v databázi</h2>";
    
    try {
        $sql = "SELECT id_image FROM `" . _DB_PREFIX_ . "product_attribute_image` WHERE id_product_attribute = " . (int)$test_variant_id;
        $variant_images = Db::getInstance()->executeS($sql);
        
        if ($variant_images) {
            echo "<div class='test-result success'>";
            echo "✅ Varianta má " . count($variant_images) . " přiřazených obrázků: ";
            foreach ($variant_images as $img) {
                echo $img['id_image'] . " ";
            }
            echo "</div>";
            $expected_image_id = $variant_images[0]['id_image'];
        } else {
            echo "<div class='test-result error'>❌ Varianta nemá přiřazené žádné obrázky</div>";
            $expected_image_id = null;
        }
    } catch (Exception $e) {
        echo "<div class='test-result error'>❌ Chyba při ověření dat: " . $e->getMessage() . "</div>";
        $expected_image_id = null;
    }
    
    // Test všech tří endpointů
    $endpoints = [
        'Původní' => 'get_variant_image.php',
        'Rozšířený' => 'get_variant_image_simple.php',
        'Základní' => 'get_variant_image_basic.php'
    ];
    
    $results = [];
    
    foreach ($endpoints as $name => $file) {
        echo "<div class='endpoint-test'>";
        echo "<h2>Test: $name endpoint ($file)</h2>";
        
        // Nastavíme parametry
        $_GET['id_product'] = $test_product_id;
        $_GET['id_product_attribute'] = $test_variant_id;
        
        ob_start();
        if (file_exists(dirname(__FILE__) . '/' . $file)) {
            include dirname(__FILE__) . '/' . $file;
        } else {
            echo json_encode(['success' => false, 'error' => 'File not found']);
        }
        $output = ob_get_clean();
        
        echo "<h4>Výstup:</h4>";
        echo "<pre>" . htmlspecialchars($output) . "</pre>";
        
        $data = json_decode($output, true);
        if ($data && $data['success']) {
            $image_id = isset($data['debug']['variant_image_id']) ? $data['debug']['variant_image_id'] : 'neznámé';
            $method = isset($data['debug']['method_used']) ? $data['debug']['method_used'] : 'neznámá';
            
            $results[$name] = [
                'success' => true,
                'image_id' => $image_id,
                'method' => $method,
                'image_url' => $data['image_url']
            ];
            
            if ($expected_image_id && $image_id == $expected_image_id) {
                echo "<div class='test-result success'>✅ SPRÁVNÝ obrázek: $image_id (metoda: $method)</div>";
            } else {
                echo "<div class='test-result error'>❌ NESPRÁVNÝ obrázek: $image_id (očekáváno: $expected_image_id, metoda: $method)</div>";
            }
            
            // Náhled obrázku
            echo "<div class='image-preview'>";
            echo "<img src='" . $data['image_url'] . "' alt='$name endpoint' title='ID: $image_id'>";
            echo "<br><small>ID: $image_id</small>";
            echo "</div>";
            
        } else {
            $results[$name] = [
                'success' => false,
                'error' => isset($data['error']) ? $data['error'] : 'Neznámá chyba'
            ];
            
            echo "<div class='test-result error'>❌ Endpoint selhal: " . $results[$name]['error'] . "</div>";
        }
        
        echo "</div>";
    }
    
    // Shrnutí
    echo "<h2>Shrnutí výsledků</h2>";
    
    $working_endpoints = 0;
    $correct_endpoints = 0;
    
    foreach ($results as $name => $result) {
        if ($result['success']) {
            $working_endpoints++;
            if ($expected_image_id && $result['image_id'] == $expected_image_id) {
                $correct_endpoints++;
            }
        }
    }
    
    echo "<div class='test-result info'>";
    echo "<strong>Statistiky:</strong><br>";
    echo "Funkční endpointy: $working_endpoints / " . count($endpoints) . "<br>";
    echo "Správné výsledky: $correct_endpoints / " . count($endpoints) . "<br>";
    echo "</div>";
    
    if ($correct_endpoints > 0) {
        echo "<div class='test-result success'>";
        echo "<h3>🎉 ÚSPĚCH!</h3>";
        echo "$correct_endpoints endpoint(ů) vrací správný obrázek varianty.";
        echo "</div>";
        
        // Doporučení
        echo "<div class='test-result info'>";
        echo "<h4>Doporučení:</h4>";
        foreach ($results as $name => $result) {
            if ($result['success'] && $expected_image_id && $result['image_id'] == $expected_image_id) {
                echo "✅ Použijte <strong>$name endpoint</strong> (metoda: {$result['method']})<br>";
            }
        }
        echo "</div>";
        
    } else {
        echo "<div class='test-result error'>";
        echo "<h3>❌ Problém</h3>";
        echo "Žádný endpoint nevrací správný obrázek varianty.";
        echo "</div>";
    }
    
    ?>
    
    <h2>Další kroky</h2>
    <ul>
        <li><a href="test_ajax.html?id_product=<?php echo $test_product_id; ?>&id_product_attribute=<?php echo $test_variant_id; ?>">Interaktivní AJAX test</a></li>
        <li><a href="find_red_variants.php">Najít další červené varianty</a></li>
        <li><a href="diagnostika.php?test=all">Kompletní diagnostika</a></li>
    </ul>
    
    <h2>Přímé odkazy na endpointy</h2>
    <ul>
        <li><a href="get_variant_image.php?id_product=<?php echo $test_product_id; ?>&id_product_attribute=<?php echo $test_variant_id; ?>" target="_blank">Původní endpoint</a></li>
        <li><a href="get_variant_image_simple.php?id_product=<?php echo $test_product_id; ?>&id_product_attribute=<?php echo $test_variant_id; ?>" target="_blank">Rozšířený endpoint</a></li>
        <li><a href="get_variant_image_basic.php?id_product=<?php echo $test_product_id; ?>&id_product_attribute=<?php echo $test_variant_id; ?>" target="_blank">Základní endpoint</a></li>
    </ul>
</body>
</html>
