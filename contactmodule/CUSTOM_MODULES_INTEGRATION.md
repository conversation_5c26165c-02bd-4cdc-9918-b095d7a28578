# Integrace Contact Module do sekce "Vlastní moduly"

## 🎯 Přehled

Contact Module byl úsp<PERSON>šně integrován do univerzálního systému pro správu vlastních modulů v administraci PrestaShop. Modul nyní má vlastní admin rozhraní a je automaticky zařazen do sekce "Vlastní moduly".

## 📁 Přidané soubory

### Admin Controller
- `controllers/admin/AdminContactModuleController.php` - Hlavní admin controller
- `controllers/admin/index.php` - Bezpečnostní soubor

### Správa tabů
- `classes/CustomModulesTabManager.php` - Univerzální třída pro správu tabů
- `classes/index.php` - Bezpečnostní soubor

### Styly
- `views/css/admin.css` - CSS styly pro admin rozhraní

### Utility
- `setup-custom-modules.php` - Script pro nastavení a testování

## 🔧 Provedené úpravy

### contactmodule.php
```php
// Přidáno do install()
$this->createTab();

// Přidáno do uninstall()
$this->removeTab();

// Nové metody
private function createTab()
{
    require_once(_PS_MODULE_DIR_ . $this->name . '/classes/CustomModulesTabManager.php');
    return CustomModulesTabManager::createModuleTab('AdminContactModule', 'Kontaktní modul', $this->name);
}

private function removeTab()
{
    require_once(_PS_MODULE_DIR_ . $this->name . '/classes/CustomModulesTabManager.php');
    return CustomModulesTabManager::removeModuleTab('AdminContactModule');
}
```

## 🚀 Instalace a nastavení

### 1. Automatické nastavení
```bash
# Spusťte setup script
http://vase-domena.cz/modules/contactmodule/setup-custom-modules.php
```

### 2. Ruční kroky
1. Ujistěte se, že modul contactmodule je nainstalován
2. Spusťte setup script a postupujte podle instrukcí
3. Vyčistěte cache administrace
4. Zkontrolujte sekci "Vlastní moduly" v administraci

## 📋 Funkce admin rozhraní

### Správa kontaktních údajů
- ✅ Název společnosti
- ✅ Adresa
- ✅ Telefon a email
- ✅ Bankovní údaje
- ✅ Google Maps embed kód

### Zobrazení obchodních zástupců
- ✅ Seznam všech zástupců
- ✅ Kontaktní údaje
- ✅ Oblasti působnosti

## 🔄 Použití pro další moduly

Tento přístup lze použít pro jakýkoli modul. Postup:

### Pro nové moduly
1. Zkopírujte `CustomModulesTabManager.php` do `classes/`
2. Vytvořte admin controller v `controllers/admin/`
3. Přidejte metody `createTab()` a `removeTab()` do hlavního souboru modulu
4. Upravte `install()` a `uninstall()` metody

### Pro existující moduly
1. Zkopírujte `CustomModulesTabManager.php` do `classes/`
2. Spusťte `custom-modules-section/add-to-existing-module.php?module=nazev&controller=AdminNazev`
3. Postupujte podle instrukcí

## 🛠️ Template pro nový modul

```php
// V hlavním souboru modulu
public function install()
{
    return parent::install() &&
           // ... ostatní instalační kroky ...
           $this->createTab();
}

public function uninstall()
{
    return $this->removeTab() && 
           // ... ostatní odinstalační kroky ...
           parent::uninstall();
}

private function createTab()
{
    require_once(_PS_MODULE_DIR_ . $this->name . '/classes/CustomModulesTabManager.php');
    return CustomModulesTabManager::createModuleTab('AdminVasModul', 'Název modulu', $this->name);
}

private function removeTab()
{
    require_once(_PS_MODULE_DIR_ . $this->name . '/classes/CustomModulesTabManager.php');
    return CustomModulesTabManager::removeModuleTab('AdminVasModul');
}
```

## ✅ Výhody tohoto řešení

### Organizace
- ✅ Všechny custom moduly na jednom místě
- ✅ Čistší struktura administrace
- ✅ Oddělení od standardních PrestaShop modulů

### Automatizace
- ✅ Automatické vytváření sekce při instalaci prvního modulu
- ✅ Automatické mazání prázdné sekce při odinstalaci posledního modulu
- ✅ Žádná ruční správa potřebná

### Univerzálnost
- ✅ Jedna implementace pro všechny moduly
- ✅ Snadná integrace do existujících modulů
- ✅ Kompatibilita s PrestaShop 1.7+ a 8.x

## 🔍 Troubleshooting

### Sekce se nezobrazuje
1. Vyčistěte cache administrace
2. Zkontrolujte oprávnění uživatele
3. Spusťte setup script znovu

### Tab se nevytváří
1. Zkontrolujte, že CustomModulesTabManager.php existuje
2. Ověřte oprávnění k databázi
3. Zkontrolujte logy PrestaShop

### Admin controller nefunguje
1. Zkontrolujte, že controller třída existuje
2. Ověřte správný název třídy (AdminContactModuleController)
3. Vyčistěte cache

## 📞 Podpora

Pro řešení problémů:
- Spusťte `setup-custom-modules.php` pro diagnostiku
- Zkontrolujte logy PrestaShop v `var/logs/`
- Použijte `custom-modules-section/add-to-existing-module.php` pro obecné problémy
