<?php
/**
 * Script pro přidání Contact Module do sekce "Vlastní moduly"
 * 
 * POUŽITÍ:
 * 1. Spusťte tento script: modules/contactmodule/setup-custom-modules.php
 * 2. Script automaticky přidá contactmodule do vlastní sekce
 */

// Základní kontrola prostředí
if (!defined('_PS_VERSION_')) {
    require_once(dirname(__FILE__) . '/../../config/config.inc.php');
}

echo "<h1>Přidání Contact Module do vlastní sekce</h1>";

$module_name = 'contactmodule';
$controller_class = 'AdminContactModule';

echo "<h2>Zpracování modulu: <strong>$module_name</strong></h2>";
echo "<p>Controller: <strong>$controller_class</strong></p>";

// Zkontroluj, zda modul existuje
$module = Module::getInstanceByName($module_name);
if (!$module) {
    echo "<p style='color: red;'>❌ Modul '$module_name' neexistuje nebo není nainstal<PERSON>n</p>";
    echo "<p><strong>Akce:</strong> Nainstalujte modul contactmodule</p>";
    exit;
}

echo "<p style='color: green;'>✅ Modul nalezen</p>";

// Zkontroluj, zda CustomModulesTabManager existuje
$manager_path = _PS_MODULE_DIR_ . $module_name . '/classes/CustomModulesTabManager.php';
if (!file_exists($manager_path)) {
    echo "<p style='color: red;'>❌ CustomModulesTabManager.php nenalezen v $manager_path</p>";
    echo "<p><strong>Akce:</strong> Zkopírujte CustomModulesTabManager.php do složky classes/ modulu</p>";
    exit;
}

echo "<p style='color: green;'>✅ CustomModulesTabManager.php nalezen</p>";

// Načti CustomModulesTabManager
require_once($manager_path);

// Zkontroluj aktuální stav tabu
$current_tab_id = Tab::getIdFromClassName($controller_class);
if (!$current_tab_id) {
    echo "<p style='color: orange;'>⚠️ Tab '$controller_class' neexistuje - bude vytvořen</p>";
    
    if (isset($_GET['action']) && $_GET['action'] == 'create') {
        echo "<h2>Vytváření admin tabu...</h2>";
        
        // Vytvoř tab v vlastní sekci
        $result = CustomModulesTabManager::createModuleTab($controller_class, 'Kontaktní modul', $module_name);
        
        if ($result) {
            echo "<p style='color: green;'>✅ Tab úspěšně vytvořen v sekci 'Vlastní moduly'</p>";
            $current_tab_id = Tab::getIdFromClassName($controller_class);
            echo "<p><strong>Nový tab ID:</strong> $current_tab_id</p>";
        } else {
            echo "<p style='color: red;'>❌ Chyba při vytváření tabu</p>";
        }
    } else {
        echo "<p><a href='?action=create' style='background: #4caf50; color: white; padding: 10px; text-decoration: none; border-radius: 4px;'>➕ Vytvořit admin tab</a></p>";
    }
} else {
    echo "<p style='color: green;'>✅ Tab '$controller_class' nalezen (ID: $current_tab_id)</p>";
    
    // Získej informace o aktuálním tabu
    $sql = 'SELECT t.*, tl.name 
            FROM `' . _DB_PREFIX_ . 'tab` t
            LEFT JOIN `' . _DB_PREFIX_ . 'tab_lang` tl ON (t.id_tab = tl.id_tab AND tl.id_lang = ' . (int)Context::getContext()->language->id . ')
            WHERE t.id_tab = ' . (int)$current_tab_id;

    $tab_info = Db::getInstance()->getRow($sql);
    if ($tab_info) {
        echo "<p><strong>Aktuální umístění:</strong> Parent ID: {$tab_info['id_parent']}, Pozice: {$tab_info['position']}</p>";
    }

    // Zkontroluj, zda už je v vlastní sekci
    $custom_section_id = Tab::getIdFromClassName('AdminCustomModules');
    if ($custom_section_id && $tab_info['id_parent'] == $custom_section_id) {
        echo "<p style='color: green;'>✅ Tab je už v sekci 'Vlastní moduly'</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ Tab NENÍ v sekci 'Vlastní moduly'</p>";
        
        if (isset($_GET['action']) && $_GET['action'] == 'move') {
            echo "<h2>Přesun do vlastní sekce...</h2>";
            
            // Přesuň tab do vlastní sekce
            $result = CustomModulesTabManager::moveTabToCustomModules($controller_class);
            
            if ($result) {
                echo "<p style='color: green;'>✅ Tab úspěšně přesunut do sekce 'Vlastní moduly'</p>";
                
                // Zkontroluj nový stav
                $new_tab_info = Db::getInstance()->getRow($sql);
                if ($new_tab_info) {
                    echo "<p><strong>Nové umístění:</strong> Parent ID: {$new_tab_info['id_parent']}</p>";
                }
            } else {
                echo "<p style='color: red;'>❌ Chyba při přesunu tabu</p>";
            }
        } else {
            echo "<p><a href='?action=move' style='background: #4caf50; color: white; padding: 10px; text-decoration: none; border-radius: 4px;'>🔄 Přesunout do vlastní sekce</a></p>";
        }
    }
}

echo "<h2>Stav vlastní sekce</h2>";
if (CustomModulesTabManager::customModulesSectionExists()) {
    echo "<p style='color: green;'>✅ Sekce 'Vlastní moduly' existuje</p>";
    
    // Zobraz všechny moduly v sekci
    $custom_modules_id = Tab::getIdFromClassName('AdminCustomModules');
    $sql = 'SELECT t.class_name, tl.name 
            FROM `' . _DB_PREFIX_ . 'tab` t
            LEFT JOIN `' . _DB_PREFIX_ . 'tab_lang` tl ON (t.id_tab = tl.id_tab AND tl.id_lang = ' . (int)Context::getContext()->language->id . ')
            WHERE t.id_parent = ' . (int)$custom_modules_id . ' AND t.active = 1 
            ORDER BY t.position';
    
    $modules_in_section = Db::getInstance()->executeS($sql);
    if ($modules_in_section) {
        echo "<h3>Moduly v sekci:</h3>";
        echo "<ul>";
        foreach ($modules_in_section as $mod) {
            $highlight = ($mod['class_name'] == $controller_class) ? ' style="background: #e8f5e8; font-weight: bold;"' : '';
            echo "<li$highlight><strong>{$mod['name']}</strong> ({$mod['class_name']})</li>";
        }
        echo "</ul>";
    }
} else {
    echo "<p style='color: orange;'>⚠️ Sekce 'Vlastní moduly' neexistuje</p>";
    echo "<p><a href='?action=create_section' style='background: #2196f3; color: white; padding: 10px; text-decoration: none; border-radius: 4px;'>➕ Vytvořit sekci</a></p>";
    
    if (isset($_GET['action']) && $_GET['action'] == 'create_section') {
        echo "<h2>Vytváření sekce...</h2>";
        $result = CustomModulesTabManager::createOrGetCustomModulesTab();
        if ($result) {
            echo "<p style='color: green;'>✅ Sekce 'Vlastní moduly' vytvořena (ID: $result)</p>";
        } else {
            echo "<p style='color: red;'>❌ Chyba při vytváření sekce</p>";
        }
    }
}

echo "<h2>Kontrola admin controlleru</h2>";
$controller_path = _PS_MODULE_DIR_ . $module_name . '/controllers/admin/AdminContactModuleController.php';
if (file_exists($controller_path)) {
    echo "<p style='color: green;'>✅ Admin controller nalezen: $controller_path</p>";
} else {
    echo "<p style='color: red;'>❌ Admin controller nenalezen: $controller_path</p>";
}

echo "<h2>Návod pro dokončení</h2>";
echo "<ol>";
echo "<li>Ujistěte se, že modul contactmodule je nainstalován</li>";
echo "<li>Pokud tab neexistuje, klikněte na 'Vytvořit admin tab'</li>";
echo "<li>Pokud tab není v vlastní sekci, klikněte na 'Přesunout do vlastní sekce'</li>";
echo "<li>Vyčistěte cache administrace (Nástroje > Vymazat cache)</li>";
echo "<li>Přejděte do administrace a zkontrolujte sekci 'Vlastní moduly'</li>";
echo "</ol>";

echo "<hr>";
echo "<p><small>Script: " . __FILE__ . "</small></p>";
?>
