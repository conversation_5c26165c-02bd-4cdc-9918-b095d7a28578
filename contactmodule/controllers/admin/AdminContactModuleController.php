<?php
/**
 * Admin Controller pro Contact Module
 * Správa kontaktních údajů v administraci PrestaShop
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

class AdminContactModuleController extends ModuleAdminController
{
    public function __construct()
    {
        parent::__construct();
        $this->bootstrap = true;
        $this->context = Context::getContext();
        $this->meta_title = 'Správa kontaktních údajů';
    }

    public function initContent()
    {
        parent::initContent();

        $output = '';

        // Zpracování formuláře
        if (Tools::isSubmit('submitContactModule')) {
            $output .= $this->processForm();
        }

        // Zobrazení obsahu ve stejném stylu jako původní konfigurace
        $output .= $this->displayForm();

        $this->content = $output;
        $this->context->smarty->assign('content', $this->content);
    }

    private function displayForm()
    {
        $representatives = json_decode(Configuration::get('CONTACT_REPRESENTATIVES'), true);
        if (!$representatives) {
            $representatives = [];
        }

        $offices = json_decode(Configuration::get('CONTACT_OFFICES'), true);
        if (!$offices) {
            $offices = [];
        }

        $output = '<div class="panel">
            <div class="panel-heading">
                <i class="icon-cogs"></i> Nastavení kontaktní stránky
            </div>
            <div class="panel-body">
                <form id="configuration_form" class="defaultForm form-horizontal" method="post">
                    <input type="hidden" name="token" value="' . Tools::getAdminTokenLite('AdminContactModule') . '">

                    <div class="form-group">
                        <label class="control-label col-lg-3">Název společnosti</label>
                        <div class="col-lg-9">
                            <input type="text" name="company_name" value="' . htmlspecialchars(Configuration::get('CONTACT_COMPANY_NAME')) . '" class="form-control" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="control-label col-lg-3">Adresa</label>
                        <div class="col-lg-9">
                            <textarea name="address" rows="3" class="form-control">' . htmlspecialchars(Configuration::get('CONTACT_ADDRESS')) . '</textarea>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="control-label col-lg-3">Telefon</label>
                        <div class="col-lg-9">
                            <input type="text" name="phone" value="' . htmlspecialchars(Configuration::get('CONTACT_PHONE')) . '" class="form-control">
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="control-label col-lg-3">Email</label>
                        <div class="col-lg-9">
                            <input type="email" name="email" value="' . htmlspecialchars(Configuration::get('CONTACT_EMAIL')) . '" class="form-control">
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="control-label col-lg-3">Mapa (embed kód)</label>
                        <div class="col-lg-9">
                            <textarea name="map_embed" rows="5" class="form-control" placeholder="Vložte iframe kód z Google Maps nebo Mapy.cz">' . htmlspecialchars($this->getMapEmbedSafe()) . '</textarea>
                            <p class="help-block">Vložte celý iframe kód včetně &lt;iframe&gt; tagů. Podporuje Google Maps i Mapy.cz</p>
                            <div class="alert alert-info" style="margin-top: 10px;">
                                <strong>Debug:</strong> Aktuálně uloženo: ' . (empty($this->getMapEmbedSafe()) ? 'PRÁZDNÉ' : strlen($this->getMapEmbedSafe()) . ' znaků') . '
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="control-label col-lg-3">Bankovní spojení</label>
                        <div class="col-lg-9">
                            <textarea name="bank_info" rows="3" class="form-control">' . htmlspecialchars(Configuration::get('CONTACT_BANK_INFO')) . '</textarea>
                        </div>
                    </div>

                    <hr>
                    <h4><i class="icon-users"></i> Obchodní zástupci</h4>
                    <p class="help-block">Můžete přetahovat zástupce pro změnu pořadí</p>

                    <div id="representatives-container" class="sortable-container">';

        foreach ($representatives as $index => $rep) {
            $output .= $this->generateRepresentativeForm($index, $rep);
        }

        $output .= '</div>

                    <div class="form-group">
                        <div class="col-lg-offset-3 col-lg-9">
                            <button type="button" id="add-representative" class="btn btn-success">
                                <i class="icon-plus"></i> Přidat zástupce
                            </button>
                        </div>
                    </div>

                    <hr>
                    <h4><i class="icon-building"></i> Kanceláře</h4>
                    <p class="help-block">Můžete přetahovat kanceláře pro změnu pořadí</p>

                    <div id="offices-container" class="sortable-container">';

        foreach ($offices as $index => $office) {
            $output .= $this->generateOfficeForm($index, $office);
        }

        $output .= '</div>

                    <div class="form-group">
                        <div class="col-lg-offset-3 col-lg-9">
                            <button type="button" id="add-office" class="btn btn-success">
                                <i class="icon-plus"></i> Přidat kancelář
                            </button>
                        </div>
                    </div>

                    <div class="panel-footer">
                        <button type="submit" name="submitContactModule" class="btn btn-default pull-right" onclick="debugMapValue()">
                            <i class="process-icon-save"></i> Uložit
                        </button>
                    </div>
                </form>
            </div>
        </div>';

        $output .= $this->getRepresentativeJS();

        return $output;
    }



    public function processForm()
    {
        $output = '';

        // Získání mapy bez HTML filtrování - stejně jako v původní konfiguraci
        $map_embed_value = isset($_POST['map_embed']) ? $_POST['map_embed'] : '';

        Configuration::updateValue('CONTACT_COMPANY_NAME', Tools::getValue('company_name'));
        Configuration::updateValue('CONTACT_ADDRESS', Tools::getValue('address'));
        Configuration::updateValue('CONTACT_PHONE', Tools::getValue('phone'));
        Configuration::updateValue('CONTACT_EMAIL', Tools::getValue('email'));

        // Speciální zpracování pro mapu - base64 kódování pro bezpečnost
        if (!empty($map_embed_value) && strpos($map_embed_value, '<iframe') !== false) {
            // Uložíme jako base64 pro obejití HTML filtrů
            $encoded_map = base64_encode($map_embed_value);
            $result = Configuration::updateValue('CONTACT_MAP_EMBED_ENCODED', $encoded_map);
            $result2 = Configuration::updateValue('CONTACT_MAP_EMBED', $map_embed_value);
        } else {
            // Normální uložení pro jiný obsah
            $result = Configuration::updateValue('CONTACT_MAP_EMBED', $map_embed_value);
            $result2 = true;
        }

        Configuration::updateValue('CONTACT_BANK_INFO', Tools::getValue('bank_info'));

        // Uložení obchodních zástupců
        $representatives = [];
        $rep_names = Tools::getValue('rep_name');
        $rep_positions = Tools::getValue('rep_position');
        $rep_phones = Tools::getValue('rep_phone');
        $rep_emails = Tools::getValue('rep_email');
        $rep_regions = Tools::getValue('rep_region');

        if (is_array($rep_names)) {
            for ($i = 0; $i < count($rep_names); $i++) {
                if (!empty($rep_names[$i])) {
                    $representatives[] = [
                        'name' => $rep_names[$i],
                        'position' => isset($rep_positions[$i]) ? $rep_positions[$i] : '',
                        'phone' => isset($rep_phones[$i]) ? $rep_phones[$i] : '',
                        'email' => isset($rep_emails[$i]) ? $rep_emails[$i] : '',
                        'region' => isset($rep_regions[$i]) ? $rep_regions[$i] : ''
                    ];
                }
            }
        }

        Configuration::updateValue('CONTACT_REPRESENTATIVES', json_encode($representatives));

        // Uložení kanceláří
        $offices = [];
        $office_names = Tools::getValue('office_name');
        $office_addresses = Tools::getValue('office_address');
        $office_emails = Tools::getValue('office_email');
        $office_contact_persons = Tools::getValue('office_contact_person');
        $office_phones = Tools::getValue('office_phone');
        $office_bank_infos = Tools::getValue('office_bank_info');

        if (is_array($office_names)) {
            for ($i = 0; $i < count($office_names); $i++) {
                if (!empty($office_names[$i])) {
                    $offices[] = [
                        'name' => $office_names[$i],
                        'address' => isset($office_addresses[$i]) ? $office_addresses[$i] : '',
                        'email' => isset($office_emails[$i]) ? $office_emails[$i] : '',
                        'contact_person' => isset($office_contact_persons[$i]) ? $office_contact_persons[$i] : '',
                        'phone' => isset($office_phones[$i]) ? $office_phones[$i] : '',
                        'bank_info' => isset($office_bank_infos[$i]) ? $office_bank_infos[$i] : ''
                    ];
                }
            }
        }

        Configuration::updateValue('CONTACT_OFFICES', json_encode($offices));

        $output .= $this->displayConfirmation('Nastavení bylo úspěšně uloženo');
        return $output;
    }

    public function setMedia($isNewTheme = false)
    {
        parent::setMedia($isNewTheme);

        // Přidání vlastních stylů pokud potřeba
        $this->addCSS($this->module->getPathUri() . 'views/css/admin.css');
    }

    private function getMapEmbedSafe()
    {
        // Zkus nejdříve base64 verzi
        $encoded = Configuration::get('CONTACT_MAP_EMBED_ENCODED');
        if (!empty($encoded)) {
            $decoded = base64_decode($encoded);
            if ($decoded !== false && !empty($decoded)) {
                return $decoded;
            }
        }

        // Fallback na normální verzi
        return Configuration::get('CONTACT_MAP_EMBED');
    }

    private function generateRepresentativeForm($index, $rep = [])
    {
        $name = isset($rep['name']) ? htmlspecialchars($rep['name']) : '';
        $position = isset($rep['position']) ? htmlspecialchars($rep['position']) : '';
        $phone = isset($rep['phone']) ? htmlspecialchars($rep['phone']) : '';
        $email = isset($rep['email']) ? htmlspecialchars($rep['email']) : '';
        $region = isset($rep['region']) ? htmlspecialchars($rep['region']) : '';
        $indexNum = (int)$index + 1;

        return '<div class="representative-form panel panel-default sortable-item" style="margin-bottom: 15px; cursor: move;">
            <div class="panel-heading">
                <i class="icon-move drag-handle" style="margin-right: 10px; cursor: move;"></i>
                <span>Zástupce #' . $indexNum . '</span>
                <button type="button" class="btn btn-danger btn-xs pull-right remove-representative">
                    <i class="icon-trash"></i> Odstranit
                </button>
            </div>
            <div class="panel-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>Jméno</label>
                            <input type="text" name="rep_name[]" value="' . $name . '" class="form-control">
                        </div>
                        <div class="form-group">
                            <label>Pozice</label>
                            <input type="text" name="rep_position[]" value="' . $position . '" class="form-control">
                        </div>
                        <div class="form-group">
                            <label>Oblast</label>
                            <input type="text" name="rep_region[]" value="' . $region . '" class="form-control">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>Telefon</label>
                            <input type="text" name="rep_phone[]" value="' . $phone . '" class="form-control">
                        </div>
                        <div class="form-group">
                            <label>Email</label>
                            <input type="email" name="rep_email[]" value="' . $email . '" class="form-control">
                        </div>
                    </div>
                </div>
            </div>
        </div>';
    }

    private function generateOfficeForm($index, $office = [])
    {
        $name = isset($office['name']) ? htmlspecialchars($office['name']) : '';
        $address = isset($office['address']) ? htmlspecialchars($office['address']) : '';
        $email = isset($office['email']) ? htmlspecialchars($office['email']) : '';
        $contact_person = isset($office['contact_person']) ? htmlspecialchars($office['contact_person']) : '';
        $phone = isset($office['phone']) ? htmlspecialchars($office['phone']) : '';
        $bank_info = isset($office['bank_info']) ? htmlspecialchars($office['bank_info']) : '';
        $indexNum = (int)$index + 1;

        return '<div class="office-form panel panel-default sortable-item" style="margin-bottom: 15px; cursor: move;">
            <div class="panel-heading">
                <i class="icon-move drag-handle" style="margin-right: 10px; cursor: move;"></i>
                <span>Kancelář #' . $indexNum . '</span>
                <button type="button" class="btn btn-danger btn-xs pull-right remove-office">
                    <i class="icon-trash"></i> Odstranit
                </button>
            </div>
            <div class="panel-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>Název kanceláře</label>
                            <input type="text" name="office_name[]" value="' . $name . '" class="form-control">
                        </div>
                        <div class="form-group">
                            <label>Adresa</label>
                            <textarea name="office_address[]" rows="3" class="form-control">' . $address . '</textarea>
                        </div>
                        <div class="form-group">
                            <label>Email</label>
                            <input type="email" name="office_email[]" value="' . $email . '" class="form-control">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>Kontaktní osoba</label>
                            <input type="text" name="office_contact_person[]" value="' . $contact_person . '" class="form-control">
                        </div>
                        <div class="form-group">
                            <label>Telefon</label>
                            <input type="text" name="office_phone[]" value="' . $phone . '" class="form-control">
                        </div>
                        <div class="form-group">
                            <label>Bankovní informace</label>
                            <textarea name="office_bank_info[]" rows="3" class="form-control">' . $bank_info . '</textarea>
                        </div>
                    </div>
                </div>
            </div>
        </div>';
    }

    private function getRepresentativeJS()
    {
        return '<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
        <script>
        $(document).ready(function() {
            var repIndex = $("#representatives-container .representative-form").length;
            var officeIndex = $("#offices-container .office-form").length;

            // Inicializace Sortable pro drag and drop
            var sortable = Sortable.create(document.getElementById("representatives-container"), {
                handle: ".drag-handle",
                animation: 150,
                ghostClass: "sortable-ghost",
                onEnd: function(evt) {
                    updateRepresentativeNumbers();
                }
            });

            var sortableOffices = Sortable.create(document.getElementById("offices-container"), {
                handle: ".drag-handle",
                animation: 150,
                ghostClass: "sortable-ghost",
                onEnd: function(evt) {
                    updateOfficeNumbers();
                }
            });

            function updateRepresentativeNumbers() {
                $("#representatives-container .representative-form").each(function(index) {
                    $(this).find(".panel-heading span").text("Zástupce #" + (index + 1));
                });
            }

            function updateOfficeNumbers() {
                $("#offices-container .office-form").each(function(index) {
                    $(this).find(".panel-heading span").text("Kancelář #" + (index + 1));
                });
            }

            // Přidání nového zástupce
            $("#add-representative").click(function() {
                var newForm = `<div class="representative-form panel panel-default sortable-item" style="margin-bottom: 15px; cursor: move;">
                    <div class="panel-heading">
                        <i class="icon-move drag-handle" style="margin-right: 10px; cursor: move;"></i>
                        <span>Zástupce #${repIndex + 1}</span>
                        <button type="button" class="btn btn-danger btn-xs pull-right remove-representative">
                            <i class="icon-trash"></i> Odstranit
                        </button>
                    </div>
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Jméno</label>
                                    <input type="text" name="rep_name[]" value="" class="form-control">
                                </div>
                                <div class="form-group">
                                    <label>Pozice</label>
                                    <input type="text" name="rep_position[]" value="" class="form-control">
                                </div>
                                <div class="form-group">
                                    <label>Oblast</label>
                                    <input type="text" name="rep_region[]" value="" class="form-control">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Telefon</label>
                                    <input type="text" name="rep_phone[]" value="" class="form-control">
                                </div>
                                <div class="form-group">
                                    <label>Email</label>
                                    <input type="email" name="rep_email[]" value="" class="form-control">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>`;

                $("#representatives-container").append(newForm);
                repIndex++;
                updateRepresentativeNumbers();
            });

            // Přidání nové kanceláře
            $("#add-office").click(function() {
                var newForm = `<div class="office-form panel panel-default sortable-item" style="margin-bottom: 15px; cursor: move;">
                    <div class="panel-heading">
                        <i class="icon-move drag-handle" style="margin-right: 10px; cursor: move;"></i>
                        <span>Kancelář #${officeIndex + 1}</span>
                        <button type="button" class="btn btn-danger btn-xs pull-right remove-office">
                            <i class="icon-trash"></i> Odstranit
                        </button>
                    </div>
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Název kanceláře</label>
                                    <input type="text" name="office_name[]" value="" class="form-control">
                                </div>
                                <div class="form-group">
                                    <label>Adresa</label>
                                    <textarea name="office_address[]" rows="3" class="form-control"></textarea>
                                </div>
                                <div class="form-group">
                                    <label>Email</label>
                                    <input type="email" name="office_email[]" value="" class="form-control">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Kontaktní osoba</label>
                                    <input type="text" name="office_contact_person[]" value="" class="form-control">
                                </div>
                                <div class="form-group">
                                    <label>Telefon</label>
                                    <input type="text" name="office_phone[]" value="" class="form-control">
                                </div>
                                <div class="form-group">
                                    <label>Bankovní informace</label>
                                    <textarea name="office_bank_info[]" rows="3" class="form-control"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>`;

                $("#offices-container").append(newForm);
                officeIndex++;
                updateOfficeNumbers();
            });

            // Odstranění zástupce
            $(document).on("click", ".remove-representative", function() {
                $(this).closest(".representative-form").remove();
                updateRepresentativeNumbers();
            });

            // Odstranění kanceláře
            $(document).on("click", ".remove-office", function() {
                $(this).closest(".office-form").remove();
                updateOfficeNumbers();
            });
        });

        function debugMapValue() {
            var mapValue = document.querySelector("textarea[name=\'map_embed\']").value;
            console.log("Map value length:", mapValue.length);
            console.log("Map value:", mapValue);
        }
        </script>

        <style>
        .sortable-ghost {
            opacity: 0.4;
        }
        .sortable-container {
            min-height: 50px;
        }
        .drag-handle {
            cursor: move;
        }
        </style>';
    }
}
