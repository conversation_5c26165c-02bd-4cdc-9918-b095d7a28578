<?php

use P<PERSON><PERSON>ailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;

require_once 'PHPMailer/src/Exception.php';
require_once 'PHPMailer/src/PHPMailer.php';
require_once 'PHPMailer/src/SMTP.php';

class EmailNotifier {
    private $mailer;

    public function __construct() {
        $this->mailer = new PHPMailer(true);
        $this->configure();
    }

    private function configure() {
        // Nastavení SMTP - bude potřeba doplnit do config.php
        // define('SMTP_HOST', 'smtp.example.com');
        // define('SMTP_USER', '<EMAIL>');
        // define('SMTP_PASS', 'password');
        // define('SMTP_PORT', 587);
        // define('SMTP_FROM_EMAIL', '<EMAIL>');
        // define('SMTP_FROM_NAME', 'Výroba App');

        // $this->mailer->isSMTP();
        // $this->mailer->Host = SMTP_HOST;
        // $this->mailer->SMTPAuth = true;
        // $this->mailer->Username = SMTP_USER;
        // $this->mailer->Password = SMTP_PASS;
        // $this->mailer->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
        // $this->mailer->Port = SMTP_PORT;
        // $this->mailer->setFrom(SMTP_FROM_EMAIL, SMTP_FROM_NAME);
        
        if (defined('SMTP_HOST')) {
            $this->mailer->isSMTP();
            $this->mailer->Host = SMTP_HOST;
            $this->mailer->SMTPAuth = true;
            $this->mailer->Username = SMTP_USER;
            $this->mailer->Password = SMTP_PASS;
            $this->mailer->SMTPSecure = SMTP_SECURE;
            $this->mailer->Port = SMTP_PORT;
            $this->mailer->setFrom(SMTP_FROM_EMAIL, SMTP_FROM_NAME);
        } else {
            // Prozatím použijeme mail() pro jednoduchost, aby nebylo nutné konfigurovat SMTP
            $this->mailer->isMail();
            $this->mailer->setFrom('<EMAIL>', 'Výroba App');
        }
        $this->mailer->CharSet = 'UTF-8';
    }

    public function sendOrderCompletedEmail($sales_person, $order, $user_who_completed) {
        try {
            $this->mailer->addAddress($sales_person['email'], $sales_person['username']);
            
            $this->mailer->isHTML(true);
            $this->mailer->Subject = 'Objednávka ' . $order['id'] . ' byla dokončena';
            
            $body = "<h1>Objednávka dokončena</h1>";
            $body .= "<p>Dobrý den,</p>";
            $body .= "<p>objednávka <strong>" . htmlspecialchars($order['id']) . "</strong> byla právě dokončena ve výrobě.</p>";
            $body .= "<ul>";
            $body .= "<li><strong>Datum objednání:</strong> " . date('d.m.Y', strtotime($order['order_date'])) . "</li>";
            $body .= "<li><strong>Datum dokončení:</strong> " . date('d.m.Y H:i') . "</li>";
            $body .= "<li><strong>Označil jako hotové:</strong> " . htmlspecialchars($user_who_completed['username']) . "</li>";
            $body .= "</ul>";

            if (!empty($order['items'])) {
                $body .= "<h2>Položky objednávky:</h2>";
                $body .= "<table border=\"1\" cellpadding=\"5\" cellspacing=\"0\" style=\"border-collapse: collapse;\">";
                $body .= "<thead><tr><th>Kód produktu</th><th>Množství</th></tr></thead>";
                $body .= "<tbody>";
                foreach ($order['items'] as $item) {
                    $body .= "<tr>";
                    $body .= "<td>" . htmlspecialchars($item['product_code']) . "</td>";
                    $body .= "<td>" . htmlspecialchars($item['quantity']) . "</td>";
                    $body .= "</tr>";
                }
                $body .= "</tbody></table>";
            }

            $body .= "<p>Detail objednávky naleznete zde: <a href=\"" . APP_URL . "/index.php?page=order_detail&id=" . urlencode($order['id']) . "\">" . APP_URL . "/index.php?page=order_detail&id=" . urlencode($order['id']) . "</a></p>";
            $body .= "<p>S pozdravem,<br>Váš systém pro sledování výroby</p>";

            $this->mailer->Body = $body;
            $this->mailer->AltBody = strip_tags($body);

            return $this->mailer->send();

        } catch (Exception $e) {
            error_log("Email se nepodařilo odeslat. Chyba: {$this->mailer->ErrorInfo}");
            return false;
        }
    }
}
