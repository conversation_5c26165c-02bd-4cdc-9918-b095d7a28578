<?php

class Technology {
    private $db;

    public function __construct(Database $db) {
        $this->db = $db;
    }

    /**
     * Získá všechny technologie.
     * @return array
     */
    public function getAll() {
        $this->db->query('SELECT * FROM printing_technologies ORDER BY name ASC');
        return $this->db->resultSet();
    }

    /**
     * Získá technologii podle ID.
     * @param int $id
     * @return mixed
     */
    public function getById($id) {
        $this->db->query('SELECT * FROM printing_technologies WHERE id = :id');
        $this->db->bind(':id', $id);
        return $this->db->single();
    }

    /**
     * Vytvoří novou technologii.
     * @param array $data
     * @return bool
     */
    public function create($data) {
        $this->db->query('INSERT INTO printing_technologies (name, product_code_map) VALUES (:name, :product_code_map)');
        $this->db->bind(':name', $data['name']);
        $this->db->bind(':product_code_map', $data['product_code_map']);
        return $this->db->execute();
    }

    /**
     * Aktualizuje existující technologii.
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function update($id, $data) {
        $this->db->query('UPDATE printing_technologies SET name = :name, product_code_map = :product_code_map WHERE id = :id');
        $this->db->bind(':id', $id);
        $this->db->bind(':name', $data['name']);
        $this->db->bind(':product_code_map', $data['product_code_map']);
        return $this->db->execute();
    }

    /**
     * Smaže technologii.
     * @param int $id
     * @return bool
     */
    public function delete($id) {
        $this->db->query('DELETE FROM printing_technologies WHERE id = :id');
        $this->db->bind(':id', $id);
        return $this->db->execute();
    }
}
