<?php

class Database {
    private $host = DB_HOST;
    private $db_name = DB_NAME;
    private $username = DB_USER;
    private $password = DB_PASS;
    private $charset = DB_CHARSET;

    private $pdo;
    private $stmt;
    private $error;

    public function __construct() {
        $dsn = "mysql:host={$this->host};dbname={$this->db_name};charset={$this->charset}";
        $options = [
            PDO::ATTR_ERRMODE            => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES   => false,
        ];

        try {
            $this->pdo = new PDO($dsn, $this->username, $this->password, $options);
        } catch (PDOException $e) {
            $this->error = $e->getMessage();
            // V reálné aplikaci by z<PERSON> <PERSON><PERSON><PERSON> b<PERSON>t le<PERSON><PERSON> logování chyb
            die("Chyba připojení k databázi: " . $this->error);
        }
    }

    // Připraví statement s dotazem
    public function query($sql) {
        $this->stmt = $this->pdo->prepare($sql);
    }

    // Naváže hodnoty na parametry
    public function bind($param, $value, $type = null) {
        if (is_null($type)) {
            switch (true) {
                case is_int($value):
                    $type = PDO::PARAM_INT;
                    break;
                case is_bool($value):
                    $type = PDO::PARAM_BOOL;
                    break;
                case is_null($value):
                    $type = PDO::PARAM_NULL;
                    break;
                default:
                    $type = PDO::PARAM_STR;
            }
        }
        $this->stmt->bindValue($param, $value, $type);
    }

    // Provede připravený statement
    public function execute() {
        return $this->stmt->execute();
    }

    // Získá všechny výsledky jako pole asociativních polí
    public function resultSet() {
        $this->execute();
        return $this->stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Získá jeden výsledek jako asociativní pole
    public function single() {
        $this->execute();
        return $this->stmt->fetch(PDO::FETCH_ASSOC);
    }

    // Získá počet ovlivněných řádků
    public function rowCount() {
        return $this->stmt->rowCount();
    }

    // Získá ID posledního vloženého záznamu
    public function lastInsertId() {
        return $this->pdo->lastInsertId();
    }
}
