<?php

class Order {
    private $db;

    public function __construct(Database $db) {
        $this->db = $db;
    }

    /**
     * <PERSON><PERSON>ká objednávky čekající na schválení náhledu.
     * @param string|null $sales_person_prefix Prefix obchodníka pro filtrování, null pro všechny.
     * @return array
     */
    public function getPendingOrders($sales_person_prefix = null) {
        $sql = "SELECT o.id, o.order_date, o.preview_status, u.username as sales_person_name
                FROM orders o
                LEFT JOIN users u ON o.sales_person_prefix = u.sales_person_prefix
                WHERE o.preview_status != 'schvaleno' AND o.is_done = 0";
        
        if ($sales_person_prefix) {
            $sql .= " AND o.sales_person_prefix = :sales_person_prefix";
        }
        
        $sql .= " ORDER BY o.order_date DESC";

        $this->db->query($sql);

        if ($sales_person_prefix) {
            $this->db->bind(':sales_person_prefix', $sales_person_prefix);
        }

        return $this->db->resultSet();
    }

    /**
     * <PERSON>ísk<PERSON> detail jedné objednávky.
     * @param string $order_id
     * @return mixed
     */
    public function getOrderById($order_id) {
        $logger = new Logger();
        $logger->info("Attempting to get order by ID: " . $order_id);
        $this->db->query("SELECT * FROM orders WHERE id = :id");
        $this->db->bind(':id', $order_id);
        $order = $this->db->single();

        if ($order) {
            $logger->info("Order found. Getting items for order: " . $order_id);
            $this->db->query("SELECT * FROM order_items WHERE order_id = :order_id");
            $this->db->bind(':order_id', $order_id);
            $order['items'] = $this->db->resultSet();
            $logger->info("Items for order " . $order_id . " fetched. Number of items: " . count($order['items']));
        } else {
            $logger->warning("Order with ID " . $order_id . " not found.");
        }

        return $order;
    }
    
    /**
     * Označí položku objednávky jako hotovou.
     * @param int $item_id
     * @return bool
     */
    public function updateItemDoneStatus($item_id, $is_done) {
        $logger = new Logger();
        $logger->info("Attempting to update item " . $item_id . " done status to " . ($is_done ? '1' : '0') . ".");
        $this->db->query('UPDATE order_items SET is_done = :is_done WHERE id = :item_id');
        $this->db->bind(':is_done', $is_done, PDO::PARAM_INT);
        $this->db->bind(':item_id', $item_id);
        $result = $this->db->execute();
        $logger->info("Item " . $item_id . " done status update result: " . ($result ? 'Success' : 'Failure'));
        return $result;
    }

    /**
     * Označí celou objednávku jako hotovou.
     * @param string $order_id
     * @param int $user_id ID uživatele, který objednávku dokončil.
     * @return bool
     */
    public function markOrderAsDone($order_id, $user_id) {
        // Mark all items in the order as done
        $this->db->query('UPDATE order_items SET is_done = 1 WHERE order_id = :order_id');
        $this->db->bind(':order_id', $order_id);
        $items_updated = $this->db->execute();

        if (!$items_updated) {
            // Log error or handle failure if items couldn't be marked
            error_log("Failed to mark all items as done for order: " . $order_id);
            return false;
        }

        // Mark the order itself as done
        $this->db->query('UPDATE orders SET is_done = 1, done_at = NOW(), done_by_user_id = :user_id WHERE id = :order_id');
        $this->db->bind(':order_id', $order_id);
        $this->db->bind(':user_id', $user_id);
        return $this->db->execute();
    }

    /**
     * Zjistí počet nedokončených položek pro danou objednávku.
     * @param string $order_id
     * @return int
     */
    public function countUndoneItems($order_id) {
        $this->db->query('SELECT COUNT(*) AS undone_count FROM order_items WHERE order_id = :order_id AND is_done = 0');
        $this->db->bind(':order_id', $order_id);
        $result = $this->db->single();
        return (int) $result['undone_count'];
    }

    /**
     * Získá informace o obchodníkovi na základě sales_person_prefix z objednávky.
     * @param string $order_id
     * @return mixed Pole s daty uživatele nebo false, pokud není nalezen.
     */
    public function getSalesPersonByOrderId($order_id) {
        $logger = new Logger();
        $logger->info("Attempting to get sales person for order: " . $order_id);
        $sql = 'SELECT u.* FROM users u JOIN orders o ON u.sales_person_prefix = o.sales_person_prefix WHERE o.id = :order_id';
        $this->db->query($sql);
        $this->db->bind(':order_id', $order_id);
        $result = $this->db->single();
        $logger->info("SQL for getSalesPersonByOrderId: " . $sql . " with order_id=".$order_id.". Result: " . var_export($result, true));
        return $result;
    }

    /**
     * Získá stav skladu pro danou položku objednávky.
     * @param array $item Data položky objednávky (obsahující item_ordered_date a item_stocked_date).
     * @return string
     */
    public function getItemStockStatus($item) {
        if (!empty($item['item_stocked_date'])) {
            return 'Skladem';
        } elseif (!empty($item['item_ordered_date'])) {
            return 'Objednáno';
        } else {
            return 'Není skladem';
        }
    }

    /**
     * Získá celkový stav skladu pro danou objednávku.
     * @param string $order_id
     * @return string
     */
    public function getOrderOverallStockStatus($order_id) {
        $this->db->query("SELECT item_ordered_date, item_stocked_date FROM order_items WHERE order_id = :order_id");
        $this->db->bind(':order_id', $order_id);
        $items = $this->db->resultSet();

        if (empty($items)) {
            return 'N/A'; // No items in order
        }

        $overall_status = 'Skladem'; // Assume all are in stock initially
        $any_ordered = false;

        foreach ($items as $item) {
            $status = $this->getItemStockStatus($item);
            if ($status === 'Není skladem') {
                return 'Není skladem'; // If any item is not in stock, the whole order is 'Není skladem'
            } elseif ($status === 'Objednáno') {
                $any_ordered = true;
            }
        }

        if ($any_ordered) {
            return 'Objednáno'; // If all are not 'Není skladem', but some are 'Objednáno', then 'Objednáno'
        } else {
            return 'Skladem'; // All items are 'Skladem'
        }
    }

    /**
     * Aktualizuje stav náhledu objednávky.
     * @param string $order_id
     * @param string $new_status
     * @return bool
     */
    public function updatePreviewStatus($order_id, $new_status) {
        $sql = '';
        if ($new_status === 'schvaleno') {
            // Při schválení nastavíme datum schválení a výchozí datum expedice (+14 dní)
            // Odstraněna podmínka AND preview_approved_at IS NULL, aby bylo možné stav schvaleno aktualizovat opakovaně
            $sql = 'UPDATE orders SET preview_status = :status, preview_approved_at = NOW(), required_shipping_date = DATE_ADD(NOW(), INTERVAL 14 DAY) WHERE id = :id';
        } else {
            $sql = 'UPDATE orders SET preview_status = :status WHERE id = :id';
        }

        $this->db->query($sql);
        $this->db->bind(':status', $new_status);
        $this->db->bind(':id', $order_id);
        $result = $this->db->execute();

        // Log the query and result
        $logger = new Logger(); // Instantiate logger here as it's not passed via constructor
        $logger->info("SQL for updatePreviewStatus: " . $sql . " with status=".$new_status." and id=".$order_id.". Result: " . ($result ? 'Success' : 'Failure'));

        return $result;
    }

    /**
     * Aktualizuje tiskovou technologii pro položku objednávky.
     * @param int $item_id ID položky objednávky.
     * @param int|null $technology_id ID tiskové technologie, nebo null pro zrušení.
     * @return bool
     */
    public function updateTechnology($item_id, $technology_id) {
        $this->db->query('UPDATE order_items SET printing_technology_id = :technology_id WHERE id = :item_id');
        $this->db->bind(':technology_id', $technology_id, $technology_id === null ? PDO::PARAM_NULL : PDO::PARAM_INT);
        $this->db->bind(':item_id', $item_id);
        return $this->db->execute();
    }

    /**
     * Aktualizuje stav skladu pro položku.
     * @param int $item_id ID položky.
     * @param string $status Nový stav ('ordered' nebo 'stocked').
     * @return bool
     */
    public function updateItemStockStatus($item_id, $status) {
        $sql = '';
        if ($status === 'ordered') {
            $sql = 'UPDATE order_items SET item_ordered_date = CURDATE(), item_stocked_date = NULL WHERE id = :item_id';
        } elseif ($status === 'stocked') {
            $sql = 'UPDATE order_items SET item_stocked_date = CURDATE() WHERE id = :item_id';
        } else {
            return false; // Neznámý status
        }

        $this->db->query($sql);
        $this->db->bind(':item_id', $item_id);
        return $this->db->execute();
    }

    /**
     * Získá události pro kalendář.
     * @return array
     */
    public function getCalendarEvents() {
        $sql = "SELECT 
                    oi.id, 
                    oi.order_id,
                    oi.quantity,
                    o.preview_approved_at as start, 
                    o.required_shipping_date as end,
                    t.name as technology_name
                FROM order_items oi
                JOIN orders o ON oi.order_id = o.id
                LEFT JOIN printing_technologies t ON oi.printing_technology_id = t.id
                WHERE o.preview_status = 'schvaleno'";
        
        $this->db->query($sql);
        $results = $this->db->resultSet();
        
        $events = [];
        foreach ($results as $row) {
            $title = $row['order_id'];
            if ($row['technology_name']) {
                $title .= ' - ' . $row['technology_name'];
            }
            $title .= ' - ' . rtrim(rtrim(number_format($row['quantity'], 2, ',', ''), '0'), ',');

            $events[] = [
                'id' => $row['id'],
                'title' => $title,
                'start' => $row['start'],
                'end' => $row['end'] ? $row['end'] . 'T23:59:59' : null,
                'extendedProps' => [
                    'order_id' => $row['order_id']
                ]
            ];
        }

        return $events;
    }

    /**
     * Získá hotové objednávky.
     * @param string|null $sales_person_prefix Prefix obchodníka pro filtrování, null pro všechny.
     * @return array
     */
    public function getCompletedOrders($sales_person_prefix = null) {
        $sql = "SELECT o.id, o.order_date, o.done_at, u_sales.username as sales_person_name, u_done.username as done_by_username
                FROM orders o
                LEFT JOIN users u_sales ON o.sales_person_prefix = u_sales.sales_person_prefix
                LEFT JOIN users u_done ON o.done_by_user_id = u_done.id
                WHERE o.is_done = 1";

        if ($sales_person_prefix) {
            $sql .= " AND o.sales_person_prefix = :sales_person_prefix";
        }

        $sql .= " ORDER BY o.done_at DESC";

        $this->db->query($sql);

        if ($sales_person_prefix) {
            $this->db->bind(':sales_person_prefix', $sales_person_prefix);
        }

        return $this->db->resultSet();
    }
}
