<?php

class User {
    private $db;

    public function __construct(Database $db) {
        $this->db = $db;
    }

    /**
     * Najde uživatele podle emailu.
     * @param string $email
     * @return mixed
     */
    public function findUserByEmail($email) {
        $this->db->query('SELECT * FROM users WHERE email = :email');
        $this->db->bind(':email', $email);
        return $this->db->single();
    }

    /**
     * Najde uživatele podle ID.
     * @param int $id
     * @return mixed
     */
    public function findUserById($id) {
        $this->db->query('SELECT id, username, email, role, sales_person_prefix FROM users WHERE id = :id');
        $this->db->bind(':id', $id);
        return $this->db->single();
    }

    /**
     * Zaregistruje nového uživatele (vytvoří účet).
     * @param array $data
     * @return bool
     */
    public function register($data) {
        $this->db->query('INSERT INTO users (username, email, password, role, sales_person_prefix) VALUES (:username, :email, :password, :role, :sales_person_prefix)');
        
        // <PERSON><PERSON><PERSON><PERSON> hesla
        $hashed_password = password_hash($data['password'], PASSWORD_DEFAULT);

        // Bind dat
        $this->db->bind(':username', $data['username']);
        $this->db->bind(':email', $data['email']);
        $this->db->bind(':password', $hashed_password);
        $this->db->bind(':role', $data['role']);
        $this->db->bind(':sales_person_prefix', $data['sales_person_prefix'] ?? null);

        // Provedení
        if ($this->db->execute()) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * Přihlásí uživatele.
     * @param string $email
     * @param string $password
     * @return mixed Vrací data o uživateli při úspěchu, jinak false.
     */
    public function login($email, $password) {
        $row = $this->findUserByEmail($email);

        if ($row) {
            $hashed_password = $row['password'];
            if (password_verify($password, $hashed_password)) {
                return $row; // Heslo je správné
            }
        }
        
        return false; // Uživatel nenalezen nebo špatné heslo
    }

    /**
     * Získá všechny uživatele z databáze.
     * @return array
     */
    public function getAllUsers() {
        $this->db->query('SELECT id, username, email, role, sales_person_prefix FROM users');
        return $this->db->resultSet();
    }

    /**
     * Aktualizuje existujícího uživatele.
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function updateUser($id, $data) {
        $sql = 'UPDATE users SET username = :username, email = :email, role = :role, sales_person_prefix = :sales_person_prefix';
        if (!empty($data['password'])) {
            $sql .= ', password = :password';
        }
        $sql .= ' WHERE id = :id';

        $this->db->query($sql);
        $this->db->bind(':id', $id);
        $this->db->bind(':username', $data['username']);
        $this->db->bind(':email', $data['email']);
        $this->db->bind(':role', $data['role']);
        $this->db->bind(':sales_person_prefix', $data['sales_person_prefix'] ?? null);
        if (!empty($data['password'])) {
            $hashed_password = password_hash($data['password'], PASSWORD_DEFAULT);
            $this->db->bind(':password', $hashed_password);
        }

        return $this->db->execute();
    }

    /**
     * Smaže uživatele podle ID.
     * @param int $id
     * @return bool
     */
    public function deleteUser($id) {
        $this->db->query('DELETE FROM users WHERE id = :id');
        $this->db->bind(':id', $id);
        return $this->db->execute();
    }
}
