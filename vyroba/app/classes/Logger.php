<?php

class Logger {
    private $log_file;

    public function __construct($log_file = 'app.log') {
        $this->log_file = BASE_PATH . '/logs/' . $log_file;
        // Ensure log directory exists
        $log_dir = dirname($this->log_file);
        if (!is_dir($log_dir)) {
            mkdir($log_dir, 0775, true);
        }
    }

    public function log($level, $message) {
        $timestamp = date('Y-m-d H:i:s');
        $log_entry = "[$timestamp] [" . strtoupper($level) . "] $message\n";
        file_put_contents($this->log_file, $log_entry, FILE_APPEND);
    }

    public function info($message) {
        $this->log('info', $message);
    }

    public function warning($message) {
        $this->log('warning', $message);
    }

    public function error($message) {
        $this->log('error', $message);
    }
}

?>