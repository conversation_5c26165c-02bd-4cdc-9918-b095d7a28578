<?php

class CsvParser {
    private $db;
    private $file_path;
    private $data = [];

    public function __construct(Database $db) {
        $this->db = $db;
    }

    /**
     * Načte soubor a převede ho do UTF-8.
     * @param string $path Cesta k souboru.
     * @return bool True při <PERSON>, false při ch<PERSON>.
     */
    public function loadFile($path) {
        if (!file_exists($path) || !is_readable($path)) {
            error_log("CSV soubor nenalezen nebo není čitelný: " . $path);
            return false;
        }

        // Převedení obsahu z Windows-1250 na UTF-8
        $content = file_get_contents($path);
        $utf8_content = iconv('Windows-1250', 'UTF-8', $content);
        
        // Uložení do dočasného streamu pro fgetcsv
        $stream = fopen('php://memory', 'r+');
        fwrite($stream, $utf8_content);
        rewind($stream);

        $this->file_path = $path;
        $this->data = $stream;
        return true;
    }

    /**
     * Zpracuje CSV data a uloží je do databáze.
     * @return array Pole se statistikami importu.
     */
    public function process() {
        if (!$this->data) {
            return ['success' => false, 'message' => 'Nejprve načtěte soubor pomocí loadFile().'];
        }

        $stats = [
            'total_rows' => 0,
            'imported_orders' => 0,
            'imported_items' => 0,
            'skipped_rows' => 0,
            'errors' => []
        ];

        // Přeskočení hlavičky
        fgetcsv($this->data, 0, ';');

        while (($row = fgetcsv($this->data, 0, ';')) !== FALSE) {
            $stats['total_rows']++;

            // Základní validace - musí existovat číslo objednávky
            if (empty($row[0])) {
                $stats['skipped_rows']++;
                continue;
            }
            
            // Ignorování specifických řádků
            $item_description = trim($row[1]);
            if (in_array($item_description, ['PVY', 'Poštovné', 'Dopravné'])) {
                $stats['skipped_rows']++;
                continue;
            }

            try {
                $this->db->query("SELECT id FROM orders WHERE id = :id");
                $this->db->bind(':id', $row[0]);
                $orderExists = $this->db->single();

                if (!$orderExists) {
                    $this->createOrder($row);
                    $stats['imported_orders']++;
                }

                $this->createOrderItem($row);
                $stats['imported_items']++;

            } catch (Exception $e) {
                $stats['errors'][] = "Chyba na řádku " . ($stats['total_rows'] + 1) . ": " . $e->getMessage();
            }
        }

        fclose($this->data);
        return ['success' => true, 'stats' => $stats];
    }

    /**
     * Vytvoří novou objednávku v databázi.
     */
    private function createOrder($row) {
        $order_id = trim($row[0]);
        $sales_person_prefix = explode('-', $order_id)[0];
        $order_date = $this->parseDate($row[3]);

        $this->db->query('INSERT INTO orders (id, sales_person_prefix, order_date) VALUES (:id, :sales_person_prefix, :order_date)');
        $this->db->bind(':id', $order_id);
        $this->db->bind(':sales_person_prefix', $sales_person_prefix);
        $this->db->bind(':order_date', $order_date);
        $this->db->execute();
    }

    /**
     * Vytvoří novou položku objednávky v databázi.
     */
    private function createOrderItem($row) {
        $order_id = trim($row[0]);
        $product_code = trim($row[1]);
        $quantity = $this->parseQuantity($row[2]);
        $item_ordered_date = $this->parseDate($row[4]);
        $item_stocked_date = $this->parseDate($row[5]);

        // Check if item already exists to prevent duplicates
        $this->db->query('SELECT id FROM order_items WHERE order_id = :order_id AND product_code = :product_code AND quantity = :quantity AND item_ordered_date = :item_ordered_date AND item_stocked_date = :item_stocked_date');
        $this->db->bind(':order_id', $order_id);
        $this->db->bind(':product_code', $product_code ?: null);
        $this->db->bind(':quantity', $quantity);
        $this->db->bind(':item_ordered_date', $item_ordered_date);
        $this->db->bind(':item_stocked_date', $item_stocked_date);
        $existingItem = $this->db->single();

        if (!$existingItem) {
            $this->db->query('INSERT INTO order_items (order_id, product_code, quantity, item_ordered_date, item_stocked_date) VALUES (:order_id, :product_code, :quantity, :item_ordered_date, :item_stocked_date)');
            $this->db->bind(':order_id', $order_id);
            $this->db->bind(':product_code', $product_code ?: null);
            $this->db->bind(':quantity', $quantity);
            $this->db->bind(':item_ordered_date', $item_ordered_date);
            $this->db->bind(':item_stocked_date', $item_stocked_date);
            $this->db->execute();
        }
    }

    /**
     * Převede datum z formátu d.m.Y na Y-m-d.
     */
    private function parseDate($date_string) {
        if (empty($date_string)) return null;
        $date = DateTime::createFromFormat('d.m.Y', $date_string);
        return $date ? $date->format('Y-m-d') : null;
    }

    /**
     * Převede množství na desetinné číslo.
     */
    private function parseQuantity($quantity_string) {
        return floatval(str_replace(',', '.', $quantity_string));
    }
}
