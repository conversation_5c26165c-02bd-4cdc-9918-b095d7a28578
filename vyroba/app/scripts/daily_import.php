<?php
// daily_import.php - Skript pro automatický denní import
// Tento skript by m<PERSON><PERSON> b<PERSON><PERSON> spouštěn cronem.

// Nastavení cesty k hlavnímu konfiguračnímu souboru
require_once dirname(__DIR__) . '/config/config.php';
require_once dirname(__DIR__) . '/classes/Database.php';
require_once dirname(__DIR__) . '/classes/CsvParser.php';

// Název souboru pro dnešní den
$file_name = 'file_' . date('Y-m-d') . '.csv';
$file_path = CSV_UPLOAD_PATH . '/' . $file_name;

// Logování
$log_message = "Cron import spuštěn v " . date('Y-m-d H:i:s') . "\n";
$log_message .= "Hledám soubor: " . $file_path . "\n";

$db = new Database();

if (file_exists($file_path)) {
    $parser = new CsvParser($db);
    if ($parser->loadFile($file_path)) {
        $result = $parser->process();
        if ($result['success']) {
            $log_message .= "Soubor úspěšně zpracován. Statistiky: " . json_encode($result['stats']) . "\n";
        } else {
            $log_message .= "Chyba při zpracování: " . $result['message'] . "\n";
        }
    } else {
        $log_message .= "Nepodařilo se načíst soubor.\n";
    }
} else {
    $log_message .= "Soubor pro dnešní den nebyl nalezen.\n";
}

// Uložení logu do systémového logu nebo do souboru/databáze
// Pro jednoduchost zde jen vypisujeme, v reálu by se to mělo logovat lépe.
error_log($log_message);

echo $log_message;

?>