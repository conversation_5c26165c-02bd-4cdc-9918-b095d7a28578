{* views/templates/front/contact.tpl *}
<div class="contact-module">
    {* Horní část - 2 sloupce *}
    <div class="contact-top-section">
        <div class="contact-info">
            <h3>Kontaktní informace</h3>
            <div class="company-info">
                <h4>{$company_name|escape:'html':'UTF-8'}</h4>
                <div class="address">
                    {$address|nl2br nofilter}
                </div>
                <div class="contact-details">
                    {if $phone}
                        <div class="phone">
                            <i class="icon-phone"></i>
                            <strong>Telefon:</strong> <a href="tel:{$phone|replace:' ':''}">{$phone|escape:'html':'UTF-8'}</a>
                        </div>
                    {/if}
                    {if $email}
                        <div class="email">
                            <i class="icon-envelope"></i>
                            <strong>Email:</strong> <a href="mailto:{$email}">{$email|escape:'html':'UTF-8'}</a>
                        </div>
                    {/if}
                </div>
                {if $bank_info}
                    <div class="bank-info">
                        <h5>Bankovní informace</h5>
                        <div class="bank-details">
                            {$bank_info|nl2br nofilter}
                        </div>
                    </div>
                {/if}
            </div>
        </div>

        <div class="contact-map">
            <h3>Kde nás najdete</h3>
            <div class="map-container">
                {if $map_embed}
                    {$map_embed nofilter}
                {else}
                    <p>Mapa není k dispozici.</p>
                {/if}
            </div>
        </div>
    </div>

    {* Obchodní zástupci - 3 sloupce *}
    {if $representatives && count($representatives) > 0}
        <div class="representatives-section">
            <h3>Obchodní zástupci</h3>
            <div class="representatives-grid">
                {foreach from=$representatives item=rep}
                    {if $rep.name}
                        <div class="representative-card">
                            <div class="rep-header">
                                <h4 class="rep-name">{$rep.name|escape:'html':'UTF-8'}</h4>
                                {if $rep.position}
                                    <p class="rep-position">{$rep.position|escape:'html':'UTF-8'}</p>
                                {/if}
                            </div>
                            <div class="rep-contact">
                                {if $rep.phone}
                                    <div class="rep-phone">
                                        <i class="icon-phone"></i>
                                        <strong>Telefon:</strong> <a href="tel:{$rep.phone|replace:' ':''}">{$rep.phone|escape:'html':'UTF-8'}</a>
                                    </div>
                                {/if}
                                {if $rep.email}
                                    <div class="rep-email">
                                        <i class="icon-envelope"></i>
                                        <strong>Email:</strong> <a href="mailto:{$rep.email}">{$rep.email|escape:'html':'UTF-8'}</a>
                                    </div>
                                {/if}
                                {if $rep.region}
                                    <div class="rep-region">
                                        <i class="icon-map-marker"></i>
                                        <strong>Oblast:</strong> {$rep.region|escape:'html':'UTF-8'}
                                    </div>
                                {/if}
                            </div>
                        </div>
                    {/if}
                {/foreach}
            </div>
        </div>
    {/if}

    {* Kanceláře - 3 sloupce *}
    {if $offices && count($offices) > 0}
        <div class="offices-section">
            <h3>Kanceláře</h3>
            <div class="offices-grid">
                {foreach from=$offices item=office}
                    {if $office.name}
                        <div class="office-card">
                            <div class="office-header">
                                <h4 class="office-name">{$office.name|escape:'html':'UTF-8'}</h4>
                                {if $office.address}
                                    <div class="office-address">
                                        {$office.address|nl2br nofilter}
                                    </div>
                                {/if}
                            </div>
                            <div class="office-contact">
                                {if $office.contact_person}
                                    <div class="office-contact-person">
                                        <i class="icon-user"></i>
                                        <strong>Kontaktní osoba:</strong> {$office.contact_person|escape:'html':'UTF-8'}
                                    </div>
                                {/if}
                                {if $office.phone}
                                    <div class="office-phone">
                                        <i class="icon-phone"></i>
                                        <strong>Telefon:</strong> <a href="tel:{$office.phone|replace:' ':''}">{$office.phone|escape:'html':'UTF-8'}</a>
                                    </div>
                                {/if}
                                {if $office.email}
                                    <div class="office-email">
                                        <i class="icon-envelope"></i>
                                        <strong>Email:</strong> <a href="mailto:{$office.email}">{$office.email|escape:'html':'UTF-8'}</a>
                                    </div>
                                {/if}
                                {if $office.bank_info}
                                    <div class="office-bank-info">
                                        <h5>Bankovní spojení</h5>
                                        <div class="bank-details">
                                            {$office.bank_info|nl2br nofilter}
                                        </div>
                                    </div>
                                {/if}
                            </div>
                        </div>
                    {/if}
                {/foreach}
            </div>
        </div>
    {/if}
</div>
