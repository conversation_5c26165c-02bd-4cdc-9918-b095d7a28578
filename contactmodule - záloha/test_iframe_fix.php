<?php
/**
 * Test opravy iframe problému
 */

require_once(dirname(__FILE__).'/../../config/config.inc.php');
require_once(dirname(__FILE__).'/contactmodule.php');

echo "<h2>Test opravy iframe problému</h2>";

$module = new ContactModule();

$test_iframe = '<iframe style="border:none" src="https://mapy.com/s/nefocevema" width="400" height="280" frameborder="0"></iframe>';

echo "<h3>Test 1: Simulace POST dat</h3>";
$_POST['map_embed'] = $test_iframe;

echo "<p>POST data nastavena: " . strlen($_POST['map_embed']) . " znaků</p>";
echo "<p>Tools::getValue(): '" . Tools::getValue('map_embed') . "' (délka: " . strlen(Tools::getValue('map_embed')) . ")</p>";
echo "<p>Přímý $_POST: '" . htmlspecialchars($_POST['map_embed']) . "' (délka: " . strlen($_POST['map_embed']) . ")</p>";

echo "<h3>Test 2: Base64 kódování</h3>";
$encoded = base64_encode($test_iframe);
echo "<p>Encoded: " . $encoded . "</p>";
echo "<p>Encoded délka: " . strlen($encoded) . " znaků</p>";

$decoded = base64_decode($encoded);
echo "<p>Decoded: " . htmlspecialchars($decoded) . "</p>";
echo "<p>Decoded délka: " . strlen($decoded) . " znaků</p>";
echo "<p>Stejné jako originál: " . ($decoded === $test_iframe ? 'ANO' : 'NE') . "</p>";

echo "<h3>Test 3: Uložení pomocí base64</h3>";
Configuration::updateValue('CONTACT_MAP_EMBED_ENCODED', $encoded);
Configuration::updateValue('CONTACT_MAP_EMBED', $test_iframe);

echo "<p>Uloženo do CONTACT_MAP_EMBED_ENCODED</p>";
echo "<p>Uloženo do CONTACT_MAP_EMBED</p>";

echo "<h3>Test 4: Načtení pomocí getMapEmbedSafe()</h3>";
$safe_map = $module->getMapEmbedSafe();
echo "<p>getMapEmbedSafe(): " . (empty($safe_map) ? 'PRÁZDNÉ' : 'Načteno (' . strlen($safe_map) . ' znaků)') . "</p>";

if (!empty($safe_map)) {
    echo "<h4>Obsah:</h4>";
    echo "<textarea style='width: 100%; height: 100px;'>" . htmlspecialchars($safe_map) . "</textarea>";
    
    echo "<h4>Zobrazení:</h4>";
    echo "<div style='border: 1px solid #ccc; padding: 10px;'>";
    echo $safe_map;
    echo "</div>";
}

echo "<h3>Test 5: Aktuální konfigurace</h3>";
echo "<ul>";
echo "<li>CONTACT_MAP_EMBED: " . (empty(Configuration::get('CONTACT_MAP_EMBED')) ? 'PRÁZDNÉ' : 'Uloženo (' . strlen(Configuration::get('CONTACT_MAP_EMBED')) . ' znaků)') . "</li>";
echo "<li>CONTACT_MAP_EMBED_ENCODED: " . (empty(Configuration::get('CONTACT_MAP_EMBED_ENCODED')) ? 'PRÁZDNÉ' : 'Uloženo (' . strlen(Configuration::get('CONTACT_MAP_EMBED_ENCODED')) . ' znaků)') . "</li>";
echo "</ul>";

// Vyčistíme POST data
unset($_POST['map_embed']);
?>
