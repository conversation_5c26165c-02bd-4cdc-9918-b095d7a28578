<?php
/**
 * AJAX Controller for Contact Module
 */

class ContactModuleAjaxModuleFrontController extends ModuleFrontController
{
    public function initContent()
    {
        $action = Tools::getValue('action');
        
        switch ($action) {
            case 'getContent':
                $this->ajaxGetContent();
                break;
            default:
                $this->ajaxDie('Invalid action');
                break;
        }
    }
    
    private function ajaxGetContent()
    {
        header('Content-Type: text/html; charset=utf-8');
        echo $this->module->getContactPageContent();
        exit;
    }
}
