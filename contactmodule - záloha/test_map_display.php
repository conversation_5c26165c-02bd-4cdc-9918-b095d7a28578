<?php
/**
 * Test zobrazení mapy
 */

require_once(dirname(__FILE__).'/../../config/config.inc.php');
require_once(dirname(__FILE__).'/contactmodule.php');

echo "<h2>Test zobrazení mapy</h2>";

$module = new ContactModule();

echo "<h3>1. Aktuální mapa v databázi:</h3>";
$map_safe = $module->getMapEmbedSafe();
echo "<p>Délka: " . strlen($map_safe) . " znaků</p>";

if (!empty($map_safe)) {
    echo "<h4>Původní kód:</h4>";
    echo "<textarea style='width: 100%; height: 100px;'>" . htmlspecialchars($map_safe) . "</textarea>";
    
    echo "<h3>2. Zpracovaná mapa:</h3>";
    $processed = $module->processMapEmbed($map_safe);
    echo "<textarea style='width: 100%; height: 100px;'>" . htmlspecialchars($processed) . "</textarea>";
    
    echo "<h3>3. Zobrazení s kontejnerem (400px výška):</h3>";
    echo "<div style='border: 2px solid #007acc; border-radius: 8px; overflow: hidden; width: 100%; height: 400px; position: relative;'>";
    echo $processed;
    echo "</div>";
    
    echo "<h3>4. Zobrazení s kontejnerem (300px výška):</h3>";
    echo "<div style='border: 2px solid #ff6600; border-radius: 8px; overflow: hidden; width: 100%; height: 300px; position: relative;'>";
    echo $processed;
    echo "</div>";
    
    echo "<h3>5. Zobrazení s kontejnerem (500px výška):</h3>";
    echo "<div style='border: 2px solid #00cc00; border-radius: 8px; overflow: hidden; width: 100%; height: 500px; position: relative;'>";
    echo $processed;
    echo "</div>";
    
} else {
    echo "<p style='color: red;'>Mapa není uložená!</p>";
    
    echo "<h3>Test s ukázkovou mapou:</h3>";
    $test_map = '<iframe style="border:none" src="https://mapy.com/s/nefocevema" width="100%" height="100%" frameborder="0"></iframe>';
    $processed_test = $module->processMapEmbed($test_map);
    
    echo "<h4>Zpracovaný test:</h4>";
    echo "<textarea style='width: 100%; height: 100px;'>" . htmlspecialchars($processed_test) . "</textarea>";
    
    echo "<h4>Zobrazení testu:</h4>";
    echo "<div style='border: 2px solid #007acc; border-radius: 8px; overflow: hidden; width: 100%; height: 400px; position: relative;'>";
    echo $processed_test;
    echo "</div>";
}

echo "<h3>6. CSS styly pro referenci:</h3>";
echo "<pre>";
echo ".map-container {
    position: relative;
    width: 100%;
    height: 400px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.map-container iframe {
    width: 100% !important;
    height: 100% !important;
    border: 0 !important;
    display: block !important;
}";
echo "</pre>";
?>
