<?php
/**
 * AJAX endpoint pro načítání obsahu kontaktního modulu
 */

// Najde PrestaShop config
$config_file = dirname(__FILE__) . '/../../config/config.inc.php';
if (file_exists($config_file)) {
    require_once $config_file;
} else {
    // Fallback pro jiné um<PERSON>
    require_once dirname(__FILE__) . '/../../../config/config.inc.php';
}

// Načte modul
$module = Module::getInstanceByName('contactmodule');

if ($module && $module->active) {
    // Vrátí obsah modulu
    echo $module->getContactPageContent();
} else {
    // Fallback obsah, pokud modul není aktivní
    $company_name = Configuration::get('CONTACT_COMPANY_NAME') ?: 'CZECH IMAGE GROUP s.r.o.';
    $address = Configuration::get('CONTACT_ADDRESS') ?: 'Sladovnická 508/19<br>620 00 Brno';
    $phone = Configuration::get('CONTACT_PHONE') ?: '+*********** 780';
    $email = Configuration::get('CONTACT_EMAIL') ?: '<EMAIL>';
    
    echo '
    <div class="contact-module">
        <div class="contact-top-section">
            <div class="contact-info">
                <h3>Kontaktní informace</h3>
                <div class="company-info">
                    <h4>' . $company_name . '</h4>
                    <div class="address">' . $address . '</div>
                    <div class="contact-details">
                        <div class="phone">
                            <strong>Telefon:</strong> <a href="tel:' . str_replace(' ', '', $phone) . '">' . $phone . '</a>
                        </div>
                        <div class="email">
                            <strong>Email:</strong> <a href="mailto:' . $email . '">' . $email . '</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>';
}
