<?php
/**
 * Debug formul<PERSON><PERSON><PERSON> pro Contact Module
 */

require_once(dirname(__FILE__).'/../../config/config.inc.php');

echo "<h2>Debug formul<PERSON>ře - Contact Module</h2>";

if ($_POST) {
    echo "<h3>POST data přijata:</h3>";
    echo "<pre>";
    print_r($_POST);
    echo "</pre>";
    
    echo "<h3>Mapa specificky:</h3>";
    echo "<p><strong>map_embed:</strong> ";
    if (isset($_POST['map_embed'])) {
        echo "'" . htmlspecialchars($_POST['map_embed']) . "' (délka: " . strlen($_POST['map_embed']) . ")";
    } else {
        echo "NENÍ NASTAVENO";
    }
    echo "</p>";
    
    // Zkusíme uložit mapu
    if (isset($_POST['map_embed'])) {
        $result = Configuration::updateValue('CONTACT_MAP_EMBED_TEST', $_POST['map_embed']);
        echo "<p><strong>Test uložení:</strong> " . ($result ? 'ÚSPĚCH' : 'CHYBA') . "</p>";
        
        $saved = Configuration::get('CONTACT_MAP_EMBED_TEST');
        echo "<p><strong>Test načtení:</strong> " . (empty($saved) ? 'PRÁZDNÉ' : 'Uloženo (' . strlen($saved) . ' znaků)') . "</p>";
    }
} else {
    echo "<p>Žádná POST data nebyla odeslána.</p>";
}

echo "<h3>Test formulář:</h3>";
?>

<form method="post" action="">
    <div style="margin-bottom: 20px;">
        <label>Test mapa:</label><br>
        <textarea name="map_embed" rows="5" cols="80" placeholder="Vložte iframe kód zde..."></textarea>
    </div>
    <button type="submit">Test odeslání</button>
</form>

<h3>Aktuální konfigurace:</h3>
<ul>
    <li>CONTACT_MAP_EMBED: <?php echo empty(Configuration::get('CONTACT_MAP_EMBED')) ? 'PRÁZDNÉ' : 'Uloženo (' . strlen(Configuration::get('CONTACT_MAP_EMBED')) . ' znaků)'; ?></li>
    <li>CONTACT_MAP_EMBED_TEST: <?php echo empty(Configuration::get('CONTACT_MAP_EMBED_TEST')) ? 'PRÁZDNÉ' : 'Uloženo (' . strlen(Configuration::get('CONTACT_MAP_EMBED_TEST')) . ' znaků)'; ?></li>
</ul>
