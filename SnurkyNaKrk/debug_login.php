<?php
/**
 * Debug script pro login.php
 * <PERSON><PERSON><PERSON><PERSON>, kde je problém s b<PERSON><PERSON> str<PERSON>
 */

// Zapnutí zobrazování chyb
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

echo "<h1>🔍 Debug login.php</h1>";
echo "<p>Diagnostika problému s b<PERSON><PERSON> str<PERSON></p>";

echo "<h2>1. Test základní<PERSON> soubor<PERSON></h2>";

// Test 1: Existence souborů
$files = [
    '../core/init.php',
    '../core/auth.php', 
    '../core/middleware.php'
];

foreach ($files as $file) {
    if (file_exists($file)) {
        echo "✅ {$file} existuje<br>";
    } else {
        echo "❌ {$file} NEEXISTUJE<br>";
    }
}

echo "<h2>2. Test načítání so<PERSON></h2>";

try {
    echo "Načítám core/init.php...<br>";
    require_once '../core/init.php';
    echo "✅ core/init.php načten<br>";
} catch (Exception $e) {
    echo "❌ Chyba při načítání init.php: " . $e->getMessage() . "<br>";
    exit;
}

try {
    echo "Načítám core/auth.php...<br>";
    require_once '../core/auth.php';
    echo "✅ core/auth.php načten<br>";
} catch (Exception $e) {
    echo "❌ Chyba při načítání auth.php: " . $e->getMessage() . "<br>";
    exit;
}

try {
    echo "Načítám core/middleware.php...<br>";
    require_once '../core/middleware.php';
    echo "✅ core/middleware.php načten<br>";
} catch (Exception $e) {
    echo "❌ Chyba při načítání middleware.php: " . $e->getMessage() . "<br>";
    exit;
}

echo "<h2>3. Test vytváření objektů</h2>";

try {
    echo "Vytvářím Database objekt...<br>";
    $db = new Database();
    echo "✅ Database objekt vytvořen<br>";
} catch (Exception $e) {
    echo "❌ Chyba při vytváření Database: " . $e->getMessage() . "<br>";
    exit;
}

try {
    echo "Vytvářím Auth objekt...<br>";
    $auth = new Auth($db);
    echo "✅ Auth objekt vytvořen<br>";
} catch (Exception $e) {
    echo "❌ Chyba při vytváření Auth: " . $e->getMessage() . "<br>";
    exit;
}

try {
    echo "Vytvářím SecurityMiddleware objekt...<br>";
    $middleware = new SecurityMiddleware($auth);
    echo "✅ SecurityMiddleware objekt vytvořen<br>";
} catch (Exception $e) {
    echo "❌ Chyba při vytváření SecurityMiddleware: " . $e->getMessage() . "<br>";
    exit;
}

echo "<h2>4. Test databázového připojení</h2>";

try {
    if ($db->isConnected()) {
        echo "✅ Databáze je připojena<br>";
    } else {
        echo "❌ Databáze NENÍ připojena<br>";
    }
} catch (Exception $e) {
    echo "❌ Chyba při testování databáze: " . $e->getMessage() . "<br>";
}

echo "<h2>5. Test CSRF tokenu</h2>";

try {
    $token = $auth->generateCSRFToken();
    if (!empty($token)) {
        echo "✅ CSRF token vygenerován: " . substr($token, 0, 10) . "...<br>";
    } else {
        echo "❌ CSRF token se nepodařilo vygenerovat<br>";
    }
} catch (Exception $e) {
    echo "❌ Chyba při generování CSRF tokenu: " . $e->getMessage() . "<br>";
}

echo "<h2>6. Test CSS souboru</h2>";

if (file_exists('../assets/css/admin.css')) {
    echo "✅ CSS soubor existuje<br>";
} else {
    echo "❌ CSS soubor NEEXISTUJE<br>";
}

echo "<h2>7. Simulace login.php</h2>";

try {
    // Simulace toho, co dělá login.php
    if ($auth->isLoggedIn()) {
        echo "ℹ️ Uživatel je již přihlášen<br>";
    } else {
        echo "ℹ️ Uživatel není přihlášen<br>";
    }
    
    $csrf_token = $auth->generateCSRFToken();
    echo "✅ Login simulace úspěšná<br>";
    
} catch (Exception $e) {
    echo "❌ Chyba při simulaci login.php: " . $e->getMessage() . "<br>";
}

echo "<h2>8. PHP informace</h2>";
echo "PHP verze: " . PHP_VERSION . "<br>";
echo "Server: " . ($_SERVER['SERVER_NAME'] ?? 'neznámý') . "<br>";
echo "Document root: " . ($_SERVER['DOCUMENT_ROOT'] ?? 'neznámý') . "<br>";
echo "Script path: " . __FILE__ . "<br>";

echo "<h2>9. Error log</h2>";
if (file_exists('../logs/error.log')) {
    echo "📋 Posledních 10 řádků error logu:<br>";
    echo "<pre>";
    $lines = file('../logs/error.log');
    $last_lines = array_slice($lines, -10);
    foreach ($last_lines as $line) {
        echo htmlspecialchars($line);
    }
    echo "</pre>";
} else {
    echo "ℹ️ Error log neexistuje<br>";
}

echo "<h2>✅ Debug dokončen</h2>";
echo "<p>Pokud všechny testy prošly, problém může být v:</p>";
echo "<ul>";
echo "<li>Chybějícím CSS souboru</li>";
echo "<li>Problému s cestami</li>";
echo "<li>PHP chybě, která se nezobrazuje</li>";
echo "</ul>";

echo "<p><a href='../admin/login.php'>→ Zkusit login.php znovu</a></p>";
?>
