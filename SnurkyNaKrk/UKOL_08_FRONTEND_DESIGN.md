# 🎨 Úkol 8: Frontend - Responzivní design

## 🎯 C<PERSON>l úkolu
Vytvořit responzivní frontend s moderním designem, HTML strukturou a interaktivním JavaScriptem

## 📋 Checklist úkolu
- [ ] HTML struktura pro single page application
- [ ] Responzivní CSS s mobile-first přístupem
- [ ] JavaScript pro plynulou navigaci
- [ ] Smooth scrolling a animace
- [ ] Loading states a transitions
- [ ] Cross-browser kompatibilita

## 🏗️ HTML struktura

### index.php
```php
<?php
require_once 'core/config.php';
require_once 'core/database.php';
require_once 'core/functions.php';

// Načtení obsahu sekcí
$sections = [];
$sectionData = $db->select('content_sections', ['is_active' => 1], '*', 'sort_order ASC');
foreach ($sectionData as $section) {
    $sections[$section['section_key']] = $section;
}

// Načtení aktivních produktů
$products = $db->select('products', ['is_active' => 1], '*', 'sort_order ASC');

// Načtení aktivních členů týmu
$teamMembers = $db->select('team_members', ['is_active' => 1], '*', 'sort_order ASC');

// SEO meta data
$pageTitle = $sections['hero']['title'] ?? 'Snurkynakrk.cz - Šňůrky na krk';
$pageDescription = $sections['hero']['meta_description'] ?? 'Kvalitní šňůrky na krk s přímým propojením na e-shop.';
?>
<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= Utils::escape($pageTitle) ?></title>
    <meta name="description" content="<?= Utils::escape($pageDescription) ?>">
    
    <!-- Open Graph -->
    <meta property="og:title" content="<?= Utils::escape($pageTitle) ?>">
    <meta property="og:description" content="<?= Utils::escape($pageDescription) ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://snurkynakrk.cz">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico">
    
    <!-- CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "Snurkynakrk.cz",
        "url": "https://snurkynakrk.cz",
        "description": "<?= Utils::escape($pageDescription) ?>"
    }
    </script>
</head>
<body>
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-spinner"></div>
        <p>Načítání...</p>
    </div>
    
    <!-- Navigation -->
    <nav class="navbar" id="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <a href="#hero">Snurkynakrk.cz</a>
            </div>
            
            <div class="nav-menu" id="nav-menu">
                <a href="#hero" class="nav-link">Úvod</a>
                <a href="#products" class="nav-link">Produkty</a>
                <a href="#team" class="nav-link">Náš tým</a>
                <a href="#contact" class="nav-link">Kontakt</a>
            </div>
            
            <div class="nav-toggle" id="nav-toggle">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>
    
    <!-- Main Content -->
    <main class="main-content">
        <!-- Hero Section -->
        <section id="hero" class="hero-section">
            <div class="hero-container">
                <div class="hero-content">
                    <h1 class="hero-title animate-fade-up">
                        <?= $sections['hero']['title'] ?? 'Kvalitní šňůrky na krk' ?>
                    </h1>
                    <p class="hero-description animate-fade-up delay-1">
                        <?= $sections['hero']['content'] ?? 'Objevte naši kolekci šňůrek na krk s přímým propojením na e-shop.' ?>
                    </p>
                    <div class="hero-actions animate-fade-up delay-2">
                        <a href="#products" class="btn btn-primary">Prohlédnout produkty</a>
                        <a href="#team" class="btn btn-outline">Náš tým</a>
                    </div>
                </div>
                <div class="hero-visual animate-fade-left">
                    <div class="hero-image-placeholder">
                        <!-- Zde bude hlavní obrázek -->
                    </div>
                </div>
            </div>
        </section>
        
        <!-- About Products Section -->
        <?php if (isset($sections['about_products'])): ?>
        <section id="about" class="about-section">
            <div class="container">
                <div class="section-header">
                    <h2><?= Utils::escape($sections['about_products']['title']) ?></h2>
                </div>
                <div class="about-content">
                    <?= $sections['about_products']['content'] ?>
                </div>
            </div>
        </section>
        <?php endif; ?>
        
        <!-- Products Section -->
        <section id="products" class="products-section">
            <div class="container">
                <div class="section-header">
                    <h2>Naše produkty</h2>
                    <p>Prohlédněte si naši kolekci a přejděte přímo do e-shopu</p>
                </div>
                
                <div class="products-grid" id="products-grid">
                    <?php foreach ($products as $index => $product): ?>
                        <div class="product-card animate-fade-up" style="animation-delay: <?= $index * 0.1 ?>s">
                            <div class="product-image">
                                <?php
                                $productImages = $db->select('media_files', 
                                    ['category' => 'product', 'related_id' => $product['id']], 
                                    '*', 
                                    'created_at ASC', 
                                    '1'
                                );
                                ?>
                                
                                <?php if ($productImages): ?>
                                    <img src="<?= Utils::escape($productImages[0]['file_path']) ?>" 
                                         alt="<?= Utils::escape($product['name']) ?>"
                                         loading="lazy">
                                <?php else: ?>
                                    <div class="product-placeholder">
                                        <i class="icon-product"></i>
                                    </div>
                                <?php endif; ?>
                            </div>
                            
                            <div class="product-content">
                                <h3 class="product-name"><?= Utils::escape($product['name']) ?></h3>
                                
                                <?php if ($product['description']): ?>
                                    <p class="product-description">
                                        <?= Utils::truncateText(Utils::escape($product['description']), 120) ?>
                                    </p>
                                <?php endif; ?>
                                
                                <?php if ($product['price_info']): ?>
                                    <div class="product-price">
                                        <?= Utils::escape($product['price_info']) ?>
                                    </div>
                                <?php endif; ?>
                                
                                <div class="product-actions">
                                    <a href="redirect.php?product=<?= $product['id'] ?>" 
                                       class="btn btn-primary product-cta"
                                       target="_blank"
                                       rel="noopener">
                                        <?= Utils::escape($product['cta_text']) ?>
                                        <i class="icon-external"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </section>
        
        <!-- Team Section -->
        <section id="team" class="team-section">
            <div class="container">
                <div class="section-header">
                    <h2>Náš tým</h2>
                    <?php if (isset($sections['team_intro'])): ?>
                        <p><?= Utils::escape($sections['team_intro']['content']) ?></p>
                    <?php endif; ?>
                </div>
                
                <div class="team-grid">
                    <?php foreach ($teamMembers as $index => $member): ?>
                        <div class="team-member animate-fade-up" style="animation-delay: <?= $index * 0.1 ?>s">
                            <div class="member-photo">
                                <?php if ($member['photo_filename']): ?>
                                    <img src="uploads/team/<?= Utils::escape($member['photo_filename']) ?>" 
                                         alt="<?= Utils::escape($member['name']) ?>"
                                         loading="lazy">
                                <?php else: ?>
                                    <div class="photo-placeholder">
                                        <i class="icon-user"></i>
                                    </div>
                                <?php endif; ?>
                            </div>
                            
                            <div class="member-info">
                                <h3 class="member-name"><?= Utils::escape($member['name']) ?></h3>
                                
                                <div class="member-contacts">
                                    <?php if ($member['phone']): ?>
                                        <a href="tel:<?= Utils::escape($member['phone']) ?>" 
                                           class="contact-link">
                                            <i class="icon-phone"></i>
                                            <?= Utils::escape($member['phone']) ?>
                                        </a>
                                    <?php endif; ?>
                                    
                                    <?php if ($member['email']): ?>
                                        <a href="mailto:<?= Utils::escape($member['email']) ?>" 
                                           class="contact-link">
                                            <i class="icon-email"></i>
                                            <?= Utils::escape($member['email']) ?>
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </section>
        
        <!-- Contact Section -->
        <section id="contact" class="contact-section">
            <div class="container">
                <div class="section-header">
                    <h2>Kontakt</h2>
                </div>
                
                <div class="contact-content">
                    <?php if (isset($sections['contact_info'])): ?>
                        <?= $sections['contact_info']['content'] ?>
                    <?php endif; ?>
                </div>
            </div>
        </section>
    </main>
    
    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <?php if (isset($sections['footer_content'])): ?>
                    <?= $sections['footer_content']['content'] ?>
                <?php endif; ?>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; <?= date('Y') ?> Snurkynakrk.cz. Všechna práva vyhrazena.</p>
            </div>
        </div>
    </footer>
    
    <!-- Back to Top Button -->
    <button id="back-to-top" class="back-to-top" aria-label="Zpět nahoru">
        <i class="icon-arrow-up"></i>
    </button>
    
    <!-- JavaScript -->
    <script src="assets/js/main.js"></script>
    <script src="assets/js/eshop-integration.js"></script>
</body>
</html>
```

## 🎨 CSS Framework

### assets/css/style.css (základní struktura)
```css
/* CSS Custom Properties */
:root {
    /* Barvy */
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --secondary-color: #64748b;
    --accent-color: #f59e0b;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-light: #94a3b8;
    --background: #ffffff;
    --background-alt: #f8fafc;
    --border-color: #e2e8f0;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
    
    /* Typography */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    
    /* Spacing */
    --spacing-xs: 0.5rem;
    --spacing-sm: 0.75rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;
    
    /* Layout */
    --container-max-width: 1200px;
    --header-height: 70px;
    --border-radius: 0.5rem;
    --border-radius-lg: 1rem;
    
    /* Transitions */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
}

/* Reset a základní styly */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--background);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Container */
.container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: var(--spacing-md);
}

h1 { font-size: var(--font-size-4xl); }
h2 { font-size: var(--font-size-3xl); }
h3 { font-size: var(--font-size-2xl); }

p {
    margin-bottom: var(--spacing-md);
    color: var(--text-secondary);
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-lg);
    font-size: var(--font-size-base);
    font-weight: 500;
    text-decoration: none;
    border: 2px solid transparent;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all var(--transition-normal);
    white-space: nowrap;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-outline {
    background-color: transparent;
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline:hover {
    background-color: var(--primary-color);
    color: white;
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--background);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity var(--transition-slow);
}

.loading-screen.hidden {
    opacity: 0;
    pointer-events: none;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--border-color);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: var(--spacing-md);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: var(--header-height);
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-color);
    z-index: 1000;
    transition: all var(--transition-normal);
}

.navbar.scrolled {
    box-shadow: var(--shadow-md);
}

.nav-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

.nav-logo a {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--primary-color);
    text-decoration: none;
}

.nav-menu {
    display: flex;
    gap: var(--spacing-xl);
}

.nav-link {
    color: var(--text-primary);
    text-decoration: none;
    font-weight: 500;
    transition: color var(--transition-fast);
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--primary-color);
    transition: width var(--transition-normal);
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

/* Mobile Navigation */
.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.nav-toggle .bar {
    width: 25px;
    height: 3px;
    background-color: var(--text-primary);
    margin: 3px 0;
    transition: var(--transition-fast);
}

/* Animations */
@keyframes fadeUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeLeft {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.animate-fade-up {
    animation: fadeUp 0.6s ease forwards;
    opacity: 0;
}

.animate-fade-left {
    animation: fadeLeft 0.6s ease forwards;
    opacity: 0;
}

.delay-1 { animation-delay: 0.2s; }
.delay-2 { animation-delay: 0.4s; }

/* Responsive Design */
@media (max-width: 768px) {
    :root {
        --font-size-4xl: 2rem;
        --font-size-3xl: 1.5rem;
        --spacing-md: 0.75rem;
        --spacing-lg: 1rem;
        --spacing-xl: 1.5rem;
    }
    
    .nav-menu {
        position: fixed;
        left: -100%;
        top: var(--header-height);
        flex-direction: column;
        background-color: var(--background);
        width: 100%;
        text-align: center;
        transition: var(--transition-normal);
        box-shadow: var(--shadow-lg);
        padding: var(--spacing-xl) 0;
    }
    
    .nav-menu.active {
        left: 0;
    }
    
    .nav-toggle {
        display: flex;
    }
    
    .nav-toggle.active .bar:nth-child(2) {
        opacity: 0;
    }
    
    .nav-toggle.active .bar:nth-child(1) {
        transform: translateY(8px) rotate(45deg);
    }
    
    .nav-toggle.active .bar:nth-child(3) {
        transform: translateY(-8px) rotate(-45deg);
    }
}
```

## 🚀 Implementační kroky
1. Vytvořit index.php s kompletní HTML strukturou
2. Implementovat responzivní CSS framework
3. Přidat JavaScript pro interaktivitu
4. Vytvořit animace a transitions
5. Optimalizovat pro mobile zařízení
6. Otestovat cross-browser kompatibilitu

## ✅ Kritéria dokončení
- HTML struktura je sémantická a přístupná
- CSS je responzivní s mobile-first přístupem
- JavaScript poskytuje plynulou navigaci
- Animace fungují správně
- Loading states jsou implementované
- Cross-browser kompatibilita je zajištěna
- Performance je optimalizovaná
