<?php
/**
 * Kontrola nahraných souborů na serveru
 */

echo "<h1>📁 Kontrola nahraných souborů</h1>";

echo "<h2>1. Aktuální umístění</h2>";
echo "Current directory: " . getcwd() . "<br>";
echo "Script path: " . __FILE__ . "<br>";

echo "<h2>2. Obsah root složky (www)</h2>";
$files = scandir('.');
echo "<ul>";
foreach ($files as $file) {
    if ($file !== '.' && $file !== '..') {
        $type = is_dir($file) ? '📁 [DIR]' : '📄 [FILE]';
        $size = is_file($file) ? ' (' . filesize($file) . ' bytů)' : '';
        echo "<li>$type $file$size</li>";
    }
}
echo "</ul>";

echo "<h2>3. Obsah core/ složky</h2>";
if (is_dir('core')) {
    $core_files = scandir('core');
    echo "<ul>";
    foreach ($core_files as $file) {
        if ($file !== '.' && $file !== '..') {
            $size = filesize('core/' . $file);
            echo "<li>📄 $file ($size bytů)</li>";
        }
    }
    echo "</ul>";
} else {
    echo "❌ Složka core/ neexistuje!<br>";
}

echo "<h2>4. Obsah admin/ složky</h2>";
if (is_dir('admin')) {
    $admin_files = scandir('admin');
    echo "<ul>";
    foreach ($admin_files as $file) {
        if ($file !== '.' && $file !== '..') {
            $size = filesize('admin/' . $file);
            echo "<li>📄 $file ($size bytů)</li>";
        }
    }
    echo "</ul>";
} else {
    echo "❌ Složka admin/ neexistuje!<br>";
}

echo "<h2>5. Test cest z admin/ složky</h2>";
echo "Test z pohledu admin/login.php:<br>";

$paths_from_admin = [
    '../core/init.php',
    '../core/auth.php', 
    '../core/middleware.php',
    '../core/config.php',
    '../core/database.php'
];

foreach ($paths_from_admin as $path) {
    $full_path = 'admin/' . $path;
    if (file_exists($full_path)) {
        $size = filesize($full_path);
        echo "✅ $path existuje ($size bytů)<br>";
    } else {
        echo "❌ $path NEEXISTUJE<br>";
    }
}

echo "<h2>6. Test načtení core/config.php</h2>";
if (file_exists('core/config.php')) {
    echo "✅ core/config.php existuje<br>";
    echo "Velikost: " . filesize('core/config.php') . " bytů<br>";
    
    // Zkusíme načíst první řádky
    $content = file_get_contents('core/config.php', false, null, 0, 200);
    echo "První řádky:<br>";
    echo "<pre>" . htmlspecialchars($content) . "</pre>";
} else {
    echo "❌ core/config.php neexistuje<br>";
}

echo "<h2>7. Test načtení admin/login.php</h2>";
if (file_exists('admin/login.php')) {
    echo "✅ admin/login.php existuje<br>";
    echo "Velikost: " . filesize('admin/login.php') . " bytů<br>";
    
    // Zkusíme načíst první řádky
    $content = file_get_contents('admin/login.php', false, null, 0, 300);
    echo "První řádky:<br>";
    echo "<pre>" . htmlspecialchars($content) . "</pre>";
} else {
    echo "❌ admin/login.php neexistuje<br>";
}

echo "<h2>8. Simulace require_once z admin/</h2>";
echo "Simulujeme co se stane když admin/login.php načítá ../core/init.php:<br>";

// Změníme working directory na admin
if (chdir('admin')) {
    echo "✅ Změněno do admin/ složky<br>";
    echo "Nový working directory: " . getcwd() . "<br>";
    
    $test_paths = [
        '../core/init.php',
        '../core/config.php',
        '../core/database.php'
    ];
    
    foreach ($test_paths as $path) {
        if (file_exists($path)) {
            echo "✅ $path existuje z admin/ složky<br>";
        } else {
            echo "❌ $path NEEXISTUJE z admin/ složky<br>";
        }
    }
    
    // Vrátíme se zpět
    chdir('..');
} else {
    echo "❌ Nelze přejít do admin/ složky<br>";
}

echo "<h2>✅ Kontrola dokončena</h2>";
?>
