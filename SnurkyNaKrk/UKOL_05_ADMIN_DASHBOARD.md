# 🎛️ Úkol 5: <PERSON><PERSON> - Dashboard a Content

## 🎯 Cíl úkolu
Vytvořit admin dashboard a content editor pro správu editovatelných sekcí webu

## 📋 Checklist úkolu
- [ ] Admin dashboard s přehledem
- [ ] Content editor pro sekce webu
- [ ] WYSIWYG editor integrace
- [ ] Navigation menu
- [ ] Responsive admin design
- [ ] Quick actions a statistiky

## 🏠 Admin Dashboard

### admin/dashboard.php
```php
<?php
require_once '../core/config.php';
require_once '../core/database.php';
require_once '../core/auth.php';
require_once '../core/functions.php';

$auth = new Auth($db);
$auth->requireAdmin();

// Získání základních statistik
$stats = [
    'products' => $db->select('products', ['is_active' => 1], 'COUNT(*) as count')[0]['count'],
    'team_members' => $db->select('team_members', ['is_active' => 1], 'COUNT(*) as count')[0]['count'],
    'total_clicks' => $db->select('products', [], 'SUM(link_clicks) as total')[0]['total'] ?: 0,
    'media_files' => $db->select('media_files', [], 'COUNT(*) as count')[0]['count']
];

// Nejkliknutější produkty
$topProducts = $db->select('products', 
    ['is_active' => 1], 
    'name, link_clicks', 
    'link_clicks DESC', 
    '5'
);

// Poslední aktivity (simulace)
$recentActivities = [
    ['action' => 'Aktualizace produktu', 'item' => 'Šňůrka na krk Premium', 'time' => '2 hodiny'],
    ['action' => 'Nahrání obrázku', 'item' => 'team-photo.jpg', 'time' => '5 hodin'],
    ['action' => 'Úprava obsahu', 'item' => 'Hero sekce', 'time' => '1 den']
];
?>

<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Admin Snurkynakrk.cz</title>
    <link rel="stylesheet" href="../assets/css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="admin-body">
    <?php include 'includes/admin-header.php'; ?>
    
    <div class="admin-container">
        <?php include 'includes/admin-sidebar.php'; ?>
        
        <main class="admin-main">
            <div class="dashboard-header">
                <h1>Dashboard</h1>
                <p>Přehled webu Snurkynakrk.cz</p>
            </div>
            
            <!-- Statistiky -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-box"></i>
                    </div>
                    <div class="stat-content">
                        <h3><?= $stats['products'] ?></h3>
                        <p>Aktivní produkty</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-content">
                        <h3><?= $stats['team_members'] ?></h3>
                        <p>Členové týmu</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-mouse-pointer"></i>
                    </div>
                    <div class="stat-content">
                        <h3><?= number_format($stats['total_clicks']) ?></h3>
                        <p>Celkem kliků</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-images"></i>
                    </div>
                    <div class="stat-content">
                        <h3><?= $stats['media_files'] ?></h3>
                        <p>Média soubory</p>
                    </div>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="quick-actions">
                <h2>Rychlé akce</h2>
                <div class="action-buttons">
                    <a href="products.php?action=add" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Přidat produkt
                    </a>
                    <a href="content.php" class="btn btn-secondary">
                        <i class="fas fa-edit"></i> Upravit obsah
                    </a>
                    <a href="media.php" class="btn btn-secondary">
                        <i class="fas fa-upload"></i> Nahrát média
                    </a>
                    <a href="../index.php" target="_blank" class="btn btn-outline">
                        <i class="fas fa-external-link-alt"></i> Zobrazit web
                    </a>
                </div>
            </div>
            
            <div class="dashboard-grid">
                <!-- Nejkliknutější produkty -->
                <div class="dashboard-widget">
                    <h3>Nejkliknutější produkty</h3>
                    <div class="product-list">
                        <?php foreach ($topProducts as $product): ?>
                            <div class="product-item">
                                <span class="product-name"><?= Utils::escape($product['name']) ?></span>
                                <span class="product-clicks"><?= number_format($product['link_clicks']) ?> kliků</span>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                
                <!-- Poslední aktivity -->
                <div class="dashboard-widget">
                    <h3>Poslední aktivity</h3>
                    <div class="activity-list">
                        <?php foreach ($recentActivities as $activity): ?>
                            <div class="activity-item">
                                <div class="activity-content">
                                    <strong><?= Utils::escape($activity['action']) ?></strong>
                                    <span><?= Utils::escape($activity['item']) ?></span>
                                </div>
                                <span class="activity-time"><?= Utils::escape($activity['time']) ?></span>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </main>
    </div>
    
    <script src="../assets/js/admin.js"></script>
</body>
</html>
```

## 📝 Content Editor

### admin/content.php
```php
<?php
require_once '../core/config.php';
require_once '../core/database.php';
require_once '../core/auth.php';
require_once '../core/functions.php';

$auth = new Auth($db);
$auth->requireAdmin();

$message = '';
$messageType = '';

// Zpracování formuláře
if ($_POST && isset($_POST['section_key'])) {
    try {
        $sectionKey = $_POST['section_key'];
        $title = trim($_POST['title']);
        $content = $_POST['content']; // WYSIWYG obsah
        $metaDescription = trim($_POST['meta_description']);
        
        // Kontrola existence sekce
        $existing = $db->select('content_sections', ['section_key' => $sectionKey]);
        
        if ($existing) {
            // Aktualizace
            $db->update('content_sections', [
                'title' => $title,
                'content' => $content,
                'meta_description' => $metaDescription,
                'updated_at' => date('Y-m-d H:i:s')
            ], ['section_key' => $sectionKey]);
        } else {
            // Vložení nové sekce
            $db->insert('content_sections', [
                'section_key' => $sectionKey,
                'title' => $title,
                'content' => $content,
                'meta_description' => $metaDescription,
                'is_active' => 1,
                'sort_order' => 0
            ]);
        }
        
        $message = 'Obsah byl úspěšně uložen.';
        $messageType = 'success';
        
    } catch (Exception $e) {
        $message = 'Chyba při ukládání: ' . $e->getMessage();
        $messageType = 'error';
    }
}

// Načtení všech sekcí
$sections = $db->select('content_sections', [], '*', 'sort_order ASC');

// Definice dostupných sekcí
$availableSections = [
    'hero' => 'Hero sekce',
    'about_products' => 'O produktech',
    'team_intro' => 'Úvod týmu',
    'contact_info' => 'Kontaktní informace',
    'footer_content' => 'Footer obsah'
];

// Vytvoření asociativního pole pro rychlý přístup
$sectionData = [];
foreach ($sections as $section) {
    $sectionData[$section['section_key']] = $section;
}
?>

<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Správa obsahu - Admin</title>
    <link rel="stylesheet" href="../assets/css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- TinyMCE WYSIWYG Editor -->
    <script src="https://cdn.tiny.cloud/1/no-api-key/tinymce/6/tinymce.min.js" referrerpolicy="origin"></script>
</head>
<body class="admin-body">
    <?php include 'includes/admin-header.php'; ?>
    
    <div class="admin-container">
        <?php include 'includes/admin-sidebar.php'; ?>
        
        <main class="admin-main">
            <div class="page-header">
                <h1>Správa obsahu</h1>
                <p>Upravte textový obsah jednotlivých sekcí webu</p>
            </div>
            
            <?php if ($message): ?>
                <div class="message message-<?= $messageType ?>">
                    <?= Utils::escape($message) ?>
                </div>
            <?php endif; ?>
            
            <div class="content-sections">
                <?php foreach ($availableSections as $key => $name): ?>
                    <div class="section-card">
                        <div class="section-header">
                            <h3><?= Utils::escape($name) ?></h3>
                            <button type="button" class="btn-toggle" onclick="toggleSection('<?= $key ?>')">
                                <i class="fas fa-chevron-down"></i>
                            </button>
                        </div>
                        
                        <div class="section-content" id="section-<?= $key ?>" style="display: none;">
                            <form method="POST" class="content-form">
                                <input type="hidden" name="section_key" value="<?= $key ?>">
                                
                                <div class="form-group">
                                    <label for="title-<?= $key ?>">Nadpis:</label>
                                    <input type="text" 
                                           id="title-<?= $key ?>" 
                                           name="title" 
                                           value="<?= Utils::escape($sectionData[$key]['title'] ?? '') ?>"
                                           maxlength="200">
                                </div>
                                
                                <div class="form-group">
                                    <label for="content-<?= $key ?>">Obsah:</label>
                                    <textarea id="content-<?= $key ?>" 
                                              name="content" 
                                              class="wysiwyg-editor"
                                              rows="10"><?= $sectionData[$key]['content'] ?? '' ?></textarea>
                                </div>
                                
                                <div class="form-group">
                                    <label for="meta-<?= $key ?>">Meta popis (SEO):</label>
                                    <textarea id="meta-<?= $key ?>" 
                                              name="meta_description" 
                                              maxlength="160" 
                                              rows="2"><?= Utils::escape($sectionData[$key]['meta_description'] ?? '') ?></textarea>
                                    <small>Maximálně 160 znaků pro vyhledávače</small>
                                </div>
                                
                                <div class="form-actions">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i> Uložit sekci
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </main>
    </div>
    
    <script>
        // Inicializace TinyMCE editoru
        tinymce.init({
            selector: '.wysiwyg-editor',
            height: 300,
            menubar: false,
            plugins: [
                'advlist', 'autolink', 'lists', 'link', 'image', 'charmap',
                'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
                'insertdatetime', 'media', 'table', 'help', 'wordcount'
            ],
            toolbar: 'undo redo | blocks | bold italic forecolor | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | removeformat | help',
            content_style: 'body { font-family: -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; font-size: 14px; }',
            language: 'cs'
        });
        
        // Toggle sekce
        function toggleSection(sectionKey) {
            const content = document.getElementById('section-' + sectionKey);
            const button = content.previousElementSibling.querySelector('.btn-toggle i');
            
            if (content.style.display === 'none') {
                content.style.display = 'block';
                button.classList.remove('fa-chevron-down');
                button.classList.add('fa-chevron-up');
            } else {
                content.style.display = 'none';
                button.classList.remove('fa-chevron-up');
                button.classList.add('fa-chevron-down');
            }
        }
    </script>
    
    <script src="../assets/js/admin.js"></script>
</body>
</html>
```

## 🧭 Admin Navigation

### admin/includes/admin-sidebar.php
```php
<aside class="admin-sidebar">
    <nav class="admin-nav">
        <ul>
            <li><a href="dashboard.php" class="<?= basename($_SERVER['PHP_SELF']) == 'dashboard.php' ? 'active' : '' ?>">
                <i class="fas fa-tachometer-alt"></i> Dashboard
            </a></li>
            
            <li><a href="content.php" class="<?= basename($_SERVER['PHP_SELF']) == 'content.php' ? 'active' : '' ?>">
                <i class="fas fa-edit"></i> Obsah stránek
            </a></li>
            
            <li><a href="products.php" class="<?= basename($_SERVER['PHP_SELF']) == 'products.php' ? 'active' : '' ?>">
                <i class="fas fa-box"></i> Produkty
            </a></li>
            
            <li><a href="team.php" class="<?= basename($_SERVER['PHP_SELF']) == 'team.php' ? 'active' : '' ?>">
                <i class="fas fa-users"></i> Tým
            </a></li>
            
            <li><a href="media.php" class="<?= basename($_SERVER['PHP_SELF']) == 'media.php' ? 'active' : '' ?>">
                <i class="fas fa-images"></i> Média
            </a></li>
            
            <li><a href="eshop-settings.php" class="<?= basename($_SERVER['PHP_SELF']) == 'eshop-settings.php' ? 'active' : '' ?>">
                <i class="fas fa-shopping-cart"></i> E-shop nastavení
            </a></li>
        </ul>
        
        <div class="sidebar-footer">
            <a href="../index.php" target="_blank" class="btn btn-outline btn-sm">
                <i class="fas fa-external-link-alt"></i> Zobrazit web
            </a>
            <a href="logout.php" class="btn btn-danger btn-sm">
                <i class="fas fa-sign-out-alt"></i> Odhlásit
            </a>
        </div>
    </nav>
</aside>
```

## 🚀 Implementační kroky
1. Vytvořit admin dashboard s přehledem statistik
2. Implementovat content editor s WYSIWYG
3. Vytvořit admin navigation a layout
4. Přidat responsive design pro admin rozhraní
5. Implementovat quick actions a widgets
6. Otestovat všechny admin funkce

## ✅ Kritéria dokončení
- Dashboard zobrazuje správné statistiky
- Content editor umožňuje úpravu všech sekcí
- WYSIWYG editor funguje správně
- Navigation je funkční a responzivní
- Quick actions vedou na správné stránky
- Admin rozhraní je uživatelsky přívětivé
