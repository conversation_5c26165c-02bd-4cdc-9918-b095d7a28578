# 👥 Úkol 7: <PERSON><PERSON> - Tým a Media

## 🎯 Cíl úkolu
Implementovat správu týmu (6 kolegů) a centralizovanou media library s file optimalizací

## 📋 Checklist úkolu
- [ ] Team management pro 6 kolegů
- [ ] Upload a správa profilových fotek
- [ ] Centralizovaná media library
- [ ] File optimization (resize, komprese)
- [ ] Bulk media operations
- [ ] Media kategorization

## 👥 Správa týmu

### admin/team.php
```php
<?php
require_once '../core/config.php';
require_once '../core/database.php';
require_once '../core/auth.php';
require_once '../core/functions.php';

$auth = new Auth($db);
$auth->requireAdmin();

$message = '';
$messageType = '';

// Zpracování akcí
$action = $_GET['action'] ?? 'list';
$memberId = $_GET['id'] ?? null;

// Zpracování formuláře
if ($_POST) {
    try {
        switch ($_POST['form_action']) {
            case 'save_member':
                $data = [
                    'name' => trim($_POST['name']),
                    'phone' => trim($_POST['phone']),
                    'email' => trim($_POST['email']),
                    'sort_order' => (int)$_POST['sort_order'],
                    'is_active' => isset($_POST['is_active']) ? 1 : 0
                ];
                
                // Zpracování nahrané fotky
                if (!empty($_FILES['photo']['name'])) {
                    $photoResult = handleTeamPhoto($_FILES['photo'], $memberId);
                    if ($photoResult['success']) {
                        $data['photo_filename'] = $photoResult['filename'];
                    }
                }
                
                if ($memberId) {
                    // Aktualizace
                    $data['updated_at'] = date('Y-m-d H:i:s');
                    $db->update('team_members', $data, ['id' => $memberId]);
                    $message = 'Člen týmu byl úspěšně aktualizován.';
                } else {
                    // Nový člen
                    $db->insert('team_members', $data);
                    $message = 'Člen týmu byl úspěšně přidán.';
                }
                
                $messageType = 'success';
                break;
                
            case 'delete_member':
                $db->update('team_members', ['is_active' => 0], ['id' => $memberId]);
                $message = 'Člen týmu byl deaktivován.';
                $messageType = 'success';
                break;
                
            case 'update_order':
                $orders = json_decode($_POST['order_data'], true);
                foreach ($orders as $order) {
                    $db->update('team_members', 
                        ['sort_order' => $order['position']], 
                        ['id' => $order['id']]
                    );
                }
                Utils::jsonResponse(['success' => true]);
                break;
        }
    } catch (Exception $e) {
        $message = 'Chyba: ' . $e->getMessage();
        $messageType = 'error';
    }
}

// Načtení členů týmu
if ($action === 'edit' && $memberId) {
    $member = $db->select('team_members', ['id' => $memberId])[0] ?? null;
    if (!$member) {
        Utils::redirect('team.php');
    }
}

$teamMembers = $db->select('team_members', [], '*', 'sort_order ASC, created_at DESC');

// Funkce pro zpracování fotky
function handleTeamPhoto($file, $memberId = null) {
    global $db;
    
    if ($file['error'] !== UPLOAD_ERR_OK) {
        return ['success' => false, 'error' => 'Chyba při nahrávání souboru'];
    }
    
    // Validace
    $allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
    if (!in_array($file['type'], $allowedTypes)) {
        return ['success' => false, 'error' => 'Nepodporovaný typ souboru'];
    }
    
    if ($file['size'] > MAX_UPLOAD_SIZE) {
        return ['success' => false, 'error' => 'Soubor je příliš velký'];
    }
    
    $uploadDir = UPLOAD_PATH . 'team/';
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }
    
    // Generování názvu souboru
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = 'team_' . ($memberId ?: 'new') . '_' . time() . '.' . $extension;
    $filePath = $uploadDir . $filename;
    
    if (move_uploaded_file($file['tmp_name'], $filePath)) {
        // Optimalizace obrázku
        optimizeTeamPhoto($filePath);
        
        // Uložení do media_files
        $db->insert('media_files', [
            'filename' => $filename,
            'original_name' => $file['name'],
            'file_path' => 'uploads/team/' . $filename,
            'file_size' => filesize($filePath),
            'mime_type' => $file['type'],
            'category' => 'team',
            'related_id' => $memberId
        ]);
        
        return ['success' => true, 'filename' => $filename];
    }
    
    return ['success' => false, 'error' => 'Nepodařilo se uložit soubor'];
}

// Optimalizace fotky týmu
function optimizeTeamPhoto($filePath) {
    $maxWidth = 400;
    $maxHeight = 400;
    $quality = 85;
    
    $imageInfo = getimagesize($filePath);
    if (!$imageInfo) return;
    
    $originalWidth = $imageInfo[0];
    $originalHeight = $imageInfo[1];
    $imageType = $imageInfo[2];
    
    // Pokud je obrázek menší než maximum, neupravuj
    if ($originalWidth <= $maxWidth && $originalHeight <= $maxHeight) {
        return;
    }
    
    // Výpočet nových rozměrů (zachování poměru stran)
    $ratio = min($maxWidth / $originalWidth, $maxHeight / $originalHeight);
    $newWidth = round($originalWidth * $ratio);
    $newHeight = round($originalHeight * $ratio);
    
    // Vytvoření zdrojového obrázku
    switch ($imageType) {
        case IMAGETYPE_JPEG:
            $sourceImage = imagecreatefromjpeg($filePath);
            break;
        case IMAGETYPE_PNG:
            $sourceImage = imagecreatefrompng($filePath);
            break;
        case IMAGETYPE_WEBP:
            $sourceImage = imagecreatefromwebp($filePath);
            break;
        default:
            return;
    }
    
    if (!$sourceImage) return;
    
    // Vytvoření nového obrázku
    $newImage = imagecreatetruecolor($newWidth, $newHeight);
    
    // Zachování průhlednosti pro PNG
    if ($imageType === IMAGETYPE_PNG) {
        imagealphablending($newImage, false);
        imagesavealpha($newImage, true);
        $transparent = imagecolorallocatealpha($newImage, 255, 255, 255, 127);
        imagefill($newImage, 0, 0, $transparent);
    }
    
    // Změna velikosti
    imagecopyresampled($newImage, $sourceImage, 0, 0, 0, 0, 
                      $newWidth, $newHeight, $originalWidth, $originalHeight);
    
    // Uložení optimalizovaného obrázku
    switch ($imageType) {
        case IMAGETYPE_JPEG:
            imagejpeg($newImage, $filePath, $quality);
            break;
        case IMAGETYPE_PNG:
            imagepng($newImage, $filePath, 9);
            break;
        case IMAGETYPE_WEBP:
            imagewebp($newImage, $filePath, $quality);
            break;
    }
    
    imagedestroy($sourceImage);
    imagedestroy($newImage);
}
?>

<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Správa týmu - Admin</title>
    <link rel="stylesheet" href="../assets/css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="admin-body">
    <?php include 'includes/admin-header.php'; ?>
    
    <div class="admin-container">
        <?php include 'includes/admin-sidebar.php'; ?>
        
        <main class="admin-main">
            <?php if ($action === 'list'): ?>
                <!-- Seznam členů týmu -->
                <div class="page-header">
                    <h1>Správa týmu</h1>
                    <div class="header-actions">
                        <span class="team-count">Členů: <?= count(array_filter($teamMembers, fn($m) => $m['is_active'])) ?>/6</span>
                        <?php if (count(array_filter($teamMembers, fn($m) => $m['is_active'])) < 6): ?>
                            <a href="?action=add" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Přidat člena
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
                
                <?php if ($message): ?>
                    <div class="message message-<?= $messageType ?>">
                        <?= Utils::escape($message) ?>
                    </div>
                <?php endif; ?>
                
                <div class="team-grid" id="sortable-team">
                    <?php foreach ($teamMembers as $member): ?>
                        <div class="team-card <?= $member['is_active'] ? 'active' : 'inactive' ?>" 
                             data-id="<?= $member['id'] ?>">
                            <div class="team-drag-handle">
                                <i class="fas fa-grip-vertical"></i>
                            </div>
                            
                            <div class="team-photo">
                                <?php if ($member['photo_filename']): ?>
                                    <img src="../uploads/team/<?= Utils::escape($member['photo_filename']) ?>" 
                                         alt="<?= Utils::escape($member['name']) ?>">
                                <?php else: ?>
                                    <div class="photo-placeholder">
                                        <i class="fas fa-user"></i>
                                    </div>
                                <?php endif; ?>
                            </div>
                            
                            <div class="team-info">
                                <h3><?= Utils::escape($member['name']) ?></h3>
                                
                                <?php if ($member['phone']): ?>
                                    <p class="team-contact">
                                        <i class="fas fa-phone"></i>
                                        <a href="tel:<?= Utils::escape($member['phone']) ?>">
                                            <?= Utils::escape($member['phone']) ?>
                                        </a>
                                    </p>
                                <?php endif; ?>
                                
                                <?php if ($member['email']): ?>
                                    <p class="team-contact">
                                        <i class="fas fa-envelope"></i>
                                        <a href="mailto:<?= Utils::escape($member['email']) ?>">
                                            <?= Utils::escape($member['email']) ?>
                                        </a>
                                    </p>
                                <?php endif; ?>
                                
                                <div class="team-status">
                                    <span class="status-badge <?= $member['is_active'] ? 'active' : 'inactive' ?>">
                                        <?= $member['is_active'] ? 'Aktivní' : 'Neaktivní' ?>
                                    </span>
                                </div>
                            </div>
                            
                            <div class="team-actions">
                                <a href="?action=edit&id=<?= $member['id'] ?>" class="btn btn-sm btn-secondary">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button onclick="deleteMember(<?= $member['id'] ?>)" class="btn btn-sm btn-danger">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
                
            <?php else: ?>
                <!-- Formulář pro přidání/úpravu člena -->
                <div class="page-header">
                    <h1><?= $action === 'edit' ? 'Upravit člena týmu' : 'Přidat člena týmu' ?></h1>
                    <a href="team.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Zpět na seznam
                    </a>
                </div>
                
                <?php if ($message): ?>
                    <div class="message message-<?= $messageType ?>">
                        <?= Utils::escape($message) ?>
                    </div>
                <?php endif; ?>
                
                <form method="POST" enctype="multipart/form-data" class="team-form">
                    <input type="hidden" name="form_action" value="save_member">
                    
                    <div class="form-grid">
                        <div class="form-section">
                            <h3>Základní informace</h3>
                            
                            <div class="form-group">
                                <label for="name">Jméno a příjmení *</label>
                                <input type="text" id="name" name="name" 
                                       value="<?= Utils::escape($member['name'] ?? '') ?>" 
                                       required maxlength="100">
                            </div>
                            
                            <div class="form-group">
                                <label for="phone">Telefon</label>
                                <input type="tel" id="phone" name="phone" 
                                       value="<?= Utils::escape($member['phone'] ?? '') ?>" 
                                       maxlength="20">
                            </div>
                            
                            <div class="form-group">
                                <label for="email">Email</label>
                                <input type="email" id="email" name="email" 
                                       value="<?= Utils::escape($member['email'] ?? '') ?>" 
                                       maxlength="100">
                            </div>
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="sort_order">Pořadí</label>
                                    <input type="number" id="sort_order" name="sort_order" 
                                           value="<?= $member['sort_order'] ?? 0 ?>" min="0" max="5">
                                </div>
                                
                                <div class="form-group">
                                    <label class="checkbox-label">
                                        <input type="checkbox" name="is_active" 
                                               <?= ($member['is_active'] ?? 1) ? 'checked' : '' ?>>
                                        Aktivní člen
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-section">
                            <h3>Profilová fotka</h3>
                            
                            <?php if (isset($member) && $member['photo_filename']): ?>
                                <div class="current-photo">
                                    <img src="../uploads/team/<?= Utils::escape($member['photo_filename']) ?>" 
                                         alt="Současná fotka" class="preview-image">
                                </div>
                            <?php endif; ?>
                            
                            <div class="form-group">
                                <label for="photo">Nahrát novou fotku</label>
                                <input type="file" id="photo" name="photo" 
                                       accept="image/jpeg,image/png,image/webp" class="file-input">
                                <small>Doporučená velikost: 400x400px. Formáty: JPG, PNG, WebP</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> 
                            <?= $action === 'edit' ? 'Aktualizovat člena' : 'Přidat člena' ?>
                        </button>
                        <a href="team.php" class="btn btn-secondary">Zrušit</a>
                    </div>
                </form>
            <?php endif; ?>
        </main>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
    <script>
        // Drag & Drop řazení týmu
        if (document.getElementById('sortable-team')) {
            new Sortable(document.getElementById('sortable-team'), {
                handle: '.team-drag-handle',
                animation: 150,
                onEnd: function(evt) {
                    updateTeamOrder();
                }
            });
        }
        
        function updateTeamOrder() {
            const members = document.querySelectorAll('.team-card');
            const orderData = [];
            
            members.forEach((member, index) => {
                orderData.push({
                    id: member.dataset.id,
                    position: index
                });
            });
            
            fetch('team.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'form_action=update_order&order_data=' + encodeURIComponent(JSON.stringify(orderData))
            });
        }
        
        function deleteMember(id) {
            if (confirm('Opravdu chcete deaktivovat tohoto člena týmu?')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="form_action" value="delete_member">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }
        
        // Preview nahrané fotky
        document.getElementById('photo')?.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    let preview = document.querySelector('.preview-image');
                    if (!preview) {
                        preview = document.createElement('img');
                        preview.className = 'preview-image';
                        e.target.parentNode.appendChild(preview);
                    }
                    preview.src = e.target.result;
                };
                reader.readAsDataURL(file);
            }
        });
    </script>
    
    <script src="../assets/js/admin.js"></script>
</body>
</html>
```

## 🚀 Implementační kroky
1. Vytvořit team.php s kompletním CRUD pro členy týmu
2. Implementovat photo upload s optimalizací
3. Vytvořit drag & drop řazení členů týmu
4. Přidat validaci pro maximálně 6 členů
5. Implementovat image optimization funkce
6. Vytvořit responsive design pro team cards

## ✅ Kritéria dokončení
- Team management umožňuje správu max. 6 členů
- Photo upload funguje s optimalizací obrázků
- Drag & drop řazení ukládá pořadí
- Validace kontaktních údajů funguje
- Image optimization zmenšuje velikost fotek
- Rozhraní je responzivní a uživatelsky přívětivé
- Preview nahraných fotek funguje
