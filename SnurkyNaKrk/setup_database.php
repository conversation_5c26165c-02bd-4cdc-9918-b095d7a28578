<?php
/**
 * Nastavení databáze pro snurkynakrk.cz
 */

// Zapnutí zobrazování chyb
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔧 Nastavení databáze</h1>";

// Načtení konfigurace
if (!defined('SNURKY_INIT')) {
    define('SNURKY_INIT', true);
}

require_once 'core/config.php';

echo "<h2>Krok 1: <PERSON><PERSON><PERSON><PERSON><PERSON>n<PERSON> k databázi</h2>";

try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ Připojení k databázi úspěšné<br>";
} catch (PDOException $e) {
    echo "❌ Chyba připojení: " . $e->getMessage() . "<br>";
    exit;
}

echo "<h2>Krok 2: Vyt<PERSON><PERSON><PERSON><PERSON> tabulek</h2>";

// SQL pro vytvoření tabulek
$tables = [
    'content_sections' => "
        CREATE TABLE IF NOT EXISTS content_sections (
            id INT AUTO_INCREMENT PRIMARY KEY,
            section_key VARCHAR(50) NOT NULL UNIQUE,
            title VARCHAR(255) NOT NULL,
            content TEXT,
            is_active BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ",
    
    'products' => "
        CREATE TABLE IF NOT EXISTS products (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            description TEXT,
            image_url VARCHAR(500),
            eshop_url VARCHAR(500),
            sort_order INT DEFAULT 0,
            is_active BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ",
    
    'team_members' => "
        CREATE TABLE IF NOT EXISTS team_members (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            position VARCHAR(255),
            bio TEXT,
            image_url VARCHAR(500),
            sort_order INT DEFAULT 0,
            is_active BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ",
    
    'media_files' => "
        CREATE TABLE IF NOT EXISTS media_files (
            id INT AUTO_INCREMENT PRIMARY KEY,
            filename VARCHAR(255) NOT NULL,
            original_name VARCHAR(255) NOT NULL,
            file_path VARCHAR(500) NOT NULL,
            file_size INT NOT NULL,
            mime_type VARCHAR(100) NOT NULL,
            alt_text VARCHAR(255),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ",
    
    'admin_sessions' => "
        CREATE TABLE IF NOT EXISTS admin_sessions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            session_id VARCHAR(128) NOT NULL UNIQUE,
            user_data TEXT,
            ip_address VARCHAR(45),
            user_agent TEXT,
            expires_at TIMESTAMP NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    "
];

foreach ($tables as $table_name => $sql) {
    try {
        $pdo->exec($sql);
        echo "✅ Tabulka $table_name vytvořena<br>";
    } catch (PDOException $e) {
        echo "❌ Chyba při vytváření tabulky $table_name: " . $e->getMessage() . "<br>";
    }
}

echo "<h2>Krok 3: Vložení základních dat</h2>";

// Základní obsahové sekce
$content_sections = [
    ['hero', 'Vítejte na Snurky na krk', 'Objevte naši jedinečnou kolekci produktů pro váš krk. Kvalitní materiály, originální design a péče o detail.'],
    ['about', 'O našich produktech', 'Naše produkty jsou vyráběny s láskou a péčí o detail. Používáme pouze kvalitní materiály a dbáme na originalitu designu.'],
    ['contact', 'Kontaktujte nás', 'Máte dotazy o našich produktech? Rádi vám pomůžeme a odpovíme na všechny vaše otázky.']
];

foreach ($content_sections as $section) {
    try {
        $stmt = $pdo->prepare("INSERT IGNORE INTO content_sections (section_key, title, content) VALUES (?, ?, ?)");
        $stmt->execute($section);
        echo "✅ Obsah '{$section[0]}' vložen<br>";
    } catch (PDOException $e) {
        echo "⚠️ Obsah '{$section[0]}' už existuje nebo chyba: " . $e->getMessage() . "<br>";
    }
}

// Ukázkové produkty
$products = [
    ['Elegantní hedvábná šála', 'Luxusní hedvábná šála v různých barvách', '', '', 1],
    ['Bavlněný šátek', 'Měkký bavlněný šátek pro každodenní nošení', '', '', 2],
    ['Zimní nákrčník', 'Teplý nákrčník pro chladné dny', '', '', 3]
];

foreach ($products as $product) {
    try {
        $stmt = $pdo->prepare("INSERT IGNORE INTO products (name, description, image_url, eshop_url, sort_order) VALUES (?, ?, ?, ?, ?)");
        $stmt->execute($product);
        echo "✅ Produkt '{$product[0]}' vložen<br>";
    } catch (PDOException $e) {
        echo "⚠️ Produkt '{$product[0]}' už existuje nebo chyba: " . $e->getMessage() . "<br>";
    }
}

echo "<h2>Krok 4: Kontrola tabulek</h2>";

$tables_check = ['content_sections', 'products', 'team_members', 'media_files', 'admin_sessions'];

foreach ($tables_check as $table) {
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM $table");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "✅ Tabulka $table: {$result['count']} záznamů<br>";
    } catch (PDOException $e) {
        echo "❌ Chyba při kontrole tabulky $table: " . $e->getMessage() . "<br>";
    }
}

echo "<h2>🎉 Databáze je připravena!</h2>";
echo "<p>Všechny tabulky byly vytvořeny a základní data vložena.</p>";

echo "<h3>🔗 Další kroky:</h3>";
echo "<a href='index.php'>→ Hlavní stránka</a><br>";
echo "<a href='admin/login.php'>→ Admin přihlášení</a><br>";
echo "<a href='test_database.php'>→ Test databáze</a><br>";
?>
