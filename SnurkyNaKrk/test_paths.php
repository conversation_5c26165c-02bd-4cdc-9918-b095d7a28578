<?php
/**
 * Test správnosti cest na hostingu
 */

echo "<h1>🔍 Test cest na hostingu</h1>";

echo "<h2>1. Aktu<PERSON><PERSON><PERSON> um<PERSON>ní</h2>";
echo "Script path: " . __FILE__ . "<br>";
echo "Document root: " . ($_SERVER['DOCUMENT_ROOT'] ?? 'neznámý') . "<br>";
echo "Current directory: " . getcwd() . "<br>";

echo "<h2>2. Test existence souborů</h2>";

$files_to_check = [
    'core/config.php',
    'core/database.php', 
    'core/init.php',
    'core/auth.php',
    'core/middleware.php',
    'admin/login.php',
    'admin/dashboard.php',
    'assets/css/admin.css',
    'database_schema.sql'
];

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        echo "✅ $file - existuje<br>";
    } else {
        echo "❌ $file - NEEXISTUJE<br>";
    }
}

echo "<h2>3. Test načítání core souborů</h2>";

try {
    if (file_exists('core/config.php')) {
        define('SNURKY_INIT', true);
        require_once 'core/config.php';
        echo "✅ config.php načten<br>";
    } else {
        echo "❌ config.php nenalezen<br>";
    }
} catch (Exception $e) {
    echo "❌ Chyba při načítání config.php: " . $e->getMessage() . "<br>";
}

try {
    if (file_exists('core/database.php')) {
        require_once 'core/database.php';
        echo "✅ database.php načten<br>";
    } else {
        echo "❌ database.php nenalezen<br>";
    }
} catch (Exception $e) {
    echo "❌ Chyba při načítání database.php: " . $e->getMessage() . "<br>";
}

echo "<h2>4. Test vytvoření Database objektu</h2>";

try {
    if (class_exists('Database')) {
        $db = new Database();
        echo "✅ Database objekt vytvořen<br>";
        
        if ($db->isConnected()) {
            echo "✅ Databáze připojena<br>";
        } else {
            echo "❌ Databáze nepřipojena<br>";
        }
    } else {
        echo "❌ Database třída neexistuje<br>";
    }
} catch (Exception $e) {
    echo "❌ Chyba při vytváření Database: " . $e->getMessage() . "<br>";
}

echo "<h2>5. Struktura složek</h2>";

$dirs = ['core', 'admin', 'assets', 'assets/css', 'uploads', 'logs'];

foreach ($dirs as $dir) {
    if (is_dir($dir)) {
        echo "✅ Složka $dir existuje<br>";
    } else {
        echo "❌ Složka $dir NEEXISTUJE<br>";
    }
}

echo "<h2>6. Užitečné odkazy</h2>";
echo "<a href='admin/login.php'>→ Admin login</a><br>";
echo "<a href='test_database.php'>→ Test databáze</a><br>";
echo "<a href='debug_login.php'>→ Debug login</a><br>";

echo "<h2>✅ Test cest dokončen</h2>";
?>
