<?php
/**
 * Error handling a logging pro snurkynakrk.cz
 * 
 * Centralizované zpracování chyb a logování pro celou aplikaci
 */

class ErrorHandler {
    private static $logFile;
    
    /**
     * Inicializace error handleru
     * @param string $logFile Cesta k log souboru
     */
    public static function init($logFile = '../logs/error.log') {
        self::$logFile = $logFile;
        
        // Nastavení error handleru
        set_error_handler([self::class, 'handleError']);
        set_exception_handler([self::class, 'handleException']);
        register_shutdown_function([self::class, 'handleFatalError']);
        
        // Vytvoření log složky pokud neexistuje
        $logDir = dirname(self::$logFile);
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
    }
    
    /**
     * Zpracování běžných chyb
     * @param int $severity Závažnost chyby
     * @param string $message Zprá<PERSON>
     * @param string $file Soubor
     * @param int $line Řádek
     * @return bool
     */
    public static function handleError($severity, $message, $file, $line) {
        if (!(error_reporting() & $severity)) {
            return false;
        }
        
        $errorMsg = "ERROR: {$message} in {$file} on line {$line}";
        self::log($errorMsg);
        
        // V produkci nezobrazovat chyby
        if (!defined('DEBUG') || !DEBUG) {
            return true;
        }
        
        return false;
    }
    
    /**
     * Zpracování výjimek
     * @param Exception $exception Výjimka
     */
    public static function handleException($exception) {
        $errorMsg = "EXCEPTION: " . $exception->getMessage() . 
                   " in " . $exception->getFile() . 
                   " on line " . $exception->getLine();
        
        self::log($errorMsg);
        
        // V produkci zobrazit obecnou chybu
        if (!defined('DEBUG') || !DEBUG) {
            http_response_code(500);
            self::showErrorPage();
        } else {
            echo "<pre>{$errorMsg}</pre>";
            echo "<pre>" . $exception->getTraceAsString() . "</pre>";
        }
        
        exit;
    }
    
    /**
     * Zpracování fatálních chyb
     */
    public static function handleFatalError() {
        $error = error_get_last();
        if ($error && in_array($error['type'], [E_ERROR, E_CORE_ERROR, E_COMPILE_ERROR])) {
            $errorMsg = "FATAL ERROR: {$error['message']} in {$error['file']} on line {$error['line']}";
            self::log($errorMsg);
            
            if (!defined('DEBUG') || !DEBUG) {
                self::showErrorPage();
            }
        }
    }
    
    /**
     * Logování zprávy
     * @param string $message Zpráva k zalogování
     */
    private static function log($message) {
        $timestamp = date('Y-m-d H:i:s');
        $logEntry = "[{$timestamp}] {$message}" . PHP_EOL;
        file_put_contents(self::$logFile, $logEntry, FILE_APPEND | LOCK_EX);
    }
    
    /**
     * Zobrazení error stránky
     */
    private static function showErrorPage() {
        ?>
        <!DOCTYPE html>
        <html lang="cs">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Chyba serveru - Snurkynakrk.cz</title>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    background: #f5f5f5;
                    margin: 0;
                    padding: 20px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    min-height: 100vh;
                }
                .error-container {
                    background: white;
                    padding: 40px;
                    border-radius: 10px;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                    text-align: center;
                    max-width: 500px;
                }
                .error-code {
                    font-size: 72px;
                    font-weight: bold;
                    color: #dc3545;
                    margin-bottom: 20px;
                }
                .error-message {
                    font-size: 24px;
                    color: #333;
                    margin-bottom: 20px;
                }
                .error-description {
                    color: #666;
                    margin-bottom: 30px;
                    line-height: 1.6;
                }
                .btn {
                    display: inline-block;
                    padding: 12px 24px;
                    background: #007bff;
                    color: white;
                    text-decoration: none;
                    border-radius: 5px;
                    transition: background 0.3s;
                }
                .btn:hover {
                    background: #0056b3;
                }
            </style>
        </head>
        <body>
            <div class="error-container">
                <div class="error-code">500</div>
                <div class="error-message">Chyba serveru</div>
                <div class="error-description">
                    Omlouváme se, ale došlo k neočekávané chybě. 
                    Náš tým byl informován a pracuje na řešení problému.
                </div>
                <a href="/" class="btn">Zpět na hlavní stránku</a>
            </div>
        </body>
        </html>
        <?php
    }
    
    /**
     * Manuální logování zprávy
     * @param string $message Zpráva
     * @param string $level Úroveň (INFO, WARNING, ERROR)
     */
    public static function logMessage($message, $level = 'INFO') {
        $timestamp = date('Y-m-d H:i:s');
        $logEntry = "[{$timestamp}] [{$level}] {$message}" . PHP_EOL;
        file_put_contents(self::$logFile, $logEntry, FILE_APPEND | LOCK_EX);
    }
}

// Inicializace error handleru
ErrorHandler::init();
