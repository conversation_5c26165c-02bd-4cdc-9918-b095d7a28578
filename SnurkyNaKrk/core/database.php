<?php
/**
 * Databázová třída pro snurkynakrk.cz
 * 
 * Poskytuje bezpečné PDO připojení k databázi s:
 * - Prepared statements
 * - Error handling
 * - Connection pooling
 * - Query logging (v debug režimu)
 */

// Zabránění přímému přístupu
if (!defined('SNURKY_INIT')) {
    die('Přímý přístup není povolen');
}

class Database {
    
    private static $instance = null;
    private $connection = null;
    private $query_count = 0;
    private $query_log = [];
    
    /**
     * Singleton pattern - vrací jedinou instanci databáze
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Privátní konstruktor pro singleton
     */
    private function __construct() {
        $this->connect();
    }
    
    /**
     * Vytvoř<PERSON> přip<PERSON> k databázi
     */
    private function connect() {
        try {
            $dsn = sprintf(
                'mysql:host=%s;dbname=%s;charset=%s',
                DB_HOST,
                DB_NAME,
                DB_CHARSET
            );
            
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES " . DB_CHARSET . " COLLATE " . DB_COLLATE,
                PDO::ATTR_PERSISTENT => false, // Vypnuto kvůli shared hostingu
                PDO::ATTR_TIMEOUT => 30
            ];
            
            $this->connection = new PDO($dsn, DB_USER, DB_PASS, $options);
            
            // Nastavení SQL módu pro konzistenci
            $this->connection->exec("SET sql_mode = 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO'");
            
        } catch (PDOException $e) {
            $this->logError('Chyba připojení k databázi: ' . $e->getMessage());
            
            if (DEBUG_MODE) {
                die('Chyba připojení k databázi: ' . $e->getMessage());
            } else {
                die('Chyba připojení k databázi. Kontaktujte administrátora.');
            }
        }
    }
    
    /**
     * Vrací PDO připojení
     */
    public function getConnection() {
        // Kontrola zda je připojení stále aktivní
        if ($this->connection === null) {
            $this->connect();
        }
        
        return $this->connection;
    }
    
    /**
     * Provede SELECT dotaz s prepared statement
     * 
     * @param string $query SQL dotaz s placeholdery
     * @param array $params Parametry pro prepared statement
     * @return array Výsledky dotazu
     */
    public function select($query, $params = []) {
        try {
            $stmt = $this->prepare($query);
            $stmt->execute($params);
            
            $this->logQuery($query, $params);
            
            return $stmt->fetchAll();
            
        } catch (PDOException $e) {
            $this->logError('SELECT chyba: ' . $e->getMessage() . ' | Query: ' . $query);
            throw new Exception('Chyba při čtení z databáze');
        }
    }
    
    /**
     * Provede SELECT dotaz a vrátí pouze první řádek
     */
    public function selectOne($query, $params = []) {
        $results = $this->select($query, $params);
        return !empty($results) ? $results[0] : null;
    }
    
    /**
     * Provede INSERT dotaz
     * 
     * @param string $query SQL dotaz
     * @param array $params Parametry
     * @return int ID vloženého záznamu
     */
    public function insert($query, $params = []) {
        try {
            $stmt = $this->prepare($query);
            $stmt->execute($params);
            
            $this->logQuery($query, $params);
            
            return $this->connection->lastInsertId();
            
        } catch (PDOException $e) {
            $this->logError('INSERT chyba: ' . $e->getMessage() . ' | Query: ' . $query);
            throw new Exception('Chyba při vkládání do databáze');
        }
    }
    
    /**
     * Provede UPDATE dotaz
     * 
     * @param string $query SQL dotaz
     * @param array $params Parametry
     * @return int Počet ovlivněných řádků
     */
    public function update($query, $params = []) {
        try {
            $stmt = $this->prepare($query);
            $stmt->execute($params);
            
            $this->logQuery($query, $params);
            
            return $stmt->rowCount();
            
        } catch (PDOException $e) {
            $this->logError('UPDATE chyba: ' . $e->getMessage() . ' | Query: ' . $query);
            throw new Exception('Chyba při aktualizaci databáze');
        }
    }
    
    /**
     * Provede DELETE dotaz
     * 
     * @param string $query SQL dotaz
     * @param array $params Parametry
     * @return int Počet smazaných řádků
     */
    public function delete($query, $params = []) {
        try {
            $stmt = $this->prepare($query);
            $stmt->execute($params);
            
            $this->logQuery($query, $params);
            
            return $stmt->rowCount();
            
        } catch (PDOException $e) {
            $this->logError('DELETE chyba: ' . $e->getMessage() . ' | Query: ' . $query);
            throw new Exception('Chyba při mazání z databáze');
        }
    }
    
    /**
     * Připraví prepared statement
     */
    private function prepare($query) {
        try {
            return $this->connection->prepare($query);
        } catch (PDOException $e) {
            $this->logError('PREPARE chyba: ' . $e->getMessage() . ' | Query: ' . $query);
            throw new Exception('Chyba při přípravě SQL dotazu');
        }
    }
    
    /**
     * Začne transakci
     */
    public function beginTransaction() {
        return $this->connection->beginTransaction();
    }
    
    /**
     * Potvrdí transakci
     */
    public function commit() {
        return $this->connection->commit();
    }
    
    /**
     * Zruší transakci
     */
    public function rollback() {
        return $this->connection->rollback();
    }
    
    /**
     * Kontroluje zda je v transakci
     */
    public function inTransaction() {
        return $this->connection->inTransaction();
    }
    
    /**
     * Zaloguje SQL dotaz (pouze v debug režimu)
     */
    private function logQuery($query, $params = []) {
        $this->query_count++;
        
        if (DEBUG_MODE) {
            $this->query_log[] = [
                'query' => $query,
                'params' => $params,
                'time' => microtime(true)
            ];
        }
    }
    
    /**
     * Zaloguje chybu
     */
    private function logError($message) {
        $log_message = date('Y-m-d H:i:s') . ' - ' . $message . PHP_EOL;
        
        if (defined('ERROR_LOG_FILE') && is_writable(dirname(ERROR_LOG_FILE))) {
            file_put_contents(ERROR_LOG_FILE, $log_message, FILE_APPEND | LOCK_EX);
        }
        
        if (DEBUG_MODE) {
            error_log($message);
        }
    }
    
    /**
     * Vrací počet provedených dotazů
     */
    public function getQueryCount() {
        return $this->query_count;
    }
    
    /**
     * Vrací log dotazů (pouze v debug režimu)
     */
    public function getQueryLog() {
        return DEBUG_MODE ? $this->query_log : [];
    }
    
    /**
     * Testuje připojení k databázi
     */
    public function testConnection() {
        try {
            $stmt = $this->connection->query('SELECT 1');
            return $stmt !== false;
        } catch (PDOException $e) {
            return false;
        }
    }
    
    /**
     * Uzavře připojení
     */
    public function close() {
        $this->connection = null;
    }
    
    /**
     * Destruktor
     */
    public function __destruct() {
        $this->close();
    }
    
    /**
     * Zabránění klonování
     */
    private function __clone() {}
    
    /**
     * Zabránění unserialize
     */
    public function __wakeup() {
        throw new Exception("Nelze unserializovat singleton");
    }
}

/**
 * Pomocná funkce pro rychlý přístup k databázi
 */
function db() {
    return Database::getInstance();
}
