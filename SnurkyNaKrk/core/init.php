<?php
/**
 * Inicializační soubor pro snurkynakrk.cz
 * 
 * Tento soubor se načítá na začátku každé stránky a zajišťuje:
 * - Základní bezpečnostní nastavení
 * - Načtení konfigurace
 * - Inicializaci databáze
 * - Session management
 * - Error handling
 */

// Zabránění přímému přístupu
if (!defined('SNURKY_INIT')) {
    define('SNURKY_INIT', true);
}

// Spuštění session pokud ještě není spuštěna
if (session_status() === PHP_SESSION_NONE) {
    // Bezpečné nastavení session pouze pokud hlavičky ještě nebyly odeslány
    if (!headers_sent()) {
        ini_set('session.cookie_httponly', 1);
        ini_set('session.use_only_cookies', 1);
        ini_set('session.use_strict_mode', 1);
        ini_set('session.cookie_secure', isset($_SERVER['HTTPS']) ? 1 : 0);
        ini_set('session.cookie_samesite', 'Strict');
        ini_set('session.gc_maxlifetime', 3600); // 1 hodina

        session_start();

        // Regenerace session ID pro bezpečnost
        if (!isset($_SESSION['last_regeneration'])) {
            $_SESSION['last_regeneration'] = time();
        } elseif (time() - $_SESSION['last_regeneration'] > 1800) { // 30 minut
            session_regenerate_id(true);
            $_SESSION['last_regeneration'] = time();
        }
    } else {
        // Pokud jsou hlavičky už odeslané, spustíme session bez nastavení
        @session_start();
    }
}

// Načtení konfigurace
require_once __DIR__ . '/config.php';

// Načtení core komponent
require_once __DIR__ . '/database.php';
require_once __DIR__ . '/functions.php';
require_once __DIR__ . '/validator.php';
require_once __DIR__ . '/error-handler.php';

/**
 * Pomocná funkce pro bezpečné zobrazení dat
 */
function escape($data) {
    return htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
}

/**
 * Pomocná funkce pro redirect
 */
function redirect($url, $permanent = false) {
    $status_code = $permanent ? 301 : 302;
    header("Location: $url", true, $status_code);
    exit;
}

/**
 * Pomocná funkce pro JSON odpověď
 */
function json_response($data, $status_code = 200) {
    http_response_code($status_code);
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode($data, JSON_UNESCAPED_UNICODE);
    exit;
}

/**
 * Pomocná funkce pro logování
 */
function log_message($message, $level = 'INFO') {
    $timestamp = date('Y-m-d H:i:s');
    $log_entry = "[$timestamp] [$level] $message" . PHP_EOL;
    
    if (defined('ERROR_LOG_FILE') && is_writable(dirname(ERROR_LOG_FILE))) {
        file_put_contents(ERROR_LOG_FILE, $log_entry, FILE_APPEND | LOCK_EX);
    }
}

// Inicializace dokončena
define('SNURKY_INITIALIZED', true);
