<?php
/**
 * Input validation třídy pro snurkynakrk.cz
 * 
 * Poskytuje komplexní validaci vstupních dat s českými chybovými zprávami
 */

class Validator {
    private $errors = [];
    private $data = [];
    
    /**
     * Konstruktor
     * @param array $data Data k validaci
     */
    public function __construct($data = []) {
        $this->data = $data;
    }
    
    /**
     * Validace povinného pole
     * @param string $field Název pole
     * @param string $message Vlastní chybová zpráva
     * @return self
     */
    public function required($field, $message = null) {
        if (!isset($this->data[$field]) || empty(trim($this->data[$field]))) {
            $this->errors[$field] = $message ?: "Pole {$field} je povinné.";
        }
        return $this;
    }
    
    /**
     * Validace emailu
     * @param string $field Název pole
     * @param string $message Vlastn<PERSON> chybov<PERSON> zpráva
     * @return self
     */
    public function email($field, $message = null) {
        if (isset($this->data[$field]) && !empty($this->data[$field])) {
            if (!Utils::isValidEmail($this->data[$field])) {
                $this->errors[$field] = $message ?: "Pole {$field} musí obsahovat platný email.";
            }
        }
        return $this;
    }
    
    /**
     * Validace minimální délky
     * @param string $field Název pole
     * @param int $length Minimální délka
     * @param string $message Vlastní chybová zpráva
     * @return self
     */
    public function minLength($field, $length, $message = null) {
        if (isset($this->data[$field]) && mb_strlen($this->data[$field]) < $length) {
            $this->errors[$field] = $message ?: "Pole {$field} musí mít alespoň {$length} znaků.";
        }
        return $this;
    }
    
    /**
     * Validace maximální délky
     * @param string $field Název pole
     * @param int $length Maximální délka
     * @param string $message Vlastní chybová zpráva
     * @return self
     */
    public function maxLength($field, $length, $message = null) {
        if (isset($this->data[$field]) && mb_strlen($this->data[$field]) > $length) {
            $this->errors[$field] = $message ?: "Pole {$field} může mít maximálně {$length} znaků.";
        }
        return $this;
    }
    
    /**
     * Validace číselné hodnoty
     * @param string $field Název pole
     * @param string $message Vlastní chybová zpráva
     * @return self
     */
    public function numeric($field, $message = null) {
        if (isset($this->data[$field]) && !is_numeric($this->data[$field])) {
            $this->errors[$field] = $message ?: "Pole {$field} musí být číslo.";
        }
        return $this;
    }
    
    /**
     * Validace URL
     * @param string $field Název pole
     * @param string $message Vlastní chybová zpráva
     * @return self
     */
    public function url($field, $message = null) {
        if (isset($this->data[$field]) && !empty($this->data[$field])) {
            if (!filter_var($this->data[$field], FILTER_VALIDATE_URL)) {
                $this->errors[$field] = $message ?: "Pole {$field} musí obsahovat platnou URL.";
            }
        }
        return $this;
    }
    
    /**
     * Validace telefonu
     * @param string $field Název pole
     * @param string $message Vlastní chybová zpráva
     * @return self
     */
    public function phone($field, $message = null) {
        if (isset($this->data[$field]) && !empty($this->data[$field])) {
            if (!Utils::isValidPhone($this->data[$field])) {
                $this->errors[$field] = $message ?: "Pole {$field} musí obsahovat platné telefonní číslo.";
            }
        }
        return $this;
    }
    
    /**
     * Validace regulárním výrazem
     * @param string $field Název pole
     * @param string $pattern Regulární výraz
     * @param string $message Vlastní chybová zpráva
     * @return self
     */
    public function pattern($field, $pattern, $message = null) {
        if (isset($this->data[$field]) && !preg_match($pattern, $this->data[$field])) {
            $this->errors[$field] = $message ?: "Pole {$field} má neplatný formát.";
        }
        return $this;
    }
    
    /**
     * Validace hodnoty v seznamu
     * @param string $field Název pole
     * @param array $values Povolené hodnoty
     * @param string $message Vlastní chybová zpráva
     * @return self
     */
    public function in($field, $values, $message = null) {
        if (isset($this->data[$field]) && !in_array($this->data[$field], $values)) {
            $this->errors[$field] = $message ?: "Pole {$field} obsahuje neplatnou hodnotu.";
        }
        return $this;
    }
    
    /**
     * Validace souboru
     * @param string $field Název pole
     * @param array $allowedTypes Povolené MIME typy
     * @param int $maxSize Maximální velikost v bytech
     * @param string $message Vlastní chybová zpráva
     * @return self
     */
    public function file($field, $allowedTypes = [], $maxSize = null, $message = null) {
        if (isset($_FILES[$field]) && $_FILES[$field]['error'] === UPLOAD_ERR_OK) {
            $file = $_FILES[$field];
            
            // Kontrola typu souboru
            if (!empty($allowedTypes) && !in_array($file['type'], $allowedTypes)) {
                $this->errors[$field] = $message ?: "Soubor {$field} má nepovolený typ.";
                return $this;
            }
            
            // Kontrola velikosti
            if ($maxSize && $file['size'] > $maxSize) {
                $this->errors[$field] = $message ?: "Soubor {$field} je příliš velký.";
                return $this;
            }
        }
        return $this;
    }
    
    /**
     * Kontrola, zda validace prošla
     * @return bool True pokud jsou data validní
     */
    public function isValid() {
        return empty($this->errors);
    }
    
    /**
     * Získání chyb
     * @return array Pole chyb
     */
    public function getErrors() {
        return $this->errors;
    }
    
    /**
     * Získání první chyby
     * @return string|null První chyba nebo null
     */
    public function getFirstError() {
        return !empty($this->errors) ? reset($this->errors) : null;
    }
    
    /**
     * Přidání vlastní chyby
     * @param string $field Název pole
     * @param string $message Chybová zpráva
     * @return self
     */
    public function addError($field, $message) {
        $this->errors[$field] = $message;
        return $this;
    }
    
    /**
     * Vyčištění chyb
     * @return self
     */
    public function clearErrors() {
        $this->errors = [];
        return $this;
    }
    
    /**
     * Statická metoda pro rychlou validaci
     * @param array $data Data k validaci
     * @param array $rules Pravidla validace
     * @return array Pole chyb
     */
    public static function validate($data, $rules) {
        $validator = new self($data);
        
        foreach ($rules as $field => $fieldRules) {
            foreach ($fieldRules as $rule => $params) {
                if (method_exists($validator, $rule)) {
                    if (is_array($params)) {
                        call_user_func_array([$validator, $rule], array_merge([$field], $params));
                    } else {
                        $validator->$rule($field, $params);
                    }
                }
            }
        }
        
        return $validator->getErrors();
    }
}
