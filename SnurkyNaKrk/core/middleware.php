<?php
/**
 * Bezpečnostní middleware pro snurkynakrk.cz
 * 
 * Zajišťuje ochranu admin stránek a validaci vstupních dat
 */

class SecurityMiddleware {
    private $auth;
    private $allowed_file_types = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'pdf', 'doc', 'docx'];
    private $max_file_size = 5242880; // 5MB
    
    public function __construct($auth) {
        $this->auth = $auth;
    }
    
    /**
     * Kontrola admin přístupu - middleware pro admin stránky
     */
    public function requireAdmin() {
        if (!$this->auth->isLoggedIn()) {
            // Pokud je to AJAX požadavek, vrátíme JSON
            if ($this->isAjaxRequest()) {
                http_response_code(401);
                header('Content-Type: application/json');
                echo json_encode(['error' => 'Neautorizovaný přístup']);
                exit;
            }
            
            // Jinak přesměrujeme na login
            header('Location: login.php');
            exit;
        }
    }
    
    /**
     * Kontrola CSRF tokenu pro POST požadavky
     */
    public function validateCSRF() {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $token = $_POST['csrf_token'] ?? '';
            if (!$this->auth->validateCSRFToken($token)) {
                if ($this->isAjaxRequest()) {
                    http_response_code(403);
                    header('Content-Type: application/json');
                    echo json_encode(['error' => 'Neplatný bezpečnostní token']);
                    exit;
                }
                
                die('Neplatný bezpečnostní token');
            }
        }
    }
    
    /**
     * Sanitizace vstupních dat
     */
    public function sanitizeInput($data) {
        if (is_array($data)) {
            return array_map([$this, 'sanitizeInput'], $data);
        }
        
        if (is_string($data)) {
            // Odstranění nebezpečných znaků
            $data = trim($data);
            $data = stripslashes($data);
            $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
            return $data;
        }
        
        return $data;
    }
    
    /**
     * Validace a sanitizace HTML obsahu (pro WYSIWYG editor)
     */
    public function sanitizeHTML($html) {
        // Povolené HTML tagy pro obsah
        $allowed_tags = '<p><br><strong><b><em><i><u><h1><h2><h3><h4><h5><h6><ul><ol><li><a><img><blockquote><code><pre>';
        
        // Základní čištění
        $html = strip_tags($html, $allowed_tags);
        
        // Odstranění nebezpečných atributů
        $html = preg_replace('/(<[^>]+)\s+(on\w+|javascript:|vbscript:|data:)[^>]*>/i', '$1>', $html);
        
        return $html;
    }
    
    /**
     * Validace nahrávaného souboru
     */
    public function validateFileUpload($file) {
        $errors = [];
        
        // Kontrola zda byl soubor nahrán
        if (!isset($file['tmp_name']) || !is_uploaded_file($file['tmp_name'])) {
            $errors[] = 'Soubor nebyl správně nahrán';
            return $errors;
        }
        
        // Kontrola velikosti souboru
        if ($file['size'] > $this->max_file_size) {
            $errors[] = 'Soubor je příliš velký (max ' . ($this->max_file_size / 1024 / 1024) . 'MB)';
        }
        
        // Kontrola typu souboru podle přípony
        $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!in_array($file_extension, $this->allowed_file_types)) {
            $errors[] = 'Nepodporovaný typ souboru. Povolené: ' . implode(', ', $this->allowed_file_types);
        }
        
        // Kontrola MIME typu
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mime_type = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);
        
        $allowed_mime_types = [
            'image/jpeg', 'image/png', 'image/gif', 'image/webp',
            'application/pdf', 'application/msword', 
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        ];
        
        if (!in_array($mime_type, $allowed_mime_types)) {
            $errors[] = 'Nepodporovaný MIME typ souboru';
        }
        
        // Kontrola na škodlivý obsah v obrázcích
        if (in_array($mime_type, ['image/jpeg', 'image/png', 'image/gif'])) {
            $image_info = getimagesize($file['tmp_name']);
            if ($image_info === false) {
                $errors[] = 'Soubor není platný obrázek';
            }
        }
        
        return $errors;
    }
    
    /**
     * Bezpečné přejmenování souboru
     */
    public function generateSafeFilename($original_name, $prefix = '') {
        // Získání přípony
        $extension = strtolower(pathinfo($original_name, PATHINFO_EXTENSION));
        
        // Generování bezpečného názvu
        $safe_name = $prefix . date('Y-m-d_H-i-s') . '_' . uniqid();
        
        return $safe_name . '.' . $extension;
    }
    
    /**
     * Kontrola zda je požadavek AJAX
     */
    private function isAjaxRequest() {
        return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
               strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
    }
    
    /**
     * Validace URL
     */
    public function validateURL($url) {
        return filter_var($url, FILTER_VALIDATE_URL) !== false;
    }
    
    /**
     * Validace emailu
     */
    public function validateEmail($email) {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }
    
    /**
     * Validace telefonního čísla (české formáty)
     */
    public function validatePhone($phone) {
        // Odstranění mezer a pomlček
        $phone = preg_replace('/[\s\-]/', '', $phone);
        
        // České formáty: +420..., 00420..., nebo 9 číslic začínajících 2-7
        $patterns = [
            '/^\+420[2-7]\d{8}$/',
            '/^00420[2-7]\d{8}$/',
            '/^[2-7]\d{8}$/'
        ];
        
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $phone)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Rate limiting pro obecné akce
     */
    public function checkActionRateLimit($action, $max_attempts = 10, $time_window = 300) {
        $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $cache_key = "rate_limit_{$action}_{$ip}";
        
        if (!isset($_SESSION[$cache_key])) {
            $_SESSION[$cache_key] = [];
        }
        
        $attempts = $_SESSION[$cache_key];
        $current_time = time();
        
        // Vyčištění starých pokusů
        $attempts = array_filter($attempts, function($timestamp) use ($current_time, $time_window) {
            return ($current_time - $timestamp) < $time_window;
        });
        
        $_SESSION[$cache_key] = $attempts;
        
        if (count($attempts) >= $max_attempts) {
            return false;
        }
        
        // Zaznamenání nového pokusu
        $_SESSION[$cache_key][] = $current_time;
        return true;
    }
    
    /**
     * Bezpečnostní hlavičky
     */
    public function setSecurityHeaders() {
        // Ochrana proti XSS
        header('X-XSS-Protection: 1; mode=block');
        
        // Ochrana proti clickjacking
        header('X-Frame-Options: DENY');
        
        // Ochrana proti MIME type sniffing
        header('X-Content-Type-Options: nosniff');
        
        // Referrer policy
        header('Referrer-Policy: strict-origin-when-cross-origin');
        
        // Content Security Policy (základní)
        header("Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data:;");
    }
    
    /**
     * Logování bezpečnostních událostí
     */
    public function logSecurityEvent($event, $details = '') {
        $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
        $request_uri = $_SERVER['REQUEST_URI'] ?? 'unknown';
        
        $message = "Security Event: $event | Details: $details | IP: $ip | URI: $request_uri | User-Agent: $user_agent";
        
        if (function_exists('log_message')) {
            log_message($message, 'SECURITY');
        }
    }
    
    /**
     * Kontrola síly hesla
     */
    public function validatePasswordStrength($password) {
        $errors = [];
        
        if (strlen($password) < 8) {
            $errors[] = 'Heslo musí mít alespoň 8 znaků';
        }
        
        if (!preg_match('/[A-Z]/', $password)) {
            $errors[] = 'Heslo musí obsahovat alespoň jedno velké písmeno';
        }
        
        if (!preg_match('/[a-z]/', $password)) {
            $errors[] = 'Heslo musí obsahovat alespoň jedno malé písmeno';
        }
        
        if (!preg_match('/[0-9]/', $password)) {
            $errors[] = 'Heslo musí obsahovat alespoň jednu číslici';
        }
        
        if (!preg_match('/[^A-Za-z0-9]/', $password)) {
            $errors[] = 'Heslo musí obsahovat alespoň jeden speciální znak';
        }
        
        return $errors;
    }
}
