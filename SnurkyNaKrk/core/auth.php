<?php
/**
 * Autentifikační systém pro snurkynakrk.cz
 * 
 * Z<PERSON><PERSON>š<PERSON>uje be<PERSON>pe<PERSON>, session management a CSRF ochranu
 */

class Auth {
    private $db;
    private $session_timeout;
    private $max_login_attempts = 5;
    private $lockout_time = 900; // 15 minut
    
    public function __construct($database) {
        $this->db = $database;
        $this->session_timeout = 3600; // 1 hodina
        $this->cleanupExpiredSessions();
    }
    
    /**
     * Přihlášení administrátora
     */
    public function login($username, $password) {
        // Kontrola rate limiting
        if (!$this->checkRateLimit()) {
            $this->logSecurityEvent('Rate limit exceeded', $username);
            return false;
        }
        
        // Validace vstupních dat
        if (empty($username) || empty($password)) {
            $this->recordFailedAttempt();
            return false;
        }
        
        // Kontrola př<PERSON><PERSON><PERSON><PERSON>
        if ($this->validateCredentials($username, $password)) {
            $this->createSession($username);
            $this->clearFailedAttempts();
            $this->logSecurityEvent('Successful login', $username);
            return true;
        } else {
            $this->recordFailedAttempt();
            $this->logSecurityEvent('Failed login attempt', $username);
            return false;
        }
    }
    
    /**
     * Kontrola přihlašovacích údajů
     */
    private function validateCredentials($username, $password) {
        // Pro jednoduchost používáme hardcoded admin údaje
        // V produkci by měly být v databázi s hash heslem
        $admin_username = 'admin';
        $admin_password = 'snurky2024!';
        
        return ($username === $admin_username && $password === $admin_password);
    }
    
    /**
     * Vytvoření admin session
     */
    private function createSession($username) {
        // Regenerace session ID pro bezpečnost
        session_regenerate_id(true);
        
        // Nastavení session dat
        $_SESSION['admin_logged_in'] = true;
        $_SESSION['admin_username'] = $username;
        $_SESSION['login_time'] = time();
        $_SESSION['last_activity'] = time();
        $_SESSION['csrf_token'] = $this->generateRandomToken();
        
        // Uložení do databáze
        $session_id = session_id();
        $expires_at = date('Y-m-d H:i:s', time() + $this->session_timeout);
        
        $user_data = json_encode(['username' => $username]);

        $this->db->insert(
            "INSERT INTO admin_sessions (session_id, user_data, ip_address, user_agent, expires_at)
             VALUES (?, ?, ?, ?, ?)",
            [
                $session_id,
                $user_data,
                $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
                $expires_at
            ]
        );
    }
    
    /**
     * Kontrola zda je uživatel přihlášen
     */
    public function isLoggedIn() {
        // Kontrola session existence
        if (!isset($_SESSION['admin_logged_in']) || !$_SESSION['admin_logged_in']) {
            return false;
        }
        
        // Kontrola timeout
        if (isset($_SESSION['last_activity'])) {
            if (time() - $_SESSION['last_activity'] > $this->session_timeout) {
                $this->logout();
                return false;
            }
            $_SESSION['last_activity'] = time();
        }
        
        // Kontrola session v databázi
        $session_id = session_id();
        $session = $this->db->select(
            "SELECT * FROM admin_sessions WHERE session_id = ? AND expires_at > ?",
            [$session_id, date('Y-m-d H:i:s')]
        );
        
        if (empty($session)) {
            $this->logout();
            return false;
        }
        
        return true;
    }
    
    /**
     * Odhlášení uživatele
     */
    public function logout() {
        // Smazání session z databáze
        if (isset($_SESSION['admin_logged_in'])) {
            $session_id = session_id();
            $this->db->delete("DELETE FROM admin_sessions WHERE session_id = ?", [$session_id]);
            
            $this->logSecurityEvent('User logout', $_SESSION['admin_username'] ?? 'unknown');
        }
        
        // Vyčištění session dat
        $_SESSION = [];
        
        // Smazání session cookie
        if (ini_get("session.use_cookies")) {
            $params = session_get_cookie_params();
            setcookie(session_name(), '', time() - 42000,
                $params["path"], $params["domain"],
                $params["secure"], $params["httponly"]
            );
        }
        
        // Zničení session
        session_destroy();
    }
    
    /**
     * Generování CSRF tokenu
     */
    public function generateCSRFToken() {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = $this->generateRandomToken();
        }
        return $_SESSION['csrf_token'];
    }
    
    /**
     * Validace CSRF tokenu
     */
    public function validateCSRFToken($token) {
        if (!isset($_SESSION['csrf_token']) || empty($token)) {
            return false;
        }
        
        return hash_equals($_SESSION['csrf_token'], $token);
    }
    
    /**
     * Kontrola rate limiting
     */
    private function checkRateLimit() {
        $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $cache_key = "login_attempts_$ip";
        
        if (!isset($_SESSION[$cache_key])) {
            $_SESSION[$cache_key] = [];
        }
        
        $attempts = $_SESSION[$cache_key];
        $current_time = time();
        
        // Vyčištění starých pokusů
        $attempts = array_filter($attempts, function($timestamp) use ($current_time) {
            return ($current_time - $timestamp) < $this->lockout_time;
        });
        
        $_SESSION[$cache_key] = $attempts;
        
        return count($attempts) < $this->max_login_attempts;
    }
    
    /**
     * Zaznamenání neúspěšného pokusu
     */
    private function recordFailedAttempt() {
        $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $cache_key = "login_attempts_$ip";
        
        if (!isset($_SESSION[$cache_key])) {
            $_SESSION[$cache_key] = [];
        }
        
        $_SESSION[$cache_key][] = time();
    }
    
    /**
     * Vyčištění neúspěšných pokusů
     */
    private function clearFailedAttempts() {
        $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $cache_key = "login_attempts_$ip";
        unset($_SESSION[$cache_key]);
    }
    
    /**
     * Vyčištění vypršených sessions
     */
    private function cleanupExpiredSessions() {
        $this->db->delete("DELETE FROM admin_sessions WHERE expires_at < ?", [date('Y-m-d H:i:s')]);
    }
    
    /**
     * Generování náhodného tokenu
     */
    private function generateRandomToken() {
        return bin2hex(random_bytes(32));
    }
    
    /**
     * Logování bezpečnostních událostí
     */
    private function logSecurityEvent($event, $username = '') {
        $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
        
        $message = "Security Event: $event | User: $username | IP: $ip | User-Agent: $user_agent";
        
        if (function_exists('log_message')) {
            log_message($message, 'SECURITY');
        }
    }
    
    /**
     * Získání informací o aktuální session
     */
    public function getSessionInfo() {
        if (!$this->isLoggedIn()) {
            return null;
        }
        
        return [
            'username' => $_SESSION['admin_username'] ?? '',
            'login_time' => $_SESSION['login_time'] ?? 0,
            'last_activity' => $_SESSION['last_activity'] ?? 0,
            'session_id' => session_id()
        ];
    }
}
