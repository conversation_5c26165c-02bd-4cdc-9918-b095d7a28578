<?php
/**
 * Konfigurační soubor pro snurkynakrk.cz
 * 
 * Obsahuje všechna základní nastavení aplikace včetně:
 * - Databázového připojení
 * - Bezpečnostních nastavení
 * - E-shop konfigurace
 * - Cest k souborům
 */

// Zabránění přímému přístupu
if (!defined('SNURKY_INIT')) {
    die('Přímý přístup není povolen');
}

// =====================================================
// DATABÁZOVÉ PŘIPOJENÍ
// =====================================================
define('DB_HOST', 'uvds125.active24.cz');
define('DB_NAME', 'snurkynakr');
define('DB_USER', 'snurkynakr');
define('DB_PASS', 'GKkcdg9m');
define('DB_CHARSET', 'utf8mb4');
define('DB_COLLATE', 'utf8mb4_unicode_ci');

// =====================================================
// BEZPEČNOSTNÍ NASTAVENÍ
// =====================================================

// Session nastavení
define('ADMIN_SESSION_TIMEOUT', 3600); // 1 hodina v sekundách
define('SESSION_NAME', 'snurky_admin_session');
define('SESSION_SECURE', false); // Nastavit na true pro HTTPS
define('SESSION_HTTPONLY', true);

// CSRF ochrana
define('CSRF_TOKEN_NAME', 'csrf_token');
define('CSRF_TOKEN_TIMEOUT', 1800); // 30 minut

// Upload nastavení
define('MAX_UPLOAD_SIZE', 5 * 1024 * 1024); // 5MB v bytech
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'webp', 'gif']);
define('ALLOWED_MIME_TYPES', [
    'image/jpeg',
    'image/png', 
    'image/webp',
    'image/gif'
]);

// Rate limiting
define('LOGIN_ATTEMPTS_LIMIT', 5);
define('LOGIN_LOCKOUT_TIME', 900); // 15 minut

// =====================================================
// E-SHOP NASTAVENÍ
// =====================================================

// Základní URL (bude nastaveno v admin panelu)
define('ESHOP_BASE_URL', ''); 

// UTM parametry pro tracking
define('UTM_SOURCE', 'snurkynakrk');
define('UTM_MEDIUM', 'website');
define('UTM_CAMPAIGN', 'product_link');

// Timeout pro validaci odkazů
define('LINK_VALIDATION_TIMEOUT', 5); // sekundy
define('LINK_VALIDATION_ENABLED', true);

// =====================================================
// CESTY K SOUBORŮM
// =====================================================

// Základní cesty
define('ROOT_PATH', dirname(__DIR__));
define('CORE_PATH', __DIR__);
define('ADMIN_PATH', ROOT_PATH . '/admin');
define('ASSETS_PATH', ROOT_PATH . '/assets');
define('UPLOAD_PATH', ROOT_PATH . '/uploads');

// Upload složky
define('UPLOAD_PRODUCTS_PATH', UPLOAD_PATH . '/products');
define('UPLOAD_TEAM_PATH', UPLOAD_PATH . '/team');
define('UPLOAD_CONTENT_PATH', UPLOAD_PATH . '/content');

// URL cesty (relativní k document root)
define('ASSETS_URL', '/assets');
define('UPLOAD_URL', '/uploads');
define('UPLOAD_PRODUCTS_URL', UPLOAD_URL . '/products');
define('UPLOAD_TEAM_URL', UPLOAD_URL . '/team');
define('UPLOAD_CONTENT_URL', UPLOAD_URL . '/content');

// =====================================================
// APLIKAČNÍ NASTAVENÍ
// =====================================================

// Obecné nastavení
define('SITE_NAME', 'Snurky na krk');
define('SITE_VERSION', '1.0.0');
define('ADMIN_EMAIL', ''); // Bude nastaveno v admin panelu

// Debug režim (POUZE pro development!)
define('DEBUG_MODE', false);
define('SHOW_ERRORS', false);

// Timezone
define('DEFAULT_TIMEZONE', 'Europe/Prague');
date_default_timezone_set(DEFAULT_TIMEZONE);

// Locale nastavení
define('DEFAULT_LOCALE', 'cs_CZ.UTF-8');
setlocale(LC_ALL, DEFAULT_LOCALE);

// =====================================================
// CACHE NASTAVENÍ
// =====================================================

// Cache doba platnosti (v sekundách)
define('CACHE_CONTENT_TTL', 3600); // 1 hodina
define('CACHE_PRODUCTS_TTL', 1800); // 30 minut
define('CACHE_TEAM_TTL', 7200); // 2 hodiny

// Cache složka
define('CACHE_PATH', ROOT_PATH . '/cache');

// =====================================================
// SEO A ANALYTICS
// =====================================================

// Meta tagy
define('DEFAULT_META_DESCRIPTION', 'Snurky na krk - jedinečné produkty pro váš krk');
define('DEFAULT_META_KEYWORDS', 'snurky, krk, produkty, e-shop');
define('DEFAULT_OG_IMAGE', ASSETS_URL . '/images/og-image.jpg');

// Analytics (bude nastaveno v admin panelu)
define('GOOGLE_ANALYTICS_ID', '');
define('GOOGLE_TAG_MANAGER_ID', '');

// =====================================================
// ERROR HANDLING
// =====================================================

// Error reporting
if (DEBUG_MODE) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// Log soubory
define('ERROR_LOG_PATH', ROOT_PATH . '/logs');
define('ERROR_LOG_FILE', ERROR_LOG_PATH . '/error.log');
define('ACCESS_LOG_FILE', ERROR_LOG_PATH . '/access.log');

// =====================================================
// PERFORMANCE NASTAVENÍ
// =====================================================

// Komprese
define('GZIP_COMPRESSION', true);

// Browser cache (v sekundách)
define('STATIC_CACHE_TIME', 86400); // 24 hodin
define('IMAGE_CACHE_TIME', 604800); // 7 dní

// =====================================================
// INICIALIZACE
// =====================================================

// Vytvoření potřebných složek pokud neexistují
$required_dirs = [
    UPLOAD_PATH,
    UPLOAD_PRODUCTS_PATH,
    UPLOAD_TEAM_PATH,
    UPLOAD_CONTENT_PATH,
    ERROR_LOG_PATH,
    CACHE_PATH
];

foreach ($required_dirs as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
}

// Nastavení error logu
if (!DEBUG_MODE) {
    ini_set('log_errors', 1);
    ini_set('error_log', ERROR_LOG_FILE);
}

// =====================================================
// BEZPEČNOSTNÍ HLAVIČKY
// =====================================================

// Pouze pokud nejsme v CLI režimu
if (php_sapi_name() !== 'cli') {
    // Zabránění clickjacking
    header('X-Frame-Options: SAMEORIGIN');
    
    // XSS ochrana
    header('X-XSS-Protection: 1; mode=block');
    
    // Content type sniffing ochrana
    header('X-Content-Type-Options: nosniff');
    
    // Referrer policy
    header('Referrer-Policy: strict-origin-when-cross-origin');
    
    // Content Security Policy (základní)
    if (!DEBUG_MODE) {
        header("Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' https://www.google-analytics.com; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self';");
    }
}

// =====================================================
// KONEC KONFIGURACE
// =====================================================
