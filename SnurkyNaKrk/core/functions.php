<?php
/**
 * Core utility funkce pro snurkynakrk.cz
 * 
 * Obsahuje základní utility funkce pro práci s textem, validací,
 * formátování a další pomocné funkce.
 */

class Utils {
    
    /**
     * Sanitizace HTML výstupu
     * @param string $string Text k sanitizaci
     * @return string Sanitizovaný text
     */
    public static function escape($string) {
        return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
    }
    
    /**
     * Validace emailu
     * @param string $email Email k validaci
     * @return bool True pokud je email validní
     */
    public static function isValidEmail($email) {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }
    
    /**
     * Validace telefonu (český formát)
     * @param string $phone Telefon k validaci
     * @return bool True pokud je telefon validní
     */
    public static function isValidPhone($phone) {
        $phone = preg_replace('/\s+/', '', $phone);
        return preg_match('/^(\+420)?[0-9]{9}$/', $phone);
    }
    
    /**
     * Generování náhodného řetězce
     * @param int $length Délka řetězce
     * @return string Náhodný řetězec
     */
    public static function generateRandomString($length = 32) {
        return bin2hex(random_bytes($length / 2));
    }
    
    /**
     * Slug generování z českého textu
     * @param string $text Text k převodu na slug
     * @return string URL-friendly slug
     */
    public static function createSlug($text) {
        $text = mb_strtolower($text, 'UTF-8');
        
        // České znaky na ASCII
        $czech = ['á','č','ď','é','ě','í','ň','ó','ř','š','ť','ú','ů','ý','ž'];
        $ascii = ['a','c','d','e','e','i','n','o','r','s','t','u','u','y','z'];
        $text = str_replace($czech, $ascii, $text);
        
        // Pouze alfanumerické znaky a pomlčky
        $text = preg_replace('/[^a-z0-9]+/', '-', $text);
        $text = trim($text, '-');
        
        return $text;
    }
    
    /**
     * Formátování velikosti souboru
     * @param int $bytes Velikost v bytech
     * @return string Formátovaná velikost
     */
    public static function formatFileSize($bytes) {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }
    
    /**
     * Zkrácení textu s třemi tečkami
     * @param string $text Text k zkrácení
     * @param int $length Maximální délka
     * @param string $suffix Přípona (výchozí ...)
     * @return string Zkrácený text
     */
    public static function truncateText($text, $length = 100, $suffix = '...') {
        if (mb_strlen($text) <= $length) {
            return $text;
        }
        
        return mb_substr($text, 0, $length) . $suffix;
    }
    
    /**
     * Redirect s exit
     * @param string $url URL pro přesměrování
     * @param int $statusCode HTTP status kód
     */
    public static function redirect($url, $statusCode = 302) {
        header("Location: {$url}", true, $statusCode);
        exit;
    }
    
    /**
     * JSON response
     * @param mixed $data Data k odeslání
     * @param int $statusCode HTTP status kód
     */
    public static function jsonResponse($data, $statusCode = 200) {
        http_response_code($statusCode);
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode($data, JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    /**
     * Validace CSRF tokenu
     * @param string $token Token k validaci
     * @return bool True pokud je token validní
     */
    public static function validateCSRFToken($token) {
        return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
    }
    
    /**
     * Generování CSRF tokenu
     * @return string CSRF token
     */
    public static function generateCSRFToken() {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = self::generateRandomString(32);
        }
        return $_SESSION['csrf_token'];
    }
    
    /**
     * Sanitizace názvu souboru
     * @param string $filename Název souboru
     * @return string Sanitizovaný název
     */
    public static function sanitizeFilename($filename) {
        // Odstranění nebezpečných znaků
        $filename = preg_replace('/[^a-zA-Z0-9._-]/', '', $filename);
        
        // Omezení délky
        if (strlen($filename) > 255) {
            $filename = substr($filename, 0, 255);
        }
        
        return $filename;
    }
    
    /**
     * Kontrola, zda je soubor obrázek
     * @param string $filename Název souboru
     * @return bool True pokud je obrázek
     */
    public static function isImageFile($filename) {
        $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
        $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
        return in_array($extension, $allowedExtensions);
    }
    
    /**
     * Formátování českého data
     * @param string $date Datum ve formátu Y-m-d H:i:s
     * @return string Formátované datum
     */
    public static function formatCzechDate($date) {
        $timestamp = strtotime($date);
        return date('d.m.Y H:i', $timestamp);
    }
}
