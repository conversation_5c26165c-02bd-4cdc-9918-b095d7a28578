<?php
/**
 * FTP Deployment script pro snurkynakrk.cz
 * 
 * <PERSON>ké nahrávání souborů na produkční server
 */

// FTP konfigurace
define('FTP_HOST', 'snurkynakrk.cz');
define('FTP_USER', 'snurkynakr');
define('FTP_PASS', 'mvUFSdLTeL');
define('FTP_ROOT', '/www/');

class FTPDeployer {
    private $connection;
    private $host;
    private $username;
    private $password;
    private $root_path;
    
    public function __construct($host, $username, $password, $root_path = '/') {
        $this->host = $host;
        $this->username = $username;
        $this->password = $password;
        $this->root_path = rtrim($root_path, '/') . '/';
    }
    
    /**
     * Připojení k FTP serveru
     */
    public function connect() {
        $this->connection = ftp_connect($this->host);
        
        if (!$this->connection) {
            throw new Exception("Nelze se připojit k FTP serveru: {$this->host}");
        }
        
        $login = ftp_login($this->connection, $this->username, $this->password);
        
        if (!$login) {
            throw new Exception("Neplatné FTP přihlašovací údaje");
        }
        
        // Nastavení pasivního režimu
        ftp_pasv($this->connection, true);
        
        // Přechod do root složky
        if (!ftp_chdir($this->connection, $this->root_path)) {
            throw new Exception("Nelze přejít do složky: {$this->root_path}");
        }
        
        return true;
    }
    
    /**
     * Odpojení od FTP serveru
     */
    public function disconnect() {
        if ($this->connection) {
            ftp_close($this->connection);
        }
    }
    
    /**
     * Vytvoření složky na FTP serveru
     */
    public function createDirectory($dir) {
        $parts = explode('/', trim($dir, '/'));
        $current_path = '';
        
        foreach ($parts as $part) {
            if (empty($part)) continue;
            
            $current_path .= $part . '/';
            
            // Zkusíme přejít do složky
            if (!@ftp_chdir($this->connection, $current_path)) {
                // Pokud neexistuje, vytvoříme ji
                if (!@ftp_mkdir($this->connection, $current_path)) {
                    throw new Exception("Nelze vytvořit složku: {$current_path}");
                }
                echo "✅ Vytvořena složka: {$current_path}\n";
            }
        }
        
        // Vrátíme se do root složky
        ftp_chdir($this->connection, $this->root_path);
    }
    
    /**
     * Nahrání souboru na FTP server
     */
    public function uploadFile($local_file, $remote_file) {
        if (!file_exists($local_file)) {
            throw new Exception("Lokální soubor neexistuje: {$local_file}");
        }
        
        // Vytvoření složky pokud neexistuje
        $remote_dir = dirname($remote_file);
        if ($remote_dir !== '.') {
            $this->createDirectory($remote_dir);
        }
        
        // Určení módu přenosu podle přípony
        $mode = $this->getTransferMode($local_file);
        
        $upload = ftp_put($this->connection, $remote_file, $local_file, $mode);
        
        if (!$upload) {
            throw new Exception("Chyba při nahrávání souboru: {$local_file} -> {$remote_file}");
        }
        
        return true;
    }
    
    /**
     * Určení módu přenosu podle typu souboru
     */
    private function getTransferMode($file) {
        $extension = strtolower(pathinfo($file, PATHINFO_EXTENSION));
        
        $binary_extensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'pdf', 'zip', 'rar'];
        
        return in_array($extension, $binary_extensions) ? FTP_BINARY : FTP_ASCII;
    }
    
    /**
     * Nahrání celé složky
     */
    public function uploadDirectory($local_dir, $remote_dir = '') {
        $local_dir = rtrim($local_dir, '/');
        $remote_dir = trim($remote_dir, '/');
        
        if (!is_dir($local_dir)) {
            throw new Exception("Lokální složka neexistuje: {$local_dir}");
        }
        
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($local_dir, RecursiveDirectoryIterator::SKIP_DOTS),
            RecursiveIteratorIterator::SELF_FIRST
        );
        
        foreach ($iterator as $file) {
            $local_path = $file->getPathname();
            $relative_path = substr($local_path, strlen($local_dir) + 1);
            $remote_path = $remote_dir ? $remote_dir . '/' . $relative_path : $relative_path;
            
            // Normalizace cest (Windows -> Unix)
            $remote_path = str_replace('\\', '/', $remote_path);
            
            if ($file->isFile()) {
                echo "📤 Nahrávám: {$relative_path}\n";
                $this->uploadFile($local_path, $remote_path);
                echo "✅ Nahráno: {$remote_path}\n";
            }
        }
    }
    
    /**
     * Kontrola připojení
     */
    public function testConnection() {
        try {
            $this->connect();
            $pwd = ftp_pwd($this->connection);
            echo "✅ FTP připojení úspěšné\n";
            echo "📁 Aktuální složka: {$pwd}\n";
            
            // Výpis obsahu složky
            $files = ftp_nlist($this->connection, '.');
            if ($files) {
                echo "📋 Obsah složky:\n";
                foreach ($files as $file) {
                    echo "   - {$file}\n";
                }
            }
            
            $this->disconnect();
            return true;
        } catch (Exception $e) {
            echo "❌ Chyba FTP připojení: " . $e->getMessage() . "\n";
            return false;
        }
    }
}

// Definice souborů k nahrání
$deployment_files = [
    // Core soubory
    'core/config.php' => 'core/config.php',
    'core/database.php' => 'core/database.php',
    'core/init.php' => 'core/init.php',
    'core/auth.php' => 'core/auth.php',
    'core/middleware.php' => 'core/middleware.php',
    
    // Admin soubory
    'admin/login.php' => 'admin/login.php',
    'admin/dashboard.php' => 'admin/dashboard.php',
    
    // Assets
    'assets/css/admin.css' => 'assets/css/admin.css',
    
    // Database
    'database_schema.sql' => 'database_schema.sql',
    
    // Test soubory (volitelné)
    'test_database.php' => 'test_database.php',
    'test_auth_basic.php' => 'test_auth_basic.php'
];

// Hlavní deployment funkce
function deployToProduction($files_only = false) {
    echo "🚀 Spouštím deployment na snurkynakrk.cz\n";
    echo "=====================================\n\n";
    
    $deployer = new FTPDeployer(FTP_HOST, FTP_USER, FTP_PASS, FTP_ROOT);
    
    try {
        // Test připojení
        echo "🔗 Testování FTP připojení...\n";
        if (!$deployer->testConnection()) {
            throw new Exception("FTP připojení selhalo");
        }
        
        echo "\n📤 Zahajuji nahrávání souborů...\n";
        $deployer->connect();
        
        global $deployment_files;
        $uploaded = 0;
        $errors = 0;
        
        foreach ($deployment_files as $local => $remote) {
            try {
                if (file_exists($local)) {
                    echo "📤 Nahrávám: {$local} -> {$remote}\n";
                    $deployer->uploadFile($local, $remote);
                    echo "✅ Úspěch: {$remote}\n";
                    $uploaded++;
                } else {
                    echo "⚠️  Soubor neexistuje: {$local}\n";
                }
            } catch (Exception $e) {
                echo "❌ Chyba při nahrávání {$local}: " . $e->getMessage() . "\n";
                $errors++;
            }
        }
        
        $deployer->disconnect();
        
        echo "\n🎉 Deployment dokončen!\n";
        echo "=====================================\n";
        echo "✅ Nahráno souborů: {$uploaded}\n";
        echo "❌ Chyb: {$errors}\n";
        
        if ($errors === 0) {
            echo "\n🌐 Web je dostupný na: https://snurkynakrk.cz\n";
            echo "🔐 Admin přístup: https://snurkynakrk.cz/admin/login.php\n";
            echo "👤 Přihlašovací údaje: admin / snurky2024!\n";
        }
        
    } catch (Exception $e) {
        echo "❌ Deployment selhal: " . $e->getMessage() . "\n";
        return false;
    }
    
    return true;
}

// CLI interface
if (php_sapi_name() === 'cli') {
    $action = $argv[1] ?? 'help';
    
    switch ($action) {
        case 'test':
            echo "🧪 Testování FTP připojení...\n";
            $deployer = new FTPDeployer(FTP_HOST, FTP_USER, FTP_PASS, FTP_ROOT);
            $deployer->testConnection();
            break;
            
        case 'deploy':
            deployToProduction();
            break;
            
        case 'help':
        default:
            echo "FTP Deployment script pro snurkynakrk.cz\n";
            echo "=====================================\n\n";
            echo "Použití:\n";
            echo "  php deploy_ftp.php test    - Test FTP připojení\n";
            echo "  php deploy_ftp.php deploy  - Nahrání všech souborů\n";
            echo "  php deploy_ftp.php help    - Tato nápověda\n\n";
            echo "FTP údaje:\n";
            echo "  Host: " . FTP_HOST . "\n";
            echo "  User: " . FTP_USER . "\n";
            echo "  Path: " . FTP_ROOT . "\n";
            break;
    }
} else {
    // Web interface
    echo "<!DOCTYPE html>
    <html lang='cs'>
    <head>
        <meta charset='UTF-8'>
        <title>FTP Deployment - snurkynakrk.cz</title>
        <style>
            body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }
            .btn { padding: 10px 20px; margin: 10px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; }
            .success { color: #28a745; }
            .error { color: #dc3545; }
            pre { background: #f8f9fa; padding: 15px; border-radius: 5px; }
        </style>
    </head>
    <body>
        <h1>🚀 FTP Deployment - snurkynakrk.cz</h1>";
    
    if (isset($_GET['action'])) {
        echo "<pre>";
        if ($_GET['action'] === 'test') {
            $deployer = new FTPDeployer(FTP_HOST, FTP_USER, FTP_PASS, FTP_ROOT);
            $deployer->testConnection();
        } elseif ($_GET['action'] === 'deploy') {
            deployToProduction();
        }
        echo "</pre>";
    }
    
    echo "
        <p>Vyberte akci:</p>
        <a href='?action=test' class='btn'>🧪 Test FTP připojení</a>
        <a href='?action=deploy' class='btn'>🚀 Deploy na produkci</a>
        
        <h3>ℹ️ Informace o deploymentu:</h3>
        <ul>
            <li><strong>Cílový server:</strong> snurkynakrk.cz</li>
            <li><strong>FTP host:</strong> " . FTP_HOST . "</li>
            <li><strong>Cílová složka:</strong> " . FTP_ROOT . "</li>
            <li><strong>Počet souborů:</strong> " . count($deployment_files) . "</li>
        </ul>
        
        <h3>📋 Soubory k nahrání:</h3>
        <ul>";
    
    foreach ($deployment_files as $local => $remote) {
        $exists = file_exists($local) ? '✅' : '❌';
        echo "<li>{$exists} {$local} → {$remote}</li>";
    }
    
    echo "</ul>
    </body>
    </html>";
}
?>
