<?php
/**
 * Test databázového připojení a vytvoření schématu
 * 
 * Tento soubor:
 * 1. Testuje připojení k databázi
 * 2. Vytvoří databázové schéma
 * 3. Vloží základní data
 * 4. Ově<PERSON><PERSON>
 */

// Inicializace aplikace
define('SNURKY_INIT', true);

// Načtení konfigurace
require_once 'core/config.php';
require_once 'core/database.php';

// Nastavení pro zobrazení chyb během testu
ini_set('display_errors', 1);
error_reporting(E_ALL);

echo "<h1>🧪 Test databázového připojení - Snurky na krk</h1>\n";
echo "<pre>\n";

// =====================================================
// 1. Test základního připojení
// =====================================================
echo "1. Testování připojení k databázi...\n";
echo "   Host: " . DB_HOST . "\n";
echo "   Databáze: " . DB_NAME . "\n";
echo "   Uživatel: " . DB_USER . "\n";

try {
    $db = Database::getInstance();
    
    if ($db->testConnection()) {
        echo "   ✅ Připojení k databázi úspěšné!\n\n";
    } else {
        echo "   ❌ Připojení k databázi selhalo!\n\n";
        exit(1);
    }
    
} catch (Exception $e) {
    echo "   ❌ Chyba připojení: " . $e->getMessage() . "\n\n";
    exit(1);
}

// =====================================================
// 2. Načtení a spuštění SQL schématu
// =====================================================
echo "2. Vytváření databázového schématu...\n";

try {
    // Načtení SQL souboru
    $sql_file = 'database_schema.sql';
    
    if (!file_exists($sql_file)) {
        throw new Exception("SQL soubor '$sql_file' nebyl nalezen");
    }
    
    $sql_content = file_get_contents($sql_file);
    
    if ($sql_content === false) {
        throw new Exception("Nelze načíst obsah SQL souboru");
    }
    
    echo "   📄 SQL soubor načten (" . strlen($sql_content) . " bytů)\n";
    
    // Rozdělení na jednotlivé příkazy
    $sql_statements = array_filter(
        array_map('trim', explode(';', $sql_content)),
        function($stmt) {
            return !empty($stmt) && !preg_match('/^--/', $stmt);
        }
    );
    
    echo "   📊 Nalezeno " . count($sql_statements) . " SQL příkazů\n";
    
    // Spuštění jednotlivých příkazů
    $pdo = $db->getConnection();
    $success_count = 0;
    
    foreach ($sql_statements as $statement) {
        if (trim($statement)) {
            try {
                $pdo->exec($statement);
                $success_count++;
            } catch (PDOException $e) {
                // Ignorujeme chyby typu "table already exists"
                if (strpos($e->getMessage(), 'already exists') === false) {
                    echo "   ⚠️  Chyba při spouštění SQL: " . $e->getMessage() . "\n";
                }
            }
        }
    }
    
    echo "   ✅ Úspěšně spuštěno $success_count SQL příkazů\n\n";
    
} catch (Exception $e) {
    echo "   ❌ Chyba při vytváření schématu: " . $e->getMessage() . "\n\n";
    exit(1);
}

// =====================================================
// 3. Ověření vytvořených tabulek
// =====================================================
echo "3. Ověřování vytvořených tabulek...\n";

$expected_tables = [
    'content_sections',
    'products', 
    'team_members',
    'media_files',
    'eshop_settings',
    'admin_sessions',
    'site_settings'
];

try {
    $tables = $db->select("SHOW TABLES");
    $existing_tables = array_column($tables, 'Tables_in_' . DB_NAME);
    
    echo "   📋 Nalezené tabulky:\n";
    
    foreach ($expected_tables as $table) {
        if (in_array($table, $existing_tables)) {
            echo "   ✅ $table\n";
        } else {
            echo "   ❌ $table (chybí!)\n";
        }
    }
    
    echo "\n";
    
} catch (Exception $e) {
    echo "   ❌ Chyba při ověřování tabulek: " . $e->getMessage() . "\n\n";
}

// =====================================================
// 4. Test základních operací
// =====================================================
echo "4. Testování základních databázových operací...\n";

try {
    // Test SELECT
    $content_sections = $db->select("SELECT * FROM content_sections LIMIT 5");
    echo "   ✅ SELECT: Načteno " . count($content_sections) . " obsahových sekcí\n";
    
    // Test INSERT
    $test_id = $db->insert(
        "INSERT INTO content_sections (section_key, title, content) VALUES (?, ?, ?)",
        ['test_section', 'Test sekce', 'Test obsah']
    );
    echo "   ✅ INSERT: Vytvořen testovací záznam s ID $test_id\n";
    
    // Test UPDATE
    $updated_rows = $db->update(
        "UPDATE content_sections SET title = ? WHERE id = ?",
        ['Test sekce (upraveno)', $test_id]
    );
    echo "   ✅ UPDATE: Upraveno $updated_rows záznamů\n";
    
    // Test DELETE
    $deleted_rows = $db->delete(
        "DELETE FROM content_sections WHERE id = ?",
        [$test_id]
    );
    echo "   ✅ DELETE: Smazáno $deleted_rows záznamů\n";
    
    echo "\n";
    
} catch (Exception $e) {
    echo "   ❌ Chyba při testování operací: " . $e->getMessage() . "\n\n";
}

// =====================================================
// 5. Ověření základních dat
// =====================================================
echo "5. Ověřování základních dat...\n";

try {
    // Obsahové sekce
    $content_count = $db->selectOne("SELECT COUNT(*) as count FROM content_sections")['count'];
    echo "   📄 Obsahové sekce: $content_count záznamů\n";
    
    // E-shop nastavení
    $eshop_count = $db->selectOne("SELECT COUNT(*) as count FROM eshop_settings")['count'];
    echo "   🛍️  E-shop nastavení: $eshop_count záznamů\n";
    
    // Obecná nastavení
    $site_count = $db->selectOne("SELECT COUNT(*) as count FROM site_settings")['count'];
    echo "   ⚙️  Obecná nastavení: $site_count záznamů\n";
    
    echo "\n";
    
} catch (Exception $e) {
    echo "   ❌ Chyba při ověřování dat: " . $e->getMessage() . "\n\n";
}

// =====================================================
// 6. Statistiky výkonu
// =====================================================
echo "6. Statistiky výkonu...\n";
echo "   📊 Celkem SQL dotazů: " . $db->getQueryCount() . "\n";

if (DEBUG_MODE) {
    $query_log = $db->getQueryLog();
    echo "   🐛 Debug režim: Zaznamenáno " . count($query_log) . " dotazů\n";
}

echo "\n";

// =====================================================
// 7. Závěrečné shrnutí
// =====================================================
echo "🎉 SHRNUTÍ TESTU:\n";
echo "================\n";
echo "✅ Databázové připojení funkční\n";
echo "✅ Schéma úspěšně vytvořeno\n";
echo "✅ Základní data vložena\n";
echo "✅ CRUD operace funkční\n";
echo "\n";
echo "🚀 Databáze je připravena pro použití!\n";
echo "\n";

// Informace o dalších krocích
echo "📋 DALŠÍ KROKY:\n";
echo "===============\n";
echo "1. Implementovat autentifikační systém (Úkol 2)\n";
echo "2. Vytvořit core PHP framework (Úkol 3)\n";
echo "3. Implementovat e-shop integraci (Úkol 4)\n";
echo "\n";

echo "</pre>\n";

// Zobrazení některých základních dat pro kontrolu
echo "<h2>📊 Ukázka základních dat</h2>\n";

try {
    echo "<h3>Obsahové sekce:</h3>\n";
    $sections = $db->select("SELECT section_key, title FROM content_sections ORDER BY sort_order");
    
    if (!empty($sections)) {
        echo "<ul>\n";
        foreach ($sections as $section) {
            echo "<li><strong>" . htmlspecialchars($section['section_key']) . ":</strong> " . 
                 htmlspecialchars($section['title']) . "</li>\n";
        }
        echo "</ul>\n";
    }
    
    echo "<h3>E-shop nastavení:</h3>\n";
    $eshop_settings = $db->select("SELECT setting_key, setting_value, description FROM eshop_settings ORDER BY setting_key");
    
    if (!empty($eshop_settings)) {
        echo "<ul>\n";
        foreach ($eshop_settings as $setting) {
            echo "<li><strong>" . htmlspecialchars($setting['setting_key']) . ":</strong> " . 
                 htmlspecialchars($setting['setting_value']) . " <em>(" . 
                 htmlspecialchars($setting['description']) . ")</em></li>\n";
        }
        echo "</ul>\n";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Chyba při zobrazování dat: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}

echo "<p><strong>✅ Test dokončen úspěšně!</strong></p>\n";
?>
