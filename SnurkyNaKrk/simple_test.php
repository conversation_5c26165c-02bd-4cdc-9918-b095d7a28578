<?php
/**
 * <PERSON><PERSON><PERSON> test bez závislostí
 */

// Zapnutí zobrazování chyb
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔍 Jednoduchý test</h1>";

echo "<h2>1. PHP informace</h2>";
echo "PHP verze: " . PHP_VERSION . "<br>";
echo "Current directory: " . getcwd() . "<br>";
echo "Script path: " . __FILE__ . "<br>";
echo "Document root: " . ($_SERVER['DOCUMENT_ROOT'] ?? 'neznámý') . "<br>";

echo "<h2>2. Test databázového připojení</h2>";

// Přímý test databáze bez našich tříd
$db_host = 'uvds125.active24.cz';
$db_name = 'snurkynakr';
$db_user = 'snurkynakr';
$db_pass = 'GKkcdg9m';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ Databáze připojena úspěšně<br>";
    
    // Test dotazu
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM content_sections");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "✅ Test dotazu: " . $result['count'] . " záznamů v content_sections<br>";
    
} catch (PDOException $e) {
    echo "❌ Chyba databáze: " . $e->getMessage() . "<br>";
}

echo "<h2>3. Test existence souborů</h2>";

$files = [
    'core/config.php',
    'core/database.php',
    'core/init.php',
    'admin/login.php'
];

foreach ($files as $file) {
    if (file_exists($file)) {
        echo "✅ $file existuje (" . filesize($file) . " bytů)<br>";
    } else {
        echo "❌ $file NEEXISTUJE<br>";
    }
}

echo "<h2>4. Test načtení config.php</h2>";

if (file_exists('core/config.php')) {
    try {
        // Definujeme konstantu pro povolení přístupu
        if (!defined('SNURKY_INIT')) {
            define('SNURKY_INIT', true);
        }
        
        require_once 'core/config.php';
        echo "✅ config.php načten<br>";
        
        // Zkontrolujeme konstanty
        if (defined('DB_HOST')) {
            echo "✅ DB_HOST: " . DB_HOST . "<br>";
        } else {
            echo "❌ DB_HOST není definováno<br>";
        }
        
    } catch (Exception $e) {
        echo "❌ Chyba při načítání config.php: " . $e->getMessage() . "<br>";
    }
} else {
    echo "❌ core/config.php neexistuje<br>";
}

echo "<h2>5. Obsah aktuální složky</h2>";
$files = scandir('.');
echo "Soubory a složky:<br>";
foreach ($files as $file) {
    if ($file !== '.' && $file !== '..') {
        $type = is_dir($file) ? '[DIR]' : '[FILE]';
        echo "$type $file<br>";
    }
}

echo "<h2>6. Test session</h2>";
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
$_SESSION['test'] = 'funguje';
if (isset($_SESSION['test'])) {
    echo "✅ Session funguje<br>";
} else {
    echo "❌ Session nefunguje<br>";
}

echo "<h2>7. Velmi jednoduchý login test</h2>";
echo '<form method="POST">';
echo '<input type="text" name="test_user" placeholder="admin" value="admin"><br><br>';
echo '<input type="password" name="test_pass" placeholder="heslo"><br><br>';
echo '<input type="submit" value="Test přihlášení">';
echo '</form>';

if ($_POST) {
    $user = $_POST['test_user'] ?? '';
    $pass = $_POST['test_pass'] ?? '';
    
    if ($user === 'admin' && $pass === 'snurky2024!') {
        echo "<div style='background: green; color: white; padding: 10px;'>✅ Přihlašovací údaje jsou správné!</div>";
    } else {
        echo "<div style='background: red; color: white; padding: 10px;'>❌ Nesprávné přihlašovací údaje</div>";
    }
}

echo "<h2>✅ Test dokončen</h2>";
echo "<p>Pokud všechny testy prošly, problém je v komplexnosti našich souborů.</p>";
?>
