# 🛍️ Úkol 4: E-shop integrace a connector

## 🎯 <PERSON><PERSON>l úko<PERSON>
Implementovat kompletní e-shop integraci s URL buildingem, UTM parametry a link validací

## 📋 Checklist úkolu
- [ ] E-shop connector třída
- [ ] URL building s UTM parametry
- [ ] Link validation systém
- [ ] Click tracking
- [ ] Fallback handling
- [ ] Redirect handler

## 🔗 E-shop connector

### core/eshop-connector.php
```php
<?php
class EshopConnector {
    private $db;
    private $baseUrl;
    private $utmSource;
    private $utmMedium;
    private $utmCampaign;
    private $fallbackUrl;
    
    public function __construct($database) {
        $this->db = $database;
        $this->loadSettings();
    }
    
    // Načtení nastavení z databáze
    private function loadSettings() {
        $settings = $this->db->select('eshop_settings', ['is_active' => 1]);
        
        foreach ($settings as $setting) {
            switch ($setting['setting_key']) {
                case 'base_url':
                    $this->baseUrl = $setting['setting_value'];
                    break;
                case 'utm_source':
                    $this->utmSource = $setting['setting_value'];
                    break;
                case 'utm_medium':
                    $this->utmMedium = $setting['setting_value'];
                    break;
                case 'utm_campaign':
                    $this->utmCampaign = $setting['setting_value'];
                    break;
                case 'fallback_url':
                    $this->fallbackUrl = $setting['setting_value'];
                    break;
            }
        }
        
        // Výchozí hodnoty pokud nejsou nastavené
        $this->utmSource = $this->utmSource ?: UTM_SOURCE;
        $this->utmMedium = $this->utmMedium ?: UTM_MEDIUM;
    }
    
    // Sestavení URL pro produkt
    public function buildProductUrl($productId, $customUrl = null, $additionalParams = []) {
        // Pokud je zadána custom URL, použij ji
        if ($customUrl) {
            $url = $customUrl;
        } else {
            // Jinak sestav URL z base URL a product ID
            $url = rtrim($this->baseUrl, '/') . '/product/' . $productId;
        }
        
        // Přidání UTM parametrů
        $utmParams = [
            'utm_source' => $this->utmSource,
            'utm_medium' => $this->utmMedium
        ];
        
        if ($this->utmCampaign) {
            $utmParams['utm_campaign'] = $this->utmCampaign;
        }
        
        // Sloučení s dodatečnými parametry
        $allParams = array_merge($utmParams, $additionalParams);
        
        // Přidání parametrů k URL
        if (!empty($allParams)) {
            $separator = strpos($url, '?') !== false ? '&' : '?';
            $url .= $separator . http_build_query($allParams);
        }
        
        return $url;
    }
    
    // Validace URL (kontrola dostupnosti)
    public function validateUrl($url, $timeout = 5) {
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_NOBODY => true,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_TIMEOUT => $timeout,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_USERAGENT => 'Snurkynakrk Link Validator 1.0'
        ]);
        
        curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            return [
                'valid' => false,
                'error' => $error,
                'http_code' => 0
            ];
        }
        
        return [
            'valid' => $httpCode >= 200 && $httpCode < 400,
            'http_code' => $httpCode,
            'error' => null
        ];
    }
    
    // Zaznamenání kliku na odkaz
    public function trackClick($productId, $userAgent = '', $referer = '') {
        try {
            // Aktualizace počtu kliků v products tabulce
            $this->db->update('products', 
                ['link_clicks' => 'link_clicks + 1'], 
                ['id' => $productId]
            );
            
            // Detailní tracking do samostatné tabulky (volitelné)
            $this->db->insert('link_clicks', [
                'product_id' => $productId,
                'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
                'user_agent' => $userAgent,
                'referer' => $referer,
                'clicked_at' => date('Y-m-d H:i:s')
            ]);
            
            return true;
        } catch (Exception $e) {
            error_log("Click tracking failed: " . $e->getMessage());
            return false;
        }
    }
    
    // Získání fallback URL
    public function getFallbackUrl() {
        return $this->fallbackUrl ?: $this->baseUrl;
    }
    
    // Aktualizace nastavení
    public function updateSetting($key, $value) {
        return $this->db->update('eshop_settings', 
            ['setting_value' => $value, 'updated_at' => date('Y-m-d H:i:s')],
            ['setting_key' => $key]
        );
    }
    
    // Získání všech nastavení
    public function getSettings() {
        return $this->db->select('eshop_settings', ['is_active' => 1]);
    }
}
```

## 🔄 Redirect handler

### redirect.php
```php
<?php
require_once 'core/config.php';
require_once 'core/database.php';
require_once 'core/eshop-connector.php';

$eshop = new EshopConnector($db);

// Získání parametrů
$productId = $_GET['product'] ?? null;
$type = $_GET['type'] ?? 'product';

if (!$productId) {
    // Přesměrování na fallback URL
    header('Location: ' . $eshop->getFallbackUrl());
    exit;
}

try {
    // Získání produktu z databáze
    $product = $db->select('products', 
        ['id' => $productId, 'is_active' => 1], 
        '*', 
        '', 
        '1'
    );
    
    if (empty($product)) {
        throw new Exception('Produkt nenalezen');
    }
    
    $product = $product[0];
    
    // Sestavení URL
    $targetUrl = $eshop->buildProductUrl(
        $product['eshop_product_id'] ?: $productId,
        $product['eshop_url'],
        ['ref' => 'snurkynakrk']
    );
    
    // Tracking kliku
    $eshop->trackClick(
        $productId,
        $_SERVER['HTTP_USER_AGENT'] ?? '',
        $_SERVER['HTTP_REFERER'] ?? ''
    );
    
    // Přesměrování
    header('Location: ' . $targetUrl, true, 302);
    exit;
    
} catch (Exception $e) {
    error_log("Redirect error: " . $e->getMessage());
    
    // Přesměrování na fallback URL
    header('Location: ' . $eshop->getFallbackUrl());
    exit;
}
```

## 📊 Click tracking tabulka

### SQL pro link_clicks tabulku
```sql
CREATE TABLE link_clicks (
    id INT PRIMARY KEY AUTO_INCREMENT,
    product_id INT NOT NULL,
    ip_address VARCHAR(45),
    user_agent VARCHAR(500),
    referer VARCHAR(500),
    clicked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_product (product_id),
    INDEX idx_date (clicked_at),
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
);
```

## 🎛️ Admin nastavení e-shopu

### admin/eshop-settings.php
```php
<?php
require_once '../core/config.php';
require_once '../core/database.php';
require_once '../core/auth.php';
require_once '../core/eshop-connector.php';

$auth = new Auth($db);
$auth->requireAdmin();

$eshop = new EshopConnector($db);
$message = '';

if ($_POST) {
    try {
        // Aktualizace nastavení
        $settings = [
            'base_url' => trim($_POST['base_url']),
            'utm_source' => trim($_POST['utm_source']),
            'utm_medium' => trim($_POST['utm_medium']),
            'utm_campaign' => trim($_POST['utm_campaign']),
            'fallback_url' => trim($_POST['fallback_url'])
        ];
        
        foreach ($settings as $key => $value) {
            $eshop->updateSetting($key, $value);
        }
        
        $message = 'Nastavení bylo úspěšně uloženo.';
    } catch (Exception $e) {
        $message = 'Chyba při ukládání: ' . $e->getMessage();
    }
}

// Načtení současných nastavení
$currentSettings = [];
foreach ($eshop->getSettings() as $setting) {
    $currentSettings[$setting['setting_key']] = $setting['setting_value'];
}
?>

<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <title>E-shop nastavení - Admin</title>
    <link rel="stylesheet" href="../assets/css/admin.css">
</head>
<body>
    <div class="admin-container">
        <h1>E-shop nastavení</h1>
        
        <?php if ($message): ?>
            <div class="message"><?= Utils::escape($message) ?></div>
        <?php endif; ?>
        
        <form method="POST">
            <div class="form-group">
                <label>Základní URL e-shopu:</label>
                <input type="url" name="base_url" 
                       value="<?= Utils::escape($currentSettings['base_url'] ?? '') ?>" 
                       required>
            </div>
            
            <div class="form-group">
                <label>UTM Source:</label>
                <input type="text" name="utm_source" 
                       value="<?= Utils::escape($currentSettings['utm_source'] ?? 'snurkynakrk') ?>">
            </div>
            
            <div class="form-group">
                <label>UTM Medium:</label>
                <input type="text" name="utm_medium" 
                       value="<?= Utils::escape($currentSettings['utm_medium'] ?? 'website') ?>">
            </div>
            
            <div class="form-group">
                <label>UTM Campaign:</label>
                <input type="text" name="utm_campaign" 
                       value="<?= Utils::escape($currentSettings['utm_campaign'] ?? '') ?>">
            </div>
            
            <div class="form-group">
                <label>Fallback URL:</label>
                <input type="url" name="fallback_url" 
                       value="<?= Utils::escape($currentSettings['fallback_url'] ?? '') ?>">
            </div>
            
            <button type="submit" class="btn-primary">Uložit nastavení</button>
        </form>
    </div>
</body>
</html>
```

## 🚀 Implementační kroky
1. Vytvořit EshopConnector třídu s kompletní funkcionalitou
2. Implementovat redirect.php handler
3. Vytvořit link_clicks tabulku pro tracking
4. Implementovat admin rozhraní pro nastavení
5. Naplnit základní e-shop nastavení do databáze
6. Otestovat URL building a redirecty

## ✅ Kritéria dokončení
- E-shop connector funguje s URL buildingem
- UTM parametry se správně přidávají
- Link validation funguje
- Click tracking zaznamenává kliky
- Redirect handler správně přesměrovává
- Admin rozhraní umožňuje konfiguraci
- Fallback mechanismus funguje při chybách
