<?php
/**
 * Minimální login stránka bez závislostí
 */

// Zapnutí zobrazování chyb
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Spuštění session
session_start();

$error = '';
$success = '';

// Zpracování přihlášení
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = trim($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    
    if (empty($username) || empty($password)) {
        $error = 'Vyplňte prosím všechna pole.';
    } elseif ($username === 'admin' && $password === 'snurky2024!') {
        // Úspěšné přihlášení
        $_SESSION['admin_logged_in'] = true;
        $_SESSION['admin_username'] = $username;
        $_SESSION['login_time'] = time();
        
        $success = 'Přihlášení úspěšné! Přesměrování...';
        header('refresh:2;url=dashboard_minimal.php');
    } else {
        $error = 'Neplatné přihlašovací údaje.';
    }
}

// Kontrola zda je už přihlášen
if (isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in']) {
    header('Location: dashboard_minimal.php');
    exit;
}
?>
<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Minimální Admin Login - Snurkynakrk.cz</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 400px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .login-form {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        input:focus {
            border-color: #007bff;
            outline: none;
        }
        button {
            width: 100%;
            padding: 12px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
            border: 1px solid #f5c6cb;
        }
        .success {
            background: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
            border: 1px solid #c3e6cb;
        }
        .debug-info {
            background: #e9ecef;
            padding: 15px;
            border-radius: 4px;
            margin-top: 20px;
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="login-form">
        <h1>🔐 Minimální Admin Login</h1>
        <p style="text-align: center; color: #666; margin-bottom: 20px;">
            Zjednodušená verze bez závislostí
        </p>
        
        <?php if ($error): ?>
            <div class="error">
                <strong>Chyba:</strong> <?= htmlspecialchars($error) ?>
            </div>
        <?php endif; ?>
        
        <?php if ($success): ?>
            <div class="success">
                <?= htmlspecialchars($success) ?>
            </div>
        <?php endif; ?>
        
        <form method="POST" action="">
            <div class="form-group">
                <label for="username">Uživatelské jméno:</label>
                <input 
                    type="text" 
                    id="username" 
                    name="username" 
                    value="<?= htmlspecialchars($_POST['username'] ?? '') ?>"
                    required 
                    autocomplete="username"
                >
            </div>
            
            <div class="form-group">
                <label for="password">Heslo:</label>
                <input 
                    type="password" 
                    id="password" 
                    name="password" 
                    required 
                    autocomplete="current-password"
                >
            </div>
            
            <button type="submit">Přihlásit se</button>
        </form>
        
        <div class="debug-info">
            <strong>Debug informace:</strong><br>
            PHP verze: <?= PHP_VERSION ?><br>
            Server čas: <?= date('Y-m-d H:i:s') ?><br>
            Session status: <?= session_status() === PHP_SESSION_ACTIVE ? 'Aktivní' : 'Neaktivní' ?><br>
            Current directory: <?= getcwd() ?><br>
            Script path: <?= __FILE__ ?><br>
        </div>
        
        <div style="text-align: center; margin-top: 20px;">
            <p style="font-size: 12px; color: #999;">
                Přihlašovací údaje: <strong>admin</strong> / <strong>snurky2024!</strong>
            </p>
            <p style="font-size: 12px;">
                <a href="../simple_test.php">🔍 Jednoduchý test</a> |
                <a href="debug.php">🔧 Debug admin</a>
            </p>
        </div>
    </div>
</body>
</html>
