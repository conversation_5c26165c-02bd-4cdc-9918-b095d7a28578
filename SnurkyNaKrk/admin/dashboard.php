<?php
/**
 * Admin dashboard pro snurkynakrk.cz
 */

// Načtení základních souborů s absolutní cestou
require_once __DIR__ . '/../core/init.php';
require_once __DIR__ . '/../core/auth.php';
require_once __DIR__ . '/../core/middleware.php';

// Inicializace
$db = Database::getInstance();
$auth = new Auth($db);
$middleware = new SecurityMiddleware($auth);

// Kontrola přihlášení
$middleware->requireAdmin();

// Nastavení bezpečnostních hlaviček
$middleware->setSecurityHeaders();

// Získání informací o session
$session_info = $auth->getSessionInfo();

// Zpracování logout
if (isset($_GET['action']) && $_GET['action'] === 'logout') {
    // Validace CSRF tokenu pro logout
    if (isset($_GET['token']) && $auth->validateCSRFToken($_GET['token'])) {
        $auth->logout();
        header('Location: login.php');
        exit;
    }
}

// Generování CSRF tokenu pro logout
$logout_token = $auth->generateCSRFToken();

// Získání základních statistik
try {
    $content_sections = $db->selectOne("SELECT COUNT(*) as count FROM content_sections");
    $products = $db->selectOne("SELECT COUNT(*) as count FROM products");
    $team_members = $db->selectOne("SELECT COUNT(*) as count FROM team_members");
    $media_files = $db->selectOne("SELECT COUNT(*) as count FROM media_files");

    $stats = [
        'content_sections' => $content_sections ? $content_sections['count'] : 0,
        'products' => $products ? $products['count'] : 0,
        'team_members' => $team_members ? $team_members['count'] : 0,
        'media_files' => $media_files ? $media_files['count'] : 0
    ];
} catch (Exception $e) {
    $stats = [
        'content_sections' => 0,
        'products' => 0,
        'team_members' => 0,
        'media_files' => 0
    ];
    error_log('Dashboard stats error: ' . $e->getMessage());
}

// Získání posledních aktivit (simulace)
$recent_activities = [
    [
        'action' => 'Přihlášení do admin panelu',
        'time' => date('H:i:s'),
        'type' => 'login'
    ]
];
?>
<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Admin Snurkynakrk.cz</title>
    <meta name="description" content="Administrační dashboard pro správu obsahu webu Snurkynakrk.cz">
    <meta name="robots" content="noindex, nofollow">
    
    <!-- CSS -->
    <link rel="stylesheet" href="../assets/css/admin.css">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="../assets/images/favicon.ico">
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="admin-sidebar">
            <h2>Snurkynakrk.cz</h2>
            <nav>
                <ul class="admin-nav">
                    <li><a href="dashboard.php" class="active">📊 Dashboard</a></li>
                    <li><a href="content.php">📝 Obsah stránky</a></li>
                    <li><a href="products.php">🛍️ Produkty</a></li>
                    <li><a href="team.php">👥 Náš tým</a></li>
                    <li><a href="media.php">🖼️ Média</a></li>
                    <li><a href="eshop-settings.php">🔗 E-shop nastavení</a></li>
                    <li><a href="../" target="_blank">🌐 Zobrazit web</a></li>
                </ul>
            </nav>
        </aside>

        <!-- Main content -->
        <main class="admin-content">
            <!-- Header -->
            <header class="admin-header">
                <h1>Dashboard</h1>
                <div class="admin-user-info">
                    <span>Přihlášen jako: <strong><?= htmlspecialchars($session_info['username']) ?></strong></span>
                    <span>|</span>
                    <span>Čas přihlášení: <?= date('H:i:s', $session_info['login_time']) ?></span>
                    <span>|</span>
                    <a href="?action=logout&token=<?= htmlspecialchars($logout_token) ?>" 
                       class="btn-secondary"
                       onclick="return confirm('Opravdu se chcete odhlásit?')">
                        Odhlásit se
                    </a>
                </div>
            </header>

            <!-- Dashboard content -->
            <div class="dashboard-content fade-in">
                <!-- Statistiky -->
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px;">
                    <div class="card">
                        <div class="card-header">📝 Obsahové sekce</div>
                        <div class="card-body">
                            <div style="font-size: 32px; font-weight: bold; color: #667eea;">
                                <?= $stats['content_sections'] ?>
                            </div>
                            <p style="color: #666; margin-top: 10px;">Aktivních sekcí na webu</p>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">🛍️ Produkty</div>
                        <div class="card-body">
                            <div style="font-size: 32px; font-weight: bold; color: #28a745;">
                                <?= $stats['products'] ?>
                            </div>
                            <p style="color: #666; margin-top: 10px;">Produktů v galerii</p>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">👥 Tým</div>
                        <div class="card-body">
                            <div style="font-size: 32px; font-weight: bold; color: #ffc107;">
                                <?= $stats['team_members'] ?>
                            </div>
                            <p style="color: #666; margin-top: 10px;">Členů týmu</p>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">🖼️ Média</div>
                        <div class="card-body">
                            <div style="font-size: 32px; font-weight: bold; color: #dc3545;">
                                <?= $stats['media_files'] ?>
                            </div>
                            <p style="color: #666; margin-top: 10px;">Nahraných souborů</p>
                        </div>
                    </div>
                </div>

                <!-- Rychlé akce -->
                <div class="card mb-20">
                    <div class="card-header">⚡ Rychlé akce</div>
                    <div class="card-body">
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                            <a href="content.php" class="btn-primary" style="text-decoration: none; text-align: center;">
                                📝 Upravit obsah stránky
                            </a>
                            <a href="products.php" class="btn-success" style="text-decoration: none; text-align: center;">
                                ➕ Přidat nový produkt
                            </a>
                            <a href="team.php" class="btn-secondary" style="text-decoration: none; text-align: center;">
                                👤 Spravovat tým
                            </a>
                            <a href="media.php" class="btn-secondary" style="text-decoration: none; text-align: center;">
                                📁 Správa médií
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Poslední aktivity -->
                <div class="card">
                    <div class="card-header">📋 Poslední aktivity</div>
                    <div class="card-body">
                        <?php if (!empty($recent_activities)): ?>
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>Akce</th>
                                        <th>Čas</th>
                                        <th>Typ</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recent_activities as $activity): ?>
                                        <tr>
                                            <td><?= htmlspecialchars($activity['action']) ?></td>
                                            <td><?= htmlspecialchars($activity['time']) ?></td>
                                            <td>
                                                <span style="padding: 4px 8px; border-radius: 4px; font-size: 12px; 
                                                      background: <?= $activity['type'] === 'login' ? '#d4edda' : '#f8d7da' ?>; 
                                                      color: <?= $activity['type'] === 'login' ? '#155724' : '#721c24' ?>;">
                                                    <?= htmlspecialchars($activity['type']) ?>
                                                </span>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        <?php else: ?>
                            <p style="color: #666; text-align: center; padding: 20px;">
                                Žádné aktivity k zobrazení.
                            </p>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Systémové informace -->
                <div class="card">
                    <div class="card-header">ℹ️ Systémové informace</div>
                    <div class="card-body">
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                            <div>
                                <h4 style="margin-bottom: 10px; color: #333;">Session informace</h4>
                                <p><strong>Session ID:</strong> <?= htmlspecialchars(substr($session_info['session_id'], 0, 10)) ?>...</p>
                                <p><strong>Přihlášen:</strong> <?= date('d.m.Y H:i:s', $session_info['login_time']) ?></p>
                                <p><strong>Poslední aktivita:</strong> <?= date('d.m.Y H:i:s', $session_info['last_activity']) ?></p>
                            </div>
                            <div>
                                <h4 style="margin-bottom: 10px; color: #333;">Server informace</h4>
                                <p><strong>PHP verze:</strong> <?= PHP_VERSION ?></p>
                                <p><strong>Server čas:</strong> <?= date('d.m.Y H:i:s') ?></p>
                                <p><strong>Databáze:</strong> Připojeno ✅</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
    // Automatické obnovení času
    function updateTime() {
        const now = new Date();
        const timeString = now.toLocaleTimeString('cs-CZ');
        document.title = `Dashboard (${timeString}) - Admin Snurkynakrk.cz`;
    }
    
    // Aktualizace každou sekundu
    setInterval(updateTime, 1000);
    
    // Kontrola session každých 5 minut
    setInterval(function() {
        fetch('dashboard.php', {
            method: 'HEAD',
            credentials: 'same-origin'
        }).catch(function() {
            // Pokud selže, pravděpodobně vypršela session
            if (confirm('Vaše session možná vypršela. Chcete se znovu přihlásit?')) {
                window.location.href = 'login.php';
            }
        });
    }, 300000); // 5 minut
    </script>
</body>
</html>
