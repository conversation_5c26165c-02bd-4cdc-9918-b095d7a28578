<?php
/**
 * Minimální dashboard bez závislostí
 */

// Zapnutí zobrazování chyb
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Spuštění session
session_start();

// Kontrola přihlášení
if (!isset($_SESSION['admin_logged_in']) || !$_SESSION['admin_logged_in']) {
    header('Location: login_minimal.php');
    exit;
}

// Zpracování logout
if (isset($_GET['action']) && $_GET['action'] === 'logout') {
    session_destroy();
    header('Location: login_minimal.php');
    exit;
}

$username = $_SESSION['admin_username'] ?? 'admin';
$login_time = $_SESSION['login_time'] ?? time();
?>
<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Minimální Dashboard - Snurkynakrk.cz</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            margin: 0;
            color: #333;
        }
        .btn {
            padding: 8px 16px;
            background: #dc3545;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            font-size: 14px;
        }
        .btn:hover {
            background: #c82333;
        }
        .btn-primary {
            background: #007bff;
        }
        .btn-primary:hover {
            background: #0056b3;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .stat-number {
            font-size: 32px;
            font-weight: bold;
            color: #007bff;
        }
        .debug-info {
            background: #e9ecef;
            padding: 15px;
            border-radius: 4px;
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 Minimální Dashboard</h1>
            <div>
                <span>Přihlášen: <strong><?= htmlspecialchars($username) ?></strong></span>
                <span style="margin: 0 10px;">|</span>
                <a href="?action=logout" class="btn" onclick="return confirm('Opravdu se chcete odhlásit?')">
                    Odhlásit se
                </a>
            </div>
        </div>

        <div class="stats">
            <div class="stat-card">
                <div class="stat-number">✅</div>
                <h3>Přihlášení</h3>
                <p>Úspěšně přihlášen</p>
            </div>
            
            <div class="stat-card">
                <div class="stat-number"><?= date('H:i', $login_time) ?></div>
                <h3>Čas přihlášení</h3>
                <p><?= date('d.m.Y', $login_time) ?></p>
            </div>
            
            <div class="stat-card">
                <div class="stat-number"><?= session_id() ? '✅' : '❌' ?></div>
                <h3>Session</h3>
                <p>Aktivní session</p>
            </div>
        </div>

        <div class="card">
            <h3>🎉 Gratulujeme!</h3>
            <p>Minimální autentifikační systém funguje správně. Úspěšně jste se přihlásili do admin panelu.</p>
            
            <h4>📋 Co funguje:</h4>
            <ul>
                <li>✅ PHP session management</li>
                <li>✅ Přihlašování s validací</li>
                <li>✅ Ochrana admin stránek</li>
                <li>✅ Bezpečné odhlášení</li>
            </ul>
            
            <h4>🔗 Další kroky:</h4>
            <ul>
                <li>Opravit cesty v plné verzi login.php</li>
                <li>Otestovat databázové připojení</li>
                <li>Implementovat kompletní admin funkcionalita</li>
            </ul>
        </div>

        <div class="card">
            <h3>🔧 Testovací odkazy</h3>
            <p>
                <a href="../simple_test.php" class="btn btn-primary">🧪 Jednoduchý test</a>
                <a href="debug.php" class="btn btn-primary">🔍 Debug admin</a>
                <a href="login.php" class="btn btn-primary">🔐 Plný login</a>
            </p>
        </div>

        <div class="debug-info">
            <strong>Debug informace:</strong><br>
            PHP verze: <?= PHP_VERSION ?><br>
            Server čas: <?= date('Y-m-d H:i:s') ?><br>
            Session ID: <?= session_id() ?><br>
            Current directory: <?= getcwd() ?><br>
            Script path: <?= __FILE__ ?><br>
            Login time: <?= date('Y-m-d H:i:s', $login_time) ?><br>
        </div>
    </div>
</body>
</html>
