<?php
/**
 * Zjednodušená admin přihlašovací stránka pro snurkynakrk.cz
 * Bez závislosti na CSS pro debugging
 */

// Zapnutí zobrazování chyb pro debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    // Načtení základních souborů
    require_once '../core/init.php';
    require_once '../core/auth.php';
    require_once '../core/middleware.php';

    // Inicializace
    $db = Database::getInstance();
    $auth = new Auth($db);
    $middleware = new SecurityMiddleware($auth);

    // Pokud už je přihl<PERSON>en, přesměruj na dashboard
    if ($auth->isLoggedIn()) {
        header('Location: dashboard.php');
        exit;
    }

    // Inicializace proměnných
    $error = '';
    $success = '';
    $csrf_token = $auth->generateCSRFToken();

    // Zpracování přihlášení
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // Kontrola rate limiting
        if (!$middleware->checkActionRateLimit('login', 5, 900)) {
            $error = 'Příliš mnoho pokusů o přihlášení. Zkuste to znovu za 15 minut.';
        } else {
            // Validace CSRF tokenu
            if (!$auth->validateCSRFToken($_POST['csrf_token'] ?? '')) {
                $error = 'Neplatný bezpečnostní token. Obnovte stránku a zkuste to znovu.';
            } else {
                // Sanitizace vstupních dat
                $username = $middleware->sanitizeInput($_POST['username'] ?? '');
                $password = $_POST['password'] ?? '';
                
                // Validace vstupních dat
                if (empty($username) || empty($password)) {
                    $error = 'Vyplňte prosím všechna pole.';
                } else {
                    // Pokus o přihlášení
                    if ($auth->login($username, $password)) {
                        // Úspěšné přihlášení - přesměrování
                        header('Location: dashboard.php');
                        exit;
                    } else {
                        $error = 'Neplatné přihlašovací údaje.';
                    }
                }
            }
        }
        
        // Regenerace CSRF tokenu po neúspěšném pokusu
        $csrf_token = $auth->generateCSRFToken();
    }

} catch (Exception $e) {
    $error = 'Systémová chyba: ' . $e->getMessage();
}
?>
<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin přihlášení - Snurkynakrk.cz</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 400px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .login-form {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        input:focus {
            border-color: #007bff;
            outline: none;
        }
        button {
            width: 100%;
            padding: 12px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
            border: 1px solid #f5c6cb;
        }
        .success {
            background: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
            border: 1px solid #c3e6cb;
        }
        .debug-info {
            background: #e9ecef;
            padding: 15px;
            border-radius: 4px;
            margin-top: 20px;
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="login-form">
        <h1>🔐 Admin přihlášení</h1>
        <p style="text-align: center; color: #666; margin-bottom: 20px;">
            Správa obsahu webu Snurkynakrk.cz
        </p>
        
        <?php if ($error): ?>
            <div class="error">
                <strong>Chyba:</strong> <?= htmlspecialchars($error) ?>
            </div>
        <?php endif; ?>
        
        <?php if ($success): ?>
            <div class="success">
                <?= htmlspecialchars($success) ?>
            </div>
        <?php endif; ?>
        
        <form method="POST" action="">
            <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($csrf_token ?? '') ?>">
            
            <div class="form-group">
                <label for="username">Uživatelské jméno:</label>
                <input 
                    type="text" 
                    id="username" 
                    name="username" 
                    value="<?= htmlspecialchars($_POST['username'] ?? '') ?>"
                    required 
                    autocomplete="username"
                >
            </div>
            
            <div class="form-group">
                <label for="password">Heslo:</label>
                <input 
                    type="password" 
                    id="password" 
                    name="password" 
                    required 
                    autocomplete="current-password"
                >
            </div>
            
            <button type="submit">Přihlásit se</button>
        </form>
        
        <div class="debug-info">
            <strong>Debug informace:</strong><br>
            PHP verze: <?= PHP_VERSION ?><br>
            Server čas: <?= date('Y-m-d H:i:s') ?><br>
            Session status: <?= session_status() === PHP_SESSION_ACTIVE ? 'Aktivní' : 'Neaktivní' ?><br>
            <?php if (isset($db)): ?>
                Databáze: <?= $db->isConnected() ? 'Připojeno ✅' : 'Nepřipojeno ❌' ?><br>
            <?php endif; ?>
            CSRF token: <?= isset($csrf_token) ? 'Vygenerován ✅' : 'Chyba ❌' ?><br>
        </div>
        
        <div style="text-align: center; margin-top: 20px;">
            <p style="font-size: 12px; color: #999;">
                Přihlašovací údaje: <strong>admin</strong> / <strong>snurky2024!</strong>
            </p>
            <p style="font-size: 12px;">
                <a href="../debug_login.php">🔍 Debug diagnostika</a> |
                <a href="../test_database.php">🗄️ Test databáze</a>
            </p>
        </div>
    </div>
</body>
</html>
