<?php
/**
 * Ad<PERSON> správa obsahu stránky pro snurkynakrk.cz
 */

// Načtení z<PERSON>lad<PERSON> so<PERSON>
require_once __DIR__ . '/../core/init.php';
require_once __DIR__ . '/../core/auth.php';
require_once __DIR__ . '/../core/middleware.php';

// Inicializace
$db = Database::getInstance();
$auth = new Auth($db);
$middleware = new SecurityMiddleware($auth);

// Kontrola přihlášení
$middleware->requireAdmin();
$middleware->setSecurityHeaders();

$message = '';
$messageType = '';

// Zpracování akcí
$action = $_GET['action'] ?? 'list';
$sectionId = $_GET['id'] ?? null;

// Zpracování formuláře
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $validator = new Validator($_POST);
    
    if ($action === 'edit' && $sectionId) {
        // Validace
        $validator->required('title', 'Název sekce je povinný')
                 ->maxLength('title', 255, 'Název může mít maximálně 255 znaků')
                 ->required('content', 'Obsah je povinný');
        
        if ($validator->isValid()) {
            try {
                $result = $db->update('content_sections', [
                    'title' => $_POST['title'],
                    'content' => $_POST['content'],
                    'updated_at' => date('Y-m-d H:i:s')
                ], ['id' => $sectionId]);
                
                if ($result) {
                    $message = 'Obsah byl úspěšně aktualizován.';
                    $messageType = 'success';
                } else {
                    $message = 'Chyba při aktualizaci obsahu.';
                    $messageType = 'error';
                }
            } catch (Exception $e) {
                $message = 'Chyba databáze: ' . $e->getMessage();
                $messageType = 'error';
                ErrorHandler::logMessage('Content update error: ' . $e->getMessage(), 'ERROR');
            }
        } else {
            $message = $validator->getFirstError();
            $messageType = 'error';
        }
    }
}

// Získání dat podle akce
$contentSections = [];
$currentSection = null;

try {
    if ($action === 'edit' && $sectionId) {
        $currentSection = $db->selectOne("SELECT * FROM content_sections WHERE id = ?", [$sectionId]);
        if (!$currentSection) {
            $message = 'Sekce nebyla nalezena.';
            $messageType = 'error';
            $action = 'list';
        }
    }
    
    if ($action === 'list' || !$currentSection) {
        $contentSections = $db->select("SELECT * FROM content_sections ORDER BY sort_order ASC, id ASC");
    }
} catch (Exception $e) {
    $message = 'Chyba při načítání dat: ' . $e->getMessage();
    $messageType = 'error';
    ErrorHandler::logMessage('Content load error: ' . $e->getMessage(), 'ERROR');
}

// Generování CSRF tokenu
$csrfToken = Utils::generateCSRFToken();
?>
<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Správa obsahu - Admin Snurkynakrk.cz</title>
    <meta name="robots" content="noindex, nofollow">
    
    <link rel="stylesheet" href="../assets/css/admin.css">
    <link rel="icon" type="image/x-icon" href="../assets/images/favicon.ico">
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="admin-sidebar">
            <h2>Snurkynakrk.cz</h2>
            <nav>
                <ul class="admin-nav">
                    <li><a href="dashboard.php">📊 Dashboard</a></li>
                    <li><a href="content.php" class="active">📝 Obsah stránky</a></li>
                    <li><a href="products.php">🛍️ Produkty</a></li>
                    <li><a href="team.php">👥 Náš tým</a></li>
                    <li><a href="media.php">🖼️ Média</a></li>
                    <li><a href="eshop-settings.php">🔗 E-shop nastavení</a></li>
                    <li><a href="../" target="_blank">🌐 Zobrazit web</a></li>
                </ul>
            </nav>
        </aside>

        <!-- Main content -->
        <main class="admin-content">
            <!-- Header -->
            <header class="admin-header">
                <h1><?= $action === 'edit' ? 'Upravit obsah' : 'Správa obsahu' ?></h1>
                <div class="admin-user-info">
                    <a href="dashboard.php" class="btn-secondary">← Zpět na dashboard</a>
                </div>
            </header>

            <!-- Messages -->
            <?php if ($message): ?>
                <div class="<?= $messageType ?>-message">
                    <?= Utils::escape($message) ?>
                </div>
            <?php endif; ?>

            <!-- Content -->
            <div class="fade-in">
                <?php if ($action === 'edit' && $currentSection): ?>
                    <!-- Edit form -->
                    <div class="card">
                        <div class="card-header">
                            Upravit sekci: <?= Utils::escape($currentSection['title']) ?>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="">
                                <input type="hidden" name="csrf_token" value="<?= $csrfToken ?>">
                                
                                <div class="form-group">
                                    <label for="title">Název sekce:</label>
                                    <input type="text" id="title" name="title" 
                                           value="<?= Utils::escape($currentSection['title']) ?>" 
                                           required maxlength="255">
                                </div>
                                
                                <div class="form-group">
                                    <label for="content">Obsah:</label>
                                    <textarea id="content" name="content" rows="15" required><?= Utils::escape($currentSection['content']) ?></textarea>
                                </div>
                                
                                <div style="display: flex; gap: 10px;">
                                    <button type="submit" class="btn-primary">💾 Uložit změny</button>
                                    <a href="content.php" class="btn-secondary">❌ Zrušit</a>
                                </div>
                            </form>
                        </div>
                    </div>
                <?php else: ?>
                    <!-- List view -->
                    <div class="card">
                        <div class="card-header">
                            Obsahové sekce webu
                        </div>
                        <div class="card-body">
                            <?php if (!empty($contentSections)): ?>
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>Název sekce</th>
                                            <th>Klíč</th>
                                            <th>Poslední úprava</th>
                                            <th>Akce</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($contentSections as $section): ?>
                                            <tr>
                                                <td>
                                                    <strong><?= Utils::escape($section['title']) ?></strong>
                                                </td>
                                                <td>
                                                    <code><?= Utils::escape($section['section_key']) ?></code>
                                                </td>
                                                <td>
                                                    <?= Utils::formatCzechDate($section['updated_at']) ?>
                                                </td>
                                                <td>
                                                    <a href="?action=edit&id=<?= $section['id'] ?>" 
                                                       class="btn-primary" style="text-decoration: none;">
                                                        ✏️ Upravit
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            <?php else: ?>
                                <p style="text-align: center; color: #666; padding: 40px;">
                                    Žádné obsahové sekce nebyly nalezeny.
                                </p>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <!-- Info -->
                    <div class="card">
                        <div class="card-header">ℹ️ Informace</div>
                        <div class="card-body">
                            <p>Zde můžete upravovat obsahové sekce webu. Každá sekce má svůj unikátní klíč, 
                               který se používá pro zobrazení obsahu na webu.</p>
                            <p><strong>Tip:</strong> Obsah podporuje HTML značky pro formátování textu.</p>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </main>
    </div>

    <script>
    // Auto-save draft (každých 30 sekund)
    if (document.getElementById('content')) {
        setInterval(function() {
            const content = document.getElementById('content').value;
            const title = document.getElementById('title').value;
            
            if (content.length > 10) {
                localStorage.setItem('content_draft_<?= $sectionId ?>', JSON.stringify({
                    title: title,
                    content: content,
                    timestamp: Date.now()
                }));
            }
        }, 30000);
        
        // Načtení draftu při načtení stránky
        window.addEventListener('load', function() {
            const draft = localStorage.getItem('content_draft_<?= $sectionId ?>');
            if (draft) {
                const data = JSON.parse(draft);
                const age = Date.now() - data.timestamp;
                
                // Pokud je draft mladší než 1 hodina
                if (age < 3600000) {
                    if (confirm('Byl nalezen neuložený draft. Chcete ho načíst?')) {
                        document.getElementById('title').value = data.title;
                        document.getElementById('content').value = data.content;
                    }
                }
            }
        });
    }
    </script>
</body>
</html>
