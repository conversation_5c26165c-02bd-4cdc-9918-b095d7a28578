<?php
/**
 * <PERSON>min přihlašovací stránka pro snurkynakrk.cz
 */

// Zapnutí zobrazování chyb pro debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    // Načtení základních souborů s absolutní cestou
    require_once __DIR__ . '/../core/init.php';
    require_once __DIR__ . '/../core/auth.php';
    require_once __DIR__ . '/../core/middleware.php';
} catch (Exception $e) {
    die('Chyba při načítání systémových souborů: ' . $e->getMessage());
}

// Inicializace s error handlingem
try {
    $db = Database::getInstance();
    $auth = new Auth($db);
    $middleware = new SecurityMiddleware($auth);
} catch (Exception $e) {
    die('Chyba při inicializaci systému: ' . $e->getMessage());
}

// Nastavení bezpečnostních hlaviček
$middleware->setSecurityHeaders();

// Pokud už je p<PERSON>, přesměruj na dashboard
if ($auth->isLoggedIn()) {
    header('Location: dashboard.php');
    exit;
}

// Inicializace proměnných
$error = '';
$success = '';
$csrf_token = $auth->generateCSRFToken();

// Zpracování přihlášení
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Kontrola rate limiting
    if (!$middleware->checkActionRateLimit('login', 5, 900)) { // 5 pokusů za 15 minut
        $error = 'Příliš mnoho pokusů o přihlášení. Zkuste to znovu za 15 minut.';
        $middleware->logSecurityEvent('Login rate limit exceeded', $_POST['username'] ?? '');
    } else {
        // Validace CSRF tokenu
        if (!$auth->validateCSRFToken($_POST['csrf_token'] ?? '')) {
            $error = 'Neplatný bezpečnostní token. Obnovte stránku a zkuste to znovu.';
            $middleware->logSecurityEvent('Invalid CSRF token on login', $_POST['username'] ?? '');
        } else {
            // Sanitizace vstupních dat
            $username = $middleware->sanitizeInput($_POST['username'] ?? '');
            $password = $_POST['password'] ?? '';
            
            // Validace vstupních dat
            if (empty($username) || empty($password)) {
                $error = 'Vyplňte prosím všechna pole.';
            } else {
                // Pokus o přihlášení
                if ($auth->login($username, $password)) {
                    // Úspěšné přihlášení - přesměrování
                    header('Location: dashboard.php');
                    exit;
                } else {
                    $error = 'Neplatné přihlašovací údaje.';
                }
            }
        }
    }
    
    // Regenerace CSRF tokenu po neúspěšném pokusu
    $csrf_token = $auth->generateCSRFToken();
}

// Získání informací o rate limiting pro zobrazení
$ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
$cache_key = "rate_limit_login_$ip";
$remaining_attempts = 5;
if (isset($_SESSION[$cache_key])) {
    $attempts = array_filter($_SESSION[$cache_key], function($timestamp) {
        return (time() - $timestamp) < 900; // 15 minut
    });
    $remaining_attempts = max(0, 5 - count($attempts));
}
?>
<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin přihlášení - Snurkynakrk.cz</title>
    <meta name="description" content="Administrační přihlášení pro správu obsahu webu Snurkynakrk.cz">
    <meta name="robots" content="noindex, nofollow">
    
    <!-- CSS -->
    <link rel="stylesheet" href="../assets/css/admin.css">

    <!-- Fallback CSS pokud se hlavní CSS nenačte -->
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .login-page { min-height: 100vh; display: flex; align-items: center; justify-content: center; }
        .login-container { width: 100%; max-width: 400px; }
        .login-form { background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { text-align: center; margin-bottom: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input[type="text"], input[type="password"] { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box; }
        .btn-primary { width: 100%; padding: 12px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        .error-message { background: #f8d7da; color: #721c24; padding: 10px; border-radius: 4px; margin-bottom: 15px; }
        .warning-message { background: #fff3cd; color: #856404; padding: 10px; border-radius: 4px; margin-bottom: 15px; }
    </style>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="../assets/images/favicon.ico">

    <!-- Preload kritických zdrojů -->
    <link rel="preload" href="../assets/css/admin.css" as="style">
</head>
<body class="login-page">
    <div class="login-container">
        <div class="login-form fade-in">
            <h1>Admin přihlášení</h1>
            <p style="text-align: center; color: #666; margin-bottom: 30px;">
                Správa obsahu webu Snurkynakrk.cz
            </p>
            
            <?php if ($error): ?>
                <div class="error-message">
                    <strong>Chyba:</strong> <?= htmlspecialchars($error) ?>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="success-message">
                    <?= htmlspecialchars($success) ?>
                </div>
            <?php endif; ?>
            
            <?php if ($remaining_attempts < 5 && $remaining_attempts > 0): ?>
                <div class="warning-message">
                    <strong>Upozornění:</strong> Zbývá vám <?= $remaining_attempts ?> pokusů o přihlášení.
                </div>
            <?php endif; ?>
            
            <form method="POST" action="" id="loginForm" novalidate>
                <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($csrf_token) ?>">
                
                <div class="form-group">
                    <label for="username">Uživatelské jméno:</label>
                    <input 
                        type="text" 
                        id="username" 
                        name="username" 
                        value="<?= htmlspecialchars($_POST['username'] ?? '') ?>"
                        required 
                        autocomplete="username"
                        maxlength="50"
                        <?= $remaining_attempts === 0 ? 'disabled' : '' ?>
                    >
                </div>
                
                <div class="form-group">
                    <label for="password">Heslo:</label>
                    <input 
                        type="password" 
                        id="password" 
                        name="password" 
                        required 
                        autocomplete="current-password"
                        maxlength="100"
                        <?= $remaining_attempts === 0 ? 'disabled' : '' ?>
                    >
                </div>
                
                <button 
                    type="submit" 
                    class="btn-primary" 
                    id="loginButton"
                    <?= $remaining_attempts === 0 ? 'disabled' : '' ?>
                >
                    <?= $remaining_attempts === 0 ? 'Přihlášení zablokováno' : 'Přihlásit se' ?>
                </button>
            </form>
            
            <div style="margin-top: 30px; text-align: center; font-size: 12px; color: #999;">
                <p>Bezpečné přihlášení s CSRF ochranou</p>
                <p>Session timeout: 1 hodina</p>
                <?php if ($remaining_attempts < 5): ?>
                    <p>Rate limiting aktivní</p>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script>
    // Základní JavaScript pro login formulář
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.getElementById('loginForm');
        const button = document.getElementById('loginButton');
        const usernameField = document.getElementById('username');
        const passwordField = document.getElementById('password');
        
        // Automatické zaměření na první prázdné pole
        if (!usernameField.value) {
            usernameField.focus();
        } else if (!passwordField.value) {
            passwordField.focus();
        }
        
        // Zabránění vícenásobnému odeslání formuláře
        form.addEventListener('submit', function(e) {
            if (button.disabled) {
                e.preventDefault();
                return false;
            }
            
            // Deaktivace tlačítka a zobrazení loading stavu
            button.disabled = true;
            button.textContent = 'Přihlašuji...';
            button.classList.add('loading');
            
            // Základní validace na straně klienta
            if (!usernameField.value.trim() || !passwordField.value) {
                e.preventDefault();
                button.disabled = false;
                button.textContent = 'Přihlásit se';
                button.classList.remove('loading');
                alert('Vyplňte prosím všechna pole.');
                return false;
            }
        });
        
        // Obnovení tlačítka pokud se stránka načte znovu (např. kvůli chybě)
        window.addEventListener('pageshow', function() {
            button.disabled = <?= $remaining_attempts === 0 ? 'true' : 'false' ?>;
            button.textContent = <?= $remaining_attempts === 0 ? '"Přihlášení zablokováno"' : '"Přihlásit se"' ?>;
            button.classList.remove('loading');
        });
        
        // Automatické odeslání při stisku Enter
        [usernameField, passwordField].forEach(field => {
            field.addEventListener('keypress', function(e) {
                if (e.key === 'Enter' && !button.disabled) {
                    form.submit();
                }
            });
        });
        
        // Vyčištění chybových zpráv při psaní
        [usernameField, passwordField].forEach(field => {
            field.addEventListener('input', function() {
                const errorMessage = document.querySelector('.error-message');
                if (errorMessage && this.value.length > 0) {
                    errorMessage.style.opacity = '0.5';
                }
            });
        });
    });
    
    // Ochrana proti clickjacking
    if (top !== self) {
        top.location = self.location;
    }
    </script>
</body>
</html>
