<?php
/**
 * Admin správa týmu pro snurkynakrk.cz
 */

// Načtení základních so<PERSON>
require_once __DIR__ . '/../core/init.php';
require_once __DIR__ . '/../core/auth.php';
require_once __DIR__ . '/../core/middleware.php';

// Inicializace
$db = Database::getInstance();
$auth = new Auth($db);
$middleware = new SecurityMiddleware($auth);

// Kontrola přihlášení
$middleware->requireAdmin();
$middleware->setSecurityHeaders();

$message = '';
$messageType = '';

// Zpracování akcí
$action = $_GET['action'] ?? 'list';
$memberId = $_GET['id'] ?? null;

// Zpracování formuláře
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $validator = new Validator($_POST);
    
    if ($action === 'add' || ($action === 'edit' && $memberId)) {
        // Validace
        $validator->required('name', 'J<PERSON>no je povinné')
                 ->maxLength('name', 255, 'Jméno může mít maximálně 255 znaků')
                 ->required('position', 'Pozice je povinná')
                 ->maxLength('position', 255, 'Pozice může mít maximálně 255 znaků')
                 ->email('email', 'Email musí být platný')
                 ->phone('phone', 'Telefon musí být platný');
        
        if ($validator->isValid()) {
            try {
                $data = [
                    'name' => $_POST['name'],
                    'position' => $_POST['position'],
                    'bio' => $_POST['bio'] ?? '',
                    'email' => $_POST['email'] ?? '',
                    'phone' => $_POST['phone'] ?? '',
                    'is_active' => isset($_POST['is_active']) ? 1 : 0,
                    'sort_order' => (int)($_POST['sort_order'] ?? 0),
                    'updated_at' => date('Y-m-d H:i:s')
                ];
                
                if ($action === 'add') {
                    $data['created_at'] = date('Y-m-d H:i:s');
                    $result = $db->insert('team_members', $data);
                    $message = 'Člen týmu byl úspěšně přidán.';
                } else {
                    $result = $db->update('team_members', $data, ['id' => $memberId]);
                    $message = 'Člen týmu byl úspěšně aktualizován.';
                }
                
                if ($result) {
                    $messageType = 'success';
                    $action = 'list';
                } else {
                    $message = 'Chyba při ukládání člena týmu.';
                    $messageType = 'error';
                }
            } catch (Exception $e) {
                $message = 'Chyba databáze: ' . $e->getMessage();
                $messageType = 'error';
                ErrorHandler::logMessage('Team member save error: ' . $e->getMessage(), 'ERROR');
            }
        } else {
            $message = $validator->getFirstError();
            $messageType = 'error';
        }
    }
    
    if ($action === 'delete' && $memberId) {
        try {
            $result = $db->delete('team_members', ['id' => $memberId]);
            if ($result) {
                $message = 'Člen týmu byl úspěšně smazán.';
                $messageType = 'success';
            } else {
                $message = 'Chyba při mazání člena týmu.';
                $messageType = 'error';
            }
            $action = 'list';
        } catch (Exception $e) {
            $message = 'Chyba databáze: ' . $e->getMessage();
            $messageType = 'error';
            ErrorHandler::logMessage('Team member delete error: ' . $e->getMessage(), 'ERROR');
        }
    }
}

// Získání dat podle akce
$teamMembers = [];
$currentMember = null;

try {
    if ($action === 'edit' && $memberId) {
        $currentMember = $db->selectOne("SELECT * FROM team_members WHERE id = ?", [$memberId]);
        if (!$currentMember) {
            $message = 'Člen týmu nebyl nalezen.';
            $messageType = 'error';
            $action = 'list';
        }
    }
    
    if ($action === 'list' || $action === 'add') {
        $teamMembers = $db->select("SELECT * FROM team_members ORDER BY sort_order ASC, name ASC");
    }
} catch (Exception $e) {
    $message = 'Chyba při načítání dat: ' . $e->getMessage();
    $messageType = 'error';
    ErrorHandler::logMessage('Team members load error: ' . $e->getMessage(), 'ERROR');
}

// Generování CSRF tokenu
$csrfToken = Utils::generateCSRFToken();
?>
<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Správa týmu - Admin Snurkynakrk.cz</title>
    <meta name="robots" content="noindex, nofollow">
    
    <link rel="stylesheet" href="../assets/css/admin.css">
    <link rel="icon" type="image/x-icon" href="../assets/images/favicon.ico">
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="admin-sidebar">
            <h2>Snurkynakrk.cz</h2>
            <nav>
                <ul class="admin-nav">
                    <li><a href="dashboard.php">📊 Dashboard</a></li>
                    <li><a href="content.php">📝 Obsah stránky</a></li>
                    <li><a href="products.php">🛍️ Produkty</a></li>
                    <li><a href="team.php" class="active">👥 Náš tým</a></li>
                    <li><a href="media.php">🖼️ Média</a></li>
                    <li><a href="eshop-settings.php">🔗 E-shop nastavení</a></li>
                    <li><a href="../" target="_blank">🌐 Zobrazit web</a></li>
                </ul>
            </nav>
        </aside>

        <!-- Main content -->
        <main class="admin-content">
            <!-- Header -->
            <header class="admin-header">
                <h1>
                    <?php if ($action === 'add'): ?>
                        Přidat člena týmu
                    <?php elseif ($action === 'edit'): ?>
                        Upravit člena týmu
                    <?php else: ?>
                        Správa týmu
                    <?php endif; ?>
                </h1>
                <div class="admin-user-info">
                    <?php if ($action === 'list'): ?>
                        <a href="?action=add" class="btn-primary">➕ Přidat člena</a>
                    <?php else: ?>
                        <a href="team.php" class="btn-secondary">← Zpět na seznam</a>
                    <?php endif; ?>
                </div>
            </header>

            <!-- Messages -->
            <?php if ($message): ?>
                <div class="<?= $messageType ?>-message">
                    <?= Utils::escape($message) ?>
                </div>
            <?php endif; ?>

            <!-- Content -->
            <div class="fade-in">
                <?php if ($action === 'add' || ($action === 'edit' && $currentMember)): ?>
                    <!-- Add/Edit form -->
                    <div class="card">
                        <div class="card-header">
                            <?= $action === 'add' ? 'Nový člen týmu' : 'Upravit člena týmu' ?>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="">
                                <input type="hidden" name="csrf_token" value="<?= $csrfToken ?>">
                                
                                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                                    <div>
                                        <div class="form-group">
                                            <label for="name">Jméno a příjmení:</label>
                                            <input type="text" id="name" name="name" 
                                                   value="<?= $currentMember ? Utils::escape($currentMember['name']) : '' ?>" 
                                                   required maxlength="255">
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="position">Pozice:</label>
                                            <input type="text" id="position" name="position" 
                                                   value="<?= $currentMember ? Utils::escape($currentMember['position']) : '' ?>" 
                                                   required maxlength="255" placeholder="např. Vedoucí prodeje">
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="email">Email:</label>
                                            <input type="email" id="email" name="email" 
                                                   value="<?= $currentMember ? Utils::escape($currentMember['email']) : '' ?>" 
                                                   placeholder="<EMAIL>">
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="phone">Telefon:</label>
                                            <input type="tel" id="phone" name="phone" 
                                                   value="<?= $currentMember ? Utils::escape($currentMember['phone']) : '' ?>" 
                                                   placeholder="+420 123 456 789">
                                        </div>
                                    </div>
                                    
                                    <div>
                                        <div class="form-group">
                                            <label for="bio">Popis/Bio:</label>
                                            <textarea id="bio" name="bio" rows="8" 
                                                      placeholder="Krátký popis člena týmu, jeho zkušenosti, koníčky..."><?= $currentMember ? Utils::escape($currentMember['bio']) : '' ?></textarea>
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="sort_order">Pořadí zobrazení:</label>
                                            <input type="number" id="sort_order" name="sort_order" min="0" max="999"
                                                   value="<?= $currentMember ? $currentMember['sort_order'] : '0' ?>" 
                                                   placeholder="0">
                                            <small style="color: #666;">Nižší číslo = vyšší pozice v seznamu</small>
                                        </div>
                                        
                                        <div class="form-group">
                                            <label>
                                                <input type="checkbox" name="is_active" value="1" 
                                                       <?= (!$currentMember || $currentMember['is_active']) ? 'checked' : '' ?>>
                                                Aktivní (zobrazit na webu)
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                
                                <div style="display: flex; gap: 10px; margin-top: 20px;">
                                    <button type="submit" class="btn-primary">
                                        💾 <?= $action === 'add' ? 'Přidat člena' : 'Uložit změny' ?>
                                    </button>
                                    <a href="team.php" class="btn-secondary">❌ Zrušit</a>
                                </div>
                            </form>
                        </div>
                    </div>
                <?php else: ?>
                    <!-- List view -->
                    <div class="card">
                        <div class="card-header">
                            Členové týmu (<?= count($teamMembers) ?>/6)
                        </div>
                        <div class="card-body">
                            <?php if (!empty($teamMembers)): ?>
                                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                                    <?php foreach ($teamMembers as $member): ?>
                                        <div class="card" style="margin: 0;">
                                            <div class="card-body">
                                                <div style="display: flex; justify-content: between; align-items: start; margin-bottom: 10px;">
                                                    <div style="flex: 1;">
                                                        <h3 style="margin: 0 0 5px 0; color: #333;">
                                                            <?= Utils::escape($member['name']) ?>
                                                            <?php if (!$member['is_active']): ?>
                                                                <span style="background: #f8d7da; color: #721c24; padding: 2px 6px; border-radius: 3px; font-size: 11px; margin-left: 5px;">NEAKTIVNÍ</span>
                                                            <?php endif; ?>
                                                        </h3>
                                                        <p style="margin: 0; color: #666; font-weight: 500;">
                                                            <?= Utils::escape($member['position']) ?>
                                                        </p>
                                                    </div>
                                                    <div style="font-size: 12px; color: #999;">
                                                        #<?= $member['sort_order'] ?>
                                                    </div>
                                                </div>
                                                
                                                <?php if ($member['bio']): ?>
                                                    <p style="margin: 10px 0; color: #555; font-size: 14px; line-height: 1.4;">
                                                        <?= Utils::truncateText($member['bio'], 120) ?>
                                                    </p>
                                                <?php endif; ?>
                                                
                                                <?php if ($member['email'] || $member['phone']): ?>
                                                    <div style="margin: 10px 0; font-size: 13px; color: #666;">
                                                        <?php if ($member['email']): ?>
                                                            <div>📧 <?= Utils::escape($member['email']) ?></div>
                                                        <?php endif; ?>
                                                        <?php if ($member['phone']): ?>
                                                            <div>📞 <?= Utils::escape($member['phone']) ?></div>
                                                        <?php endif; ?>
                                                    </div>
                                                <?php endif; ?>
                                                
                                                <div style="display: flex; gap: 5px; margin-top: 15px;">
                                                    <a href="?action=edit&id=<?= $member['id'] ?>" 
                                                       class="btn-primary" style="text-decoration: none; font-size: 12px; padding: 6px 12px;">
                                                        ✏️ Upravit
                                                    </a>
                                                    <a href="?action=delete&id=<?= $member['id'] ?>" 
                                                       class="btn-danger" style="text-decoration: none; font-size: 12px; padding: 6px 12px;"
                                                       onclick="return confirm('Opravdu chcete smazat tohoto člena týmu?')">
                                                        🗑️ Smazat
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php else: ?>
                                <p style="text-align: center; color: #666; padding: 40px;">
                                    Žádní členové týmu nebyli nalezeni.
                                    <br><br>
                                    <a href="?action=add" class="btn-primary">➕ Přidat prvního člena</a>
                                </p>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <?php if (count($teamMembers) < 6): ?>
                        <div class="info-message">
                            <strong>Tip:</strong> Můžete přidat až 6 členů týmu. Aktuálně máte <?= count($teamMembers) ?> členů.
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </main>
    </div>
</body>
</html>
