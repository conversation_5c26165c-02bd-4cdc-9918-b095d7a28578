<?php
/**
 * Debug verze dashboard.php
 */

// Zapnutí zobrazování chyb
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔍 Debug Dashboard</h1>";

echo "<h2>Krok 1: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></h2>";

try {
    require_once __DIR__ . '/../core/init.php';
    echo "✅ init.php načten<br>";
    
    require_once __DIR__ . '/../core/auth.php';
    echo "✅ auth.php načten<br>";
    
    require_once __DIR__ . '/../core/middleware.php';
    echo "✅ middleware.php načten<br>";
} catch (Exception $e) {
    echo "❌ Chyba při načítání: " . $e->getMessage() . "<br>";
    exit;
}

echo "<h2>Krok 2: Inicializace objektů</h2>";

try {
    $db = Database::getInstance();
    echo "✅ Database instance vytvořena<br>";
    
    $auth = new Auth($db);
    echo "✅ Auth objekt vytvořen<br>";
    
    $middleware = new SecurityMiddleware($auth);
    echo "✅ SecurityMiddleware objekt vytvořen<br>";
} catch (Exception $e) {
    echo "❌ Chyba při inicializaci: " . $e->getMessage() . "<br>";
    exit;
}

echo "<h2>Krok 3: Kontrola přihlášení</h2>";

try {
    $is_logged_in = $auth->isLoggedIn();
    echo "Stav přihlášení: " . ($is_logged_in ? 'Přihlášen ✅' : 'Nepřihlášen ❌') . "<br>";
    
    if (!$is_logged_in) {
        echo "<p><a href='login.php'>→ Přejít na přihlášení</a></p>";
        exit;
    }
    
    $session_info = $auth->getSessionInfo();
    echo "Uživatel: " . ($session_info['username'] ?? 'neznámý') . "<br>";
    
} catch (Exception $e) {
    echo "❌ Chyba při kontrole přihlášení: " . $e->getMessage() . "<br>";
    exit;
}

echo "<h2>Krok 4: Test databázových dotazů</h2>";

try {
    echo "Test základního dotazu...<br>";
    $test = $db->selectOne("SELECT 1 as test");
    echo "✅ Základní dotaz funguje: " . $test['test'] . "<br>";
    
    echo "Test tabulek...<br>";
    $tables = $db->select("SHOW TABLES");
    echo "✅ Počet tabulek: " . count($tables) . "<br>";
    
    // Test jednotlivých tabulek
    $table_names = ['content_sections', 'products', 'team_members', 'media_files'];
    $stats = [];
    
    foreach ($table_names as $table) {
        try {
            $result = $db->selectOne("SELECT COUNT(*) as count FROM $table");
            $count = $result ? $result['count'] : 0;
            $stats[$table] = $count;
            echo "✅ $table: $count záznamů<br>";
        } catch (Exception $e) {
            echo "⚠️ $table: tabulka neexistuje nebo chyba - " . $e->getMessage() . "<br>";
            $stats[$table] = 0;
        }
    }
    
} catch (Exception $e) {
    echo "❌ Chyba při testování databáze: " . $e->getMessage() . "<br>";
    exit;
}

echo "<h2>🎉 Všechny testy prošly!</h2>";
echo "<p>Dashboard by měl fungovat správně.</p>";

echo "<h3>📊 Statistiky:</h3>";
echo "<ul>";
foreach ($stats as $table => $count) {
    echo "<li>$table: $count</li>";
}
echo "</ul>";

echo "<h3>🔗 Odkazy:</h3>";
echo "<a href='dashboard.php'>→ Zkusit dashboard.php</a><br>";
echo "<a href='login.php'>→ Login stránka</a><br>";
echo "<a href='dashboard_minimal.php'>→ Minimální dashboard</a><br>";

// Zobrazení session informací
echo "<h3>📋 Session informace:</h3>";
echo "<pre>";
print_r($session_info);
echo "</pre>";
?>
