<?php
/**
 * Admin správa produktů pro snurkynakrk.cz
 */

// Načtení základní<PERSON> souborů
require_once __DIR__ . '/../core/init.php';
require_once __DIR__ . '/../core/auth.php';
require_once __DIR__ . '/../core/middleware.php';

// Inicializace
$db = Database::getInstance();
$auth = new Auth($db);
$middleware = new SecurityMiddleware($auth);

// Kontrola přihlášení
$middleware->requireAdmin();
$middleware->setSecurityHeaders();

$message = '';
$messageType = '';

// Zpracování akcí
$action = $_GET['action'] ?? 'list';
$productId = $_GET['id'] ?? null;

// Zpracování formuláře
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $validator = new Validator($_POST);
    
    if ($action === 'add' || ($action === 'edit' && $productId)) {
        // Validace
        $validator->required('name', 'Název produktu je povinný')
                 ->maxLength('name', 255, 'Název může mít maximálně 255 znaků')
                 ->required('description', 'Popis je povinný')
                 ->url('eshop_url', 'URL e-shopu musí být platná');
        
        if ($validator->isValid()) {
            try {
                $data = [
                    'name' => $_POST['name'],
                    'description' => $_POST['description'],
                    'eshop_url' => $_POST['eshop_url'] ?? '',
                    'price' => $_POST['price'] ?? null,
                    'is_featured' => isset($_POST['is_featured']) ? 1 : 0,
                    'is_active' => isset($_POST['is_active']) ? 1 : 0,
                    'updated_at' => date('Y-m-d H:i:s')
                ];
                
                if ($action === 'add') {
                    $data['created_at'] = date('Y-m-d H:i:s');
                    $result = $db->insert('products', $data);
                    $message = 'Produkt byl úspěšně přidán.';
                } else {
                    $result = $db->update('products', $data, ['id' => $productId]);
                    $message = 'Produkt byl úspěšně aktualizován.';
                }
                
                if ($result) {
                    $messageType = 'success';
                    $action = 'list'; // Přesměrování na seznam
                } else {
                    $message = 'Chyba při ukládání produktu.';
                    $messageType = 'error';
                }
            } catch (Exception $e) {
                $message = 'Chyba databáze: ' . $e->getMessage();
                $messageType = 'error';
                ErrorHandler::logMessage('Product save error: ' . $e->getMessage(), 'ERROR');
            }
        } else {
            $message = $validator->getFirstError();
            $messageType = 'error';
        }
    }
    
    if ($action === 'delete' && $productId) {
        try {
            $result = $db->delete('products', ['id' => $productId]);
            if ($result) {
                $message = 'Produkt byl úspěšně smazán.';
                $messageType = 'success';
            } else {
                $message = 'Chyba při mazání produktu.';
                $messageType = 'error';
            }
            $action = 'list';
        } catch (Exception $e) {
            $message = 'Chyba databáze: ' . $e->getMessage();
            $messageType = 'error';
            ErrorHandler::logMessage('Product delete error: ' . $e->getMessage(), 'ERROR');
        }
    }
}

// Získání dat podle akce
$products = [];
$currentProduct = null;

try {
    if ($action === 'edit' && $productId) {
        $currentProduct = $db->selectOne("SELECT * FROM products WHERE id = ?", [$productId]);
        if (!$currentProduct) {
            $message = 'Produkt nebyl nalezen.';
            $messageType = 'error';
            $action = 'list';
        }
    }
    
    if ($action === 'list' || $action === 'add') {
        $products = $db->select("SELECT * FROM products ORDER BY created_at DESC");
    }
} catch (Exception $e) {
    $message = 'Chyba při načítání dat: ' . $e->getMessage();
    $messageType = 'error';
    ErrorHandler::logMessage('Products load error: ' . $e->getMessage(), 'ERROR');
}

// Generování CSRF tokenu
$csrfToken = Utils::generateCSRFToken();
?>
<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Správa produktů - Admin Snurkynakrk.cz</title>
    <meta name="robots" content="noindex, nofollow">
    
    <link rel="stylesheet" href="../assets/css/admin.css">
    <link rel="icon" type="image/x-icon" href="../assets/images/favicon.ico">
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="admin-sidebar">
            <h2>Snurkynakrk.cz</h2>
            <nav>
                <ul class="admin-nav">
                    <li><a href="dashboard.php">📊 Dashboard</a></li>
                    <li><a href="content.php">📝 Obsah stránky</a></li>
                    <li><a href="products.php" class="active">🛍️ Produkty</a></li>
                    <li><a href="team.php">👥 Náš tým</a></li>
                    <li><a href="media.php">🖼️ Média</a></li>
                    <li><a href="eshop-settings.php">🔗 E-shop nastavení</a></li>
                    <li><a href="../" target="_blank">🌐 Zobrazit web</a></li>
                </ul>
            </nav>
        </aside>

        <!-- Main content -->
        <main class="admin-content">
            <!-- Header -->
            <header class="admin-header">
                <h1>
                    <?php if ($action === 'add'): ?>
                        Přidat produkt
                    <?php elseif ($action === 'edit'): ?>
                        Upravit produkt
                    <?php else: ?>
                        Správa produktů
                    <?php endif; ?>
                </h1>
                <div class="admin-user-info">
                    <?php if ($action === 'list'): ?>
                        <a href="?action=add" class="btn-primary">➕ Přidat produkt</a>
                    <?php else: ?>
                        <a href="products.php" class="btn-secondary">← Zpět na seznam</a>
                    <?php endif; ?>
                </div>
            </header>

            <!-- Messages -->
            <?php if ($message): ?>
                <div class="<?= $messageType ?>-message">
                    <?= Utils::escape($message) ?>
                </div>
            <?php endif; ?>

            <!-- Content -->
            <div class="fade-in">
                <?php if ($action === 'add' || ($action === 'edit' && $currentProduct)): ?>
                    <!-- Add/Edit form -->
                    <div class="card">
                        <div class="card-header">
                            <?= $action === 'add' ? 'Nový produkt' : 'Upravit produkt' ?>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="">
                                <input type="hidden" name="csrf_token" value="<?= $csrfToken ?>">
                                
                                <div class="form-group">
                                    <label for="name">Název produktu:</label>
                                    <input type="text" id="name" name="name" 
                                           value="<?= $currentProduct ? Utils::escape($currentProduct['name']) : '' ?>" 
                                           required maxlength="255">
                                </div>
                                
                                <div class="form-group">
                                    <label for="description">Popis:</label>
                                    <textarea id="description" name="description" rows="5" required><?= $currentProduct ? Utils::escape($currentProduct['description']) : '' ?></textarea>
                                </div>
                                
                                <div class="form-group">
                                    <label for="eshop_url">URL v e-shopu:</label>
                                    <input type="url" id="eshop_url" name="eshop_url" 
                                           value="<?= $currentProduct ? Utils::escape($currentProduct['eshop_url']) : '' ?>" 
                                           placeholder="https://eshop.example.com/produkt">
                                </div>
                                
                                <div class="form-group">
                                    <label for="price">Cena (Kč):</label>
                                    <input type="number" id="price" name="price" step="0.01" min="0"
                                           value="<?= $currentProduct ? $currentProduct['price'] : '' ?>" 
                                           placeholder="999.99">
                                </div>
                                
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" name="is_featured" value="1" 
                                               <?= ($currentProduct && $currentProduct['is_featured']) ? 'checked' : '' ?>>
                                        Doporučený produkt
                                    </label>
                                </div>
                                
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" name="is_active" value="1" 
                                               <?= (!$currentProduct || $currentProduct['is_active']) ? 'checked' : '' ?>>
                                        Aktivní (zobrazit na webu)
                                    </label>
                                </div>
                                
                                <div style="display: flex; gap: 10px;">
                                    <button type="submit" class="btn-primary">
                                        💾 <?= $action === 'add' ? 'Přidat produkt' : 'Uložit změny' ?>
                                    </button>
                                    <a href="products.php" class="btn-secondary">❌ Zrušit</a>
                                </div>
                            </form>
                        </div>
                    </div>
                <?php else: ?>
                    <!-- List view -->
                    <div class="card">
                        <div class="card-header">
                            Seznam produktů (<?= count($products) ?>)
                        </div>
                        <div class="card-body">
                            <?php if (!empty($products)): ?>
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>Název</th>
                                            <th>Cena</th>
                                            <th>Status</th>
                                            <th>Vytvořeno</th>
                                            <th>Akce</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($products as $product): ?>
                                            <tr>
                                                <td>
                                                    <strong><?= Utils::escape($product['name']) ?></strong>
                                                    <?php if ($product['is_featured']): ?>
                                                        <span style="background: #ffc107; color: #000; padding: 2px 6px; border-radius: 3px; font-size: 11px; margin-left: 5px;">DOPORUČENÝ</span>
                                                    <?php endif; ?>
                                                    <br>
                                                    <small style="color: #666;"><?= Utils::truncateText($product['description'], 60) ?></small>
                                                </td>
                                                <td>
                                                    <?= $product['price'] ? number_format($product['price'], 0, ',', ' ') . ' Kč' : '-' ?>
                                                </td>
                                                <td>
                                                    <span style="padding: 4px 8px; border-radius: 4px; font-size: 12px; 
                                                          background: <?= $product['is_active'] ? '#d4edda' : '#f8d7da' ?>; 
                                                          color: <?= $product['is_active'] ? '#155724' : '#721c24' ?>;">
                                                        <?= $product['is_active'] ? 'Aktivní' : 'Neaktivní' ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <?= Utils::formatCzechDate($product['created_at']) ?>
                                                </td>
                                                <td>
                                                    <div style="display: flex; gap: 5px;">
                                                        <a href="?action=edit&id=<?= $product['id'] ?>" 
                                                           class="btn-primary" style="text-decoration: none; font-size: 12px; padding: 4px 8px;">
                                                            ✏️ Upravit
                                                        </a>
                                                        <?php if ($product['eshop_url']): ?>
                                                            <a href="<?= Utils::escape($product['eshop_url']) ?>" 
                                                               target="_blank" class="btn-secondary" style="text-decoration: none; font-size: 12px; padding: 4px 8px;">
                                                                🔗 E-shop
                                                            </a>
                                                        <?php endif; ?>
                                                        <a href="?action=delete&id=<?= $product['id'] ?>" 
                                                           class="btn-danger" style="text-decoration: none; font-size: 12px; padding: 4px 8px;"
                                                           onclick="return confirm('Opravdu chcete smazat tento produkt?')">
                                                            🗑️ Smazat
                                                        </a>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            <?php else: ?>
                                <p style="text-align: center; color: #666; padding: 40px;">
                                    Žádné produkty nebyly nalezeny.
                                    <br><br>
                                    <a href="?action=add" class="btn-primary">➕ Přidat první produkt</a>
                                </p>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </main>
    </div>
</body>
</html>
