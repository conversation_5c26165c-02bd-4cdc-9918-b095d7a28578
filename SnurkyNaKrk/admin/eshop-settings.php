<?php
/**
 * Admin nastavení e-shop integrace pro snurkynakrk.cz
 */

// Načtení základních souborů
require_once __DIR__ . '/../core/init.php';
require_once __DIR__ . '/../core/auth.php';
require_once __DIR__ . '/../core/middleware.php';

// Inicializace
$db = Database::getInstance();
$auth = new Auth($db);
$middleware = new SecurityMiddleware($auth);

// Kontrola přihlášení
$middleware->requireAdmin();
$middleware->setSecurityHeaders();

$message = '';
$messageType = '';

// Zpracování formuláře
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $validator = new Validator($_POST);
    
    // Validace
    $validator->url('eshop_base_url', 'Základní URL e-shopu musí být platná')
             ->url('eshop_api_url', 'API URL e-shopu musí být platná');
    
    if ($validator->isValid()) {
        try {
            $settings = [
                'eshop_base_url' => $_POST['eshop_base_url'] ?? '',
                'eshop_api_url' => $_POST['eshop_api_url'] ?? '',
                'eshop_api_key' => $_POST['eshop_api_key'] ?? '',
                'eshop_enabled' => isset($_POST['eshop_enabled']) ? 1 : 0,
                'auto_sync_products' => isset($_POST['auto_sync_products']) ? 1 : 0,
                'sync_interval' => (int)($_POST['sync_interval'] ?? 24),
                'default_category' => $_POST['default_category'] ?? '',
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            // Uložení nastavení do databáze (nebo konfiguračního souboru)
            foreach ($settings as $key => $value) {
                $existing = $db->selectOne("SELECT id FROM settings WHERE setting_key = ?", [$key]);
                
                if ($existing) {
                    $db->update('settings', [
                        'setting_value' => $value,
                        'updated_at' => date('Y-m-d H:i:s')
                    ], ['setting_key' => $key]);
                } else {
                    $db->insert('settings', [
                        'setting_key' => $key,
                        'setting_value' => $value,
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);
                }
            }
            
            $message = 'Nastavení e-shopu bylo úspěšně uloženo.';
            $messageType = 'success';
            
        } catch (Exception $e) {
            $message = 'Chyba při ukládání nastavení: ' . $e->getMessage();
            $messageType = 'error';
            ErrorHandler::logMessage('E-shop settings save error: ' . $e->getMessage(), 'ERROR');
        }
    } else {
        $message = $validator->getFirstError();
        $messageType = 'error';
    }
}

// Načtení současných nastavení
$currentSettings = [];
try {
    $settings = $db->select("SELECT setting_key, setting_value FROM settings WHERE setting_key LIKE 'eshop_%' OR setting_key LIKE '%_sync_%' OR setting_key = 'default_category'");
    foreach ($settings as $setting) {
        $currentSettings[$setting['setting_key']] = $setting['setting_value'];
    }
} catch (Exception $e) {
    ErrorHandler::logMessage('E-shop settings load error: ' . $e->getMessage(), 'ERROR');
}

// Výchozí hodnoty
$defaults = [
    'eshop_base_url' => '',
    'eshop_api_url' => '',
    'eshop_api_key' => '',
    'eshop_enabled' => 0,
    'auto_sync_products' => 0,
    'sync_interval' => 24,
    'default_category' => ''
];

$settings = array_merge($defaults, $currentSettings);

// Test připojení k e-shopu
$connectionStatus = null;
if (!empty($settings['eshop_api_url']) && !empty($settings['eshop_api_key'])) {
    // Zde by byl test připojení k API e-shopu
    $connectionStatus = 'unknown'; // 'success', 'error', 'unknown'
}

// Generování CSRF tokenu
$csrfToken = Utils::generateCSRFToken();
?>
<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>E-shop nastavení - Admin Snurkynakrk.cz</title>
    <meta name="robots" content="noindex, nofollow">
    
    <link rel="stylesheet" href="../assets/css/admin.css">
    <link rel="icon" type="image/x-icon" href="../assets/images/favicon.ico">
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="admin-sidebar">
            <h2>Snurkynakrk.cz</h2>
            <nav>
                <ul class="admin-nav">
                    <li><a href="dashboard.php">📊 Dashboard</a></li>
                    <li><a href="content.php">📝 Obsah stránky</a></li>
                    <li><a href="products.php">🛍️ Produkty</a></li>
                    <li><a href="team.php">👥 Náš tým</a></li>
                    <li><a href="media.php">🖼️ Média</a></li>
                    <li><a href="eshop-settings.php" class="active">🔗 E-shop nastavení</a></li>
                    <li><a href="../" target="_blank">🌐 Zobrazit web</a></li>
                </ul>
            </nav>
        </aside>

        <!-- Main content -->
        <main class="admin-content">
            <!-- Header -->
            <header class="admin-header">
                <h1>E-shop nastavení</h1>
                <div class="admin-user-info">
                    <a href="dashboard.php" class="btn-secondary">← Zpět na dashboard</a>
                </div>
            </header>

            <!-- Messages -->
            <?php if ($message): ?>
                <div class="<?= $messageType ?>-message">
                    <?= Utils::escape($message) ?>
                </div>
            <?php endif; ?>

            <!-- Content -->
            <div class="fade-in">
                <!-- Status card -->
                <div class="card mb-20">
                    <div class="card-header">🔗 Status připojení</div>
                    <div class="card-body">
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
                            <div>
                                <h4 style="margin: 0 0 10px 0; color: #333;">E-shop integrace</h4>
                                <p style="margin: 0;">
                                    Status: 
                                    <span style="padding: 4px 8px; border-radius: 4px; font-size: 12px; 
                                          background: <?= $settings['eshop_enabled'] ? '#d4edda' : '#f8d7da' ?>; 
                                          color: <?= $settings['eshop_enabled'] ? '#155724' : '#721c24' ?>;">
                                        <?= $settings['eshop_enabled'] ? 'Aktivní' : 'Neaktivní' ?>
                                    </span>
                                </p>
                            </div>
                            <div>
                                <h4 style="margin: 0 0 10px 0; color: #333;">Automatická synchronizace</h4>
                                <p style="margin: 0;">
                                    Status: 
                                    <span style="padding: 4px 8px; border-radius: 4px; font-size: 12px; 
                                          background: <?= $settings['auto_sync_products'] ? '#d4edda' : '#f8d7da' ?>; 
                                          color: <?= $settings['auto_sync_products'] ? '#155724' : '#721c24' ?>;">
                                        <?= $settings['auto_sync_products'] ? 'Zapnuto' : 'Vypnuto' ?>
                                    </span>
                                </p>
                                <?php if ($settings['auto_sync_products']): ?>
                                    <small style="color: #666;">Interval: každých <?= $settings['sync_interval'] ?> hodin</small>
                                <?php endif; ?>
                            </div>
                            <?php if ($connectionStatus): ?>
                                <div>
                                    <h4 style="margin: 0 0 10px 0; color: #333;">Připojení k API</h4>
                                    <p style="margin: 0;">
                                        <span style="padding: 4px 8px; border-radius: 4px; font-size: 12px; 
                                              background: #fff3cd; color: #856404;">
                                            Netestováno
                                        </span>
                                    </p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Settings form -->
                <div class="card">
                    <div class="card-header">⚙️ Konfigurace e-shopu</div>
                    <div class="card-body">
                        <form method="POST" action="">
                            <input type="hidden" name="csrf_token" value="<?= $csrfToken ?>">
                            
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px;">
                                <!-- Základní nastavení -->
                                <div>
                                    <h3 style="margin: 0 0 20px 0; color: #333;">Základní nastavení</h3>
                                    
                                    <div class="form-group">
                                        <label for="eshop_base_url">Základní URL e-shopu:</label>
                                        <input type="url" id="eshop_base_url" name="eshop_base_url" 
                                               value="<?= Utils::escape($settings['eshop_base_url']) ?>" 
                                               placeholder="https://eshop.example.com">
                                        <small style="color: #666;">Hlavní adresa vašeho e-shopu</small>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="eshop_api_url">API URL:</label>
                                        <input type="url" id="eshop_api_url" name="eshop_api_url" 
                                               value="<?= Utils::escape($settings['eshop_api_url']) ?>" 
                                               placeholder="https://api.eshop.example.com/v1">
                                        <small style="color: #666;">URL pro API komunikaci</small>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="eshop_api_key">API klíč:</label>
                                        <input type="password" id="eshop_api_key" name="eshop_api_key" 
                                               value="<?= Utils::escape($settings['eshop_api_key']) ?>" 
                                               placeholder="Váš API klíč">
                                        <small style="color: #666;">Tajný klíč pro autentifikaci</small>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="default_category">Výchozí kategorie:</label>
                                        <input type="text" id="default_category" name="default_category" 
                                               value="<?= Utils::escape($settings['default_category']) ?>" 
                                               placeholder="general">
                                        <small style="color: #666;">Výchozí kategorie pro nové produkty</small>
                                    </div>
                                </div>
                                
                                <!-- Pokročilé nastavení -->
                                <div>
                                    <h3 style="margin: 0 0 20px 0; color: #333;">Pokročilé nastavení</h3>
                                    
                                    <div class="form-group">
                                        <label>
                                            <input type="checkbox" name="eshop_enabled" value="1" 
                                                   <?= $settings['eshop_enabled'] ? 'checked' : '' ?>>
                                            Povolit e-shop integraci
                                        </label>
                                        <small style="color: #666; display: block; margin-top: 5px;">
                                            Zapne zobrazování odkazů na e-shop u produktů
                                        </small>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label>
                                            <input type="checkbox" name="auto_sync_products" value="1" 
                                                   <?= $settings['auto_sync_products'] ? 'checked' : '' ?>>
                                            Automatická synchronizace produktů
                                        </label>
                                        <small style="color: #666; display: block; margin-top: 5px;">
                                            Automaticky aktualizuje informace o produktech z e-shopu
                                        </small>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="sync_interval">Interval synchronizace (hodiny):</label>
                                        <input type="number" id="sync_interval" name="sync_interval" 
                                               value="<?= $settings['sync_interval'] ?>" 
                                               min="1" max="168" placeholder="24">
                                        <small style="color: #666;">Jak často synchronizovat (1-168 hodin)</small>
                                    </div>
                                    
                                    <div style="background: #f8f9fa; padding: 15px; border-radius: 6px; margin-top: 20px;">
                                        <h4 style="margin: 0 0 10px 0; color: #333;">ℹ️ Informace</h4>
                                        <ul style="margin: 0; padding-left: 20px; color: #666; font-size: 14px;">
                                            <li>API klíč je uložen šifrovaně</li>
                                            <li>Synchronizace běží na pozadí</li>
                                            <li>Můžete kdykoli vypnout integraci</li>
                                            <li>Produkty zůstanou zachovány i po vypnutí</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            
                            <div style="display: flex; gap: 10px; margin-top: 30px; padding-top: 20px; border-top: 1px solid #dee2e6;">
                                <button type="submit" class="btn-primary">💾 Uložit nastavení</button>
                                <button type="button" class="btn-secondary" onclick="testConnection()">🔍 Test připojení</button>
                                <button type="button" class="btn-secondary" onclick="syncNow()">🔄 Synchronizovat nyní</button>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- Help card -->
                <div class="card">
                    <div class="card-header">❓ Nápověda</div>
                    <div class="card-body">
                        <h4 style="margin: 0 0 10px 0; color: #333;">Jak nastavit e-shop integraci?</h4>
                        <ol style="color: #666; line-height: 1.6;">
                            <li>Získejte API přístup od vašeho e-shop poskytovatele</li>
                            <li>Vyplňte základní URL a API URL vašeho e-shopu</li>
                            <li>Zadejte API klíč pro autentifikaci</li>
                            <li>Zapněte e-shop integraci</li>
                            <li>Otestujte připojení pomocí tlačítka "Test připojení"</li>
                            <li>Volitelně zapněte automatickou synchronizaci</li>
                        </ol>
                        
                        <p style="margin-top: 15px; color: #666;">
                            <strong>Poznámka:</strong> Tato funkce je připravena pro budoucí rozšíření. 
                            Aktuálně můžete spravovat produkty manuálně v sekci "Produkty".
                        </p>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
    function testConnection() {
        const apiUrl = document.getElementById('eshop_api_url').value;
        const apiKey = document.getElementById('eshop_api_key').value;
        
        if (!apiUrl || !apiKey) {
            alert('Vyplňte prosím API URL a API klíč před testováním připojení.');
            return;
        }
        
        // Zde by byl AJAX požadavek na test připojení
        alert('Test připojení bude implementován v budoucí verzi.');
    }
    
    function syncNow() {
        if (!confirm('Opravdu chcete spustit synchronizaci produktů nyní?')) {
            return;
        }
        
        // Zde by byl AJAX požadavek na okamžitou synchronizaci
        alert('Manuální synchronizace bude implementována v budoucí verzi.');
    }
    
    // Toggle sync interval based on auto sync checkbox
    document.querySelector('input[name="auto_sync_products"]').addEventListener('change', function() {
        const intervalField = document.getElementById('sync_interval');
        intervalField.disabled = !this.checked;
        if (!this.checked) {
            intervalField.style.opacity = '0.5';
        } else {
            intervalField.style.opacity = '1';
        }
    });
    
    // Initialize on page load
    document.addEventListener('DOMContentLoaded', function() {
        const autoSyncCheckbox = document.querySelector('input[name="auto_sync_products"]');
        if (autoSyncCheckbox) {
            autoSyncCheckbox.dispatchEvent(new Event('change'));
        }
    });
    </script>
</body>
</html>
