<?php
/**
 * Debug script pro admin slo<PERSON>ku
 */

echo "<h1>🔍 Debug admin slo<PERSON>ky</h1>";

echo "<h2>1. Aktuá<PERSON><PERSON> um<PERSON>í</h2>";
echo "Script path: " . __FILE__ . "<br>";
echo "Directory: " . __DIR__ . "<br>";
echo "Current working directory: " . getcwd() . "<br>";

echo "<h2>2. Test cest</h2>";

$paths_to_test = [
    '__DIR__ . "/../core/init.php"' => __DIR__ . '/../core/init.php',
    '__DIR__ . "/../core/auth.php"' => __DIR__ . '/../core/auth.php',
    '__DIR__ . "/../core/middleware.php"' => __DIR__ . '/../core/middleware.php',
    '../core/init.php' => '../core/init.php',
    'core/init.php' => 'core/init.php'
];

foreach ($paths_to_test as $description => $path) {
    if (file_exists($path)) {
        echo "✅ $description -> $path (existuje)<br>";
    } else {
        echo "❌ $description -> $path (NEEXISTUJE)<br>";
    }
}

echo "<h2>3. Obsah nadřazené složky</h2>";
$parent_dir = __DIR__ . '/..';
if (is_dir($parent_dir)) {
    echo "Obsah $parent_dir:<br>";
    $files = scandir($parent_dir);
    foreach ($files as $file) {
        if ($file !== '.' && $file !== '..') {
            $type = is_dir($parent_dir . '/' . $file) ? '[DIR]' : '[FILE]';
            echo "$type $file<br>";
        }
    }
} else {
    echo "Nadřazená složka neexistuje<br>";
}

echo "<h2>4. Test načtení init.php</h2>";

$init_path = __DIR__ . '/../core/init.php';
if (file_exists($init_path)) {
    echo "✅ Soubor $init_path existuje<br>";
    echo "Velikost: " . filesize($init_path) . " bytů<br>";
    echo "Oprávnění: " . substr(sprintf('%o', fileperms($init_path)), -4) . "<br>";
    
    try {
        require_once $init_path;
        echo "✅ init.php úspěšně načten<br>";
    } catch (Exception $e) {
        echo "❌ Chyba při načítání init.php: " . $e->getMessage() . "<br>";
    }
} else {
    echo "❌ Soubor $init_path NEEXISTUJE<br>";
}

echo "<h2>5. PHP informace</h2>";
echo "PHP verze: " . PHP_VERSION . "<br>";
echo "Include path: " . get_include_path() . "<br>";
echo "Error reporting: " . error_reporting() . "<br>";
echo "Display errors: " . ini_get('display_errors') . "<br>";

echo "<h2>6. Server informace</h2>";
echo "Server: " . ($_SERVER['SERVER_NAME'] ?? 'neznámý') . "<br>";
echo "Document root: " . ($_SERVER['DOCUMENT_ROOT'] ?? 'neznámý') . "<br>";
echo "Script name: " . ($_SERVER['SCRIPT_NAME'] ?? 'neznámý') . "<br>";
echo "Request URI: " . ($_SERVER['REQUEST_URI'] ?? 'neznámý') . "<br>";

echo "<h2>✅ Debug dokončen</h2>";
echo "<p><a href='login.php'>→ Zkusit login.php</a></p>";
?>
