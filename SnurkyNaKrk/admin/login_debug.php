<?php
/**
 * Debug verze login.php - uk<PERSON>že kde se to láme
 */

// Zapnutí zobrazování chyb
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔍 Debug login.php</h1>";
echo "<p>Krok za krokem debugging...</p>";

echo "<h2>Krok 1: Základní PHP test</h2>";
echo "✅ PHP funguje<br>";
echo "PHP verze: " . PHP_VERSION . "<br>";
echo "Current directory: " . getcwd() . "<br>";

echo "<h2>Krok 2: Test existence souborů</h2>";
$files = [
    '../core/init.php',
    '../core/auth.php',
    '../core/middleware.php'
];

foreach ($files as $file) {
    if (file_exists($file)) {
        echo "✅ $file existuje (" . filesize($file) . " bytů)<br>";
    } else {
        echo "❌ $file NEEXISTUJE<br>";
    }
}

echo "<h2>Krok 3: Test načtení init.php</h2>";
try {
    echo "Načítám ../core/init.php...<br>";
    require_once __DIR__ . '/../core/init.php';
    echo "✅ init.php načten úspěšně<br>";
} catch (ParseError $e) {
    echo "❌ Parse error v init.php: " . $e->getMessage() . "<br>";
    echo "Soubor: " . $e->getFile() . "<br>";
    echo "Řádek: " . $e->getLine() . "<br>";
    exit;
} catch (Error $e) {
    echo "❌ Fatal error v init.php: " . $e->getMessage() . "<br>";
    echo "Soubor: " . $e->getFile() . "<br>";
    echo "Řádek: " . $e->getLine() . "<br>";
    exit;
} catch (Exception $e) {
    echo "❌ Exception v init.php: " . $e->getMessage() . "<br>";
    echo "Soubor: " . $e->getFile() . "<br>";
    echo "Řádek: " . $e->getLine() . "<br>";
    exit;
}

echo "<h2>Krok 4: Test načtení auth.php</h2>";
try {
    echo "Načítám ../core/auth.php...<br>";
    require_once __DIR__ . '/../core/auth.php';
    echo "✅ auth.php načten úspěšně<br>";
} catch (ParseError $e) {
    echo "❌ Parse error v auth.php: " . $e->getMessage() . "<br>";
    echo "Soubor: " . $e->getFile() . "<br>";
    echo "Řádek: " . $e->getLine() . "<br>";
    exit;
} catch (Error $e) {
    echo "❌ Fatal error v auth.php: " . $e->getMessage() . "<br>";
    echo "Soubor: " . $e->getFile() . "<br>";
    echo "Řádek: " . $e->getLine() . "<br>";
    exit;
} catch (Exception $e) {
    echo "❌ Exception v auth.php: " . $e->getMessage() . "<br>";
    echo "Soubor: " . $e->getFile() . "<br>";
    echo "Řádek: " . $e->getLine() . "<br>";
    exit;
}

echo "<h2>Krok 5: Test načtení middleware.php</h2>";
try {
    echo "Načítám ../core/middleware.php...<br>";
    require_once __DIR__ . '/../core/middleware.php';
    echo "✅ middleware.php načten úspěšně<br>";
} catch (ParseError $e) {
    echo "❌ Parse error v middleware.php: " . $e->getMessage() . "<br>";
    echo "Soubor: " . $e->getFile() . "<br>";
    echo "Řádek: " . $e->getLine() . "<br>";
    exit;
} catch (Error $e) {
    echo "❌ Fatal error v middleware.php: " . $e->getMessage() . "<br>";
    echo "Soubor: " . $e->getFile() . "<br>";
    echo "Řádek: " . $e->getLine() . "<br>";
    exit;
} catch (Exception $e) {
    echo "❌ Exception v middleware.php: " . $e->getMessage() . "<br>";
    echo "Soubor: " . $e->getFile() . "<br>";
    echo "Řádek: " . $e->getLine() . "<br>";
    exit;
}

echo "<h2>Krok 6: Test vytvoření Database objektu</h2>";
try {
    echo "Vytvářím Database objekt pomocí getInstance()...<br>";
    $db = Database::getInstance();
    echo "✅ Database objekt vytvořen<br>";
} catch (Error $e) {
    echo "❌ Fatal error při vytváření Database: " . $e->getMessage() . "<br>";
    echo "Soubor: " . $e->getFile() . "<br>";
    echo "Řádek: " . $e->getLine() . "<br>";
    exit;
} catch (Exception $e) {
    echo "❌ Exception při vytváření Database: " . $e->getMessage() . "<br>";
    echo "Soubor: " . $e->getFile() . "<br>";
    echo "Řádek: " . $e->getLine() . "<br>";
    exit;
}

echo "<h2>Krok 7: Test vytvoření Auth objektu</h2>";
try {
    echo "Vytvářím Auth objekt...<br>";
    $auth = new Auth($db);
    echo "✅ Auth objekt vytvořen<br>";
} catch (Error $e) {
    echo "❌ Fatal error při vytváření Auth: " . $e->getMessage() . "<br>";
    echo "Soubor: " . $e->getFile() . "<br>";
    echo "Řádek: " . $e->getLine() . "<br>";
    exit;
} catch (Exception $e) {
    echo "❌ Exception při vytváření Auth: " . $e->getMessage() . "<br>";
    echo "Soubor: " . $e->getFile() . "<br>";
    echo "Řádek: " . $e->getLine() . "<br>";
    exit;
}

echo "<h2>Krok 8: Test vytvoření SecurityMiddleware objektu</h2>";
try {
    echo "Vytvářím SecurityMiddleware objekt...<br>";
    $middleware = new SecurityMiddleware($auth);
    echo "✅ SecurityMiddleware objekt vytvořen<br>";
} catch (Error $e) {
    echo "❌ Fatal error při vytváření SecurityMiddleware: " . $e->getMessage() . "<br>";
    echo "Soubor: " . $e->getFile() . "<br>";
    echo "Řádek: " . $e->getLine() . "<br>";
    exit;
} catch (Exception $e) {
    echo "❌ Exception při vytváření SecurityMiddleware: " . $e->getMessage() . "<br>";
    echo "Soubor: " . $e->getFile() . "<br>";
    echo "Řádek: " . $e->getLine() . "<br>";
    exit;
}

echo "<h2>Krok 9: Test základních funkcí</h2>";
try {
    echo "Test isLoggedIn()...<br>";
    $logged_in = $auth->isLoggedIn();
    echo "✅ isLoggedIn() funguje: " . ($logged_in ? 'true' : 'false') . "<br>";
    
    echo "Test generateCSRFToken()...<br>";
    $token = $auth->generateCSRFToken();
    echo "✅ generateCSRFToken() funguje: " . substr($token, 0, 10) . "...<br>";
    
} catch (Error $e) {
    echo "❌ Fatal error při testování funkcí: " . $e->getMessage() . "<br>";
    echo "Soubor: " . $e->getFile() . "<br>";
    echo "Řádek: " . $e->getLine() . "<br>";
    exit;
} catch (Exception $e) {
    echo "❌ Exception při testování funkcí: " . $e->getMessage() . "<br>";
    echo "Soubor: " . $e->getFile() . "<br>";
    echo "Řádek: " . $e->getLine() . "<br>";
    exit;
}

echo "<h2>🎉 Všechny testy prošly!</h2>";
echo "<p>Pokud se dostanete až sem, všechny komponenty fungují správně.</p>";
echo "<p>Problém s původním login.php musí být někde jinde.</p>";

echo "<h3>🔗 Testovací odkazy:</h3>";
echo "<a href='login.php'>→ Zkusit původní login.php</a><br>";
echo "<a href='login_minimal.php'>→ Minimální login</a><br>";
echo "<a href='../simple_test.php'>→ Jednoduchý test</a><br>";

// Pokud se dostaneme až sem, zkusíme zobrazit jednoduchý login formulář
echo "<h3>🔐 Test login formuláře:</h3>";
echo '<form method="POST">';
echo '<input type="text" name="username" placeholder="admin" value="admin"><br><br>';
echo '<input type="password" name="password" placeholder="heslo"><br><br>';
echo '<input type="submit" value="Test přihlášení">';
echo '</form>';

if ($_POST) {
    $user = $_POST['username'] ?? '';
    $pass = $_POST['password'] ?? '';
    
    try {
        if ($auth->login($user, $pass)) {
            echo "<div style='background: green; color: white; padding: 10px;'>✅ Přihlášení úspěšné!</div>";
        } else {
            echo "<div style='background: red; color: white; padding: 10px;'>❌ Nesprávné údaje</div>";
        }
    } catch (Exception $e) {
        echo "<div style='background: red; color: white; padding: 10px;'>❌ Chyba při přihlašování: " . $e->getMessage() . "</div>";
    }
}
?>
