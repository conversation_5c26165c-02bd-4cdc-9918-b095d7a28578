<?php
/**
 * Admin správa médií pro snurkynakrk.cz
 */

// Načtení základní<PERSON> so<PERSON>orů
require_once __DIR__ . '/../core/init.php';
require_once __DIR__ . '/../core/auth.php';
require_once __DIR__ . '/../core/middleware.php';

// Inicializace
$db = Database::getInstance();
$auth = new Auth($db);
$middleware = new SecurityMiddleware($auth);

// Kontrola přihlášení
$middleware->requireAdmin();
$middleware->setSecurityHeaders();

$message = '';
$messageType = '';

// Zpracování akcí
$action = $_GET['action'] ?? 'list';
$mediaId = $_GET['id'] ?? null;

// Zpracování upload
if ($_SERVER['REQUEST_METHOD'] === 'POST' && $action === 'upload') {
    if (isset($_FILES['media_file']) && $_FILES['media_file']['error'] === UPLOAD_ERR_OK) {
        $file = $_FILES['media_file'];
        $category = $_POST['category'] ?? 'general';
        
        // Validace souboru
        $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        $maxSize = 5 * 1024 * 1024; // 5MB
        
        if (!in_array($file['type'], $allowedTypes)) {
            $message = 'Povolené jsou pouze obrázky (JPEG, PNG, GIF, WebP).';
            $messageType = 'error';
        } elseif ($file['size'] > $maxSize) {
            $message = 'Soubor je příliš velký. Maximální velikost je 5MB.';
            $messageType = 'error';
        } else {
            try {
                // Vytvoření bezpečného názvu souboru
                $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
                $filename = Utils::createSlug(pathinfo($file['name'], PATHINFO_FILENAME)) . '_' . time() . '.' . $extension;
                
                // Určení cílové složky
                $uploadDir = __DIR__ . '/../uploads/' . $category . '/';
                if (!is_dir($uploadDir)) {
                    mkdir($uploadDir, 0755, true);
                }
                
                $targetPath = $uploadDir . $filename;
                $relativePath = 'uploads/' . $category . '/' . $filename;
                
                if (move_uploaded_file($file['tmp_name'], $targetPath)) {
                    // Uložení do databáze
                    $result = $db->insert('media_files', [
                        'filename' => $filename,
                        'original_name' => $file['name'],
                        'file_path' => $relativePath,
                        'file_size' => $file['size'],
                        'mime_type' => $file['type'],
                        'category' => $category,
                        'created_at' => date('Y-m-d H:i:s')
                    ]);
                    
                    if ($result) {
                        $message = 'Soubor byl úspěšně nahrán.';
                        $messageType = 'success';
                    } else {
                        $message = 'Chyba při ukládání do databáze.';
                        $messageType = 'error';
                        unlink($targetPath); // Smazání souboru při chybě DB
                    }
                } else {
                    $message = 'Chyba při nahrávání souboru.';
                    $messageType = 'error';
                }
            } catch (Exception $e) {
                $message = 'Chyba: ' . $e->getMessage();
                $messageType = 'error';
                ErrorHandler::logMessage('Media upload error: ' . $e->getMessage(), 'ERROR');
            }
        }
    } else {
        $message = 'Nebyl vybrán žádný soubor nebo došlo k chybě.';
        $messageType = 'error';
    }
}

// Zpracování mazání
if ($action === 'delete' && $mediaId) {
    try {
        $media = $db->selectOne("SELECT * FROM media_files WHERE id = ?", [$mediaId]);
        if ($media) {
            // Smazání souboru z disku
            $filePath = __DIR__ . '/../' . $media['file_path'];
            if (file_exists($filePath)) {
                unlink($filePath);
            }
            
            // Smazání z databáze
            $result = $db->delete('media_files', ['id' => $mediaId]);
            if ($result) {
                $message = 'Soubor byl úspěšně smazán.';
                $messageType = 'success';
            } else {
                $message = 'Chyba při mazání z databáze.';
                $messageType = 'error';
            }
        } else {
            $message = 'Soubor nebyl nalezen.';
            $messageType = 'error';
        }
        $action = 'list';
    } catch (Exception $e) {
        $message = 'Chyba: ' . $e->getMessage();
        $messageType = 'error';
        ErrorHandler::logMessage('Media delete error: ' . $e->getMessage(), 'ERROR');
    }
}

// Získání dat
$mediaFiles = [];
$categories = ['general' => 'Obecné', 'products' => 'Produkty', 'team' => 'Tým', 'content' => 'Obsah'];
$selectedCategory = $_GET['category'] ?? 'all';

try {
    $sql = "SELECT * FROM media_files";
    $params = [];
    
    if ($selectedCategory !== 'all') {
        $sql .= " WHERE category = ?";
        $params[] = $selectedCategory;
    }
    
    $sql .= " ORDER BY created_at DESC";
    $mediaFiles = $db->select($sql, $params);
} catch (Exception $e) {
    $message = 'Chyba při načítání médií: ' . $e->getMessage();
    $messageType = 'error';
    ErrorHandler::logMessage('Media load error: ' . $e->getMessage(), 'ERROR');
}

// Generování CSRF tokenu
$csrfToken = Utils::generateCSRFToken();
?>
<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Správa médií - Admin Snurkynakrk.cz</title>
    <meta name="robots" content="noindex, nofollow">
    
    <link rel="stylesheet" href="../assets/css/admin.css">
    <link rel="icon" type="image/x-icon" href="../assets/images/favicon.ico">
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="admin-sidebar">
            <h2>Snurkynakrk.cz</h2>
            <nav>
                <ul class="admin-nav">
                    <li><a href="dashboard.php">📊 Dashboard</a></li>
                    <li><a href="content.php">📝 Obsah stránky</a></li>
                    <li><a href="products.php">🛍️ Produkty</a></li>
                    <li><a href="team.php">👥 Náš tým</a></li>
                    <li><a href="media.php" class="active">🖼️ Média</a></li>
                    <li><a href="eshop-settings.php">🔗 E-shop nastavení</a></li>
                    <li><a href="../" target="_blank">🌐 Zobrazit web</a></li>
                </ul>
            </nav>
        </aside>

        <!-- Main content -->
        <main class="admin-content">
            <!-- Header -->
            <header class="admin-header">
                <h1>Správa médií</h1>
                <div class="admin-user-info">
                    <select onchange="window.location.href='?category=' + this.value" style="margin-right: 10px;">
                        <option value="all" <?= $selectedCategory === 'all' ? 'selected' : '' ?>>Všechny kategorie</option>
                        <?php foreach ($categories as $key => $name): ?>
                            <option value="<?= $key ?>" <?= $selectedCategory === $key ? 'selected' : '' ?>>
                                <?= $name ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
            </header>

            <!-- Messages -->
            <?php if ($message): ?>
                <div class="<?= $messageType ?>-message">
                    <?= Utils::escape($message) ?>
                </div>
            <?php endif; ?>

            <!-- Upload form -->
            <div class="card mb-20">
                <div class="card-header">📤 Nahrát nový soubor</div>
                <div class="card-body">
                    <form method="POST" enctype="multipart/form-data" action="?action=upload">
                        <input type="hidden" name="csrf_token" value="<?= $csrfToken ?>">
                        
                        <div style="display: grid; grid-template-columns: 1fr auto auto; gap: 15px; align-items: end;">
                            <div class="form-group" style="margin: 0;">
                                <label for="media_file">Vyberte soubor:</label>
                                <input type="file" id="media_file" name="media_file" 
                                       accept="image/jpeg,image/png,image/gif,image/webp" required>
                                <small style="color: #666;">Povolené formáty: JPEG, PNG, GIF, WebP. Max. 5MB</small>
                            </div>
                            
                            <div class="form-group" style="margin: 0;">
                                <label for="category">Kategorie:</label>
                                <select id="category" name="category">
                                    <?php foreach ($categories as $key => $name): ?>
                                        <option value="<?= $key ?>"><?= $name ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <button type="submit" class="btn-primary">📤 Nahrát</button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Media gallery -->
            <div class="card">
                <div class="card-header">
                    Galerie médií (<?= count($mediaFiles) ?> souborů)
                </div>
                <div class="card-body">
                    <?php if (!empty($mediaFiles)): ?>
                        <div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); gap: 20px;">
                            <?php foreach ($mediaFiles as $media): ?>
                                <div class="card" style="margin: 0;">
                                    <div style="aspect-ratio: 1; overflow: hidden; border-radius: 8px 8px 0 0;">
                                        <img src="../<?= Utils::escape($media['file_path']) ?>" 
                                             alt="<?= Utils::escape($media['original_name']) ?>"
                                             style="width: 100%; height: 100%; object-fit: cover; cursor: pointer;"
                                             onclick="openImageModal('<?= Utils::escape($media['file_path']) ?>', '<?= Utils::escape($media['original_name']) ?>')">
                                    </div>
                                    <div class="card-body" style="padding: 10px;">
                                        <h4 style="margin: 0 0 5px 0; font-size: 14px; color: #333;">
                                            <?= Utils::truncateText($media['original_name'], 25) ?>
                                        </h4>
                                        <p style="margin: 0 0 5px 0; font-size: 12px; color: #666;">
                                            <?= Utils::formatFileSize($media['file_size']) ?> • 
                                            <?= $categories[$media['category']] ?? $media['category'] ?>
                                        </p>
                                        <p style="margin: 0 0 10px 0; font-size: 11px; color: #999;">
                                            <?= Utils::formatCzechDate($media['created_at']) ?>
                                        </p>
                                        <div style="display: flex; gap: 5px;">
                                            <button onclick="copyToClipboard('<?= Utils::escape($media['file_path']) ?>')" 
                                                    class="btn-secondary" style="font-size: 11px; padding: 4px 8px; flex: 1;">
                                                📋 Kopírovat URL
                                            </button>
                                            <a href="?action=delete&id=<?= $media['id'] ?>" 
                                               class="btn-danger" style="text-decoration: none; font-size: 11px; padding: 4px 8px;"
                                               onclick="return confirm('Opravdu chcete smazat tento soubor?')">
                                                🗑️
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <p style="text-align: center; color: #666; padding: 40px;">
                            <?php if ($selectedCategory === 'all'): ?>
                                Žádné soubory nebyly nalezeny.
                            <?php else: ?>
                                Žádné soubory v kategorii "<?= $categories[$selectedCategory] ?>" nebyly nalezeny.
                            <?php endif; ?>
                        </p>
                    <?php endif; ?>
                </div>
            </div>
        </main>
    </div>

    <!-- Modal pro zobrazení obrázku -->
    <div id="imageModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; 
                                background: rgba(0,0,0,0.8); z-index: 1000; cursor: pointer;" onclick="closeImageModal()">
        <div style="display: flex; align-items: center; justify-content: center; height: 100%; padding: 20px;">
            <img id="modalImage" style="max-width: 90%; max-height: 90%; object-fit: contain;">
        </div>
    </div>

    <script>
    function openImageModal(imagePath, imageName) {
        document.getElementById('modalImage').src = '../' + imagePath;
        document.getElementById('modalImage').alt = imageName;
        document.getElementById('imageModal').style.display = 'block';
    }
    
    function closeImageModal() {
        document.getElementById('imageModal').style.display = 'none';
    }
    
    function copyToClipboard(path) {
        const fullUrl = window.location.origin + '/' + path;
        navigator.clipboard.writeText(fullUrl).then(function() {
            alert('URL byla zkopírována do schránky: ' + fullUrl);
        }).catch(function() {
            // Fallback pro starší prohlížeče
            const textArea = document.createElement('textarea');
            textArea.value = fullUrl;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            alert('URL byla zkopírována do schránky: ' + fullUrl);
        });
    }
    
    // Drag & drop upload (budoucí rozšíření)
    document.addEventListener('DOMContentLoaded', function() {
        const fileInput = document.getElementById('media_file');
        const form = fileInput.closest('form');
        
        // Prevence výchozího chování pro drag & drop
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            form.addEventListener(eventName, preventDefaults, false);
        });
        
        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }
    });
    </script>
</body>
</html>
