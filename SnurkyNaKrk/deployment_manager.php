<?php
/**
 * Deployment Manager pro snurkynakrk.cz
 * 
 * <PERSON><PERSON>é rozhraní pro správu deploymentu
 */

// Načtení FTP deployeru
require_once 'deploy_ftp.php';

// Konfigurace
$project_files = [
    'core' => [
        'core/config.php',
        'core/database.php', 
        'core/init.php',
        'core/auth.php',
        'core/middleware.php'
    ],
    'admin' => [
        'admin/login.php',
        'admin/dashboard.php'
    ],
    'assets' => [
        'assets/css/admin.css'
    ],
    'database' => [
        'database_schema.sql'
    ],
    'tests' => [
        'test_database.php',
        'test_auth_basic.php'
    ]
];

// Zpracování akcí
$action = $_GET['action'] ?? '';
$result = '';
$error = '';

if ($action) {
    ob_start();
    
    try {
        $deployer = new FTPDeployer(FTP_HOST, FTP_USER, FTP_PASS, FTP_ROOT);
        
        switch ($action) {
            case 'test':
                echo "🧪 Testování FTP připojení...\n\n";
                $deployer->testConnection();
                break;
                
            case 'deploy_all':
                echo "🚀 Deployment všech souborů...\n\n";
                deployToProduction();
                break;
                
            case 'deploy_core':
                echo "🔧 Deployment core souborů...\n\n";
                $deployer->connect();
                foreach ($project_files['core'] as $file) {
                    if (file_exists($file)) {
                        echo "📤 Nahrávám: {$file}\n";
                        $deployer->uploadFile($file, $file);
                        echo "✅ Nahráno: {$file}\n";
                    }
                }
                $deployer->disconnect();
                break;
                
            case 'deploy_admin':
                echo "👤 Deployment admin souborů...\n\n";
                $deployer->connect();
                foreach ($project_files['admin'] as $file) {
                    if (file_exists($file)) {
                        echo "📤 Nahrávám: {$file}\n";
                        $deployer->uploadFile($file, $file);
                        echo "✅ Nahráno: {$file}\n";
                    }
                }
                $deployer->disconnect();
                break;
                
            case 'deploy_assets':
                echo "🎨 Deployment assets...\n\n";
                $deployer->connect();
                foreach ($project_files['assets'] as $file) {
                    if (file_exists($file)) {
                        echo "📤 Nahrávám: {$file}\n";
                        $deployer->uploadFile($file, $file);
                        echo "✅ Nahráno: {$file}\n";
                    }
                }
                $deployer->disconnect();
                break;
                
            case 'deploy_database':
                echo "🗄️ Deployment databázových souborů...\n\n";
                $deployer->connect();
                foreach ($project_files['database'] as $file) {
                    if (file_exists($file)) {
                        echo "📤 Nahrávám: {$file}\n";
                        $deployer->uploadFile($file, $file);
                        echo "✅ Nahráno: {$file}\n";
                    }
                }
                $deployer->disconnect();
                break;
        }
        
        $result = ob_get_clean();
        
    } catch (Exception $e) {
        ob_end_clean();
        $error = $e->getMessage();
    }
}

// Kontrola stavu souborů
function getFileStatus($file) {
    if (!file_exists($file)) {
        return ['status' => 'missing', 'icon' => '❌', 'class' => 'text-danger'];
    }
    
    $size = filesize($file);
    $modified = filemtime($file);
    
    return [
        'status' => 'ready',
        'icon' => '✅',
        'class' => 'text-success',
        'size' => $size,
        'modified' => $modified
    ];
}
?>
<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Deployment Manager - snurkynakrk.cz</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .file-list { font-family: monospace; font-size: 0.9em; }
        .deployment-result { background: #f8f9fa; border-radius: 8px; padding: 20px; white-space: pre-wrap; font-family: monospace; }
        .status-card { border-left: 4px solid #007bff; }
        .action-card { transition: transform 0.2s; }
        .action-card:hover { transform: translateY(-2px); }
    </style>
</head>
<body class="bg-light">
    <div class="container py-4">
        <div class="row">
            <div class="col-12">
                <h1 class="mb-4">🚀 Deployment Manager</h1>
                <p class="lead">Správa deploymentu pro snurkynakrk.cz</p>
            </div>
        </div>

        <!-- Server informace -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card status-card">
                    <div class="card-header">
                        <h5 class="mb-0">🌐 Server informace</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>Host:</strong> <?= FTP_HOST ?></p>
                        <p><strong>Uživatel:</strong> <?= FTP_USER ?></p>
                        <p><strong>Cesta:</strong> <?= FTP_ROOT ?></p>
                        <p class="mb-0"><strong>Protokol:</strong> FTP</p>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card status-card">
                    <div class="card-header">
                        <h5 class="mb-0">📊 Statistiky projektu</h5>
                    </div>
                    <div class="card-body">
                        <?php
                        $total_files = 0;
                        $ready_files = 0;
                        foreach ($project_files as $category => $files) {
                            foreach ($files as $file) {
                                $total_files++;
                                if (file_exists($file)) $ready_files++;
                            }
                        }
                        ?>
                        <p><strong>Celkem souborů:</strong> <?= $total_files ?></p>
                        <p><strong>Připraveno:</strong> <?= $ready_files ?></p>
                        <p><strong>Chybí:</strong> <?= $total_files - $ready_files ?></p>
                        <div class="progress">
                            <div class="progress-bar" style="width: <?= round(($ready_files / $total_files) * 100) ?>%">
                                <?= round(($ready_files / $total_files) * 100) ?>%
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Deployment akce -->
        <div class="row mb-4">
            <div class="col-12">
                <h3>⚡ Deployment akce</h3>
            </div>
            <div class="col-md-4 mb-3">
                <div class="card action-card h-100">
                    <div class="card-body text-center">
                        <h5>🧪 Test připojení</h5>
                        <p>Ověří FTP připojení k serveru</p>
                        <a href="?action=test" class="btn btn-outline-primary">Spustit test</a>
                    </div>
                </div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="card action-card h-100">
                    <div class="card-body text-center">
                        <h5>🚀 Kompletní deployment</h5>
                        <p>Nahraje všechny soubory na server</p>
                        <a href="?action=deploy_all" class="btn btn-success" 
                           onclick="return confirm('Opravdu chcete nahrát všechny soubory?')">
                           Deploy vše
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="card action-card h-100">
                    <div class="card-body text-center">
                        <h5>🔧 Částečný deployment</h5>
                        <p>Nahraje pouze vybrané kategorie</p>
                        <div class="btn-group-vertical w-100">
                            <a href="?action=deploy_core" class="btn btn-sm btn-outline-secondary">Core</a>
                            <a href="?action=deploy_admin" class="btn btn-sm btn-outline-secondary">Admin</a>
                            <a href="?action=deploy_assets" class="btn btn-sm btn-outline-secondary">Assets</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Výsledek akce -->
        <?php if ($result): ?>
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">✅ Výsledek operace</h5>
                    </div>
                    <div class="card-body">
                        <div class="deployment-result"><?= htmlspecialchars($result) ?></div>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <?php if ($error): ?>
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-danger text-white">
                        <h5 class="mb-0">❌ Chyba</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-danger"><?= htmlspecialchars($error) ?></div>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Přehled souborů -->
        <div class="row">
            <div class="col-12">
                <h3>📋 Přehled souborů</h3>
            </div>
            <?php foreach ($project_files as $category => $files): ?>
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <?php
                            $icons = [
                                'core' => '🔧',
                                'admin' => '👤', 
                                'assets' => '🎨',
                                'database' => '🗄️',
                                'tests' => '🧪'
                            ];
                            echo $icons[$category] ?? '📁';
                            ?>
                            <?= ucfirst($category) ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="file-list">
                            <?php foreach ($files as $file): ?>
                                <?php $status = getFileStatus($file); ?>
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span class="<?= $status['class'] ?>">
                                        <?= $status['icon'] ?> <?= $file ?>
                                    </span>
                                    <?php if ($status['status'] === 'ready'): ?>
                                        <small class="text-muted">
                                            <?= number_format($status['size']) ?> B
                                        </small>
                                    <?php endif; ?>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        <div class="mt-3">
                            <a href="?action=deploy_<?= $category ?>" class="btn btn-sm btn-outline-primary">
                                Deploy <?= $category ?>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>

        <!-- Užitečné odkazy -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">🔗 Užitečné odkazy</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>🌐 Produkční web</h6>
                                <ul class="list-unstyled">
                                    <li><a href="https://snurkynakrk.cz" target="_blank">Hlavní stránka</a></li>
                                    <li><a href="https://snurkynakrk.cz/admin/login.php" target="_blank">Admin přihlášení</a></li>
                                    <li><a href="https://snurkynakrk.cz/test_database.php" target="_blank">Test databáze</a></li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>🔐 Přihlašovací údaje</h6>
                                <ul class="list-unstyled">
                                    <li><strong>Uživatel:</strong> admin</li>
                                    <li><strong>Heslo:</strong> snurky2024!</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Auto-refresh po deploymentu
        if (window.location.search.includes('action=')) {
            setTimeout(() => {
                const url = new URL(window.location);
                url.searchParams.delete('action');
                window.history.replaceState({}, '', url);
            }, 5000);
        }
    </script>
</body>
</html>
