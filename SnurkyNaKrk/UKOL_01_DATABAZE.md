# 📊 Úkol 1: Databázová struktura a konfigurace

## 🎯 Cíl úkolu
Vytvořit kompletní databázové schéma a konfigurační soubory pro projekt snurkynakrk.cz

## 📋 Checklist úkolu
- [ ] SQL schéma pro všechny tabulky
- [ ] Konfigurační soubor pro databázi
- [ ] Základ<PERSON><PERSON> bez<PERSON>čnostní nastavení
- [ ] Test připojení k databázi
- [ ] Vyt<PERSON>ření základních indexů

## 🗄️ Databázové tabulky

### 1. content_sections
```sql
CREATE TABLE content_sections (
    id INT PRIMARY KEY AUTO_INCREMENT,
    section_key VARCHAR(50) UNIQUE NOT NULL,
    title VARCHAR(200),
    content TEXT,
    meta_description VARCHAR(160),
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 2. products
```sql
CREATE TABLE products (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(150) NOT NULL,
    description TEXT,
    specifications TEXT,
    price_info VARCHAR(100),
    eshop_url VARCHAR(500),
    eshop_product_id VARCHAR(100),
    cta_text VARCHAR(50) DEFAULT 'Koupit v e-shopu',
    sort_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    link_clicks INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_active_sort (is_active, sort_order),
    INDEX idx_eshop_id (eshop_product_id)
);
```

### 3. team_members
```sql
CREATE TABLE team_members (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    email VARCHAR(100),
    photo_filename VARCHAR(255),
    sort_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_active_sort (is_active, sort_order)
);
```

### 4. media_files
```sql
CREATE TABLE media_files (
    id INT PRIMARY KEY AUTO_INCREMENT,
    filename VARCHAR(255) NOT NULL,
    original_name VARCHAR(255),
    file_path VARCHAR(500),
    file_size INT,
    mime_type VARCHAR(100),
    alt_text VARCHAR(200),
    category ENUM('product', 'team', 'content', 'general') DEFAULT 'general',
    related_id INT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_category (category),
    INDEX idx_related (category, related_id)
);
```

### 5. eshop_settings
```sql
CREATE TABLE eshop_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(50) UNIQUE NOT NULL,
    setting_value TEXT,
    description VARCHAR(200),
    is_active BOOLEAN DEFAULT TRUE,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 6. admin_sessions
```sql
CREATE TABLE admin_sessions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    session_id VARCHAR(128) UNIQUE NOT NULL,
    user_data TEXT,
    ip_address VARCHAR(45),
    user_agent VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP,
    INDEX idx_session (session_id),
    INDEX idx_expires (expires_at)
);
```

### 7. site_settings
```sql
CREATE TABLE site_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(50) UNIQUE NOT NULL,
    setting_value TEXT,
    setting_type ENUM('text', 'textarea', 'boolean', 'json') DEFAULT 'text',
    description VARCHAR(200),
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

## 🔧 Konfigurační soubory

### core/config.php
```php
<?php
// Databázové připojení
define('DB_HOST', 'uvds125.active24.cz');
define('DB_NAME', 'snurkynakr');
define('DB_USER', 'snurkynakr');
define('DB_PASS', 'GKkcdg9m');
define('DB_CHARSET', 'utf8mb4');

// Bezpečnostní nastavení
define('ADMIN_SESSION_TIMEOUT', 3600); // 1 hodina
define('MAX_UPLOAD_SIZE', 5 * 1024 * 1024); // 5MB
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'webp', 'gif']);

// E-shop nastavení
define('ESHOP_BASE_URL', ''); // Bude nastaveno v admin panelu
define('UTM_SOURCE', 'snurkynakrk');
define('UTM_MEDIUM', 'website');

// Cesty
define('UPLOAD_PATH', __DIR__ . '/../uploads/');
define('ASSETS_PATH', __DIR__ . '/../assets/');
```

## 🚀 Implementační kroky
1. Vytvořit SQL soubor s kompletním schématem
2. Implementovat config.php s připojovacími údaji
3. Vytvořit database.php pro PDO připojení
4. Otestovat připojení k databázi
5. Naplnit základní data (default content sections, eshop settings)

## ✅ Kritéria dokončení
- Všechny tabulky vytvořeny bez chyb
- Úspěšné připojení k databázi
- Základní data vložena
- Indexy vytvořeny pro optimalizaci
- Konfigurační soubory funkční
