<?php
/**
 * Kompletní deployment všech souborů na snurkynakrk.cz
 */

// FTP konfigurace
define('FTP_HOST', 'snurkynakrk.cz');
define('FTP_USER', 'snurkynakr');
define('FTP_PASS', 'mvUFSdLTeL');
define('FTP_ROOT', '/www/');

echo "🚀 Kompletní deployment na snurkynakrk.cz\n";
echo "==========================================\n\n";

// Připojení k FTP
$ftp = ftp_connect(FTP_HOST);
if (!$ftp) {
    die("❌ Nelze se připojit k FTP serveru\n");
}

$login = ftp_login($ftp, FTP_USER, FTP_PASS);
if (!$login) {
    die("❌ Neplatné FTP přihlašovací údaje\n");
}

ftp_pasv($ftp, true);
ftp_chdir($ftp, FTP_ROOT);

echo "✅ FTP připojení úspěšné\n\n";

// Funkce pro nahrání souboru
function uploadFile($ftp, $local_file, $remote_file) {
    if (!file_exists($local_file)) {
        echo "⚠️  Soubor neexistuje: $local_file\n";
        return false;
    }
    
    // Vytvoření složky pokud neexistuje
    $remote_dir = dirname($remote_file);
    if ($remote_dir !== '.' && $remote_dir !== '/') {
        $dirs = explode('/', trim($remote_dir, '/'));
        $current_path = '';
        
        foreach ($dirs as $dir) {
            if (empty($dir)) continue;
            $current_path .= $dir . '/';
            
            if (!@ftp_chdir($ftp, $current_path)) {
                if (@ftp_mkdir($ftp, $current_path)) {
                    echo "📁 Vytvořena složka: $current_path\n";
                }
            }
        }
        
        ftp_chdir($ftp, FTP_ROOT);
    }
    
    $mode = FTP_ASCII;
    
    if (ftp_put($ftp, $remote_file, $local_file, $mode)) {
        echo "✅ Nahráno: $local_file -> $remote_file\n";
        return true;
    } else {
        echo "❌ Chyba při nahrávání: $local_file\n";
        return false;
    }
}

// Kompletní seznam souborů
$files = [
    // Core soubory
    'core/config.php' => 'core/config.php',
    'core/database.php' => 'core/database.php',
    'core/init.php' => 'core/init.php',
    'core/auth.php' => 'core/auth.php',
    'core/middleware.php' => 'core/middleware.php',
    
    // Admin soubory - opravené verze
    'admin/login.php' => 'admin/login.php',
    'admin/dashboard.php' => 'admin/dashboard.php',
    'admin/login_minimal.php' => 'admin/login_minimal.php',
    'admin/dashboard_minimal.php' => 'admin/dashboard_minimal.php',
    'admin/debug.php' => 'admin/debug.php',
    
    // Assets
    'assets/css/admin.css' => 'assets/css/admin.css',
    
    // Database a testy
    'database_schema.sql' => 'database_schema.sql',
    'test_database.php' => 'test_database.php',
    'test_auth_basic.php' => 'test_auth_basic.php',
    'test_production.php' => 'test_production.php',
    'simple_test.php' => 'simple_test.php',
    'check_files.php' => 'check_files.php',
    
    // Deployment nástroje
    'deploy_ftp.php' => 'deploy_ftp.php',
    'deployment_manager.php' => 'deployment_manager.php'
];

echo "📤 Zahajuji nahrávání " . count($files) . " souborů...\n\n";

$uploaded = 0;
$errors = 0;

foreach ($files as $local => $remote) {
    if (uploadFile($ftp, $local, $remote)) {
        $uploaded++;
    } else {
        $errors++;
    }
}

echo "\n🎉 Deployment dokončen!\n";
echo "=====================================\n";
echo "✅ Nahráno souborů: $uploaded\n";
echo "❌ Chyb: $errors\n\n";

if ($errors === 0) {
    echo "🌐 Testovací odkazy:\n";
    echo "📋 Kontrola souborů: https://snurkynakrk.cz/check_files.php\n";
    echo "🧪 Jednoduchý test: https://snurkynakrk.cz/simple_test.php\n";
    echo "🔐 Minimální login: https://snurkynakrk.cz/admin/login_minimal.php\n";
    echo "🔐 Plný login: https://snurkynakrk.cz/admin/login.php\n";
    echo "🔍 Debug admin: https://snurkynakrk.cz/admin/debug.php\n";
    echo "\n👤 Přihlašovací údaje: admin / snurky2024!\n";
}

ftp_close($ftp);
?>
