# 📦 Úkol 6: <PERSON><PERSON> - Produkty a E-shop nastavení

## 🎯 Cíl úkolu
Implementovat kompletní správu produktů s e-shop integrací a media upload funkcionalitou

## 📋 Checklist úkolu
- [ ] Product CRUD operations (Create, Read, Update, Delete)
- [ ] E-shop URL management pro produkty
- [ ] Media upload a galerie pro produkty
- [ ] Drag & drop řazení produktů
- [ ] Bulk operations (hromadné akce)
- [ ] Preview produktů s e-shop odkazy

## 📦 Správa produktů

### admin/products.php
```php
<?php
require_once '../core/config.php';
require_once '../core/database.php';
require_once '../core/auth.php';
require_once '../core/functions.php';
require_once '../core/eshop-connector.php';

$auth = new Auth($db);
$auth->requireAdmin();

$eshop = new EshopConnector($db);
$message = '';
$messageType = '';

// Zpracování akcí
$action = $_GET['action'] ?? 'list';
$productId = $_GET['id'] ?? null;

// Zpracování formuláře
if ($_POST) {
    try {
        switch ($_POST['form_action']) {
            case 'save_product':
                $data = [
                    'name' => trim($_POST['name']),
                    'description' => trim($_POST['description']),
                    'specifications' => trim($_POST['specifications']),
                    'price_info' => trim($_POST['price_info']),
                    'eshop_url' => trim($_POST['eshop_url']),
                    'eshop_product_id' => trim($_POST['eshop_product_id']),
                    'cta_text' => trim($_POST['cta_text']) ?: 'Koupit v e-shopu',
                    'sort_order' => (int)$_POST['sort_order'],
                    'is_active' => isset($_POST['is_active']) ? 1 : 0
                ];
                
                if ($productId) {
                    // Aktualizace
                    $data['updated_at'] = date('Y-m-d H:i:s');
                    $db->update('products', $data, ['id' => $productId]);
                    $message = 'Produkt byl úspěšně aktualizován.';
                } else {
                    // Nový produkt
                    $db->insert('products', $data);
                    $productId = $db->lastInsertId();
                    $message = 'Produkt byl úspěšně vytvořen.';
                }
                
                // Zpracování nahraných obrázků
                if (!empty($_FILES['product_images']['name'][0])) {
                    handleProductImages($productId, $_FILES['product_images']);
                }
                
                $messageType = 'success';
                break;
                
            case 'delete_product':
                $db->update('products', ['is_active' => 0], ['id' => $productId]);
                $message = 'Produkt byl deaktivován.';
                $messageType = 'success';
                break;
                
            case 'update_order':
                $orders = json_decode($_POST['order_data'], true);
                foreach ($orders as $order) {
                    $db->update('products', 
                        ['sort_order' => $order['position']], 
                        ['id' => $order['id']]
                    );
                }
                Utils::jsonResponse(['success' => true]);
                break;
        }
    } catch (Exception $e) {
        $message = 'Chyba: ' . $e->getMessage();
        $messageType = 'error';
    }
}

// Načtení produktů
if ($action === 'edit' && $productId) {
    $product = $db->select('products', ['id' => $productId])[0] ?? null;
    if (!$product) {
        Utils::redirect('products.php');
    }
}

$products = $db->select('products', [], '*', 'sort_order ASC, created_at DESC');

// Funkce pro zpracování obrázků
function handleProductImages($productId, $files) {
    global $db;
    
    $uploadDir = UPLOAD_PATH . 'products/';
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }
    
    for ($i = 0; $i < count($files['name']); $i++) {
        if ($files['error'][$i] === UPLOAD_ERR_OK) {
            $originalName = $files['name'][$i];
            $tmpName = $files['tmp_name'][$i];
            $fileSize = $files['size'][$i];
            $mimeType = $files['type'][$i];
            
            // Validace souboru
            $allowedTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'];
            if (!in_array($mimeType, $allowedTypes)) {
                continue;
            }
            
            if ($fileSize > MAX_UPLOAD_SIZE) {
                continue;
            }
            
            // Generování jedinečného názvu
            $extension = pathinfo($originalName, PATHINFO_EXTENSION);
            $filename = 'product_' . $productId . '_' . time() . '_' . $i . '.' . $extension;
            $filePath = $uploadDir . $filename;
            
            if (move_uploaded_file($tmpName, $filePath)) {
                // Uložení do databáze
                $db->insert('media_files', [
                    'filename' => $filename,
                    'original_name' => $originalName,
                    'file_path' => 'uploads/products/' . $filename,
                    'file_size' => $fileSize,
                    'mime_type' => $mimeType,
                    'category' => 'product',
                    'related_id' => $productId
                ]);
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Správa produktů - Admin</title>
    <link rel="stylesheet" href="../assets/css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.css">
</head>
<body class="admin-body">
    <?php include 'includes/admin-header.php'; ?>
    
    <div class="admin-container">
        <?php include 'includes/admin-sidebar.php'; ?>
        
        <main class="admin-main">
            <?php if ($action === 'list'): ?>
                <!-- Seznam produktů -->
                <div class="page-header">
                    <h1>Správa produktů</h1>
                    <a href="?action=add" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Přidat produkt
                    </a>
                </div>
                
                <?php if ($message): ?>
                    <div class="message message-<?= $messageType ?>">
                        <?= Utils::escape($message) ?>
                    </div>
                <?php endif; ?>
                
                <div class="products-grid" id="sortable-products">
                    <?php foreach ($products as $product): ?>
                        <div class="product-card" data-id="<?= $product['id'] ?>">
                            <div class="product-drag-handle">
                                <i class="fas fa-grip-vertical"></i>
                            </div>
                            
                            <div class="product-info">
                                <h3><?= Utils::escape($product['name']) ?></h3>
                                <p><?= Utils::truncateText(Utils::escape($product['description']), 100) ?></p>
                                
                                <div class="product-meta">
                                    <span class="product-status <?= $product['is_active'] ? 'active' : 'inactive' ?>">
                                        <?= $product['is_active'] ? 'Aktivní' : 'Neaktivní' ?>
                                    </span>
                                    <span class="product-clicks">
                                        <i class="fas fa-mouse-pointer"></i> <?= number_format($product['link_clicks']) ?>
                                    </span>
                                </div>
                                
                                <?php if ($product['eshop_url'] || $product['eshop_product_id']): ?>
                                    <div class="product-eshop">
                                        <i class="fas fa-external-link-alt"></i>
                                        <a href="<?= $eshop->buildProductUrl($product['eshop_product_id'], $product['eshop_url']) ?>" 
                                           target="_blank" class="eshop-link">
                                            Zobrazit v e-shopu
                                        </a>
                                    </div>
                                <?php endif; ?>
                            </div>
                            
                            <div class="product-actions">
                                <a href="?action=edit&id=<?= $product['id'] ?>" class="btn btn-sm btn-secondary">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button onclick="deleteProduct(<?= $product['id'] ?>)" class="btn btn-sm btn-danger">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
                
            <?php else: ?>
                <!-- Formulář pro přidání/úpravu produktu -->
                <div class="page-header">
                    <h1><?= $action === 'edit' ? 'Upravit produkt' : 'Přidat produkt' ?></h1>
                    <a href="products.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Zpět na seznam
                    </a>
                </div>
                
                <?php if ($message): ?>
                    <div class="message message-<?= $messageType ?>">
                        <?= Utils::escape($message) ?>
                    </div>
                <?php endif; ?>
                
                <form method="POST" enctype="multipart/form-data" class="product-form">
                    <input type="hidden" name="form_action" value="save_product">
                    
                    <div class="form-grid">
                        <div class="form-section">
                            <h3>Základní informace</h3>
                            
                            <div class="form-group">
                                <label for="name">Název produktu *</label>
                                <input type="text" id="name" name="name" 
                                       value="<?= Utils::escape($product['name'] ?? '') ?>" 
                                       required maxlength="150">
                            </div>
                            
                            <div class="form-group">
                                <label for="description">Popis</label>
                                <textarea id="description" name="description" 
                                          rows="4"><?= Utils::escape($product['description'] ?? '') ?></textarea>
                            </div>
                            
                            <div class="form-group">
                                <label for="specifications">Specifikace</label>
                                <textarea id="specifications" name="specifications" 
                                          rows="3"><?= Utils::escape($product['specifications'] ?? '') ?></textarea>
                            </div>
                            
                            <div class="form-group">
                                <label for="price_info">Cenové informace</label>
                                <input type="text" id="price_info" name="price_info" 
                                       value="<?= Utils::escape($product['price_info'] ?? '') ?>" 
                                       maxlength="100">
                            </div>
                        </div>
                        
                        <div class="form-section">
                            <h3>E-shop integrace</h3>
                            
                            <div class="form-group">
                                <label for="eshop_url">Přímá URL v e-shopu</label>
                                <input type="url" id="eshop_url" name="eshop_url" 
                                       value="<?= Utils::escape($product['eshop_url'] ?? '') ?>">
                                <small>Pokud je vyplněno, použije se místo sestavené URL</small>
                            </div>
                            
                            <div class="form-group">
                                <label for="eshop_product_id">ID produktu v e-shopu</label>
                                <input type="text" id="eshop_product_id" name="eshop_product_id" 
                                       value="<?= Utils::escape($product['eshop_product_id'] ?? '') ?>">
                                <small>Pro sestavení URL z základní adresy e-shopu</small>
                            </div>
                            
                            <div class="form-group">
                                <label for="cta_text">Text tlačítka</label>
                                <input type="text" id="cta_text" name="cta_text" 
                                       value="<?= Utils::escape($product['cta_text'] ?? 'Koupit v e-shopu') ?>" 
                                       maxlength="50">
                            </div>
                            
                            <?php if (isset($product) && ($product['eshop_url'] || $product['eshop_product_id'])): ?>
                                <div class="form-group">
                                    <label>Náhled odkazu:</label>
                                    <a href="<?= $eshop->buildProductUrl($product['eshop_product_id'], $product['eshop_url']) ?>" 
                                       target="_blank" class="preview-link">
                                        <?= Utils::escape($eshop->buildProductUrl($product['eshop_product_id'], $product['eshop_url'])) ?>
                                        <i class="fas fa-external-link-alt"></i>
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <div class="form-section">
                        <h3>Obrázky produktu</h3>
                        
                        <div class="form-group">
                            <label for="product_images">Nahrát obrázky</label>
                            <input type="file" id="product_images" name="product_images[]" 
                                   multiple accept="image/*" class="file-input">
                            <small>Můžete vybrat více obrázků najednou. Podporované formáty: JPG, PNG, WebP, GIF</small>
                        </div>
                        
                        <?php if (isset($product)): ?>
                            <?php
                            $productImages = $db->select('media_files', 
                                ['category' => 'product', 'related_id' => $product['id']], 
                                '*', 
                                'created_at ASC'
                            );
                            ?>
                            
                            <?php if ($productImages): ?>
                                <div class="current-images">
                                    <h4>Současné obrázky:</h4>
                                    <div class="image-grid">
                                        <?php foreach ($productImages as $image): ?>
                                            <div class="image-item">
                                                <img src="../<?= Utils::escape($image['file_path']) ?>" 
                                                     alt="<?= Utils::escape($image['alt_text']) ?>">
                                                <button type="button" class="btn-remove-image" 
                                                        onclick="removeImage(<?= $image['id'] ?>)">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                    
                    <div class="form-section">
                        <h3>Nastavení</h3>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="sort_order">Pořadí</label>
                                <input type="number" id="sort_order" name="sort_order" 
                                       value="<?= $product['sort_order'] ?? 0 ?>" min="0">
                            </div>
                            
                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" name="is_active" 
                                           <?= ($product['is_active'] ?? 1) ? 'checked' : '' ?>>
                                    Aktivní produkt
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> 
                            <?= $action === 'edit' ? 'Aktualizovat produkt' : 'Vytvořit produkt' ?>
                        </button>
                        <a href="products.php" class="btn btn-secondary">Zrušit</a>
                    </div>
                </form>
            <?php endif; ?>
        </main>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
    <script>
        // Drag & Drop řazení produktů
        if (document.getElementById('sortable-products')) {
            new Sortable(document.getElementById('sortable-products'), {
                handle: '.product-drag-handle',
                animation: 150,
                onEnd: function(evt) {
                    updateProductOrder();
                }
            });
        }
        
        function updateProductOrder() {
            const products = document.querySelectorAll('.product-card');
            const orderData = [];
            
            products.forEach((product, index) => {
                orderData.push({
                    id: product.dataset.id,
                    position: index
                });
            });
            
            fetch('products.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'form_action=update_order&order_data=' + encodeURIComponent(JSON.stringify(orderData))
            });
        }
        
        function deleteProduct(id) {
            if (confirm('Opravdu chcete deaktivovat tento produkt?')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="form_action" value="delete_product">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }
        
        function removeImage(imageId) {
            if (confirm('Opravdu chcete odstranit tento obrázek?')) {
                // AJAX call pro odstranění obrázku
                fetch('ajax/remove-image.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'image_id=' + imageId
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    }
                });
            }
        }
    </script>
    
    <script src="../assets/js/admin.js"></script>
</body>
</html>
```

## 🚀 Implementační kroky
1. Vytvořit products.php s kompletním CRUD rozhraním
2. Implementovat drag & drop řazení produktů
3. Přidat media upload funkcionalita pro produktové obrázky
4. Vytvořit e-shop URL preview a validaci
5. Implementovat bulk operations
6. Přidat AJAX funkce pro rychlé akce

## ✅ Kritéria dokončení
- CRUD operace pro produkty fungují správně
- Drag & drop řazení ukládá pořadí do databáze
- Media upload zpracovává obrázky produktů
- E-shop URL se správně sestavují a validují
- Preview odkazů funguje
- Bulk operations jsou implementované
- Rozhraní je responzivní a uživatelsky přívětivé
