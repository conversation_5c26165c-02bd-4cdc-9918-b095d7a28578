# 🔐 Úkol 2: Autentifikační systém

## 🎯 Cíl úkolu
Implementovat jednoduchý a bezpečný autentifikační systém pro admin přístup

## 📋 Checklist úkolu
- [ ] Login formulář s validací
- [ ] Session management systém
- [ ] Bezpečnostní middleware
- [ ] Logout funkcionalita
- [ ] CSRF protection
- [ ] Rate limiting pro login pokusy

## 🔧 Implementační soubory

### core/auth.php
```php
<?php
class Auth {
    private $db;
    private $session_timeout;
    
    public function __construct($database) {
        $this->db = $database;
        $this->session_timeout = ADMIN_SESSION_TIMEOUT;
        $this->startSession();
    }
    
    // Přihlášení administrátora
    public function login($username, $password) {
        // Implementace s rate limiting
        // Hash kontrola hesla
        // Session vytvoření
    }
    
    // Kontrola přihlášení
    public function isLoggedIn() {
        // Session validace
        // Timeout kontrola
    }
    
    // Odhlášení
    public function logout() {
        // Session cleanup
        // Database cleanup
    }
    
    // CSRF token generování
    public function generateCSRFToken() {
        // Token generování a uložení
    }
    
    // CSRF token validace
    public function validateCSRFToken($token) {
        // Token kontrola
    }
}
```

### admin/login.php
```php
<?php
require_once '../core/config.php';
require_once '../core/database.php';
require_once '../core/auth.php';

$auth = new Auth($db);

// Pokud už je přihlášen, přesměruj na dashboard
if ($auth->isLoggedIn()) {
    header('Location: dashboard.php');
    exit;
}

$error = '';
$csrf_token = $auth->generateCSRFToken();

if ($_POST) {
    // Validace CSRF tokenu
    if (!$auth->validateCSRFToken($_POST['csrf_token'])) {
        $error = 'Neplatný bezpečnostní token';
    } else {
        // Pokus o přihlášení
        $username = trim($_POST['username']);
        $password = $_POST['password'];
        
        if ($auth->login($username, $password)) {
            header('Location: dashboard.php');
            exit;
        } else {
            $error = 'Neplatné přihlašovací údaje';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin přihlášení - Snurkynakrk.cz</title>
    <link rel="stylesheet" href="../assets/css/admin.css">
</head>
<body class="login-page">
    <div class="login-container">
        <div class="login-form">
            <h1>Admin přihlášení</h1>
            
            <?php if ($error): ?>
                <div class="error-message"><?= htmlspecialchars($error) ?></div>
            <?php endif; ?>
            
            <form method="POST" action="">
                <input type="hidden" name="csrf_token" value="<?= $csrf_token ?>">
                
                <div class="form-group">
                    <label for="username">Uživatelské jméno:</label>
                    <input type="text" id="username" name="username" required>
                </div>
                
                <div class="form-group">
                    <label for="password">Heslo:</label>
                    <input type="password" id="password" name="password" required>
                </div>
                
                <button type="submit" class="btn-primary">Přihlásit se</button>
            </form>
        </div>
    </div>
</body>
</html>
```

## 🛡️ Bezpečnostní middleware

### core/middleware.php
```php
<?php
class SecurityMiddleware {
    private $auth;
    
    public function __construct($auth) {
        $this->auth = $auth;
    }
    
    // Kontrola admin přístupu
    public function requireAdmin() {
        if (!$this->auth->isLoggedIn()) {
            header('Location: login.php');
            exit;
        }
    }
    
    // Rate limiting pro login pokusy
    public function checkRateLimit($ip) {
        // Implementace rate limiting
        // Max 5 pokusů za 15 minut
    }
    
    // Input sanitization
    public function sanitizeInput($data) {
        if (is_array($data)) {
            return array_map([$this, 'sanitizeInput'], $data);
        }
        return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
    }
    
    // File upload validace
    public function validateFileUpload($file) {
        // Kontrola typu souboru
        // Kontrola velikosti
        // Kontrola bezpečnosti
    }
}
```

## 🔑 Session management

### Konfigurace session
```php
// V config.php
ini_set('session.cookie_httponly', 1);
ini_set('session.cookie_secure', 1);
ini_set('session.use_strict_mode', 1);
ini_set('session.cookie_samesite', 'Strict');

// Session timeout
ini_set('session.gc_maxlifetime', ADMIN_SESSION_TIMEOUT);
```

### Session cleanup
```php
// Automatické čištění starých sessions
class SessionCleanup {
    public static function cleanExpiredSessions($db) {
        $stmt = $db->prepare("DELETE FROM admin_sessions WHERE expires_at < NOW()");
        $stmt->execute();
    }
}
```

## 🚀 Implementační kroky
1. Vytvořit Auth třídu s kompletní funkcionalitou
2. Implementovat login.php s formulářem
3. Vytvořit SecurityMiddleware pro ochranu admin stránek
4. Nastavit session konfiguraci
5. Implementovat CSRF protection
6. Přidat rate limiting pro login pokusy
7. Vytvořit logout funkcionalita

## ✅ Kritéria dokončení
- Funkční login formulář s validací
- Bezpečné session management
- CSRF protection implementováno
- Rate limiting funkční
- Middleware chrání admin stránky
- Logout správně čistí session data
- Všechny bezpečnostní kontroly prošly testem
