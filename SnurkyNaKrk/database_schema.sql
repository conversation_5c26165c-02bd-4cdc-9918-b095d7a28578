-- =====================================================
-- Databázové schéma pro snurkynakrk.cz
-- Vytvořeno: $(date)
-- Popis: Kompletní SQL schéma pro jednostránkovou prezentaci s e-shop integrací
-- =====================================================

-- Nastavení kódování
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- =====================================================
-- 1. Tabulka pro obsahové sekce
-- =====================================================
CREATE TABLE IF NOT EXISTS content_sections (
    id INT PRIMARY KEY AUTO_INCREMENT,
    section_key VARCHAR(50) UNIQUE NOT NULL COMMENT 'Unikátní klíč sekce (hero, about, contact, atd.)',
    title VARCHAR(200) COMMENT 'Nadpis sekce',
    content TEXT COMMENT 'Hlavní obsah sekce',
    meta_description VARCHAR(160) COMMENT 'Meta popis pro SEO',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'Zda je sekce aktivní',
    sort_order INT DEFAULT 0 COMMENT 'Pořadí zobrazení',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_active_sort (is_active, sort_order),
    INDEX idx_section_key (section_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Obsahové sekce webu';

-- =====================================================
-- 2. Tabulka pro produkty
-- =====================================================
CREATE TABLE IF NOT EXISTS products (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(150) NOT NULL COMMENT 'Název produktu',
    description TEXT COMMENT 'Popis produktu',
    specifications TEXT COMMENT 'Technické specifikace',
    price_info VARCHAR(100) COMMENT 'Informace o ceně (text)',
    eshop_url VARCHAR(500) COMMENT 'Přímý odkaz do e-shopu',
    eshop_product_id VARCHAR(100) COMMENT 'ID produktu v e-shopu',
    cta_text VARCHAR(50) DEFAULT 'Koupit v e-shopu' COMMENT 'Text call-to-action tlačítka',
    sort_order INT DEFAULT 0 COMMENT 'Pořadí zobrazení',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'Zda je produkt aktivní',
    link_clicks INT DEFAULT 0 COMMENT 'Počet kliknutí na e-shop odkaz',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_active_sort (is_active, sort_order),
    INDEX idx_eshop_id (eshop_product_id),
    INDEX idx_clicks (link_clicks)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Produkty zobrazované na webu';

-- =====================================================
-- 3. Tabulka pro členy týmu
-- =====================================================
CREATE TABLE IF NOT EXISTS team_members (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL COMMENT 'Jméno člena týmu',
    phone VARCHAR(20) COMMENT 'Telefonní číslo',
    email VARCHAR(100) COMMENT 'E-mailová adresa',
    photo_filename VARCHAR(255) COMMENT 'Název souboru s fotografií',
    sort_order INT DEFAULT 0 COMMENT 'Pořadí zobrazení',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'Zda je člen aktivní',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_active_sort (is_active, sort_order),
    INDEX idx_email (email)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Členové týmu';

-- =====================================================
-- 4. Tabulka pro media soubory
-- =====================================================
CREATE TABLE IF NOT EXISTS media_files (
    id INT PRIMARY KEY AUTO_INCREMENT,
    filename VARCHAR(255) NOT NULL COMMENT 'Název souboru',
    original_name VARCHAR(255) COMMENT 'Původní název souboru',
    file_path VARCHAR(500) COMMENT 'Cesta k souboru',
    file_size INT COMMENT 'Velikost souboru v bytech',
    mime_type VARCHAR(100) COMMENT 'MIME typ souboru',
    alt_text VARCHAR(200) COMMENT 'Alternativní text pro obrázky',
    category ENUM('product', 'team', 'content', 'general') DEFAULT 'general' COMMENT 'Kategorie souboru',
    related_id INT DEFAULT NULL COMMENT 'ID souvisejícího záznamu',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_category (category),
    INDEX idx_related (category, related_id),
    INDEX idx_filename (filename)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Media soubory';

-- =====================================================
-- 5. Tabulka pro nastavení e-shopu
-- =====================================================
CREATE TABLE IF NOT EXISTS eshop_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(50) UNIQUE NOT NULL COMMENT 'Klíč nastavení',
    setting_value TEXT COMMENT 'Hodnota nastavení',
    description VARCHAR(200) COMMENT 'Popis nastavení',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'Zda je nastavení aktivní',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_key (setting_key),
    INDEX idx_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Nastavení e-shop integrace';

-- =====================================================
-- 6. Tabulka pro admin sessions
-- =====================================================
CREATE TABLE IF NOT EXISTS admin_sessions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    session_id VARCHAR(128) UNIQUE NOT NULL COMMENT 'ID session',
    user_data TEXT COMMENT 'Data uživatele (JSON)',
    ip_address VARCHAR(45) COMMENT 'IP adresa',
    user_agent VARCHAR(500) COMMENT 'User agent prohlížeče',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP COMMENT 'Čas vypršení session',
    INDEX idx_session (session_id),
    INDEX idx_expires (expires_at),
    INDEX idx_ip (ip_address)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Admin sessions';

-- =====================================================
-- 7. Tabulka pro obecná nastavení webu
-- =====================================================
CREATE TABLE IF NOT EXISTS site_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(50) UNIQUE NOT NULL COMMENT 'Klíč nastavení',
    setting_value TEXT COMMENT 'Hodnota nastavení',
    setting_type ENUM('text', 'textarea', 'boolean', 'json') DEFAULT 'text' COMMENT 'Typ nastavení',
    description VARCHAR(200) COMMENT 'Popis nastavení',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_key (setting_key),
    INDEX idx_type (setting_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Obecná nastavení webu';

-- =====================================================
-- Vložení základních dat
-- =====================================================

-- Základní obsahové sekce
INSERT INTO content_sections (section_key, title, content, meta_description, sort_order) VALUES
('hero', 'Vítejte na Snurky na krk', 'Objevte naši jedinečnou kolekci produktů pro váš krk.', 'Snurky na krk - jedinečné produkty pro váš krk', 1),
('about', 'O našich produktech', 'Naše produkty jsou vyráběny s láskou a péčí o detail.', 'Informace o našich produktech a jejich kvalitě', 2),
('contact', 'Kontaktujte nás', 'Máte dotazy? Rádi vám pomůžeme.', 'Kontaktní informace a způsoby komunikace', 3),
('footer', 'Footer obsah', 'Copyright © 2024 Snurky na krk. Všechna práva vyhrazena.', 'Footer informace', 4);

-- Základní nastavení e-shopu
INSERT INTO eshop_settings (setting_key, setting_value, description) VALUES
('base_url', '', 'Základní URL e-shopu'),
('utm_source', 'snurkynakrk', 'UTM source parametr'),
('utm_medium', 'website', 'UTM medium parametr'),
('utm_campaign', 'product_link', 'UTM campaign parametr'),
('link_validation', '1', 'Zda validovat odkazy před přesměrováním'),
('click_tracking', '1', 'Zda sledovat kliknutí na odkazy');

-- Základní nastavení webu
INSERT INTO site_settings (setting_key, setting_value, setting_type, description) VALUES
('site_title', 'Snurky na krk', 'text', 'Název webu'),
('site_description', 'Jedinečné produkty pro váš krk', 'text', 'Popis webu'),
('admin_email', '', 'text', 'E-mail administrátora'),
('maintenance_mode', '0', 'boolean', 'Režim údržby'),
('analytics_code', '', 'textarea', 'Google Analytics kód'),
('contact_phone', '', 'text', 'Kontaktní telefon'),
('contact_email', '', 'text', 'Kontaktní e-mail');

-- Obnovení kontroly cizích klíčů
SET FOREIGN_KEY_CHECKS = 1;

-- =====================================================
-- Konec SQL schématu
-- =====================================================
