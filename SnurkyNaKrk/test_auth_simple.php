<?php
/**
 * <PERSON><PERSON><PERSON><PERSON><PERSON> test autentifikač<PERSON><PERSON><PERSON> systému
 */

echo "<h1>Test autentifikačního systému</h1>\n";

try {
    // Test 1: Načten<PERSON> souborů
    echo "<h2>Test 1: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></h2>\n";
    
    if (file_exists('core/init.php')) {
        echo "✅ core/init.php existuje\n<br>";
    } else {
        echo "❌ core/init.php neexistuje\n<br>";
    }
    
    if (file_exists('core/auth.php')) {
        echo "✅ core/auth.php existuje\n<br>";
    } else {
        echo "❌ core/auth.php neexistuje\n<br>";
    }
    
    if (file_exists('core/middleware.php')) {
        echo "✅ core/middleware.php existuje\n<br>";
    } else {
        echo "❌ core/middleware.php neexistuje\n<br>";
    }
    
    // Test 2: Načten<PERSON> tříd
    echo "<h2>Test 2: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tříd</h2>\n";
    
    require_once 'core/init.php';
    echo "✅ core/init.php načten\n<br>";
    
    require_once 'core/auth.php';
    echo "✅ core/auth.php načten\n<br>";
    
    require_once 'core/middleware.php';
    echo "✅ core/middleware.php načten\n<br>";
    
    // Test 3: Vytvoření objektů
    echo "<h2>Test 3: Vytváření objektů</h2>\n";
    
    $db = new Database();
    echo "✅ Database objekt vytvořen\n<br>";
    
    $auth = new Auth($db);
    echo "✅ Auth objekt vytvořen\n<br>";
    
    $middleware = new SecurityMiddleware($auth);
    echo "✅ SecurityMiddleware objekt vytvořen\n<br>";
    
    // Test 4: Základní funkce
    echo "<h2>Test 4: Základní funkce</h2>\n";
    
    $token = $auth->generateCSRFToken();
    if (!empty($token)) {
        echo "✅ CSRF token vygenerován: " . substr($token, 0, 10) . "...\n<br>";
    } else {
        echo "❌ CSRF token se nepodařilo vygenerovat\n<br>";
    }
    
    $is_logged_in = $auth->isLoggedIn();
    if (!$is_logged_in) {
        echo "✅ Uživatel není přihlášen (správně)\n<br>";
    } else {
        echo "❌ Uživatel je přihlášen (neočekáváno)\n<br>";
    }
    
    // Test 5: Sanitizace
    echo "<h2>Test 5: Sanitizace dat</h2>\n";
    
    $dirty_data = '<script>alert("test")</script>Hello';
    $clean_data = $middleware->sanitizeInput($dirty_data);
    
    if (strpos($clean_data, '<script>') === false && strpos($clean_data, 'Hello') !== false) {
        echo "✅ Sanitizace funguje správně\n<br>";
        echo "Původní: " . htmlspecialchars($dirty_data) . "\n<br>";
        echo "Vyčištěno: " . htmlspecialchars($clean_data) . "\n<br>";
    } else {
        echo "❌ Sanitizace nefunguje správně\n<br>";
    }
    
    echo "<h2>✅ Všechny základní testy prošly!</h2>\n";
    echo "<p><a href='admin/login.php'>→ Přejít na přihlašovací stránku</a></p>\n";
    
} catch (Exception $e) {
    echo "<h2>❌ Chyba při testování:</h2>\n";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>\n";
    echo "<pre>" . $e->getTraceAsString() . "</pre>\n";
}
?>
