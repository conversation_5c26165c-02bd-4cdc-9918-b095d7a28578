# ⚙️ Úkol 3: Core PHP framework

## 🎯 Cíl úkolu
Vytvořit základní PHP framework s databázovou abstrakcí a utility funkcemi

## 📋 Checklist úkolu
- [ ] Database abstraction layer (PDO)
- [ ] Core utility funkce
- [ ] Error handling a logging
- [ ] Input validation třídy
- [ ] Response handling
- [ ] File management utilities

## 🗄️ Database abstraction layer

### core/database.php
```php
<?php
class Database {
    private $pdo;
    private $host;
    private $dbname;
    private $username;
    private $password;
    
    public function __construct() {
        $this->host = DB_HOST;
        $this->dbname = DB_NAME;
        $this->username = DB_USER;
        $this->password = DB_PASS;
        $this->connect();
    }
    
    private function connect() {
        try {
            $dsn = "mysql:host={$this->host};dbname={$this->dbname};charset=" . DB_CHARSET;
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES " . DB_CHARSET
            ];
            
            $this->pdo = new PDO($dsn, $this->username, $this->password, $options);
        } catch (PDOException $e) {
            error_log("Database connection failed: " . $e->getMessage());
            throw new Exception("Chyba připojení k databázi");
        }
    }
    
    // Získání PDO instance
    public function getPDO() {
        return $this->pdo;
    }
    
    // Jednoduchý SELECT
    public function select($table, $conditions = [], $columns = '*', $orderBy = '', $limit = '') {
        $sql = "SELECT {$columns} FROM {$table}";
        
        if (!empty($conditions)) {
            $whereClause = [];
            foreach ($conditions as $key => $value) {
                $whereClause[] = "{$key} = :{$key}";
            }
            $sql .= " WHERE " . implode(' AND ', $whereClause);
        }
        
        if ($orderBy) $sql .= " ORDER BY {$orderBy}";
        if ($limit) $sql .= " LIMIT {$limit}";
        
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute($conditions);
        
        return $stmt->fetchAll();
    }
    
    // INSERT
    public function insert($table, $data) {
        $columns = implode(',', array_keys($data));
        $placeholders = ':' . implode(', :', array_keys($data));
        
        $sql = "INSERT INTO {$table} ({$columns}) VALUES ({$placeholders})";
        $stmt = $this->pdo->prepare($sql);
        
        return $stmt->execute($data);
    }
    
    // UPDATE
    public function update($table, $data, $conditions) {
        $setClause = [];
        foreach ($data as $key => $value) {
            $setClause[] = "{$key} = :{$key}";
        }
        
        $whereClause = [];
        foreach ($conditions as $key => $value) {
            $whereClause[] = "{$key} = :where_{$key}";
            $data["where_{$key}"] = $value;
        }
        
        $sql = "UPDATE {$table} SET " . implode(', ', $setClause) . 
               " WHERE " . implode(' AND ', $whereClause);
        
        $stmt = $this->pdo->prepare($sql);
        return $stmt->execute($data);
    }
    
    // DELETE
    public function delete($table, $conditions) {
        $whereClause = [];
        foreach ($conditions as $key => $value) {
            $whereClause[] = "{$key} = :{$key}";
        }
        
        $sql = "DELETE FROM {$table} WHERE " . implode(' AND ', $whereClause);
        $stmt = $this->pdo->prepare($sql);
        
        return $stmt->execute($conditions);
    }
    
    // Získání posledního ID
    public function lastInsertId() {
        return $this->pdo->lastInsertId();
    }
    
    // Transakce
    public function beginTransaction() {
        return $this->pdo->beginTransaction();
    }
    
    public function commit() {
        return $this->pdo->commit();
    }
    
    public function rollback() {
        return $this->pdo->rollback();
    }
}

// Globální instance databáze
try {
    $db = new Database();
} catch (Exception $e) {
    die("Chyba inicializace databáze: " . $e->getMessage());
}
```

## 🛠️ Core utility funkce

### core/functions.php
```php
<?php
class Utils {
    
    // Sanitizace HTML výstupu
    public static function escape($string) {
        return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
    }
    
    // Validace emailu
    public static function isValidEmail($email) {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }
    
    // Validace telefonu (český formát)
    public static function isValidPhone($phone) {
        $phone = preg_replace('/\s+/', '', $phone);
        return preg_match('/^(\+420)?[0-9]{9}$/', $phone);
    }
    
    // Generování náhodného řetězce
    public static function generateRandomString($length = 32) {
        return bin2hex(random_bytes($length / 2));
    }
    
    // Slug generování z českého textu
    public static function createSlug($text) {
        $text = mb_strtolower($text, 'UTF-8');
        
        // České znaky na ASCII
        $czech = ['á','č','ď','é','ě','í','ň','ó','ř','š','ť','ú','ů','ý','ž'];
        $ascii = ['a','c','d','e','e','i','n','o','r','s','t','u','u','y','z'];
        $text = str_replace($czech, $ascii, $text);
        
        // Pouze alfanumerické znaky a pomlčky
        $text = preg_replace('/[^a-z0-9]+/', '-', $text);
        $text = trim($text, '-');
        
        return $text;
    }
    
    // Formátování velikosti souboru
    public static function formatFileSize($bytes) {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }
    
    // Zkrácení textu s třemi tečkami
    public static function truncateText($text, $length = 100, $suffix = '...') {
        if (mb_strlen($text) <= $length) {
            return $text;
        }
        
        return mb_substr($text, 0, $length) . $suffix;
    }
    
    // Redirect s exit
    public static function redirect($url, $statusCode = 302) {
        header("Location: {$url}", true, $statusCode);
        exit;
    }
    
    // JSON response
    public static function jsonResponse($data, $statusCode = 200) {
        http_response_code($statusCode);
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode($data, JSON_UNESCAPED_UNICODE);
        exit;
    }
}
```

## 🚨 Error handling a logging

### core/error-handler.php
```php
<?php
class ErrorHandler {
    private static $logFile;
    
    public static function init($logFile = '../logs/error.log') {
        self::$logFile = $logFile;
        
        // Nastavení error handleru
        set_error_handler([self::class, 'handleError']);
        set_exception_handler([self::class, 'handleException']);
        register_shutdown_function([self::class, 'handleFatalError']);
        
        // Vytvoření log složky pokud neexistuje
        $logDir = dirname(self::$logFile);
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
    }
    
    public static function handleError($severity, $message, $file, $line) {
        if (!(error_reporting() & $severity)) {
            return false;
        }
        
        $errorMsg = "ERROR: {$message} in {$file} on line {$line}";
        self::log($errorMsg);
        
        // V produkci nezobrazovat chyby
        if (!defined('DEBUG') || !DEBUG) {
            return true;
        }
        
        return false;
    }
    
    public static function handleException($exception) {
        $errorMsg = "EXCEPTION: " . $exception->getMessage() . 
                   " in " . $exception->getFile() . 
                   " on line " . $exception->getLine();
        
        self::log($errorMsg);
        
        // V produkci zobrazit obecnou chybu
        if (!defined('DEBUG') || !DEBUG) {
            http_response_code(500);
            include '../templates/error-500.php';
        } else {
            echo "<pre>{$errorMsg}</pre>";
        }
        
        exit;
    }
    
    public static function handleFatalError() {
        $error = error_get_last();
        if ($error && in_array($error['type'], [E_ERROR, E_CORE_ERROR, E_COMPILE_ERROR])) {
            $errorMsg = "FATAL ERROR: {$error['message']} in {$error['file']} on line {$error['line']}";
            self::log($errorMsg);
        }
    }
    
    private static function log($message) {
        $timestamp = date('Y-m-d H:i:s');
        $logEntry = "[{$timestamp}] {$message}" . PHP_EOL;
        file_put_contents(self::$logFile, $logEntry, FILE_APPEND | LOCK_EX);
    }
}

// Inicializace error handleru
ErrorHandler::init();
```

## 🚀 Implementační kroky
1. Vytvořit Database třídu s PDO abstrakcí
2. Implementovat Utils třídu s utility funkcemi
3. Vytvořit ErrorHandler pro logging a error handling
4. Vytvořit Validator třídu pro input validaci
5. Implementovat Response handling
6. Otestovat všechny komponenty

## ✅ Kritéria dokončení
- Database třída funguje s všemi CRUD operacemi
- Utils funkce jsou otestované a funkční
- Error handling zachytává a loguje chyby
- Input validace funguje správně
- Všechny komponenty jsou propojené a testované
