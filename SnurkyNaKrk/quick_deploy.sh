#!/bin/bash

# Quick FTP deployment script pro snurkynakrk.cz
# Používá lftp pro rychlé a spolehlivé nahrávání

# FTP konfigurace
FTP_HOST="snurkynakrk.cz"
FTP_USER="snurkynakr"
FTP_PASS="mvUFSdLTeL"
FTP_PATH="/www/"

echo "🚀 Quick FTP Deployment pro snurkynakrk.cz"
echo "=========================================="

# Kontrola zda je lftp nainstalováno
if ! command -v lftp &> /dev/null; then
    echo "❌ lftp není nainstalováno"
    echo "💡 Nainstalujte pomocí:"
    echo "   macOS: brew install lftp"
    echo "   Ubuntu: sudo apt-get install lftp"
    echo "   CentOS: sudo yum install lftp"
    exit 1
fi

# Funkce pro nahrání souborů
upload_files() {
    echo "📤 Nahrávám soubory na server..."
    
    lftp -c "
    set ftp:ssl-allow no
    set ftp:passive-mode on
    open -u $FTP_USER,$FTP_PASS $FTP_HOST
    cd $FTP_PATH
    
    # Vytvoření složek
    mkdir -p core
    mkdir -p admin
    mkdir -p assets/css
    mkdir -p assets/js
    mkdir -p assets/images
    mkdir -p uploads/products
    mkdir -p uploads/team
    mkdir -p uploads/content
    mkdir -p logs
    mkdir -p cache
    
    # Nahrání core souborů
    put core/config.php core/config.php
    put core/database.php core/database.php
    put core/init.php core/init.php
    put core/auth.php core/auth.php
    put core/middleware.php core/middleware.php
    
    # Nahrání admin souborů
    put admin/login.php admin/login.php
    put admin/dashboard.php admin/dashboard.php
    
    # Nahrání assets
    put assets/css/admin.css assets/css/admin.css
    
    # Nahrání databázového schématu
    put database_schema.sql database_schema.sql
    
    # Nahrání test souborů
    put test_database.php test_database.php
    put test_auth_basic.php test_auth_basic.php
    
    quit
    "
    
    if [ $? -eq 0 ]; then
        echo "✅ Všechny soubory úspěšně nahrány!"
        echo ""
        echo "🌐 Web je dostupný na: https://snurkynakrk.cz"
        echo "🔐 Admin přístup: https://snurkynakrk.cz/admin/login.php"
        echo "👤 Přihlašovací údaje: admin / snurky2024!"
        echo "🧪 Test databáze: https://snurkynakrk.cz/test_database.php"
        echo "🔐 Test auth: https://snurkynakrk.cz/test_auth_basic.php"
    else
        echo "❌ Chyba při nahrávání souborů"
        exit 1
    fi
}

# Funkce pro test připojení
test_connection() {
    echo "🧪 Testování FTP připojení..."
    
    lftp -c "
    set ftp:ssl-allow no
    set ftp:passive-mode on
    open -u $FTP_USER,$FTP_PASS $FTP_HOST
    cd $FTP_PATH
    pwd
    ls -la
    quit
    "
    
    if [ $? -eq 0 ]; then
        echo "✅ FTP připojení úspěšné!"
    else
        echo "❌ FTP připojení selhalo"
        exit 1
    fi
}

# Funkce pro synchronizaci celého projektu
sync_project() {
    echo "🔄 Synchronizace celého projektu..."
    
    lftp -c "
    set ftp:ssl-allow no
    set ftp:passive-mode on
    open -u $FTP_USER,$FTP_PASS $FTP_HOST
    cd $FTP_PATH
    
    # Synchronizace s vyloučením některých souborů
    mirror -R \
        --exclude-glob='.git*' \
        --exclude-glob='*.md' \
        --exclude-glob='node_modules' \
        --exclude-glob='.DS_Store' \
        --exclude-glob='*.log' \
        --exclude-glob='deploy_ftp.php' \
        --exclude-glob='quick_deploy.sh' \
        . .
    
    quit
    "
    
    if [ $? -eq 0 ]; then
        echo "✅ Projekt úspěšně synchronizován!"
    else
        echo "❌ Chyba při synchronizaci"
        exit 1
    fi
}

# Funkce pro backup
backup_remote() {
    echo "💾 Vytváření zálohy vzdáleného serveru..."
    
    BACKUP_DIR="backup_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    
    lftp -c "
    set ftp:ssl-allow no
    set ftp:passive-mode on
    open -u $FTP_USER,$FTP_PASS $FTP_HOST
    cd $FTP_PATH
    
    # Stažení všech souborů
    mirror . $BACKUP_DIR
    
    quit
    "
    
    if [ $? -eq 0 ]; then
        echo "✅ Záloha vytvořena v složce: $BACKUP_DIR"
    else
        echo "❌ Chyba při vytváření zálohy"
        exit 1
    fi
}

# Hlavní menu
case "$1" in
    "test")
        test_connection
        ;;
    "upload")
        upload_files
        ;;
    "sync")
        sync_project
        ;;
    "backup")
        backup_remote
        ;;
    *)
        echo "Použití: $0 {test|upload|sync|backup}"
        echo ""
        echo "Dostupné příkazy:"
        echo "  test   - Test FTP připojení"
        echo "  upload - Nahrání základních souborů"
        echo "  sync   - Synchronizace celého projektu"
        echo "  backup - Stažení zálohy ze serveru"
        echo ""
        echo "Příklady:"
        echo "  ./quick_deploy.sh test"
        echo "  ./quick_deploy.sh upload"
        echo "  ./quick_deploy.sh sync"
        exit 1
        ;;
esac
