# 🚀 Snurkynakrk.cz - Projekt Instrukce

## 📊 Přehled projektu
**Cíl:** Jednostránková prezentace s e-shopovou integrací pro snurkynakrk.cz
**Technologie:** PHP 8.1+, MySQL, Vanilla JS, CSS3
**Hosting:** Active24 (uvds125.active24.cz)

## 🔧 Připojovací údaje

### Databáze
- **Host:** uvds125.active24.cz
- **Databáze:** snurkynakr
- **Login:** snurkynakr
- **Heslo:** GKkcdg9m

### FTP přístup
- **Host:** snurkynakrk.cz
- **User:** snurkynakr
- **Pass:** mvUFSdLTeL
- **Složka:** www/

## 📁 Struktura projektu
```
/www/
├── index.php (veřejná homepage)
├── redirect.php (e-shop link handler)
├── admin/
│   ├── login.php
│   ├── dashboard.php
│   ├── content.php
│   ├── products.php
│   ├── eshop-settings.php
│   ├── team.php
│   └── media.php
├── core/
│   ├── config.php
│   ├── database.php
│   ├── auth.php
│   ├── eshop-connector.php
│   └── functions.php
├── assets/
│   ├── css/style.css
│   ├── js/main.js
│   ├── js/eshop-integration.js
│   └── images/
└── uploads/
    ├── products/
    ├── team/
    └── content/
```

## 🎯 Klíčové funkcionality
1. **Single Page Application** - plynulá navigace
2. **Admin panel** - unified správa obsahu
3. **E-shop integrace** - seamless přechod na hlavní obchod
4. **Produktová galerie** - s přímými odkazy
5. **Team sekce** - prezentace 6 kolegů
6. **Responzivní design** - mobile-first
7. **SEO optimalizace** - strukturovaná data

## 📋 Editovatelné sekce
- Hero sekce (nadpis, popis, CTA)
- O produktech (obecný popis)
- Produktová galerie (s e-shop odkazy)
- Náš tým (6 interních kolegů)
- Kontaktní informace
- Footer obsah

## 🛍️ E-shop integrace požadavky
- Dynamic URL building (base + product ID)
- UTM parameter injection
- Click tracking a analytics
- Link validation (HTTP status check)
- Fallback handling pro nedostupné produkty
- External redirect optimization

## 🔒 Bezpečnostní požadavky
- Prepared statements pro SQL
- Input validation a sanitization
- CSRF protection
- Session security
- File upload restrictions
- Admin access control

## 📈 Performance cíle
- Optimalizované obrázky (WebP fallback)
- Minifikované CSS/JS
- Browser caching headers
- Efektivní SQL dotazy
- External link preloading

## 🧪 Testovací kritéria
- Cross-browser kompatibilita
- Mobile responsiveness
- E-shop link funkčnost
- Admin panel usability
- Performance metrics
- SEO validace
