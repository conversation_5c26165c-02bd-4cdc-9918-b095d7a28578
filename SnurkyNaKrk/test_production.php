<?php
/**
 * Test produkčního prostředí snurkynakrk.cz
 * 
 * Ov<PERSON><PERSON><PERSON>, že všechny komponenty fungují na produkčním serveru
 */

echo "<!DOCTYPE html>
<html lang='cs'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Test produkčního prostředí - snurkynakrk.cz</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }
        .test-section { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .success { color: #28a745; font-weight: bold; }
        .error { color: #dc3545; font-weight: bold; }
        .warning { color: #ffc107; font-weight: bold; }
        .info { color: #007bff; font-weight: bold; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; }
        .stat-card { background: white; padding: 15px; border-radius: 5px; border-left: 4px solid #007bff; }
        .link-card { background: white; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .link-card a { color: #007bff; text-decoration: none; font-weight: bold; }
        .link-card a:hover { text-decoration: underline; }
    </style>
</head>
<body>";

echo "<h1>🌐 Test produkčního prostředí</h1>";
echo "<p>Ověření funkčnosti všech komponent na serveru snurkynakrk.cz</p>";

$tests = [];
$total_tests = 0;
$passed_tests = 0;

// Funkce pro testování
function runTest($name, $test_function) {
    global $tests, $total_tests, $passed_tests;
    
    $total_tests++;
    echo "<div class='test-section'>";
    echo "<h3>Test: $name</h3>";
    
    try {
        $result = $test_function();
        if ($result === true) {
            echo "<span class='success'>✅ ÚSPĚCH</span>";
            $tests[$name] = 'ÚSPĚCH';
            $passed_tests++;
        } else {
            echo "<span class='error'>❌ SELHÁNÍ</span>";
            if (is_string($result)) {
                echo "<br><strong>Důvod:</strong> $result";
            }
            $tests[$name] = 'SELHÁNÍ';
        }
    } catch (Exception $e) {
        echo "<span class='error'>❌ CHYBA</span>";
        echo "<br><strong>Chyba:</strong> " . $e->getMessage();
        $tests[$name] = 'CHYBA';
    }
    
    echo "</div>";
}

// Test 1: PHP verze
runTest("PHP verze", function() {
    $version = PHP_VERSION;
    echo "<br>PHP verze: $version";
    return version_compare($version, '7.4.0', '>=');
});

// Test 2: Požadovaná rozšíření
runTest("PHP rozšíření", function() {
    $required = ['pdo', 'pdo_mysql', 'session', 'json', 'mbstring'];
    $missing = [];
    
    foreach ($required as $ext) {
        if (!extension_loaded($ext)) {
            $missing[] = $ext;
        }
    }
    
    if (!empty($missing)) {
        return "Chybí rozšíření: " . implode(', ', $missing);
    }
    
    echo "<br>Všechna požadovaná rozšíření jsou dostupná";
    return true;
});

// Test 3: Soubory existují
runTest("Existence souborů", function() {
    $files = [
        'core/config.php',
        'core/database.php',
        'core/init.php',
        'core/auth.php',
        'core/middleware.php',
        'admin/login.php',
        'admin/dashboard.php',
        'database_schema.sql'
    ];
    
    $missing = [];
    foreach ($files as $file) {
        if (!file_exists($file)) {
            $missing[] = $file;
        }
    }
    
    if (!empty($missing)) {
        return "Chybí soubory: " . implode(', ', $missing);
    }
    
    echo "<br>Všechny klíčové soubory existují";
    return true;
});

// Test 4: Načtení konfigurace
runTest("Načtení konfigurace", function() {
    define('SNURKY_INIT', true);
    
    if (!file_exists('core/config.php')) {
        return "Konfigurační soubor neexistuje";
    }
    
    require_once 'core/config.php';
    
    if (!defined('DB_HOST')) {
        return "Databázová konfigurace není načtena";
    }
    
    echo "<br>Konfigurace úspěšně načtena";
    return true;
});

// Test 5: Databázové připojení
runTest("Databázové připojení", function() {
    if (!class_exists('Database')) {
        require_once 'core/database.php';
    }
    
    $db = new Database();
    
    if (!$db->isConnected()) {
        return "Nelze se připojit k databázi";
    }
    
    echo "<br>Databázové připojení úspěšné";
    return true;
});

// Test 6: Session funkčnost
runTest("Session management", function() {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    
    $_SESSION['test'] = 'hodnota';
    
    if (!isset($_SESSION['test']) || $_SESSION['test'] !== 'hodnota') {
        return "Session nefunguje správně";
    }
    
    unset($_SESSION['test']);
    echo "<br>Session management funkční";
    return true;
});

// Test 7: Autentifikační systém
runTest("Autentifikační systém", function() {
    if (!class_exists('Auth')) {
        require_once 'core/auth.php';
    }
    
    $db = new Database();
    $auth = new Auth($db);
    
    // Test CSRF tokenu
    $token = $auth->generateCSRFToken();
    if (empty($token)) {
        return "CSRF token se nepodařilo vygenerovat";
    }
    
    // Test validace
    if (!$auth->validateCSRFToken($token)) {
        return "CSRF token validace selhala";
    }
    
    echo "<br>Autentifikační systém funkční";
    return true;
});

// Test 8: Bezpečnostní middleware
runTest("Bezpečnostní middleware", function() {
    if (!class_exists('SecurityMiddleware')) {
        require_once 'core/middleware.php';
    }
    
    $db = new Database();
    $auth = new Auth($db);
    $middleware = new SecurityMiddleware($auth);
    
    // Test sanitizace
    $dirty = '<script>alert("test")</script>Hello';
    $clean = $middleware->sanitizeInput($dirty);
    
    if (strpos($clean, '<script>') !== false) {
        return "Sanitizace nefunguje správně";
    }
    
    echo "<br>Bezpečnostní middleware funkční";
    return true;
});

// Test 9: Složky a oprávnění
runTest("Složky a oprávnění", function() {
    $dirs = ['uploads', 'logs', 'cache'];
    $issues = [];
    
    foreach ($dirs as $dir) {
        if (!is_dir($dir)) {
            $issues[] = "Složka $dir neexistuje";
        } elseif (!is_writable($dir)) {
            $issues[] = "Složka $dir není zapisovatelná";
        }
    }
    
    if (!empty($issues)) {
        return implode(', ', $issues);
    }
    
    echo "<br>Všechny složky jsou připraveny";
    return true;
});

// Test 10: URL přístupnost
runTest("URL přístupnost", function() {
    $urls = [
        'admin/login.php' => 'Admin přihlášení',
        'test_database.php' => 'Test databáze',
        'test_auth_basic.php' => 'Test autentifikace'
    ];
    
    $accessible = [];
    foreach ($urls as $url => $name) {
        if (file_exists($url)) {
            $accessible[] = $name;
        }
    }
    
    echo "<br>Přístupné URL: " . implode(', ', $accessible);
    return count($accessible) > 0;
});

// Zobrazení výsledků
echo "<div class='test-section'>";
echo "<h2>📊 Shrnutí testů</h2>";

echo "<div class='stats'>";
echo "<div class='stat-card'>";
echo "<h4>Celkem testů</h4>";
echo "<div style='font-size: 24px; color: #007bff;'>$total_tests</div>";
echo "</div>";

echo "<div class='stat-card'>";
echo "<h4>Úspěšné</h4>";
echo "<div style='font-size: 24px; color: #28a745;'>$passed_tests</div>";
echo "</div>";

echo "<div class='stat-card'>";
echo "<h4>Neúspěšné</h4>";
echo "<div style='font-size: 24px; color: #dc3545;'>" . ($total_tests - $passed_tests) . "</div>";
echo "</div>";

echo "<div class='stat-card'>";
echo "<h4>Úspěšnost</h4>";
echo "<div style='font-size: 24px; color: " . ($passed_tests == $total_tests ? '#28a745' : '#ffc107') . ";'>" . 
     round(($passed_tests / $total_tests) * 100, 1) . "%</div>";
echo "</div>";
echo "</div>";

if ($passed_tests == $total_tests) {
    echo "<div class='success' style='text-align: center; font-size: 18px; margin: 20px 0;'>";
    echo "🎉 Všechny testy prošly úspěšně! Produkční prostředí je plně funkční.";
    echo "</div>";
} else {
    echo "<div class='warning' style='text-align: center; font-size: 18px; margin: 20px 0;'>";
    echo "⚠️ Některé testy selhaly. Zkontrolujte konfiguraci systému.";
    echo "</div>";
}

echo "</div>";

// Užitečné odkazy
echo "<div class='test-section'>";
echo "<h3>🔗 Užitečné odkazy</h3>";

$links = [
    'admin/login.php' => '🔐 Admin přihlášení (admin / snurky2024!)',
    'test_database.php' => '🗄️ Test databáze',
    'test_auth_basic.php' => '🔐 Test autentifikace',
    'deployment_manager.php' => '🚀 Deployment Manager'
];

foreach ($links as $url => $title) {
    if (file_exists($url)) {
        echo "<div class='link-card'>";
        echo "<a href='$url' target='_blank'>$title</a>";
        echo "</div>";
    }
}

echo "</div>";

// Systémové informace
echo "<div class='test-section'>";
echo "<h3>ℹ️ Systémové informace</h3>";
echo "<pre>";
echo "Server: " . ($_SERVER['SERVER_NAME'] ?? 'localhost') . "\n";
echo "PHP verze: " . PHP_VERSION . "\n";
echo "Server čas: " . date('Y-m-d H:i:s') . "\n";
echo "Document root: " . ($_SERVER['DOCUMENT_ROOT'] ?? 'neznámý') . "\n";
echo "Script path: " . __FILE__ . "\n";
echo "Memory usage: " . round(memory_get_usage() / 1024 / 1024, 2) . " MB\n";
echo "Peak memory: " . round(memory_get_peak_usage() / 1024 / 1024, 2) . " MB\n";
echo "</pre>";
echo "</div>";

echo "<div style='text-align: center; margin: 30px 0; padding: 20px; background: #e9ecef; border-radius: 5px;'>";
echo "<p><strong>Test produkčního prostředí dokončen!</strong></p>";
echo "<p>Server snurkynakrk.cz je připraven pro použití.</p>";
echo "</div>";

echo "</body></html>";
?>
