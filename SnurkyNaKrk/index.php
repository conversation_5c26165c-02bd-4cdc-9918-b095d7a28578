<?php
/**
 * Hlavní stránka webu snurkynakrk.cz
 */

// Načtení základních souborů
require_once 'core/init.php';

// Získání obsahu z databáze
try {
    $db = Database::getInstance();
    
    // Načtení obsahových sekcí
    $hero = $db->selectOne("SELECT * FROM content_sections WHERE section_key = 'hero' AND is_active = 1");
    $about = $db->selectOne("SELECT * FROM content_sections WHERE section_key = 'about' AND is_active = 1");
    $contact = $db->selectOne("SELECT * FROM content_sections WHERE section_key = 'contact' AND is_active = 1");
    
    // Načtení produktů
    $products = $db->select("SELECT * FROM products WHERE is_active = 1 ORDER BY sort_order ASC LIMIT 6");
    
} catch (Exception $e) {
    // Fallback obsah pokud databáze nefunguje
    $hero = ['title' => 'Vítejte na Snurky na krk', 'content' => 'Objevte naši jedinečnou kolekci produktů pro váš krk.'];
    $about = ['title' => 'O našich produktech', 'content' => 'Naše produkty jsou vyráběny s láskou a péčí o detail.'];
    $contact = ['title' => 'Kontaktujte nás', 'content' => 'Máte dotazy? Rádi vám pomůžeme.'];
    $products = [];
}
?>
<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Snurky na krk - Jedinečné produkty pro váš krk</title>
    <meta name="description" content="Objevte naši jedinečnou kolekci produktů pro váš krk. Kvalitní materiály, originální design.">
    <meta name="keywords" content="snurky, krk, produkty, móda, doplňky">
    
    <!-- Základní CSS -->
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #fff;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        header {
            background: #2c3e50;
            color: white;
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            font-size: 1.5rem;
            font-weight: bold;
        }
        
        nav ul {
            list-style: none;
            display: flex;
            gap: 2rem;
        }
        
        nav a {
            color: white;
            text-decoration: none;
            transition: color 0.3s;
        }
        
        nav a:hover {
            color: #3498db;
        }
        
        .hero {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 4rem 0;
            text-align: center;
        }
        
        .hero h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        
        .hero p {
            font-size: 1.2rem;
            margin-bottom: 2rem;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #3498db;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: background 0.3s;
        }
        
        .btn:hover {
            background: #2980b9;
        }
        
        .section {
            padding: 4rem 0;
        }
        
        .section h2 {
            text-align: center;
            margin-bottom: 2rem;
            font-size: 2.5rem;
        }
        
        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }
        
        .product-card {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 8px;
            text-align: center;
            transition: transform 0.3s;
        }
        
        .product-card:hover {
            transform: translateY(-5px);
        }
        
        .product-card img {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 5px;
            margin-bottom: 1rem;
        }
        
        footer {
            background: #2c3e50;
            color: white;
            text-align: center;
            padding: 2rem 0;
        }
        
        .admin-link {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: #e74c3c;
            color: white;
            padding: 10px 15px;
            border-radius: 50px;
            text-decoration: none;
            font-size: 12px;
            opacity: 0.7;
            transition: opacity 0.3s;
        }
        
        .admin-link:hover {
            opacity: 1;
        }
        
        @media (max-width: 768px) {
            .hero h1 {
                font-size: 2rem;
            }
            
            nav ul {
                flex-direction: column;
                gap: 1rem;
            }
            
            .header-content {
                flex-direction: column;
                gap: 1rem;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="header-content">
                <div class="logo">Snurky na krk</div>
                <nav>
                    <ul>
                        <li><a href="#home">Domů</a></li>
                        <li><a href="#about">O nás</a></li>
                        <li><a href="#products">Produkty</a></li>
                        <li><a href="#contact">Kontakt</a></li>
                    </ul>
                </nav>
            </div>
        </div>
    </header>

    <main>
        <!-- Hero sekce -->
        <section id="home" class="hero">
            <div class="container">
                <h1><?= htmlspecialchars($hero['title'] ?? 'Vítejte na Snurky na krk') ?></h1>
                <p><?= htmlspecialchars($hero['content'] ?? 'Objevte naši jedinečnou kolekci produktů pro váš krk.') ?></p>
                <a href="#products" class="btn">Prohlédnout produkty</a>
            </div>
        </section>

        <!-- O nás sekce -->
        <section id="about" class="section">
            <div class="container">
                <h2><?= htmlspecialchars($about['title'] ?? 'O našich produktech') ?></h2>
                <p style="text-align: center; font-size: 1.1rem; max-width: 800px; margin: 0 auto;">
                    <?= htmlspecialchars($about['content'] ?? 'Naše produkty jsou vyráběny s láskou a péčí o detail.') ?>
                </p>
            </div>
        </section>

        <!-- Produkty sekce -->
        <section id="products" class="section" style="background: #f8f9fa;">
            <div class="container">
                <h2>Naše produkty</h2>
                
                <?php if (!empty($products)): ?>
                    <div class="products-grid">
                        <?php foreach ($products as $product): ?>
                            <div class="product-card">
                                <?php if (!empty($product['image_url'])): ?>
                                    <img src="<?= htmlspecialchars($product['image_url']) ?>" 
                                         alt="<?= htmlspecialchars($product['name']) ?>">
                                <?php else: ?>
                                    <div style="width: 100%; height: 200px; background: #ddd; border-radius: 5px; margin-bottom: 1rem; display: flex; align-items: center; justify-content: center; color: #666;">
                                        Obrázek produktu
                                    </div>
                                <?php endif; ?>
                                
                                <h3><?= htmlspecialchars($product['name']) ?></h3>
                                <p><?= htmlspecialchars($product['description']) ?></p>
                                
                                <?php if (!empty($product['eshop_url'])): ?>
                                    <a href="<?= htmlspecialchars($product['eshop_url']) ?>" 
                                       class="btn" target="_blank" rel="noopener">
                                        Koupit nyní
                                    </a>
                                <?php endif; ?>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <p style="text-align: center; color: #666;">
                        Produkty budou brzy k dispozici.
                    </p>
                <?php endif; ?>
            </div>
        </section>

        <!-- Kontakt sekce -->
        <section id="contact" class="section">
            <div class="container">
                <h2><?= htmlspecialchars($contact['title'] ?? 'Kontaktujte nás') ?></h2>
                <p style="text-align: center; font-size: 1.1rem;">
                    <?= htmlspecialchars($contact['content'] ?? 'Máte dotazy? Rádi vám pomůžeme.') ?>
                </p>
            </div>
        </section>
    </main>

    <footer>
        <div class="container">
            <p>&copy; 2024 Snurky na krk. Všechna práva vyhrazena.</p>
            <p style="margin-top: 10px; font-size: 14px; opacity: 0.8;">
                Vytvořeno s ❤️ pro jedinečné produkty
            </p>
        </div>
    </footer>

    <!-- Admin odkaz -->
    <a href="admin/login.php" class="admin-link">Admin</a>

    <!-- Základní JavaScript -->
    <script>
        // Smooth scrolling pro navigaci
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
</body>
</html>
