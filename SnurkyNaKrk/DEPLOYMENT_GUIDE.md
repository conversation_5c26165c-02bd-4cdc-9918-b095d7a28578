# 🚀 FTP Deployment Guide - snurkynakrk.cz

## 📋 P<PERSON>ehled

Tento guide popisuje, jak nahrávat soubory na produkční server snurkynakrk.cz pomocí připravených deployment nástrojů.

## 🔧 Dostupné nástroje

### 1. PHP Deployment Script (`deploy_ftp.php`)
Kompletní PHP script pro FTP deployment s pokročilými funkcemi.

**Použití z příkazové řádky:**
```bash
# Test FTP připojení
php deploy_ftp.php test

# Deployment všech souborů
php deploy_ftp.php deploy

# Nápověda
php deploy_ftp.php help
```

**Použití přes web:**
- Otevřít `deploy_ftp.php` v prohlížeči
- Kliknout na požadovanou akci

### 2. Shell Script (`quick_deploy.sh`)
Rychlý deployment pomocí lftp (vyžaduje instalaci lftp).

**Použití:**
```bash
# Test připojení
./quick_deploy.sh test

# <PERSON><PERSON><PERSON><PERSON> z<PERSON>ladních souborů
./quick_deploy.sh upload

# Synchronizace celého projektu
./quick_deploy.sh sync

# Stažení zálohy
./quick_deploy.sh backup
```

### 3. Deployment Manager (`deployment_manager.php`)
Grafické webové rozhraní pro snadnou správu deploymentu.

**Použití:**
- Otevřít `deployment_manager.php` v prohlížeči
- Použít tlačítka pro různé akce

## 🌐 Server informace

### FTP přístup
- **Host:** snurkynakrk.cz
- **Uživatel:** snurkynakr
- **Heslo:** mvUFSdLTeL
- **Cílová složka:** /www/

### Databáze
- **Host:** uvds125.active24.cz
- **Databáze:** snurkynakr
- **Uživatel:** snurkynakr
- **Heslo:** GKkcdg9m

## 📁 Struktura souborů k nahrání

### Core soubory
```
core/
├── config.php          # Konfigurace aplikace
├── database.php         # Databázová třída
├── init.php            # Inicializační soubor
├── auth.php            # Autentifikační systém
└── middleware.php      # Bezpečnostní middleware
```

### Admin soubory
```
admin/
├── login.php           # Přihlašovací stránka
└── dashboard.php       # Admin dashboard
```

### Assets
```
assets/
└── css/
    └── admin.css       # Admin styly
```

### Databáze a testy
```
database_schema.sql     # SQL schéma
test_database.php       # Test databáze
test_auth_basic.php     # Test autentifikace
```

## 🚀 Deployment postupy

### Rychlý deployment (doporučeno)
1. Otevřít `deployment_manager.php` v prohlížeči
2. Kliknout na "🧪 Test připojení" pro ověření
3. Kliknout na "🚀 Kompletní deployment"
4. Počkat na dokončení

### Částečný deployment
Pro nahrání pouze určitých kategorií souborů:

1. **Core soubory:** Kliknout "Deploy core"
2. **Admin soubory:** Kliknout "Deploy admin"  
3. **Assets:** Kliknout "Deploy assets"

### CLI deployment
```bash
# Test připojení
php deploy_ftp.php test

# Kompletní deployment
php deploy_ftp.php deploy
```

## 🧪 Testování po deploymentu

### 1. Test databáze
- URL: https://snurkynakrk.cz/test_database.php
- Ověří připojení k databázi a základní funkce

### 2. Test autentifikace
- URL: https://snurkynakrk.cz/test_auth_basic.php
- Ověří funkčnost autentifikačního systému

### 3. Admin přihlášení
- URL: https://snurkynakrk.cz/admin/login.php
- Přihlašovací údaje: **admin** / **snurky2024!**

### 4. Admin dashboard
- Po přihlášení: https://snurkynakrk.cz/admin/dashboard.php
- Ověří kompletní funkcionalitu admin rozhraní

## ⚠️ Důležité poznámky

### Bezpečnost
- FTP přístupové údaje jsou uloženy v kódu (pouze pro development)
- V produkci doporučujeme použít environment variables
- Pravidelně měňte hesla

### Backup
- Před každým deploymentem doporučujeme vytvořit zálohu
- Použijte: `./quick_deploy.sh backup`

### Troubleshooting
- Pokud selže nahrání CSS: Ručně vytvořte složku `assets/css/` na serveru
- Při problémech s připojením: Zkontrolujte firewall a FTP nastavení
- Pro debug: Použijte `php deploy_ftp.php test`

## 📊 Deployment checklist

### Před deploymentem
- [ ] Otestovat lokálně všechny funkce
- [ ] Zkontrolovat, že všechny soubory existují
- [ ] Vytvořit zálohu aktuálního stavu serveru
- [ ] Ověřit FTP připojení

### Po deploymentu
- [ ] Otestovat databázové připojení
- [ ] Ověřit funkčnost autentifikace
- [ ] Přihlásit se do admin panelu
- [ ] Zkontrolovat všechny stránky
- [ ] Ověřit CSS styly

## 🔗 Užitečné odkazy

### Produkční web
- **Hlavní stránka:** https://snurkynakrk.cz
- **Admin přihlášení:** https://snurkynakrk.cz/admin/login.php
- **Test databáze:** https://snurkynakrk.cz/test_database.php
- **Test auth:** https://snurkynakrk.cz/test_auth_basic.php

### Lokální nástroje
- **Deployment Manager:** `deployment_manager.php`
- **FTP Script:** `deploy_ftp.php`
- **Shell Script:** `quick_deploy.sh`

## 🆘 Řešení problémů

### Chyba připojení
```bash
# Ověření připojení
ping snurkynakrk.cz
telnet snurkynakrk.cz 21
```

### Chyba oprávnění
- Zkontrolujte FTP přihlašovací údaje
- Ověřte, že máte práva zápisu do `/www/` složky

### Chybějící soubory
- Zkontrolujte lokální existenci souborů
- Ověřte cesty v deployment scriptu

### CSS se nenačítá
- Ručně vytvořte složku `assets/css/` na serveru
- Nahrajte `admin.css` znovu

## 📞 Podpora

Pro technickou podporu kontaktujte:
- **Active24 hosting:** https://www.active24.cz/podpora
- **FTP problémy:** Zkontrolujte nastavení firewallu

---

**Poznámka:** Tento deployment systém je připraven pro Krok 2 projektu. Pro další kroky bude rozšířen o dodatečné soubory a funkce.
