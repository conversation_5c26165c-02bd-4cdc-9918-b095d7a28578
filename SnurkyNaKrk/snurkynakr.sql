-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.2
-- https://www.phpmyadmin.net/
--
-- <PERSON><PERSON><PERSON><PERSON><PERSON>: localhost
-- Vytvořeno: Pát 06. čen 2025, 14:29
-- Verze serveru: 10.11.11-MariaDB-0+deb12u1-log
-- Verze PHP: 8.1.32

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Datab<PERSON>ze: `snurkynakr`
--

-- --------------------------------------------------------

--
-- Struktura tabulky `admin_sessions`
--

CREATE TABLE `admin_sessions` (
  `id` int(11) NOT NULL,
  `session_id` varchar(128) NOT NULL COMMENT 'ID session',
  `user_data` text DEFAULT NULL COMMENT 'Data uživatele (JSON)',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP adresa',
  `user_agent` varchar(500) DEFAULT NULL COMMENT 'User agent prohlížeče',
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `expires_at` timestamp NULL DEFAULT NULL COMMENT 'Čas vypršení session'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Admin sessions';

--
-- Vypisuji data pro tabulku `admin_sessions`
--

INSERT INTO `admin_sessions` (`id`, `session_id`, `user_data`, `ip_address`, `user_agent`, `created_at`, `expires_at`) VALUES
(2, '913bc8181f9cbba276d5ab75c1c79bd0', '{\"username\":\"admin\"}', '************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15', '2025-06-06 12:19:24', '2025-06-06 13:19:24');

-- --------------------------------------------------------

--
-- Struktura tabulky `content_sections`
--

CREATE TABLE `content_sections` (
  `id` int(11) NOT NULL,
  `section_key` varchar(50) NOT NULL COMMENT 'Unikátní klíč sekce (hero, about, contact, atd.)',
  `title` varchar(200) DEFAULT NULL COMMENT 'Nadpis sekce',
  `content` text DEFAULT NULL COMMENT 'Hlavní obsah sekce',
  `meta_description` varchar(160) DEFAULT NULL COMMENT 'Meta popis pro SEO',
  `is_active` tinyint(1) DEFAULT 1 COMMENT 'Zda je sekce aktivní',
  `sort_order` int(11) DEFAULT 0 COMMENT 'Pořadí zobrazení',
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Obsahové sekce webu';

--
-- Vypisuji data pro tabulku `content_sections`
--

INSERT INTO `content_sections` (`id`, `section_key`, `title`, `content`, `meta_description`, `is_active`, `sort_order`, `created_at`, `updated_at`) VALUES
(1, 'hero', 'Vítejte na Snurky na krk', 'Objevte naši jedinečnou kolekci produktů pro váš krk.', 'Snurky na krk - jedinečné produkty pro váš krk', 1, 1, '2025-06-06 11:01:59', '2025-06-06 11:01:59'),
(2, 'about', 'O našich produktech', 'Naše produkty jsou vyráběny s láskou a péčí o detail.', 'Informace o našich produktech a jejich kvalitě', 1, 2, '2025-06-06 11:01:59', '2025-06-06 11:01:59'),
(3, 'contact', 'Kontaktujte nás', 'Máte dotazy? Rádi vám pomůžeme.', 'Kontaktní informace a způsoby komunikace', 1, 3, '2025-06-06 11:01:59', '2025-06-06 11:01:59'),
(4, 'footer', 'Footer obsah', 'Copyright © 2024 Snurky na krk. Všechna práva vyhrazena.', 'Footer informace', 1, 4, '2025-06-06 11:01:59', '2025-06-06 11:01:59');

-- --------------------------------------------------------

--
-- Struktura tabulky `eshop_settings`
--

CREATE TABLE `eshop_settings` (
  `id` int(11) NOT NULL,
  `setting_key` varchar(50) NOT NULL COMMENT 'Klíč nastavení',
  `setting_value` text DEFAULT NULL COMMENT 'Hodnota nastavení',
  `description` varchar(200) DEFAULT NULL COMMENT 'Popis nastavení',
  `is_active` tinyint(1) DEFAULT 1 COMMENT 'Zda je nastavení aktivní',
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Nastavení e-shop integrace';

--
-- Vypisuji data pro tabulku `eshop_settings`
--

INSERT INTO `eshop_settings` (`id`, `setting_key`, `setting_value`, `description`, `is_active`, `updated_at`) VALUES
(1, 'base_url', '', 'Základní URL e-shopu', 1, '2025-06-06 11:01:59'),
(2, 'utm_source', 'snurkynakrk', 'UTM source parametr', 1, '2025-06-06 11:01:59'),
(3, 'utm_medium', 'website', 'UTM medium parametr', 1, '2025-06-06 11:01:59'),
(4, 'utm_campaign', 'product_link', 'UTM campaign parametr', 1, '2025-06-06 11:01:59'),
(5, 'link_validation', '1', 'Zda validovat odkazy před přesměrováním', 1, '2025-06-06 11:01:59'),
(6, 'click_tracking', '1', 'Zda sledovat kliknutí na odkazy', 1, '2025-06-06 11:01:59');

-- --------------------------------------------------------

--
-- Struktura tabulky `media_files`
--

CREATE TABLE `media_files` (
  `id` int(11) NOT NULL,
  `filename` varchar(255) NOT NULL COMMENT 'Název souboru',
  `original_name` varchar(255) DEFAULT NULL COMMENT 'Původní název souboru',
  `file_path` varchar(500) DEFAULT NULL COMMENT 'Cesta k souboru',
  `file_size` int(11) DEFAULT NULL COMMENT 'Velikost souboru v bytech',
  `mime_type` varchar(100) DEFAULT NULL COMMENT 'MIME typ souboru',
  `alt_text` varchar(200) DEFAULT NULL COMMENT 'Alternativní text pro obrázky',
  `category` enum('product','team','content','general') DEFAULT 'general' COMMENT 'Kategorie souboru',
  `related_id` int(11) DEFAULT NULL COMMENT 'ID souvisejícího záznamu',
  `created_at` timestamp NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Media soubory';

-- --------------------------------------------------------

--
-- Struktura tabulky `products`
--

CREATE TABLE `products` (
  `id` int(11) NOT NULL,
  `name` varchar(150) NOT NULL COMMENT 'Název produktu',
  `description` text DEFAULT NULL COMMENT 'Popis produktu',
  `image_url` varchar(500) DEFAULT NULL,
  `specifications` text DEFAULT NULL COMMENT 'Technické specifikace',
  `price_info` varchar(100) DEFAULT NULL COMMENT 'Informace o ceně (text)',
  `eshop_url` varchar(500) DEFAULT NULL COMMENT 'Přímý odkaz do e-shopu',
  `eshop_product_id` varchar(100) DEFAULT NULL COMMENT 'ID produktu v e-shopu',
  `cta_text` varchar(50) DEFAULT 'Koupit v e-shopu' COMMENT 'Text call-to-action tlačítka',
  `sort_order` int(11) DEFAULT 0 COMMENT 'Pořadí zobrazení',
  `is_active` tinyint(1) DEFAULT 1 COMMENT 'Zda je produkt aktivní',
  `link_clicks` int(11) DEFAULT 0 COMMENT 'Počet kliknutí na e-shop odkaz',
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Produkty zobrazované na webu';

--
-- Vypisuji data pro tabulku `products`
--

INSERT INTO `products` (`id`, `name`, `description`, `image_url`, `specifications`, `price_info`, `eshop_url`, `eshop_product_id`, `cta_text`, `sort_order`, `is_active`, `link_clicks`, `created_at`, `updated_at`) VALUES
(1, 'Elegantní hedvábná šála', 'Luxusní hedvábná šála v různých barvách. Perfektní doplněk pro elegantní příležitosti.', '', NULL, NULL, '', NULL, 'Koupit v e-shopu', 1, 1, 0, '2025-06-06 12:28:42', '2025-06-06 12:28:42'),
(2, 'Bavlněný šátek', 'Měkký bavlněný šátek pro každodenní nošení. Příjemný materiál a krásné vzory.', '', NULL, NULL, '', NULL, 'Koupit v e-shopu', 2, 1, 0, '2025-06-06 12:28:42', '2025-06-06 12:28:42'),
(3, 'Zimní nákrčník', 'Teplý nákrčník pro chladné dny. Vyrobeno z kvalitních materiálů pro maximální pohodlí.', '', NULL, NULL, '', NULL, 'Koupit v e-shopu', 3, 1, 0, '2025-06-06 12:28:42', '2025-06-06 12:28:42'),
(4, 'Letní pareo', 'Lehké letní pareo ideální na dovolenou. Univerzální velikost a krásné barvy.', '', NULL, NULL, '', NULL, 'Koupit v e-shopu', 4, 1, 0, '2025-06-06 12:28:42', '2025-06-06 12:28:42');

-- --------------------------------------------------------

--
-- Struktura tabulky `site_settings`
--

CREATE TABLE `site_settings` (
  `id` int(11) NOT NULL,
  `setting_key` varchar(50) NOT NULL COMMENT 'Klíč nastavení',
  `setting_value` text DEFAULT NULL COMMENT 'Hodnota nastavení',
  `setting_type` enum('text','textarea','boolean','json') DEFAULT 'text' COMMENT 'Typ nastavení',
  `description` varchar(200) DEFAULT NULL COMMENT 'Popis nastavení',
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Obecná nastavení webu';

--
-- Vypisuji data pro tabulku `site_settings`
--

INSERT INTO `site_settings` (`id`, `setting_key`, `setting_value`, `setting_type`, `description`, `updated_at`) VALUES
(1, 'site_title', 'Snurky na krk', 'text', 'Název webu', '2025-06-06 11:01:59'),
(2, 'site_description', 'Jedinečné produkty pro váš krk', 'text', 'Popis webu', '2025-06-06 11:01:59'),
(3, 'admin_email', '', 'text', 'E-mail administrátora', '2025-06-06 11:01:59'),
(4, 'maintenance_mode', '0', 'boolean', 'Režim údržby', '2025-06-06 11:01:59'),
(5, 'analytics_code', '', 'textarea', 'Google Analytics kód', '2025-06-06 11:01:59'),
(6, 'contact_phone', '', 'text', 'Kontaktní telefon', '2025-06-06 11:01:59'),
(7, 'contact_email', '', 'text', 'Kontaktní e-mail', '2025-06-06 11:01:59');

-- --------------------------------------------------------

--
-- Struktura tabulky `team_members`
--

CREATE TABLE `team_members` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL COMMENT 'Jméno člena týmu',
  `phone` varchar(20) DEFAULT NULL COMMENT 'Telefonní číslo',
  `email` varchar(100) DEFAULT NULL COMMENT 'E-mailová adresa',
  `photo_filename` varchar(255) DEFAULT NULL COMMENT 'Název souboru s fotografií',
  `sort_order` int(11) DEFAULT 0 COMMENT 'Pořadí zobrazení',
  `is_active` tinyint(1) DEFAULT 1 COMMENT 'Zda je člen aktivní',
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Členové týmu';

--
-- Indexy pro exportované tabulky
--

--
-- Indexy pro tabulku `admin_sessions`
--
ALTER TABLE `admin_sessions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `session_id` (`session_id`),
  ADD KEY `idx_session` (`session_id`),
  ADD KEY `idx_expires` (`expires_at`),
  ADD KEY `idx_ip` (`ip_address`);

--
-- Indexy pro tabulku `content_sections`
--
ALTER TABLE `content_sections`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `section_key` (`section_key`),
  ADD KEY `idx_active_sort` (`is_active`,`sort_order`),
  ADD KEY `idx_section_key` (`section_key`);

--
-- Indexy pro tabulku `eshop_settings`
--
ALTER TABLE `eshop_settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `setting_key` (`setting_key`),
  ADD KEY `idx_key` (`setting_key`),
  ADD KEY `idx_active` (`is_active`);

--
-- Indexy pro tabulku `media_files`
--
ALTER TABLE `media_files`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_category` (`category`),
  ADD KEY `idx_related` (`category`,`related_id`),
  ADD KEY `idx_filename` (`filename`);

--
-- Indexy pro tabulku `products`
--
ALTER TABLE `products`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_active_sort` (`is_active`,`sort_order`),
  ADD KEY `idx_eshop_id` (`eshop_product_id`),
  ADD KEY `idx_clicks` (`link_clicks`);

--
-- Indexy pro tabulku `site_settings`
--
ALTER TABLE `site_settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `setting_key` (`setting_key`),
  ADD KEY `idx_key` (`setting_key`),
  ADD KEY `idx_type` (`setting_type`);

--
-- Indexy pro tabulku `team_members`
--
ALTER TABLE `team_members`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_active_sort` (`is_active`,`sort_order`),
  ADD KEY `idx_email` (`email`);

--
-- AUTO_INCREMENT pro tabulky
--

--
-- AUTO_INCREMENT pro tabulku `admin_sessions`
--
ALTER TABLE `admin_sessions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT pro tabulku `content_sections`
--
ALTER TABLE `content_sections`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=12;

--
-- AUTO_INCREMENT pro tabulku `eshop_settings`
--
ALTER TABLE `eshop_settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT pro tabulku `media_files`
--
ALTER TABLE `media_files`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pro tabulku `products`
--
ALTER TABLE `products`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT pro tabulku `site_settings`
--
ALTER TABLE `site_settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT pro tabulku `team_members`
--
ALTER TABLE `team_members`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
