<?php
/**
 * Test autentifikačního systému pro snurkynakrk.cz
 * 
 * Tento soubor testuje všechny komponenty autentifikačního systému
 */

// Načtení základních souborů
require_once 'core/init.php';
require_once 'core/auth.php';
require_once 'core/middleware.php';

echo "<!DOCTYPE html>
<html lang='cs'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Test autentifikačního systému - Snurkynakrk.cz</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .test-section { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .success { color: #28a745; font-weight: bold; }
        .error { color: #dc3545; font-weight: bold; }
        .info { color: #007bff; font-weight: bold; }
        .warning { color: #ffc107; font-weight: bold; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; }
        .stat-card { background: white; padding: 15px; border-radius: 5px; border-left: 4px solid #007bff; }
    </style>
</head>
<body>";

echo "<h1>🔐 Test autentifikačního systému</h1>";
echo "<p>Testování všech komponent autentifikačního systému pro snurkynakrk.cz</p>";

$test_results = [];
$total_tests = 0;
$passed_tests = 0;

// Funkce pro testování
function runTest($name, $test_function) {
    global $test_results, $total_tests, $passed_tests;
    
    $total_tests++;
    echo "<div class='test-section'>";
    echo "<h3>Test: $name</h3>";
    
    try {
        $result = $test_function();
        if ($result === true) {
            echo "<span class='success'>✅ ÚSPĚCH</span>";
            $test_results[$name] = 'ÚSPĚCH';
            $passed_tests++;
        } else {
            echo "<span class='error'>❌ SELHÁNÍ</span>";
            if (is_string($result)) {
                echo "<br><strong>Důvod:</strong> $result";
            }
            $test_results[$name] = 'SELHÁNÍ';
        }
    } catch (Exception $e) {
        echo "<span class='error'>❌ CHYBA</span>";
        echo "<br><strong>Chyba:</strong> " . $e->getMessage();
        $test_results[$name] = 'CHYBA';
    }
    
    echo "</div>";
}

// Test 1: Inicializace databáze
runTest("Inicializace databáze", function() {
    $db = new Database();
    return $db->isConnected();
});

// Test 2: Vytvoření Auth objektu
runTest("Vytvoření Auth objektu", function() {
    $db = new Database();
    $auth = new Auth($db);
    return is_object($auth);
});

// Test 3: Generování CSRF tokenu
runTest("Generování CSRF tokenu", function() {
    $db = new Database();
    $auth = new Auth($db);
    $token = $auth->generateCSRFToken();
    return !empty($token) && strlen($token) === 64;
});

// Test 4: Validace CSRF tokenu
runTest("Validace CSRF tokenu", function() {
    $db = new Database();
    $auth = new Auth($db);
    $token = $auth->generateCSRFToken();
    return $auth->validateCSRFToken($token);
});

// Test 5: Neplatný CSRF token
runTest("Neplatný CSRF token", function() {
    $db = new Database();
    $auth = new Auth($db);
    return !$auth->validateCSRFToken('neplatny_token');
});

// Test 6: Kontrola nepřihlášeného uživatele
runTest("Kontrola nepřihlášeného uživatele", function() {
    $db = new Database();
    $auth = new Auth($db);
    return !$auth->isLoggedIn();
});

// Test 7: Neplatné přihlašovací údaje
runTest("Neplatné přihlašovací údaje", function() {
    $db = new Database();
    $auth = new Auth($db);
    return !$auth->login('spatny_uzivatel', 'spatne_heslo');
});

// Test 8: Platné přihlašovací údaje
runTest("Platné přihlašovací údaje", function() {
    $db = new Database();
    $auth = new Auth($db);
    return $auth->login('admin', 'snurky2024!');
});

// Test 9: Kontrola přihlášeného uživatele
runTest("Kontrola přihlášeného uživatele", function() {
    $db = new Database();
    $auth = new Auth($db);
    return $auth->isLoggedIn();
});

// Test 10: Získání session informací
runTest("Získání session informací", function() {
    $db = new Database();
    $auth = new Auth($db);
    $info = $auth->getSessionInfo();
    return is_array($info) && isset($info['username']);
});

// Test 11: SecurityMiddleware inicializace
runTest("SecurityMiddleware inicializace", function() {
    $db = new Database();
    $auth = new Auth($db);
    $middleware = new SecurityMiddleware($auth);
    return is_object($middleware);
});

// Test 12: Sanitizace vstupních dat
runTest("Sanitizace vstupních dat", function() {
    $db = new Database();
    $auth = new Auth($db);
    $middleware = new SecurityMiddleware($auth);
    
    $dirty_data = '<script>alert("xss")</script>Test';
    $clean_data = $middleware->sanitizeInput($dirty_data);
    
    return !strpos($clean_data, '<script>') && strpos($clean_data, 'Test') !== false;
});

// Test 13: Validace emailu
runTest("Validace emailu", function() {
    $db = new Database();
    $auth = new Auth($db);
    $middleware = new SecurityMiddleware($auth);
    
    $valid_email = $middleware->validateEmail('<EMAIL>');
    $invalid_email = $middleware->validateEmail('neplatny-email');
    
    return $valid_email && !$invalid_email;
});

// Test 14: Validace URL
runTest("Validace URL", function() {
    $db = new Database();
    $auth = new Auth($db);
    $middleware = new SecurityMiddleware($auth);
    
    $valid_url = $middleware->validateURL('https://example.com');
    $invalid_url = $middleware->validateURL('neplatna-url');
    
    return $valid_url && !$invalid_url;
});

// Test 15: Rate limiting
runTest("Rate limiting", function() {
    $db = new Database();
    $auth = new Auth($db);
    $middleware = new SecurityMiddleware($auth);
    
    // První pokus by měl projít
    $first_attempt = $middleware->checkActionRateLimit('test_action', 2, 60);
    
    // Druhý pokus by měl projít
    $second_attempt = $middleware->checkActionRateLimit('test_action', 2, 60);
    
    // Třetí pokus by neměl projít (limit je 2)
    $third_attempt = $middleware->checkActionRateLimit('test_action', 2, 60);
    
    return $first_attempt && $second_attempt && !$third_attempt;
});

// Test 16: Logout funkčnost
runTest("Logout funkčnost", function() {
    $db = new Database();
    $auth = new Auth($db);
    
    // Nejprve se přihlásíme
    $auth->login('admin', 'snurky2024!');
    
    // Ověříme, že jsme přihlášeni
    if (!$auth->isLoggedIn()) {
        return "Nepodařilo se přihlásit";
    }
    
    // Odhlásíme se
    $auth->logout();
    
    // Ověříme, že jsme odhlášeni
    return !$auth->isLoggedIn();
});

// Zobrazení výsledků
echo "<div class='test-section'>";
echo "<h2>📊 Shrnutí testů</h2>";

echo "<div class='stats'>";
echo "<div class='stat-card'>";
echo "<h4>Celkem testů</h4>";
echo "<div style='font-size: 24px; color: #007bff;'>$total_tests</div>";
echo "</div>";

echo "<div class='stat-card'>";
echo "<h4>Úspěšné</h4>";
echo "<div style='font-size: 24px; color: #28a745;'>$passed_tests</div>";
echo "</div>";

echo "<div class='stat-card'>";
echo "<h4>Neúspěšné</h4>";
echo "<div style='font-size: 24px; color: #dc3545;'>" . ($total_tests - $passed_tests) . "</div>";
echo "</div>";

echo "<div class='stat-card'>";
echo "<h4>Úspěšnost</h4>";
echo "<div style='font-size: 24px; color: " . ($passed_tests == $total_tests ? '#28a745' : '#ffc107') . ";'>" . 
     round(($passed_tests / $total_tests) * 100, 1) . "%</div>";
echo "</div>";
echo "</div>";

if ($passed_tests == $total_tests) {
    echo "<div class='success' style='text-align: center; font-size: 18px; margin: 20px 0;'>";
    echo "🎉 Všechny testy prošly úspěšně! Autentifikační systém je plně funkční.";
    echo "</div>";
} else {
    echo "<div class='warning' style='text-align: center; font-size: 18px; margin: 20px 0;'>";
    echo "⚠️ Některé testy selhaly. Zkontrolujte konfiguraci systému.";
    echo "</div>";
}

echo "</div>";

// Detailní výsledky
echo "<div class='test-section'>";
echo "<h3>📋 Detailní výsledky</h3>";
echo "<table style='width: 100%; border-collapse: collapse;'>";
echo "<tr style='background: #f8f9fa;'>";
echo "<th style='padding: 10px; text-align: left; border: 1px solid #ddd;'>Test</th>";
echo "<th style='padding: 10px; text-align: left; border: 1px solid #ddd;'>Výsledek</th>";
echo "</tr>";

foreach ($test_results as $test_name => $result) {
    $color = $result === 'ÚSPĚCH' ? '#28a745' : '#dc3545';
    $icon = $result === 'ÚSPĚCH' ? '✅' : '❌';
    
    echo "<tr>";
    echo "<td style='padding: 10px; border: 1px solid #ddd;'>$test_name</td>";
    echo "<td style='padding: 10px; border: 1px solid #ddd; color: $color; font-weight: bold;'>$icon $result</td>";
    echo "</tr>";
}

echo "</table>";
echo "</div>";

// Systémové informace
echo "<div class='test-section'>";
echo "<h3>ℹ️ Systémové informace</h3>";
echo "<pre>";
echo "PHP verze: " . PHP_VERSION . "\n";
echo "Server čas: " . date('Y-m-d H:i:s') . "\n";
echo "Session status: " . (session_status() === PHP_SESSION_ACTIVE ? 'Aktivní' : 'Neaktivní') . "\n";
echo "Session ID: " . session_id() . "\n";
echo "Memory usage: " . round(memory_get_usage() / 1024 / 1024, 2) . " MB\n";
echo "Peak memory: " . round(memory_get_peak_usage() / 1024 / 1024, 2) . " MB\n";
echo "</pre>";
echo "</div>";

echo "<div style='text-align: center; margin: 30px 0; padding: 20px; background: #e9ecef; border-radius: 5px;'>";
echo "<p><strong>Test dokončen!</strong></p>";
echo "<p>Autentifikační systém je připraven pro použití.</p>";
echo "<p><a href='admin/login.php' style='color: #007bff;'>→ Přejít na přihlašovací stránku</a></p>";
echo "</div>";

echo "</body></html>";
?>
