# 🚀 Úkol 10: Testování, optimalizace a deployment

## 🎯 <PERSON><PERSON><PERSON> testování, performance tuning, cross-browser testing a production deployment

## 📋 Checklist úkolu
- [ ] Unit testy pro PHP funkce
- [ ] Frontend testování (cross-browser)
- [ ] Performance optimalizace
- [ ] Security audit
- [ ] SEO validace
- [ ] Production deployment
- [ ] Monitoring setup

## 🧪 PHP Unit testy

### tests/DatabaseTest.php
```php
<?php
require_once '../core/config.php';
require_once '../core/database.php';
require_once '../core/functions.php';

class DatabaseTest {
    private $db;
    
    public function __construct() {
        $this->db = new Database();
    }
    
    // Test připojení k databázi
    public function testDatabaseConnection() {
        try {
            $result = $this->db->select('content_sections', [], 'COUNT(*) as count', '', '1');
            return isset($result[0]['count']);
        } catch (Exception $e) {
            return false;
        }
    }
    
    // Test CRUD operací
    public function testCRUDOperations() {
        try {
            // Test INSERT
            $testData = [
                'section_key' => 'test_section_' . time(),
                'title' => 'Test Title',
                'content' => 'Test Content',
                'is_active' => 1
            ];
            
            $insertResult = $this->db->insert('content_sections', $testData);
            if (!$insertResult) return false;
            
            $insertId = $this->db->lastInsertId();
            
            // Test SELECT
            $selectResult = $this->db->select('content_sections', ['id' => $insertId]);
            if (empty($selectResult)) return false;
            
            // Test UPDATE
            $updateData = ['title' => 'Updated Title'];
            $updateResult = $this->db->update('content_sections', $updateData, ['id' => $insertId]);
            if (!$updateResult) return false;
            
            // Test DELETE
            $deleteResult = $this->db->delete('content_sections', ['id' => $insertId]);
            if (!$deleteResult) return false;
            
            return true;
        } catch (Exception $e) {
            return false;
        }
    }
    
    // Test e-shop connector
    public function testEshopConnector() {
        try {
            require_once '../core/eshop-connector.php';
            $eshop = new EshopConnector($this->db);
            
            // Test URL building
            $url = $eshop->buildProductUrl('123', null, ['test' => 'param']);
            
            return strpos($url, 'utm_source') !== false && strpos($url, 'test=param') !== false;
        } catch (Exception $e) {
            return false;
        }
    }
    
    // Spuštění všech testů
    public function runAllTests() {
        $tests = [
            'Database Connection' => $this->testDatabaseConnection(),
            'CRUD Operations' => $this->testCRUDOperations(),
            'Eshop Connector' => $this->testEshopConnector()
        ];
        
        return $tests;
    }
}

// Spuštění testů
if (basename($_SERVER['PHP_SELF']) === 'DatabaseTest.php') {
    $tester = new DatabaseTest();
    $results = $tester->runAllTests();
    
    echo "<h2>Test Results</h2>\n";
    foreach ($results as $test => $result) {
        $status = $result ? '✅ PASS' : '❌ FAIL';
        echo "<p>{$test}: {$status}</p>\n";
    }
}
?>
```

## 🌐 Cross-browser testing

### tests/browser-compatibility.html
```html
<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Browser Compatibility Test</title>
    <style>
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        .test-item {
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .pass { background-color: #d4edda; }
        .fail { background-color: #f8d7da; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Browser Compatibility Test</h1>
        <div id="test-results"></div>
    </div>

    <script>
        class BrowserCompatibilityTest {
            constructor() {
                this.results = [];
                this.runTests();
            }
            
            // Test CSS Grid support
            testCSSGrid() {
                const element = document.createElement('div');
                element.style.display = 'grid';
                return element.style.display === 'grid';
            }
            
            // Test Flexbox support
            testFlexbox() {
                const element = document.createElement('div');
                element.style.display = 'flex';
                return element.style.display === 'flex';
            }
            
            // Test ES6 features
            testES6() {
                try {
                    // Test arrow functions
                    const arrow = () => true;
                    // Test template literals
                    const template = `test`;
                    // Test const/let
                    const constTest = true;
                    let letTest = true;
                    
                    return arrow() && template === 'test' && constTest && letTest;
                } catch (e) {
                    return false;
                }
            }
            
            // Test Fetch API
            testFetchAPI() {
                return typeof fetch !== 'undefined';
            }
            
            // Test IntersectionObserver
            testIntersectionObserver() {
                return 'IntersectionObserver' in window;
            }
            
            // Test Service Worker
            testServiceWorker() {
                return 'serviceWorker' in navigator;
            }
            
            // Test Local Storage
            testLocalStorage() {
                try {
                    localStorage.setItem('test', 'test');
                    localStorage.removeItem('test');
                    return true;
                } catch (e) {
                    return false;
                }
            }
            
            // Test WebP support
            testWebP() {
                const canvas = document.createElement('canvas');
                canvas.width = 1;
                canvas.height = 1;
                return canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
            }
            
            // Spuštění všech testů
            runTests() {
                const tests = [
                    { name: 'CSS Grid', test: this.testCSSGrid },
                    { name: 'Flexbox', test: this.testFlexbox },
                    { name: 'ES6 Features', test: this.testES6 },
                    { name: 'Fetch API', test: this.testFetchAPI },
                    { name: 'IntersectionObserver', test: this.testIntersectionObserver },
                    { name: 'Service Worker', test: this.testServiceWorker },
                    { name: 'Local Storage', test: this.testLocalStorage },
                    { name: 'WebP Support', test: this.testWebP }
                ];
                
                tests.forEach(({ name, test }) => {
                    const result = test.call(this);
                    this.results.push({ name, result });
                });
                
                this.displayResults();
            }
            
            // Zobrazení výsledků
            displayResults() {
                const container = document.getElementById('test-results');
                
                this.results.forEach(({ name, result }) => {
                    const div = document.createElement('div');
                    div.className = `test-item ${result ? 'pass' : 'fail'}`;
                    div.innerHTML = `
                        <strong>${name}:</strong> 
                        ${result ? '✅ Podporováno' : '❌ Nepodporováno'}
                    `;
                    container.appendChild(div);
                });
                
                // Browser info
                const browserInfo = document.createElement('div');
                browserInfo.className = 'test-item';
                browserInfo.innerHTML = `
                    <strong>Browser Info:</strong><br>
                    User Agent: ${navigator.userAgent}<br>
                    Viewport: ${window.innerWidth}x${window.innerHeight}
                `;
                container.appendChild(browserInfo);
            }
        }
        
        // Spuštění testů po načtení stránky
        document.addEventListener('DOMContentLoaded', () => {
            new BrowserCompatibilityTest();
        });
    </script>
</body>
</html>
```

## ⚡ Performance optimalizace

### performance-audit.php
```php
<?php
class PerformanceAudit {
    
    // Audit velikosti souborů
    public function auditFileSize($directory = '.') {
        $results = [];
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($directory)
        );
        
        foreach ($iterator as $file) {
            if ($file->isFile()) {
                $size = $file->getSize();
                $extension = $file->getExtension();
                
                // Kontrola velkých souborů
                if ($size > 1024 * 1024) { // > 1MB
                    $results['large_files'][] = [
                        'file' => $file->getPathname(),
                        'size' => $this->formatFileSize($size),
                        'extension' => $extension
                    ];
                }
                
                // Statistiky podle typu
                if (!isset($results['by_type'][$extension])) {
                    $results['by_type'][$extension] = ['count' => 0, 'total_size' => 0];
                }
                $results['by_type'][$extension]['count']++;
                $results['by_type'][$extension]['total_size'] += $size;
            }
        }
        
        return $results;
    }
    
    // Audit databázových dotazů
    public function auditDatabaseQueries() {
        global $db;
        
        $results = [];
        
        // Test rychlosti základních dotazů
        $queries = [
            'content_sections' => "SELECT * FROM content_sections WHERE is_active = 1",
            'products' => "SELECT * FROM products WHERE is_active = 1 ORDER BY sort_order",
            'team_members' => "SELECT * FROM team_members WHERE is_active = 1 ORDER BY sort_order"
        ];
        
        foreach ($queries as $name => $query) {
            $start = microtime(true);
            $db->getPDO()->query($query);
            $end = microtime(true);
            
            $results[$name] = [
                'query' => $query,
                'execution_time' => round(($end - $start) * 1000, 2) . 'ms'
            ];
        }
        
        return $results;
    }
    
    // Audit obrázků
    public function auditImages($directory = 'uploads') {
        $results = [];
        $totalSize = 0;
        $count = 0;
        
        if (is_dir($directory)) {
            $iterator = new RecursiveIteratorIterator(
                new RecursiveDirectoryIterator($directory)
            );
            
            foreach ($iterator as $file) {
                if ($file->isFile()) {
                    $extension = strtolower($file->getExtension());
                    if (in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'webp'])) {
                        $size = $file->getSize();
                        $totalSize += $size;
                        $count++;
                        
                        // Kontrola velkých obrázků
                        if ($size > 500 * 1024) { // > 500KB
                            $imageInfo = getimagesize($file->getPathname());
                            $results['large_images'][] = [
                                'file' => $file->getPathname(),
                                'size' => $this->formatFileSize($size),
                                'dimensions' => $imageInfo ? $imageInfo[0] . 'x' . $imageInfo[1] : 'unknown'
                            ];
                        }
                    }
                }
            }
        }
        
        $results['summary'] = [
            'total_images' => $count,
            'total_size' => $this->formatFileSize($totalSize),
            'average_size' => $count > 0 ? $this->formatFileSize($totalSize / $count) : '0B'
        ];
        
        return $results;
    }
    
    // Formátování velikosti souboru
    private function formatFileSize($bytes) {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }
    
    // Kompletní audit
    public function runCompleteAudit() {
        return [
            'file_sizes' => $this->auditFileSize(),
            'database_queries' => $this->auditDatabaseQueries(),
            'images' => $this->auditImages()
        ];
    }
}

// Spuštění auditu
if (basename($_SERVER['PHP_SELF']) === 'performance-audit.php') {
    require_once '../core/config.php';
    require_once '../core/database.php';
    
    $audit = new PerformanceAudit();
    $results = $audit->runCompleteAudit();
    
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode($results, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
}
?>
```

## 🔒 Security audit

### security-check.php
```php
<?php
class SecurityAudit {
    
    // Kontrola file permissions
    public function checkFilePermissions() {
        $criticalFiles = [
            'core/config.php',
            'core/database.php',
            'admin/login.php'
        ];
        
        $results = [];
        foreach ($criticalFiles as $file) {
            if (file_exists($file)) {
                $perms = fileperms($file);
                $results[$file] = [
                    'permissions' => substr(sprintf('%o', $perms), -4),
                    'readable' => is_readable($file),
                    'writable' => is_writable($file),
                    'secure' => ($perms & 0044) === 0 // Kontrola čtení pro others
                ];
            }
        }
        
        return $results;
    }
    
    // Kontrola SQL injection protection
    public function checkSQLInjectionProtection() {
        // Simulace testů - v reálném prostředí by se testovalo více
        $tests = [
            'prepared_statements' => class_exists('PDO'),
            'input_validation' => function_exists('filter_var'),
            'escape_functions' => function_exists('htmlspecialchars')
        ];
        
        return $tests;
    }
    
    // Kontrola HTTPS
    public function checkHTTPS() {
        return [
            'https_enabled' => isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on',
            'secure_cookies' => ini_get('session.cookie_secure'),
            'httponly_cookies' => ini_get('session.cookie_httponly')
        ];
    }
    
    // Kontrola upload security
    public function checkUploadSecurity() {
        $uploadDir = 'uploads/';
        $htaccessExists = file_exists($uploadDir . '.htaccess');
        
        return [
            'upload_dir_exists' => is_dir($uploadDir),
            'htaccess_protection' => $htaccessExists,
            'directory_listing' => !$htaccessExists || !strpos(file_get_contents($uploadDir . '.htaccess'), 'Options -Indexes')
        ];
    }
    
    // Kompletní security audit
    public function runSecurityAudit() {
        return [
            'file_permissions' => $this->checkFilePermissions(),
            'sql_injection_protection' => $this->checkSQLInjectionProtection(),
            'https_security' => $this->checkHTTPS(),
            'upload_security' => $this->checkUploadSecurity()
        ];
    }
}
?>
```

## 📋 Deployment checklist

### deployment-checklist.md
```markdown
# 🚀 Production Deployment Checklist

## Pre-deployment
- [ ] Všechny testy prošly úspěšně
- [ ] Performance audit dokončen
- [ ] Security audit dokončen
- [ ] Cross-browser testing dokončen
- [ ] SEO validace dokončena
- [ ] Backup současné verze vytvořen

## Database
- [ ] Produkční databáze vytvořena
- [ ] SQL schéma importováno
- [ ] Základní data vložena
- [ ] Database indexy vytvořeny
- [ ] Připojovací údaje aktualizovány

## Files
- [ ] Všechny soubory nahrány na server
- [ ] File permissions nastaveny správně
- [ ] .htaccess soubory nakonfigurovány
- [ ] Upload složky vytvořeny s správnými právy
- [ ] Error log složka vytvořena

## Configuration
- [ ] config.php aktualizován pro produkci
- [ ] DEBUG mode vypnut
- [ ] Error reporting nastaven pro produkci
- [ ] Session security nakonfigurováno
- [ ] E-shop nastavení zkonfigurováno

## Security
- [ ] Admin přístupové údaje změněny
- [ ] HTTPS nakonfigurováno
- [ ] Security headers nastaveny
- [ ] File upload restrictions aktivní
- [ ] SQL injection protection ověřeno

## Performance
- [ ] CSS/JS minifikace aktivní
- [ ] Image optimization nakonfigurována
- [ ] Browser caching nastaven
- [ ] GZIP komprese aktivní
- [ ] CDN nakonfigurováno (pokud používáno)

## SEO & Analytics
- [ ] Google Analytics nakonfigurováno
- [ ] Google Search Console nastaven
- [ ] Sitemap.xml vytvořena
- [ ] Robots.txt nakonfigurován
- [ ] Meta tagy ověřeny

## Testing
- [ ] Funkčnost webu ověřena
- [ ] Admin panel testován
- [ ] E-shop odkazy testovány
- [ ] Kontaktní formuláře testovány
- [ ] Mobile responsiveness ověřena

## Monitoring
- [ ] Error monitoring nastaven
- [ ] Performance monitoring aktivní
- [ ] Uptime monitoring nakonfigurován
- [ ] Backup strategie implementována
```

## 🚀 Implementační kroky
1. Vytvořit a spustit PHP unit testy
2. Provést cross-browser testing
3. Spustit performance audit a optimalizovat
4. Provést security audit a opravit problémy
5. Validovat SEO implementaci
6. Připravit production environment
7. Provést deployment podle checklistu
8. Nastavit monitoring a backup

## ✅ Kritéria dokončení
- Všechny unit testy procházejí
- Cross-browser kompatibilita ověřena
- Performance metriky splňují cíle
- Security audit neodhalil kritické problémy
- SEO validace prošla úspěšně
- Production deployment dokončen
- Monitoring a backup funkční
- Dokumentace kompletní
