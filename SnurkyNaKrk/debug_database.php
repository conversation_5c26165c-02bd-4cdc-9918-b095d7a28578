<?php
/**
 * Debug databázových problémů
 */

// Zapnutí zobrazování chyb
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔍 Debug databázových problémů</h1>";

echo "<h2>Krok 1: Test základního připojení</h2>";

// Přímý test databáze bez našich tříd
$db_host = 'uvds125.active24.cz';
$db_name = 'snurkynakr';
$db_user = 'snurkynakr';
$db_pass = 'GKkcdg9m';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ Přímé PDO připojení ú<PERSON>ěšné<br>";
    
    // Test základního dotazu
    $stmt = $pdo->query("SELECT 1 as test");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "✅ Základní dotaz funguje: " . $result['test'] . "<br>";
    
} catch (PDOException $e) {
    echo "❌ Chyba přímého PDO připojení: " . $e->getMessage() . "<br>";
    exit;
}

echo "<h2>Krok 2: Test načtení config.php</h2>";

try {
    if (!defined('SNURKY_INIT')) {
        define('SNURKY_INIT', true);
    }
    
    require_once 'core/config.php';
    echo "✅ config.php načten<br>";
    
    echo "DB_HOST: " . DB_HOST . "<br>";
    echo "DB_NAME: " . DB_NAME . "<br>";
    echo "DB_USER: " . DB_USER . "<br>";
    echo "DB_CHARSET: " . DB_CHARSET . "<br>";
    echo "DB_COLLATE: " . DB_COLLATE . "<br>";
    
} catch (Exception $e) {
    echo "❌ Chyba při načítání config.php: " . $e->getMessage() . "<br>";
    exit;
}

echo "<h2>Krok 3: Test načtení database.php</h2>";

try {
    require_once 'core/database.php';
    echo "✅ database.php načten<br>";
} catch (Exception $e) {
    echo "❌ Chyba při načítání database.php: " . $e->getMessage() . "<br>";
    exit;
}

echo "<h2>Krok 4: Test vytvoření Database instance</h2>";

try {
    echo "Vytvářím Database::getInstance()...<br>";
    $db = Database::getInstance();
    echo "✅ Database instance vytvořena<br>";
} catch (PDOException $e) {
    echo "❌ PDO chyba při vytváření Database: " . $e->getMessage() . "<br>";
    echo "SQL State: " . $e->getCode() . "<br>";
    exit;
} catch (Exception $e) {
    echo "❌ Obecná chyba při vytváření Database: " . $e->getMessage() . "<br>";
    echo "Typ chyby: " . get_class($e) . "<br>";
    exit;
}

echo "<h2>Krok 5: Test základních databázových operací</h2>";

try {
    echo "Test připojení...<br>";
    $connection = $db->getConnection();
    echo "✅ Připojení získáno<br>";
    
    echo "Test jednoduchého dotazu...<br>";
    $result = $connection->query("SELECT 1 as test");
    echo "✅ Jednoduchý dotaz funguje<br>";
    
} catch (Exception $e) {
    echo "❌ Chyba při testování operací: " . $e->getMessage() . "<br>";
    exit;
}

echo "<h2>Krok 6: Test select metody</h2>";

try {
    echo "Test db->select()...<br>";
    
    // Nejprve zkusíme zjistit, jaké tabulky existují
    $tables = $db->getConnection()->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    echo "Dostupné tabulky: " . implode(', ', $tables) . "<br>";
    
    if (in_array('content_sections', $tables)) {
        echo "Testuji select na content_sections...<br>";
        $result = $db->select("SELECT COUNT(*) as count FROM content_sections");
        echo "✅ Select funguje, počet záznamů: " . $result[0]['count'] . "<br>";
    } else {
        echo "⚠️ Tabulka content_sections neexistuje, zkusím vytvořit...<br>";
        
        $create_sql = "CREATE TABLE IF NOT EXISTS content_sections (
            id INT AUTO_INCREMENT PRIMARY KEY,
            section_key VARCHAR(50) NOT NULL UNIQUE,
            title VARCHAR(255) NOT NULL,
            content TEXT,
            is_active BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )";
        
        $db->getConnection()->exec($create_sql);
        echo "✅ Tabulka content_sections vytvořena<br>";
        
        $result = $db->select("SELECT COUNT(*) as count FROM content_sections");
        echo "✅ Select funguje, počet záznamů: " . $result[0]['count'] . "<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Chyba při testování select: " . $e->getMessage() . "<br>";
    echo "Typ chyby: " . get_class($e) . "<br>";
}

echo "<h2>Krok 7: Test Auth třídy</h2>";

try {
    require_once 'core/auth.php';
    echo "✅ auth.php načten<br>";
    
    echo "Vytvářím Auth objekt...<br>";
    $auth = new Auth($db);
    echo "✅ Auth objekt vytvořen<br>";
    
} catch (Exception $e) {
    echo "❌ Chyba při vytváření Auth: " . $e->getMessage() . "<br>";
    echo "Typ chyby: " . get_class($e) . "<br>";
    echo "Soubor: " . $e->getFile() . "<br>";
    echo "Řádek: " . $e->getLine() . "<br>";
}

echo "<h2>✅ Debug dokončen</h2>";
echo "<p>Pokud se dostanete až sem, databáze funguje správně.</p>";
?>
