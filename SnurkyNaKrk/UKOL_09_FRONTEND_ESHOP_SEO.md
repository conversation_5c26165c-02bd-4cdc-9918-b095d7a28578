# 🛒 Úkol 9: Frontend - E-shop integrace a SEO

## 🎯 Cíl úkolu
Implementovat e-shop integraci na frontendu, external link handling a kompletní SEO optimalizaci

## 📋 Checklist úkolu
- [ ] Produktová galerie s e-shop odkazy
- [ ] External link handling a tracking
- [ ] SEO optimalizace (meta tags, structured data)
- [ ] Performance optimalizace
- [ ] Social media integration
- [ ] Analytics tracking

## 🛍️ E-shop integrace JavaScript

### assets/js/eshop-integration.js
```javascript
class EshopIntegration {
    constructor() {
        this.init();
    }
    
    init() {
        this.setupProductLinks();
        this.setupClickTracking();
        this.setupLinkPreloading();
        this.setupExternalLinkHandling();
    }
    
    // Nastavení produktových odkazů
    setupProductLinks() {
        const productCTAs = document.querySelectorAll('.product-cta');
        
        productCTAs.forEach(link => {
            // Přidání loading stavu při kliku
            link.addEventListener('click', (e) => {
                this.handleProductClick(e, link);
            });
            
            // Hover efekt s preload
            link.addEventListener('mouseenter', () => {
                this.preloadProductPage(link.href);
            });
        });
    }
    
    // Zpracování kliku na produkt
    handleProductClick(event, link) {
        // Přidání loading stavu
        link.classList.add('loading');
        link.innerHTML = `
            <span class="loading-spinner-sm"></span>
            Přesměrovávání...
        `;
        
        // Tracking kliku
        this.trackProductClick(link);
        
        // Krátké zpoždění pro UX
        setTimeout(() => {
            // Link se otevře automaticky díky target="_blank"
        }, 300);
    }
    
    // Tracking kliků na produkty
    trackProductClick(link) {
        const productId = this.extractProductId(link.href);
        
        // Google Analytics tracking
        if (typeof gtag !== 'undefined') {
            gtag('event', 'product_click', {
                'product_id': productId,
                'link_url': link.href,
                'source': 'product_gallery'
            });
        }
        
        // Custom tracking
        fetch('/api/track-click.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                product_id: productId,
                timestamp: Date.now(),
                user_agent: navigator.userAgent,
                referrer: document.referrer
            })
        }).catch(error => {
            console.log('Tracking error:', error);
        });
    }
    
    // Extrakce product ID z URL
    extractProductId(url) {
        const match = url.match(/product=(\d+)/);
        return match ? match[1] : null;
    }
    
    // Preloading produktových stránek
    preloadProductPage(url) {
        // DNS prefetch pro e-shop doménu
        const eshopDomain = this.extractDomain(url);
        if (eshopDomain && !document.querySelector(`link[href*="${eshopDomain}"]`)) {
            const prefetch = document.createElement('link');
            prefetch.rel = 'dns-prefetch';
            prefetch.href = `//${eshopDomain}`;
            document.head.appendChild(prefetch);
        }
        
        // Preconnect pro rychlejší načítání
        const preconnect = document.createElement('link');
        preconnect.rel = 'preconnect';
        preconnect.href = url;
        document.head.appendChild(preconnect);
    }
    
    // Extrakce domény z URL
    extractDomain(url) {
        try {
            return new URL(url).hostname;
        } catch {
            return null;
        }
    }
    
    // Nastavení external link handling
    setupExternalLinkHandling() {
        const externalLinks = document.querySelectorAll('a[target="_blank"]');
        
        externalLinks.forEach(link => {
            // Bezpečnostní atributy
            link.rel = 'noopener noreferrer';
            
            // Warning pro externí odkazy (volitelné)
            link.addEventListener('click', (e) => {
                if (link.classList.contains('product-cta')) {
                    // Pro produktové odkazy zobrazit potvrzení
                    const confirmed = confirm('Budete přesměrováni do e-shopu. Pokračovat?');
                    if (!confirmed) {
                        e.preventDefault();
                    }
                }
            });
        });
    }
    
    // Click tracking setup
    setupClickTracking() {
        // Tracking všech odchozích odkazů
        document.addEventListener('click', (e) => {
            const link = e.target.closest('a');
            if (link && link.hostname !== window.location.hostname) {
                this.trackExternalClick(link);
            }
        });
    }
    
    // Tracking externích kliků
    trackExternalClick(link) {
        if (typeof gtag !== 'undefined') {
            gtag('event', 'click', {
                'event_category': 'outbound',
                'event_label': link.href,
                'transport_type': 'beacon'
            });
        }
    }
}

// Inicializace po načtení DOM
document.addEventListener('DOMContentLoaded', () => {
    new EshopIntegration();
});
```

## 🔍 SEO optimalizace

### Rozšíření index.php pro SEO
```php
<?php
// Přidání do <head> sekce index.php

// Dynamické meta tagy pro produkty
if (isset($_GET['product'])) {
    $productId = (int)$_GET['product'];
    $product = $db->select('products', ['id' => $productId, 'is_active' => 1])[0] ?? null;
    
    if ($product) {
        $pageTitle = $product['name'] . ' - Snurkynakrk.cz';
        $pageDescription = $product['description'] ?: 'Kvalitní šňůrka na krk s přímým propojením na e-shop.';
    }
}

// Structured Data pro produkty
$structuredData = [
    '@context' => 'https://schema.org',
    '@type' => 'ItemList',
    'itemListElement' => []
];

foreach ($products as $index => $product) {
    $structuredData['itemListElement'][] = [
        '@type' => 'ListItem',
        'position' => $index + 1,
        'item' => [
            '@type' => 'Product',
            'name' => $product['name'],
            'description' => $product['description'],
            'url' => 'https://snurkynakrk.cz#product-' . $product['id'],
            'offers' => [
                '@type' => 'Offer',
                'availability' => 'https://schema.org/InStock',
                'price' => $product['price_info'],
                'url' => 'redirect.php?product=' . $product['id']
            ]
        ]
    ];
}

// Structured Data pro organizaci
$organizationData = [
    '@context' => 'https://schema.org',
    '@type' => 'Organization',
    'name' => 'Snurkynakrk.cz',
    'url' => 'https://snurkynakrk.cz',
    'description' => $pageDescription,
    'contactPoint' => []
];

// Přidání kontaktů týmu do structured data
foreach ($teamMembers as $member) {
    if ($member['phone'] || $member['email']) {
        $organizationData['contactPoint'][] = [
            '@type' => 'ContactPoint',
            'contactType' => 'customer service',
            'name' => $member['name'],
            'telephone' => $member['phone'],
            'email' => $member['email']
        ];
    }
}
?>

<!-- Rozšířené meta tagy -->
<meta name="keywords" content="šňůrky na krk, e-shop, kvalitní produkty">
<meta name="author" content="Snurkynakrk.cz">
<meta name="robots" content="index, follow">
<meta name="googlebot" content="index, follow">

<!-- Open Graph rozšíření -->
<meta property="og:site_name" content="Snurkynakrk.cz">
<meta property="og:locale" content="cs_CZ">
<meta property="og:image" content="https://snurkynakrk.cz/assets/images/og-image.jpg">
<meta property="og:image:width" content="1200">
<meta property="og:image:height" content="630">

<!-- Twitter Card -->
<meta name="twitter:card" content="summary_large_image">
<meta name="twitter:title" content="<?= Utils::escape($pageTitle) ?>">
<meta name="twitter:description" content="<?= Utils::escape($pageDescription) ?>">
<meta name="twitter:image" content="https://snurkynakrk.cz/assets/images/og-image.jpg">

<!-- Canonical URL -->
<link rel="canonical" href="https://snurkynakrk.cz">

<!-- Preconnect pro externí zdroje -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

<!-- Structured Data -->
<script type="application/ld+json">
<?= json_encode($structuredData, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES) ?>
</script>

<script type="application/ld+json">
<?= json_encode($organizationData, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES) ?>
</script>
```

## 📊 Analytics a tracking

### Google Analytics 4 integrace
```html
<!-- Google Analytics 4 -->
<script async src="https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID"></script>
<script>
window.dataLayer = window.dataLayer || [];
function gtag(){dataLayer.push(arguments);}
gtag('js', new Date());

gtag('config', 'GA_MEASUREMENT_ID', {
    // Enhanced ecommerce tracking
    send_page_view: true,
    // Custom parameters
    custom_map: {
        'custom_parameter_1': 'product_clicks'
    }
});

// Enhanced ecommerce events
gtag('event', 'page_view', {
    page_title: document.title,
    page_location: window.location.href
});
</script>
```

## 🚀 Performance optimalizace

### assets/js/performance.js
```javascript
class PerformanceOptimizer {
    constructor() {
        this.init();
    }
    
    init() {
        this.setupLazyLoading();
        this.setupImageOptimization();
        this.setupCriticalResourceHints();
        this.setupServiceWorker();
    }
    
    // Lazy loading pro obrázky
    setupLazyLoading() {
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        observer.unobserve(img);
                    }
                });
            });
            
            document.querySelectorAll('img[data-src]').forEach(img => {
                imageObserver.observe(img);
            });
        }
    }
    
    // Optimalizace obrázků
    setupImageOptimization() {
        // WebP support detection
        const supportsWebP = this.checkWebPSupport();
        
        if (supportsWebP) {
            document.querySelectorAll('img[data-webp]').forEach(img => {
                img.src = img.dataset.webp;
            });
        }
    }
    
    // Kontrola WebP podpory
    checkWebPSupport() {
        const canvas = document.createElement('canvas');
        canvas.width = 1;
        canvas.height = 1;
        return canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
    }
    
    // Critical resource hints
    setupCriticalResourceHints() {
        // Prefetch pro důležité zdroje
        const criticalResources = [
            '/assets/css/style.css',
            '/assets/js/main.js'
        ];
        
        criticalResources.forEach(resource => {
            const link = document.createElement('link');
            link.rel = 'prefetch';
            link.href = resource;
            document.head.appendChild(link);
        });
    }
    
    // Service Worker pro caching
    setupServiceWorker() {
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.register('/sw.js')
                .then(registration => {
                    console.log('SW registered:', registration);
                })
                .catch(error => {
                    console.log('SW registration failed:', error);
                });
        }
    }
}

// Inicializace
document.addEventListener('DOMContentLoaded', () => {
    new PerformanceOptimizer();
});
```

## 🔧 Service Worker pro caching

### sw.js
```javascript
const CACHE_NAME = 'snurkynakrk-v1';
const urlsToCache = [
    '/',
    '/assets/css/style.css',
    '/assets/js/main.js',
    '/assets/js/eshop-integration.js',
    '/assets/images/logo.png'
];

self.addEventListener('install', event => {
    event.waitUntil(
        caches.open(CACHE_NAME)
            .then(cache => cache.addAll(urlsToCache))
    );
});

self.addEventListener('fetch', event => {
    event.respondWith(
        caches.match(event.request)
            .then(response => {
                // Vrátit z cache nebo fetch ze sítě
                return response || fetch(event.request);
            })
    );
});
```

## 📱 Social Media integrace

### Přidání social sharing
```html
<!-- Social sharing buttons -->
<div class="social-sharing">
    <a href="https://www.facebook.com/sharer/sharer.php?u=https://snurkynakrk.cz" 
       target="_blank" class="social-btn facebook">
        <i class="fab fa-facebook-f"></i> Sdílet
    </a>
    
    <a href="https://twitter.com/intent/tweet?url=https://snurkynakrk.cz&text=Kvalitní šňůrky na krk" 
       target="_blank" class="social-btn twitter">
        <i class="fab fa-twitter"></i> Tweet
    </a>
</div>
```

## 🚀 Implementační kroky
1. Implementovat eshop-integration.js s click trackingem
2. Přidat rozšířené SEO meta tagy a structured data
3. Integrovat Google Analytics 4
4. Implementovat performance optimalizace
5. Vytvořit Service Worker pro caching
6. Přidat social media integration
7. Otestovat všechny tracking funkce

## ✅ Kritéria dokončení
- E-shop integrace funguje s click trackingem
- SEO meta tagy a structured data jsou implementované
- Google Analytics tracking funguje
- Performance optimalizace jsou aktivní
- Service Worker cachuje statické zdroje
- Social sharing funguje správně
- Všechny externí odkazy mají správné atributy
- Link preloading zlepšuje UX
