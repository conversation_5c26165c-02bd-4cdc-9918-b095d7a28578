#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
import csv
import json
from datetime import datetime
from pathlib import Path

def is_spam_order(data):
    """Detekuje spamové/testovac<PERSON> objedn<PERSON>"""
    post_data = data.get('_POST', {})
    
    # Kontrola testovacích hodnot
    spam_indicators = [
        'test', 'asdf', 'qwerty', 'ewqew', 'eqweq', 'qeqweqw', 
        'ewqewq', 'eqwew', 'ewqeqweqw', 'dsa.ce'
    ]
    
    # Kontrola j<PERSON>na, příjmení, e-mailu
    name = post_data.get('name', '').lower()
    surname = post_data.get('surname', '').lower()
    email = post_data.get('dmail', '').lower()
    
    for indicator in spam_indicators:
        if indicator in name or indicator in surname or indicator in email:
            return True
    
    # Kontrola prázdných nebo nevalidních e-mailů
    if not email or '@' not in email or email.endswith('.ce'):
        return True
        
    return False

def extract_datetime_from_filename(filename):
    """Extrahuje datum a čas z názvu souboru"""
    # Formát: 2025-06-10_01_41_521749512512.25.txt
    match = re.match(r'(\d{4})-(\d{2})-(\d{2})_(\d{2})_(\d{2})_(\d{2})', filename)
    if match:
        year, month, day, hour, minute, second = match.groups()
        return f"{day}.{month}.{year} {hour}:{minute}:{second}"
    return filename

def extract_products_from_html(html_content):
    """Extrahuje produkty z HTML obsahu objednávky"""
    products = []
    
    # Hledáme řádky s produkty v HTML tabulce
    product_pattern = r'<td[^>]*>([^<]+)</td>\s*<td[^>]*>([^<]+)</td>\s*<td[^>]*>([^<]+)</td>\s*<td[^>]*>[^<]*</td>\s*<td[^>]*align="center">(\d+)</td>\s*<td[^>]*>[^<]*</td>\s*<td[^>]*>([^<]+)</td>'
    
    matches = re.findall(product_pattern, html_content, re.IGNORECASE | re.DOTALL)
    
    for match in matches:
        code = match[0].strip()
        name = match[1].strip()
        price_per_unit = match[2].strip()
        quantity = match[3].strip()
        total_price = match[4].strip()
        
        # Přeskočit řádky s potiskem nebo přípravou výroby
        if 'Potisk:' in name or 'Příprava výroby' in name:
            continue
            
        if code and name and quantity:
            products.append({
                'code': code,
                'name': name,
                'price_per_unit': price_per_unit,
                'quantity': quantity,
                'total_price': total_price
            })
    
    return products

def process_order_file(filepath):
    """Zpracuje jeden soubor s objednávkou"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Extrahujeme PHP array - hledáme $DATA = array(...)
        match = re.search(r'\$DATA = array \((.*)\);', content, re.DOTALL)
        if not match:
            return None
            
        # Převedeme PHP array na Python dict (zjednodušená konverze)
        # Pro účely tohoto skriptu budeme parsovat klíčové hodnoty ručně
        
        # Extrahujeme POST data
        post_match = re.search(r"'_POST' =>\s*array \((.*?)\),\s*'_SESSION'", content, re.DOTALL)
        if not post_match:
            return None
            
        post_content = post_match.group(1)
        
        # Extrahujeme jednotlivé hodnoty
        def extract_value(key, content):
            pattern = f"'{key}' => '([^']*)',"
            match = re.search(pattern, content)
            return match.group(1) if match else ''
        
        post_data = {
            'name': extract_value('name', post_content),
            'surname': extract_value('surname', post_content),
            'dmail': extract_value('dmail', post_content),
            'dtel': extract_value('dtel', post_content),
            'company': extract_value('company', post_content),
            'street': extract_value('street', post_content),
            'city': extract_value('city', post_content),
            'psc': extract_value('psc', post_content),
            'price_all_vat': extract_value('price_all_vat', post_content),
        }
        
        data = {'_POST': post_data}
        
        # Kontrola spamu
        if is_spam_order(data):
            return None
        
        # Extrahujeme HTML obsah pro produkty
        html_match = re.search(r"'https://www\.czechimage\.cz_cartorderingMail' => '(.*?)',", content, re.DOTALL)
        html_content = html_match.group(1) if html_match else ''
        
        # Extrahujeme produkty
        products = extract_products_from_html(html_content)
        
        # Vytvoříme záznam objednávky
        filename = os.path.basename(filepath)
        order_datetime = extract_datetime_from_filename(filename)
        
        order = {
            'datum_vytvoreni': order_datetime,
            'jmeno': f"{post_data['name']} {post_data['surname']}".strip(),
            'email': post_data['dmail'],
            'telefon': post_data['dtel'],
            'firma': post_data['company'],
            'adresa': f"{post_data['street']}, {post_data['psc']} {post_data['city']}".strip(', '),
            'produkty': '; '.join([f"{p['name']} ({p['quantity']}ks)" for p in products]),
            'celkova_castka': post_data['price_all_vat'],
            'pocet_produktu': len(products)
        }
        
        return order
        
    except Exception as e:
        print(f"Chyba při zpracování souboru {filepath}: {e}")
        return None

def main():
    """Hlavní funkce pro zpracování všech objednávek"""
    orders_dir = Path('czimg-objednavky-2025-06-12')
    output_file = 'objednavky_prehled.csv'
    
    if not orders_dir.exists():
        print(f"Složka {orders_dir} neexistuje!")
        return
    
    orders = []
    processed_count = 0
    spam_count = 0
    
    # Zpracujeme všechny .txt soubory
    for txt_file in orders_dir.glob('*.txt'):
        processed_count += 1
        print(f"Zpracovávám {txt_file.name}...")
        
        order = process_order_file(txt_file)
        if order:
            orders.append(order)
        else:
            spam_count += 1
    
    # Seřadíme objednávky podle data
    orders.sort(key=lambda x: x['datum_vytvoreni'])
    
    # Zapíšeme do CSV
    if orders:
        with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['datum_vytvoreni', 'jmeno', 'email', 'telefon', 'firma', 
                         'adresa', 'produkty', 'celkova_castka', 'pocet_produktu']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames, delimiter=';')
            
            writer.writeheader()
            for order in orders:
                writer.writerow(order)
        
        print(f"\n✅ Zpracování dokončeno!")
        print(f"📊 Celkem zpracováno souborů: {processed_count}")
        print(f"🗑️  Spamových/testovacích objednávek: {spam_count}")
        print(f"✅ Validních objednávek: {len(orders)}")
        print(f"📄 Výstupní soubor: {output_file}")
        
        # Zobrazíme první pár objednávek jako ukázku
        print(f"\n📋 Ukázka prvních objednávek:")
        for i, order in enumerate(orders[:3]):
            print(f"{i+1}. {order['datum_vytvoreni']} - {order['jmeno']} - {order['celkova_castka']} CZK")
    else:
        print("❌ Nebyly nalezeny žádné validní objednávky!")

if __name__ == "__main__":
    main()
