#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
from pathlib import Path

def create_excel_from_csv():
    """Převede CSV soubor na Excel s lepším formátováním"""
    
    # Načteme CSV soubor
    csv_file = 'objednavky_prehled.csv'
    excel_file = 'objednavky_prehled.xlsx'
    
    if not Path(csv_file).exists():
        print(f"❌ Soubor {csv_file} neexistuje!")
        return
    
    # Načteme data s českým oddělovačem
    df = pd.read_csv(csv_file, delimiter=';', encoding='utf-8')
    
    # Převedeme celkovou částku na číselný formát
    df['celkova_castka_cislo'] = df['celkova_castka'].astype(str).str.replace(' CZK', '').str.replace(' ', '').astype(float)
    
    # Se<PERSON>adíme podle data
    df['datum_sort'] = pd.to_datetime(df['datum_vytvoreni'], format='%d.%m.%Y %H:%M:%S')
    df = df.sort_values('datum_sort')
    
    # Vytvoříme Excel soubor s formátováním
    with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
        # Zapíšeme hlavní list
        df_export = df[['datum_vytvoreni', 'jmeno', 'email', 'telefon', 'firma', 
                       'adresa', 'produkty', 'celkova_castka', 'pocet_produktu']].copy()
        
        df_export.to_excel(writer, sheet_name='Objednávky', index=False)
        
        # Získáme worksheet pro formátování
        worksheet = writer.sheets['Objednávky']
        
        # Nastavíme šířky sloupců
        column_widths = {
            'A': 20,  # datum_vytvoreni
            'B': 25,  # jmeno
            'C': 30,  # email
            'D': 15,  # telefon
            'E': 40,  # firma
            'F': 40,  # adresa
            'G': 60,  # produkty
            'H': 15,  # celkova_castka
            'I': 15   # pocet_produktu
        }
        
        for col, width in column_widths.items():
            worksheet.column_dimensions[col].width = width
        
        # Vytvoříme souhrnný list
        summary_data = {
            'Celkem objednávek': [len(df)],
            'Celková částka (CZK)': [df['celkova_castka_cislo'].sum()],
            'Průměrná částka (CZK)': [df['celkova_castka_cislo'].mean()],
            'Nejmenší objednávka (CZK)': [df['celkova_castka_cislo'].min()],
            'Největší objednávka (CZK)': [df['celkova_castka_cislo'].max()],
            'Celkem produktů': [df['pocet_produktu'].sum()],
            'Průměr produktů na objednávku': [df['pocet_produktu'].mean()]
        }
        
        summary_df = pd.DataFrame(summary_data).T
        summary_df.columns = ['Hodnota']
        summary_df.to_excel(writer, sheet_name='Souhrn')
        
        # Formátování souhrnného listu
        summary_ws = writer.sheets['Souhrn']
        summary_ws.column_dimensions['A'].width = 30
        summary_ws.column_dimensions['B'].width = 20
    
    print(f"✅ Excel soubor vytvořen: {excel_file}")
    print(f"📊 Obsahuje {len(df)} objednávek")
    print(f"💰 Celková částka: {df['celkova_castka_cislo'].sum():,.0f} CZK")
    print(f"📈 Průměrná objednávka: {df['celkova_castka_cislo'].mean():,.0f} CZK")

if __name__ == "__main__":
    create_excel_from_csv()
