<?php
/**
 * Test skript pro ověření nastavení modulu Mezistranka hodnocení
 */

// Základní kontrola prostředí
if (!defined('_PS_VERSION_')) {
    require_once(dirname(__FILE__) . '/config/config.inc.php');
}

echo "<h1>Test nastavení modulu Mezistranka hodnocení</h1>";

$module_name = 'mezistranka_hodnoceni';
$controller_class = 'AdminMezistrankaHodnoceni';

// 1. Zkontroluj, zda modul existuje
echo "<h2>1. Kontrola existence modulu</h2>";
$module = Module::getInstanceByName($module_name);
if (!$module) {
    echo "<p style='color: red;'>❌ Modul '$module_name' neexistuje nebo není nainstalován</p>";
    echo "<p><strong>Řešení:</strong> Nainstalujte modul přes administraci PrestaShop</p>";
} else {
    echo "<p style='color: green;'>✅ Modul '$module_name' je nainstalován</p>";
    echo "<p>Verze: {$module->version}</p>";
    echo "<p>Autor: {$module->author}</p>";
}

// 2. Zkontroluj soubory
echo "<h2>2. Kontrola souborů</h2>";

$required_files = [
    'mezistranka_hodnoceni/classes/CustomModulesTabManager.php',
    'mezistranka_hodnoceni/controllers/admin/AdminMezistrankaHodnoceniController.php',
    'mezistranka_hodnoceni/setup-custom-modules-section.php'
];

foreach ($required_files as $file) {
    if (file_exists($file)) {
        echo "<p style='color: green;'>✅ $file</p>";
    } else {
        echo "<p style='color: red;'>❌ $file - CHYBÍ</p>";
    }
}

// 3. Zkontroluj admin tab
echo "<h2>3. Kontrola admin tabu</h2>";
$tab_id = Tab::getIdFromClassName($controller_class);
if ($tab_id) {
    echo "<p style='color: green;'>✅ Admin tab '$controller_class' existuje (ID: $tab_id)</p>";
    
    // Získej informace o tabu
    $sql = 'SELECT t.*, tl.name 
            FROM `' . _DB_PREFIX_ . 'tab` t
            LEFT JOIN `' . _DB_PREFIX_ . 'tab_lang` tl ON (t.id_tab = tl.id_tab AND tl.id_lang = ' . (int)Context::getContext()->language->id . ')
            WHERE t.id_tab = ' . (int)$tab_id;
    
    $tab_info = Db::getInstance()->getRow($sql);
    if ($tab_info) {
        echo "<p>Název: {$tab_info['name']}</p>";
        echo "<p>Parent ID: {$tab_info['id_parent']}</p>";
        echo "<p>Aktivní: " . ($tab_info['active'] ? 'Ano' : 'Ne') . "</p>";
        
        // Zkontroluj, zda je v sekci Vlastní moduly
        $custom_section_id = Tab::getIdFromClassName('AdminCustomModules');
        if ($custom_section_id && $tab_info['id_parent'] == $custom_section_id) {
            echo "<p style='color: green;'>✅ Tab je v sekci 'Vlastní moduly'</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ Tab NENÍ v sekci 'Vlastní moduly'</p>";
            echo "<p><strong>Řešení:</strong> Spusťte setup-custom-modules-section.php</p>";
        }
    }
} else {
    echo "<p style='color: red;'>❌ Admin tab '$controller_class' neexistuje</p>";
    echo "<p><strong>Řešení:</strong> Spusťte setup-custom-modules-section.php</p>";
}

// 4. Zkontroluj sekci Vlastní moduly
echo "<h2>4. Kontrola sekce 'Vlastní moduly'</h2>";
$custom_section_id = Tab::getIdFromClassName('AdminCustomModules');
if ($custom_section_id) {
    echo "<p style='color: green;'>✅ Sekce 'Vlastní moduly' existuje (ID: $custom_section_id)</p>";
    
    // Zobraz všechny moduly v sekci
    $sql = 'SELECT t.class_name, tl.name 
            FROM `' . _DB_PREFIX_ . 'tab` t
            LEFT JOIN `' . _DB_PREFIX_ . 'tab_lang` tl ON (t.id_tab = tl.id_tab AND tl.id_lang = ' . (int)Context::getContext()->language->id . ')
            WHERE t.id_parent = ' . (int)$custom_section_id . ' AND t.active = 1 
            ORDER BY t.position';
    
    $modules_in_section = Db::getInstance()->executeS($sql);
    if ($modules_in_section) {
        echo "<p><strong>Moduly v sekci:</strong></p>";
        echo "<ul>";
        foreach ($modules_in_section as $mod) {
            echo "<li>{$mod['name']} ({$mod['class_name']})</li>";
        }
        echo "</ul>";
    } else {
        echo "<p>Sekce je prázdná</p>";
    }
} else {
    echo "<p style='color: red;'>❌ Sekce 'Vlastní moduly' neexistuje</p>";
    echo "<p><strong>Řešení:</strong> Spusťte setup-custom-modules-section.php</p>";
}

// 5. Zkontroluj databázovou tabulku
echo "<h2>5. Kontrola databáze</h2>";
$table_exists = Db::getInstance()->executeS("SHOW TABLES LIKE '" . _DB_PREFIX_ . "mezistranka_hodnoceni_stats'");
if ($table_exists) {
    echo "<p style='color: green;'>✅ Tabulka 'mezistranka_hodnoceni_stats' existuje</p>";
    
    // Počet záznamů
    $count = Db::getInstance()->getValue("SELECT COUNT(*) FROM `" . _DB_PREFIX_ . "mezistranka_hodnoceni_stats`");
    echo "<p>Počet hodnocení: $count</p>";
} else {
    echo "<p style='color: red;'>❌ Tabulka 'mezistranka_hodnoceni_stats' neexistuje</p>";
    echo "<p><strong>Řešení:</strong> Přeinstalujte modul</p>";
}

// 6. Zkontroluj konfiguraci
echo "<h2>6. Kontrola konfigurace</h2>";
$config_keys = [
    'MEZISTRANKAHODNOCENI_TITLE',
    'MEZISTRANKAHODNOCENI_POSITIVE_TITLE',
    'MEZISTRANKAHODNOCENI_NEGATIVE_TITLE',
    'MEZISTRANKAHODNOCENI_EMAIL',
    'MEZISTRANKAHODNOCENI_PHONE'
];

$config_ok = true;
foreach ($config_keys as $key) {
    $value = Configuration::get($key);
    if ($value) {
        echo "<p style='color: green;'>✅ $key: " . htmlspecialchars(substr($value, 0, 50)) . "...</p>";
    } else {
        echo "<p style='color: red;'>❌ $key - CHYBÍ</p>";
        $config_ok = false;
    }
}

if (!$config_ok) {
    echo "<p><strong>Řešení:</strong> Přeinstalujte modul nebo nastavte konfiguraci ručně</p>";
}

// Závěr
echo "<h2>Závěr</h2>";

$all_ok = $module && $tab_id && $custom_section_id && $table_exists && $config_ok;

if ($all_ok && $tab_info && $tab_info['id_parent'] == $custom_section_id) {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 4px;'>";
    echo "<h3>✅ Vše je v pořádku!</h3>";
    echo "<p>Modul je správně nainstalován a zařazen v sekci 'Vlastní moduly'.</p>";
    
    // Odkaz na administraci
    $admin_link = Context::getContext()->link->getAdminLink('AdminMezistrankaHodnoceni');
    echo "<p><a href='$admin_link' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;'>🔧 Přejít na administraci modulu</a></p>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 4px;'>";
    echo "<h3>⚠️ Nalezeny problémy</h3>";
    echo "<p>Některé části nastavení nejsou v pořádku. Postupujte podle návodu výše.</p>";
    echo "<p><a href='mezistranka_hodnoceni/setup-custom-modules-section.php' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;'>🔧 Spustit setup skript</a></p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><small>Test script: " . __FILE__ . "</small></p>";
?>
