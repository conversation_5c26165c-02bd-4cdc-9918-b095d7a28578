<?php
/**
 * Test skript pro ověření admin controlleru
 */

// Základní kontrola prostředí
if (!defined('_PS_VERSION_')) {
    require_once(dirname(__FILE__) . '/config/config.inc.php');
}

echo "<h1>Test Admin Controller - Mezistranka hodnocení</h1>";

$controller_class = 'AdminMezistrankaHodnoceni';

// 1. Zkontroluj soubor controlleru
echo "<h2>1. Kontrola souboru controlleru</h2>";
$controller_file = 'mezistranka_hodnoceni/controllers/admin/AdminMezistrankaHodnoceniController.php';
if (file_exists($controller_file)) {
    echo "<p style='color: green;'>✅ Controller soubor existuje: $controller_file</p>";
    
    // Zkontroluj syntaxi
    $content = file_get_contents($controller_file);
    if (strpos($content, 'class AdminMezistrankaHodnoceniController') !== false) {
        echo "<p style='color: green;'>✅ Třída controlleru je definována</p>";
    } else {
        echo "<p style='color: red;'>❌ Třída controlleru není správně definována</p>";
    }
    
    if (strpos($content, 'extends ModuleAdminController') !== false) {
        echo "<p style='color: green;'>✅ Controller správně rozšiřuje ModuleAdminController</p>";
    } else {
        echo "<p style='color: red;'>❌ Controller nerozšiřuje ModuleAdminController</p>";
    }
} else {
    echo "<p style='color: red;'>❌ Controller soubor neexistuje: $controller_file</p>";
}

// 2. Zkontroluj admin tab
echo "<h2>2. Kontrola admin tabu</h2>";
$tab_id = Tab::getIdFromClassName($controller_class);
if ($tab_id) {
    echo "<p style='color: green;'>✅ Admin tab existuje (ID: $tab_id)</p>";
    
    // Získej informace o tabu
    $sql = 'SELECT t.*, tl.name 
            FROM `' . _DB_PREFIX_ . 'tab` t
            LEFT JOIN `' . _DB_PREFIX_ . 'tab_lang` tl ON (t.id_tab = tl.id_tab AND tl.id_lang = ' . (int)Context::getContext()->language->id . ')
            WHERE t.id_tab = ' . (int)$tab_id;
    
    $tab_info = Db::getInstance()->getRow($sql);
    if ($tab_info) {
        echo "<p>Název: {$tab_info['name']}</p>";
        echo "<p>Aktivní: " . ($tab_info['active'] ? 'Ano' : 'Ne') . "</p>";
        echo "<p>Modul: {$tab_info['module']}</p>";
        
        // Zkontroluj oprávnění
        $employee = Context::getContext()->employee;
        if ($employee && $employee->id) {
            $has_access = $employee->hasAccess($tab_id);
            if ($has_access) {
                echo "<p style='color: green;'>✅ Aktuální uživatel má přístup k tabu</p>";
            } else {
                echo "<p style='color: red;'>❌ Aktuální uživatel NEMÁ přístup k tabu</p>";
            }
        }
    }
} else {
    echo "<p style='color: red;'>❌ Admin tab neexistuje</p>";
}

// 3. Test přístupu na controller
echo "<h2>3. Test přístupu na controller</h2>";
if ($tab_id) {
    $admin_link = Context::getContext()->link->getAdminLink($controller_class);
    echo "<p><strong>URL controlleru:</strong> <a href='$admin_link' target='_blank'>$admin_link</a></p>";
    
    // Test základní funkcionality
    try {
        // Simulace načtení controlleru
        if (class_exists($controller_class . 'Controller')) {
            echo "<p style='color: green;'>✅ Třída controlleru je dostupná</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ Třída controlleru není načtena (normální při testu)</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Chyba při testu controlleru: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p style='color: red;'>❌ Nelze testovat - tab neexistuje</p>";
}

// 4. Zkontroluj databázovou tabulku
echo "<h2>4. Kontrola databáze</h2>";
$table_exists = Db::getInstance()->executeS("SHOW TABLES LIKE '" . _DB_PREFIX_ . "mezistranka_hodnoceni_stats'");
if ($table_exists) {
    echo "<p style='color: green;'>✅ Tabulka 'mezistranka_hodnoceni_stats' existuje</p>";
    
    // Zkontroluj strukturu tabulky
    $columns = Db::getInstance()->executeS("SHOW COLUMNS FROM `" . _DB_PREFIX_ . "mezistranka_hodnoceni_stats`");
    echo "<p><strong>Sloupce tabulky:</strong></p>";
    echo "<ul>";
    foreach ($columns as $column) {
        echo "<li>{$column['Field']} ({$column['Type']})</li>";
    }
    echo "</ul>";
    
    // Počet záznamů
    $count = Db::getInstance()->getValue("SELECT COUNT(*) FROM `" . _DB_PREFIX_ . "mezistranka_hodnoceni_stats`");
    echo "<p>Počet hodnocení: $count</p>";
    
    // Test základního SQL dotazu
    try {
        $test_query = "SELECT id_stat, rating, date_add FROM `" . _DB_PREFIX_ . "mezistranka_hodnoceni_stats` LIMIT 1";
        $test_result = Db::getInstance()->getRow($test_query);
        echo "<p style='color: green;'>✅ Test SQL dotazu úspěšný</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Chyba v SQL dotazu: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p style='color: red;'>❌ Tabulka 'mezistranka_hodnoceni_stats' neexistuje</p>";
}

// 5. Test konfigurace
echo "<h2>5. Test konfigurace modulu</h2>";
$config_keys = [
    'MEZISTRANKAHODNOCENI_TITLE',
    'MEZISTRANKAHODNOCENI_EMAIL',
    'MEZISTRANKAHODNOCENI_PHONE'
];

foreach ($config_keys as $key) {
    $value = Configuration::get($key);
    if ($value) {
        echo "<p style='color: green;'>✅ $key: " . htmlspecialchars(substr($value, 0, 30)) . "...</p>";
    } else {
        echo "<p style='color: red;'>❌ $key - CHYBÍ</p>";
    }
}

// Závěr
echo "<h2>Závěr</h2>";

if ($tab_id && file_exists($controller_file) && $table_exists) {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 4px;'>";
    echo "<h3>✅ Admin controller je připraven!</h3>";
    echo "<p>Všechny základní komponenty jsou na místě.</p>";
    
    if ($admin_link) {
        echo "<p><a href='$admin_link' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;' target='_blank'>🔧 Otevřít administraci modulu</a></p>";
    }
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 4px;'>";
    echo "<h3>⚠️ Nalezeny problémy</h3>";
    echo "<p>Některé komponenty nejsou správně nastaveny. Zkontrolujte chyby výše.</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><small>Test script: " . __FILE__ . "</small></p>";
?>
