<?php
/**
 * Skript pro zařazení modulu Mezistranka hodnocení do sekce "Vlastní moduly"
 * 
 * POUŽITÍ:
 * 1. Spusťte tento skript v prohlížeči: modules/mezistranka_hodnoceni/setup-custom-modules-section.php
 * 2. Skript automaticky vytvoří sekci "Vlastní moduly" a přesune tam administraci modulu
 */

// Základní kontrola prostředí
if (!defined('_PS_VERSION_')) {
    require_once(dirname(__FILE__) . '/../../config/config.inc.php');
}

echo "<h1>Nastavení modulu Mezistranka hodnocení do sekce 'Vlastní moduly'</h1>";

$module_name = 'mezistranka_hodnoceni';
$controller_class = 'AdminMezistrankaHodnoceni';

echo "<h2>Zpracování modulu: <strong>$module_name</strong></h2>";
echo "<p>Controller: <strong>$controller_class</strong></p>";

// Zkontroluj, zda modul existuje
$module = Module::getInstanceByName($module_name);
if (!$module) {
    echo "<p style='color: red;'>❌ Modul '$module_name' neexistuje nebo není nainstalován</p>";
    exit;
}

echo "<p style='color: green;'>✅ Modul nalezen</p>";

// Zkontroluj, zda CustomModulesTabManager existuje
$manager_path = _PS_MODULE_DIR_ . $module_name . '/classes/CustomModulesTabManager.php';
if (!file_exists($manager_path)) {
    echo "<p style='color: red;'>❌ CustomModulesTabManager.php nenalezen v $manager_path</p>";
    echo "<p><strong>Chyba:</strong> Soubor CustomModulesTabManager.php nebyl správně zkopírován</p>";
    exit;
}

echo "<p style='color: green;'>✅ CustomModulesTabManager.php nalezen</p>";

// Načti CustomModulesTabManager
require_once($manager_path);

// Zkontroluj aktuální stav tabu
$current_tab_id = Tab::getIdFromClassName($controller_class);
if (!$current_tab_id) {
    echo "<p style='color: orange;'>⚠️ Tab '$controller_class' neexistuje - bude vytvořen</p>";
    
    // Vytvoř tab
    $result = CustomModulesTabManager::createModuleTab($controller_class, 'Mezistranka hodnocení', $module_name);
    
    if ($result) {
        echo "<p style='color: green;'>✅ Tab '$controller_class' úspěšně vytvořen</p>";
        $current_tab_id = Tab::getIdFromClassName($controller_class);
    } else {
        echo "<p style='color: red;'>❌ Chyba při vytváření tabu '$controller_class'</p>";
        exit;
    }
} else {
    echo "<p style='color: green;'>✅ Tab '$controller_class' nalezen (ID: $current_tab_id)</p>";
}

// Získej informace o aktuálním tabu
$sql = 'SELECT t.*, tl.name 
        FROM `' . _DB_PREFIX_ . 'tab` t
        LEFT JOIN `' . _DB_PREFIX_ . 'tab_lang` tl ON (t.id_tab = tl.id_tab AND tl.id_lang = ' . (int)Context::getContext()->language->id . ')
        WHERE t.id_tab = ' . (int)$current_tab_id;

$tab_info = Db::getInstance()->getRow($sql);
if ($tab_info) {
    echo "<p><strong>Aktuální umístění:</strong> Parent ID: {$tab_info['id_parent']}, Pozice: {$tab_info['position']}</p>";
}

// Zkontroluj, zda už je v vlastní sekci
$custom_section_id = Tab::getIdFromClassName('AdminCustomModules');
if ($custom_section_id && $tab_info['id_parent'] == $custom_section_id) {
    echo "<p style='color: green;'>✅ Tab je už v sekci 'Vlastní moduly'</p>";
} else {
    echo "<p style='color: orange;'>⚠️ Tab NENÍ v sekci 'Vlastní moduly'</p>";
    
    if (isset($_GET['action']) && $_GET['action'] == 'move') {
        echo "<h2>Přesun do vlastní sekce...</h2>";
        
        // Přesuň tab do vlastní sekce
        $result = CustomModulesTabManager::moveTabToCustomModules($controller_class);
        
        if ($result) {
            echo "<p style='color: green;'>✅ Tab úspěšně přesunut do sekce 'Vlastní moduly'</p>";
            
            // Zkontroluj nový stav
            $new_tab_info = Db::getInstance()->getRow($sql);
            if ($new_tab_info) {
                echo "<p><strong>Nové umístění:</strong> Parent ID: {$new_tab_info['id_parent']}</p>";
            }
        } else {
            echo "<p style='color: red;'>❌ Chyba při přesunu tabu</p>";
        }
    } else {
        echo "<p><a href='?action=move' style='background: #4caf50; color: white; padding: 10px; text-decoration: none; border-radius: 4px;'>🔄 Přesunout do vlastní sekce</a></p>";
    }
}

echo "<h2>Stav vlastní sekce</h2>";
if (CustomModulesTabManager::customModulesSectionExists()) {
    echo "<p style='color: green;'>✅ Sekce 'Vlastní moduly' existuje</p>";
    
    // Zobraz všechny moduly v sekci
    $custom_modules_id = Tab::getIdFromClassName('AdminCustomModules');
    $sql = 'SELECT t.class_name, tl.name 
            FROM `' . _DB_PREFIX_ . 'tab` t
            LEFT JOIN `' . _DB_PREFIX_ . 'tab_lang` tl ON (t.id_tab = tl.id_tab AND tl.id_lang = ' . (int)Context::getContext()->language->id . ')
            WHERE t.id_parent = ' . (int)$custom_modules_id . ' AND t.active = 1 
            ORDER BY t.position';
    
    $modules_in_section = Db::getInstance()->executeS($sql);
    if ($modules_in_section) {
        echo "<h3>Moduly v sekci:</h3>";
        echo "<ul>";
        foreach ($modules_in_section as $mod) {
            echo "<li><strong>{$mod['name']}</strong> ({$mod['class_name']})</li>";
        }
        echo "</ul>";
    }
} else {
    echo "<p style='color: orange;'>⚠️ Sekce 'Vlastní moduly' neexistuje</p>";
    echo "<p><a href='?action=create_section' style='background: #2196f3; color: white; padding: 10px; text-decoration: none; border-radius: 4px;'>➕ Vytvořit sekci</a></p>";
    
    if (isset($_GET['action']) && $_GET['action'] == 'create_section') {
        echo "<h2>Vytváření sekce...</h2>";
        $result = CustomModulesTabManager::createOrGetCustomModulesTab();
        if ($result) {
            echo "<p style='color: green;'>✅ Sekce 'Vlastní moduly' vytvořena (ID: $result)</p>";
        } else {
            echo "<p style='color: red;'>❌ Chyba při vytváření sekce</p>";
        }
    }
}

echo "<h2>Dokončení</h2>";

if ($custom_section_id && $tab_info && $tab_info['id_parent'] == $custom_section_id) {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 4px; margin: 20px 0;'>";
    echo "<h3>✅ Úspěch!</h3>";
    echo "<p>Modul <strong>Mezistranka hodnocení</strong> je nyní správně zařazen v sekci <strong>'Vlastní moduly'</strong>.</p>";
    echo "<p>Administrace modulu je nyní dostupná v menu: <strong>Vlastní moduly → Mezistranka hodnocení</strong></p>";
    echo "<p>Funkčnost je 1:1 stejná jako při přístupu přes standardní správu modulů.</p>";
    echo "</div>";
    
    // Odkaz na administraci
    $admin_link = Context::getContext()->link->getAdminLink('AdminMezistrankaHodnoceni');
    echo "<p><a href='$admin_link' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;'>🔧 Přejít na administraci modulu</a></p>";
} else {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 4px; margin: 20px 0;'>";
    echo "<h3>⚠️ Nedokončeno</h3>";
    echo "<p>Modul ještě není správně zařazen v sekci 'Vlastní moduly'.</p>";
    echo "<p>Použijte tlačítka výše pro dokončení nastavení.</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><small>Script: " . __FILE__ . "</small></p>";
?>
