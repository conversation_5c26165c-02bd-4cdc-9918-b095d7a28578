<?php
/**
 * Admin controller pro modul Mezistranka hodnocení
 * Poskytuje stejnou funkcionalnost jako getContent() metoda v hlavním modulu
 */

class AdminMezistrankaHodnoceniController extends ModuleAdminController
{
    public function __construct()
    {
        $this->bootstrap = true;

        parent::__construct();

        $this->meta_title = $this->l('Mezistranka hodnocení');
    }

    /**
     * Hlavní metoda pro zobrazení obsahu - používá původní getContent() z modulu
     */
    public function initContent()
    {
        // Získání obsahu z původní getContent() metody modulu
        $module_content = $this->module->getContent();

        // Nastavení obsahu
        $this->content = $module_content;

        // Použití standardního PrestaShop renderování
        parent::initContent();
    }

}
