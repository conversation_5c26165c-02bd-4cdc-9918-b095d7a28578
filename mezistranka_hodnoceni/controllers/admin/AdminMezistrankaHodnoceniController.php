<?php
/**
 * Admin controller pro modul Mezistranka hodnocení
 * Poskytuje stejnou funkcionalnost jako getContent() metoda v hlavním modulu
 */

class AdminMezistrankaHodnoceniController extends ModuleAdminController
{
    public function __construct()
    {
        $this->bootstrap = true;
        $this->table = 'mezistranka_hodnoceni_stats';
        $this->className = 'MezistrankaHodnoceniStats';
        $this->lang = false;
        
        parent::__construct();
        
        $this->meta_title = $this->l('Mezistranka hodnocení');
        
        // Definice polí pro seznam
        $this->fields_list = [
            'id_stat' => [
                'title' => $this->l('ID'),
                'align' => 'center',
                'class' => 'fixed-width-xs'
            ],
            'rating' => [
                'title' => $this->l('Hodnocení'),
                'align' => 'center',
                'class' => 'fixed-width-xs',
                'callback' => 'displayRatingStars'
            ],
            'clicked_link' => [
                'title' => $this->l('Kliknutý odkaz'),
                'callback' => 'displayClickedLink'
            ],
            'customer_email' => [
                'title' => $this->l('Email zákazníka'),
                'callback' => 'displayCustomerInfo'
            ],
            'ip_address' => [
                'title' => $this->l('IP adresa'),
                'align' => 'center'
            ],
            'date_add' => [
                'title' => $this->l('Datum'),
                'align' => 'center',
                'type' => 'datetime'
            ]
        ];
        
        // Akce pro seznam
        $this->addRowAction('delete');
        $this->bulk_actions = [
            'delete' => [
                'text' => $this->l('Smazat vybrané'),
                'icon' => 'icon-trash',
                'confirm' => $this->l('Opravdu chcete smazat vybrané položky?')
            ]
        ];
    }

    /**
     * Zobrazí hvězdičky pro hodnocení
     */
    public function displayRatingStars($value, $row)
    {
        $stars = '';
        for ($i = 1; $i <= 5; $i++) {
            if ($i <= $value) {
                $stars .= '<i class="icon-star" style="color: #ffc107;"></i>';
            } else {
                $stars .= '<i class="icon-star-o" style="color: #ddd;"></i>';
            }
        }
        return $stars . ' (' . $value . ')';
    }

    /**
     * Zobrazí kliknutý odkaz
     */
    public function displayClickedLink($value, $row)
    {
        if (empty($value)) {
            return '<span style="color: #999;">' . $this->l('Žádný') . '</span>';
        }
        
        // Zkrácení dlouhých URL
        $display_url = strlen($value) > 50 ? substr($value, 0, 47) . '...' : $value;
        return '<a href="' . htmlspecialchars($value) . '" target="_blank" title="' . htmlspecialchars($value) . '">' . htmlspecialchars($display_url) . '</a>';
    }

    /**
     * Zobrazí informace o zákazníkovi
     */
    public function displayCustomerInfo($value, $row)
    {
        if (!empty($row['id_customer'])) {
            $customer = new Customer($row['id_customer']);
            if (Validate::isLoadedObject($customer)) {
                return $customer->firstname . ' ' . $customer->lastname . '<br><small>' . $customer->email . '</small>';
            }
        }
        
        if (!empty($value)) {
            return '<span style="color: #999;">' . htmlspecialchars($value) . '</span>';
        }
        
        return '<span style="color: #999;">' . $this->l('Nepřihlášený') . '</span>';
    }

    /**
     * Přepsání renderList pro přidání statistik
     */
    public function renderList()
    {
        // Získání statistik
        $statistics = $this->getStatistics();
        $statSummary = $this->calculateStatistics($statistics);
        
        // Přiřazení do šablony
        $this->context->smarty->assign([
            'statistics_summary' => $statSummary,
            'show_statistics' => true
        ]);
        
        return parent::renderList();
    }

    /**
     * Hlavní metoda pro zobrazení obsahu
     */
    public function initContent()
    {
        // Zpracování formulářů
        $this->processConfiguration();

        // Nastavení obsahu podle aktuální záložky
        $current_tab = Tools::getValue('tab_module', 'configuration');

        if ($current_tab == 'statistics') {
            // Zobrazení statistik
            $this->content .= $this->renderStatistics();
        } else {
            // Zobrazení konfigurace
            $this->content .= $this->renderConfiguration();
        }

        // Nastavení záložek (přidá navigaci na začátek)
        $this->initTabModuleList();

        parent::initContent();
    }

    /**
     * Renderování konfigurace
     */
    public function renderConfiguration()
    {
        $output = '';

        // Informační box
        $output .= '<div class="alert alert-info">';
        $output .= '<p><strong>' . $this->l('Instrukce pro vložení hodnocení do CMS stránky:') . '</strong></p>';
        $output .= '<p>' . $this->l('Pro vložení komponenty hodnocení stačí do obsahu CMS stránky přidat shortcode:') . ' <code>{mezistrankahodnoceni}</code></p>';
        $output .= '<p>' . $this->l('Po uložení stránky se komponenta automaticky zobrazí bez nutnosti dalších kroků.') . '</p>';
        $output .= '</div>';

        // Formulář
        $output .= $this->renderForm();

        // Tlačítko pro aktualizaci shortcodů
        $output .= '<div class="panel">';
        $output .= '<div class="panel-heading">' . $this->l('Nástroje') . '</div>';
        $output .= '<div class="panel-body">';
        $output .= '<p>' . $this->l('Pokud komponenta hodnocení není viditelná, klikněte na tlačítko níže pro manuální aktualizaci všech stránek:') . '</p>';
        $output .= '<a href="' . $this->context->link->getAdminLink('AdminMezistrankaHodnoceni') . '&update_shortcodes=1" class="btn btn-primary">';
        $output .= '<i class="icon icon-refresh"></i> ' . $this->l('Aktualizovat všechny stránky se shortcodem');
        $output .= '</a>';
        $output .= '</div>';
        $output .= '</div>';

        return $output;
    }

    /**
     * Renderování statistik
     */
    public function renderStatistics()
    {
        $statistics = $this->getStatistics();
        $statSummary = $this->calculateStatistics($statistics);

        $output = '';

        // Souhrnné statistiky
        $output .= '<div class="row">';
        $output .= '<div class="col-lg-3">';
        $output .= '<div class="panel">';
        $output .= '<div class="panel-heading">' . $this->l('Celkem hodnocení') . '</div>';
        $output .= '<div class="panel-body text-center">';
        $output .= '<h2>' . $statSummary['total_ratings'] . '</h2>';
        $output .= '</div>';
        $output .= '</div>';
        $output .= '</div>';

        $output .= '<div class="col-lg-3">';
        $output .= '<div class="panel">';
        $output .= '<div class="panel-heading">' . $this->l('Průměrné hodnocení') . '</div>';
        $output .= '<div class="panel-body text-center">';
        $output .= '<h2>' . number_format($statSummary['average_rating'], 1) . '/5</h2>';
        $output .= '</div>';
        $output .= '</div>';
        $output .= '</div>';

        $output .= '<div class="col-lg-3">';
        $output .= '<div class="panel">';
        $output .= '<div class="panel-heading">' . $this->l('Pozitivní (4-5★)') . '</div>';
        $output .= '<div class="panel-body text-center">';
        $output .= '<h2>' . $statSummary['positive_count'] . '</h2>';
        $output .= '<small>' . number_format($statSummary['positive_percent'], 1) . '%</small>';
        $output .= '</div>';
        $output .= '</div>';
        $output .= '</div>';

        $output .= '<div class="col-lg-3">';
        $output .= '<div class="panel">';
        $output .= '<div class="panel-heading">' . $this->l('Negativní (1-3★)') . '</div>';
        $output .= '<div class="panel-body text-center">';
        $output .= '<h2>' . $statSummary['negative_count'] . '</h2>';
        $output .= '<small>' . number_format($statSummary['negative_percent'], 1) . '%</small>';
        $output .= '</div>';
        $output .= '</div>';
        $output .= '</div>';
        $output .= '</div>';

        // Seznam hodnocení
        $output .= '<div class="panel">';
        $output .= '<div class="panel-heading">';
        $output .= $this->l('Poslední hodnocení');
        $output .= '<div class="panel-heading-action">';
        $output .= '<a href="' . $this->context->link->getAdminLink('AdminMezistrankaHodnoceni') . '&export_stats=1" class="btn btn-default">';
        $output .= '<i class="icon icon-download"></i> ' . $this->l('Export CSV');
        $output .= '</a>';
        $output .= '</div>';
        $output .= '</div>';

        if (!empty($statistics)) {
            $output .= '<table class="table">';
            $output .= '<thead>';
            $output .= '<tr>';
            $output .= '<th>' . $this->l('Datum') . '</th>';
            $output .= '<th>' . $this->l('Hodnocení') . '</th>';
            $output .= '<th>' . $this->l('Zákazník') . '</th>';
            $output .= '<th>' . $this->l('Kliknutý odkaz') . '</th>';
            $output .= '<th>' . $this->l('IP adresa') . '</th>';
            $output .= '</tr>';
            $output .= '</thead>';
            $output .= '<tbody>';

            foreach (array_slice($statistics, 0, 20) as $stat) {
                $output .= '<tr>';
                $output .= '<td>' . date('d.m.Y H:i', strtotime($stat['date_add'])) . '</td>';
                $output .= '<td>' . $this->displayRatingStars($stat['rating'], $stat) . '</td>';
                $output .= '<td>' . $this->displayCustomerInfo($stat['customer_email'], $stat) . '</td>';
                $output .= '<td>' . $this->displayClickedLink($stat['clicked_link'], $stat) . '</td>';
                $output .= '<td>' . $stat['ip_address'] . '</td>';
                $output .= '</tr>';
            }

            $output .= '</tbody>';
            $output .= '</table>';
        } else {
            $output .= '<div class="panel-body">';
            $output .= '<p>' . $this->l('Zatím nejsou k dispozici žádná hodnocení.') . '</p>';
            $output .= '</div>';
        }

        $output .= '</div>';

        return $output;
    }

    /**
     * Zpracování konfigurace modulu
     */
    private function processConfiguration()
    {
        $output = '';
        
        // Zpracování konfiguračního formuláře
        if (Tools::isSubmit('submitMezistrankaHodnoceniConfig')) {
            Configuration::updateValue('MEZISTRANKAHODNOCENI_TITLE', Tools::getValue('MEZISTRANKAHODNOCENI_TITLE'));
            Configuration::updateValue('MEZISTRANKAHODNOCENI_POSITIVE_TITLE', Tools::getValue('MEZISTRANKAHODNOCENI_POSITIVE_TITLE'));
            Configuration::updateValue('MEZISTRANKAHODNOCENI_POSITIVE_TEXT', Tools::getValue('MEZISTRANKAHODNOCENI_POSITIVE_TEXT'));
            Configuration::updateValue('MEZISTRANKAHODNOCENI_NEGATIVE_TITLE', Tools::getValue('MEZISTRANKAHODNOCENI_NEGATIVE_TITLE'));
            Configuration::updateValue('MEZISTRANKAHODNOCENI_NEGATIVE_TEXT', Tools::getValue('MEZISTRANKAHODNOCENI_NEGATIVE_TEXT'));
            Configuration::updateValue('MEZISTRANKAHODNOCENI_LINK_GOOGLE', Tools::getValue('MEZISTRANKAHODNOCENI_LINK_GOOGLE'));
            Configuration::updateValue('MEZISTRANKAHODNOCENI_LINK_SEZNAM', Tools::getValue('MEZISTRANKAHODNOCENI_LINK_SEZNAM'));
            Configuration::updateValue('MEZISTRANKAHODNOCENI_LINK_HEUREKA', Tools::getValue('MEZISTRANKAHODNOCENI_LINK_HEUREKA'));
            Configuration::updateValue('MEZISTRANKAHODNOCENI_PHONE', Tools::getValue('MEZISTRANKAHODNOCENI_PHONE'));
            Configuration::updateValue('MEZISTRANKAHODNOCENI_EMAIL', Tools::getValue('MEZISTRANKAHODNOCENI_EMAIL'));
            
            // Aktualizace všech stránek se shortcodem
            $this->module->updateAllCmsWithShortcode();
            
            $this->confirmations[] = $this->l('Nastavení bylo úspěšně uloženo a všechny stránky se shortcodem byly aktualizovány.');
        }
        
        // Zpracování aktualizace shortcodů
        if (Tools::isSubmit('update_shortcodes')) {
            if ($this->module->updateAllCmsWithShortcode()) {
                $this->confirmations[] = $this->l('Všechny stránky se shortcodem byly aktualizovány.');
            } else {
                $this->errors[] = $this->l('Nastala chyba při aktualizaci stránek.');
            }
        }
        
        // Zpracování AJAX požadavků
        if (Tools::isSubmit('ajax') && Tools::getValue('ajax') == 1) {
            $this->processAjaxRequest();
            exit;
        }
        
        // Zpracování exportu statistik
        if (Tools::isSubmit('export_stats')) {
            $this->exportStatisticsCSV();
        }
    }

    /**
     * Nastavení záložek
     */
    public function initTabModuleList()
    {
        $current_tab = Tools::getValue('tab_module', 'configuration');

        // Vytvoření navigace záložek
        $tabs_html = '<ul class="nav nav-tabs" role="tablist">';

        $tabs_html .= '<li class="nav-item ' . ($current_tab == 'configuration' ? 'active' : '') . '">';
        $tabs_html .= '<a href="' . $this->context->link->getAdminLink('AdminMezistrankaHodnoceni') . '&tab_module=configuration" class="nav-link">';
        $tabs_html .= '<i class="icon-cogs"></i> ' . $this->l('Konfigurace');
        $tabs_html .= '</a>';
        $tabs_html .= '</li>';

        $tabs_html .= '<li class="nav-item ' . ($current_tab == 'statistics' ? 'active' : '') . '">';
        $tabs_html .= '<a href="' . $this->context->link->getAdminLink('AdminMezistrankaHodnoceni') . '&tab_module=statistics" class="nav-link">';
        $tabs_html .= '<i class="icon-bar-chart"></i> ' . $this->l('Statistiky');
        $tabs_html .= '</a>';
        $tabs_html .= '</li>';

        $tabs_html .= '</ul>';

        // Přidání navigace na začátek obsahu
        $this->content = $tabs_html . $this->content;
    }

    /**
     * Renderování konfiguračního formuláře
     */
    public function renderForm()
    {
        $fields_form = [
            'form' => [
                'legend' => [
                    'title' => $this->l('Konfigurace modulu'),
                    'icon' => 'icon-cogs'
                ],
                'input' => [
                    [
                        'type' => 'text',
                        'label' => $this->l('Hlavní otázka'),
                        'name' => 'MEZISTRANKAHODNOCENI_TITLE',
                        'size' => 50,
                        'required' => true
                    ],
                    [
                        'type' => 'text',
                        'label' => $this->l('Titulek pro pozitivní hodnocení'),
                        'name' => 'MEZISTRANKAHODNOCENI_POSITIVE_TITLE',
                        'size' => 50,
                        'required' => true
                    ],
                    [
                        'type' => 'textarea',
                        'label' => $this->l('Text pro pozitivní hodnocení'),
                        'name' => 'MEZISTRANKAHODNOCENI_POSITIVE_TEXT',
                        'rows' => 3,
                        'required' => true
                    ],
                    [
                        'type' => 'text',
                        'label' => $this->l('Titulek pro negativní hodnocení'),
                        'name' => 'MEZISTRANKAHODNOCENI_NEGATIVE_TITLE',
                        'size' => 50,
                        'required' => true
                    ],
                    [
                        'type' => 'textarea',
                        'label' => $this->l('Text pro negativní hodnocení'),
                        'name' => 'MEZISTRANKAHODNOCENI_NEGATIVE_TEXT',
                        'rows' => 3,
                        'required' => true
                    ],
                    [
                        'type' => 'text',
                        'label' => $this->l('Odkaz na Google recenze'),
                        'name' => 'MEZISTRANKAHODNOCENI_LINK_GOOGLE',
                        'size' => 50
                    ],
                    [
                        'type' => 'text',
                        'label' => $this->l('Odkaz na Seznam Firmy'),
                        'name' => 'MEZISTRANKAHODNOCENI_LINK_SEZNAM',
                        'size' => 50
                    ],
                    [
                        'type' => 'text',
                        'label' => $this->l('Odkaz na Heureka'),
                        'name' => 'MEZISTRANKAHODNOCENI_LINK_HEUREKA',
                        'size' => 50
                    ],
                    [
                        'type' => 'text',
                        'label' => $this->l('Telefonní číslo'),
                        'name' => 'MEZISTRANKAHODNOCENI_PHONE',
                        'size' => 20
                    ],
                    [
                        'type' => 'text',
                        'label' => $this->l('Email'),
                        'name' => 'MEZISTRANKAHODNOCENI_EMAIL',
                        'size' => 30
                    ]
                ],
                'submit' => [
                    'title' => $this->l('Uložit'),
                    'name' => 'submitMezistrankaHodnoceniConfig'
                ]
            ]
        ];

        $helper = new HelperForm();
        $helper->show_toolbar = false;
        $helper->table = 'mezistranka_hodnoceni_config';
        $helper->module = $this->module;
        $helper->default_form_language = $this->context->language->id;
        $helper->allow_employee_form_lang = Configuration::get('PS_BO_ALLOW_EMPLOYEE_FORM_LANG', 0);
        $helper->identifier = 'id_configuration';
        $helper->submit_action = 'submitMezistrankaHodnoceniConfig';
        $helper->currentIndex = $this->context->link->getAdminLink('AdminMezistrankaHodnoceni', false);
        $helper->token = Tools::getAdminTokenLite('AdminMezistrankaHodnoceni');

        $helper->fields_value = [
            'MEZISTRANKAHODNOCENI_TITLE' => Configuration::get('MEZISTRANKAHODNOCENI_TITLE'),
            'MEZISTRANKAHODNOCENI_POSITIVE_TITLE' => Configuration::get('MEZISTRANKAHODNOCENI_POSITIVE_TITLE'),
            'MEZISTRANKAHODNOCENI_POSITIVE_TEXT' => Configuration::get('MEZISTRANKAHODNOCENI_POSITIVE_TEXT'),
            'MEZISTRANKAHODNOCENI_NEGATIVE_TITLE' => Configuration::get('MEZISTRANKAHODNOCENI_NEGATIVE_TITLE'),
            'MEZISTRANKAHODNOCENI_NEGATIVE_TEXT' => Configuration::get('MEZISTRANKAHODNOCENI_NEGATIVE_TEXT'),
            'MEZISTRANKAHODNOCENI_LINK_GOOGLE' => Configuration::get('MEZISTRANKAHODNOCENI_LINK_GOOGLE'),
            'MEZISTRANKAHODNOCENI_LINK_SEZNAM' => Configuration::get('MEZISTRANKAHODNOCENI_LINK_SEZNAM'),
            'MEZISTRANKAHODNOCENI_LINK_HEUREKA' => Configuration::get('MEZISTRANKAHODNOCENI_LINK_HEUREKA'),
            'MEZISTRANKAHODNOCENI_PHONE' => Configuration::get('MEZISTRANKAHODNOCENI_PHONE'),
            'MEZISTRANKAHODNOCENI_EMAIL' => Configuration::get('MEZISTRANKAHODNOCENI_EMAIL')
        ];

        return $helper->generateForm([$fields_form]);
    }

    /**
     * Získá statistiky hodnocení z databáze
     */
    private function getStatistics()
    {
        $sql = "SELECT s.*, COALESCE(c.firstname, '') as firstname, COALESCE(c.lastname, '') as lastname
                FROM `" . _DB_PREFIX_ . "mezistranka_hodnoceni_stats` s
                LEFT JOIN `" . _DB_PREFIX_ . "customer` c ON (s.id_customer = c.id_customer)
                ORDER BY s.date_add DESC";

        return Db::getInstance()->executeS($sql);
    }

    /**
     * Vypočítá souhrnné statistiky
     */
    private function calculateStatistics($statistics)
    {
        $total_ratings = count($statistics);
        $sum_ratings = 0;
        $positive_count = 0;
        $negative_count = 0;
        $rating_distribution = [
            1 => ['count' => 0, 'percent' => 0],
            2 => ['count' => 0, 'percent' => 0],
            3 => ['count' => 0, 'percent' => 0],
            4 => ['count' => 0, 'percent' => 0],
            5 => ['count' => 0, 'percent' => 0],
        ];
        $link_clicks = [];

        // Zpracování dat
        foreach ($statistics as $stat) {
            $rating = (int)$stat['rating'];
            $sum_ratings += $rating;

            // Počítání pozitivních a negativních hodnocení
            if ($rating >= 4) {
                $positive_count++;
            } else {
                $negative_count++;
            }

            // Distribuce hodnocení
            if (isset($rating_distribution[$rating])) {
                $rating_distribution[$rating]['count']++;
            }

            // Počítání kliknutí na odkazy
            if (!empty($stat['clicked_link'])) {
                $link = $stat['clicked_link'];
                if (!isset($link_clicks[$link])) {
                    $link_clicks[$link] = ['count' => 0, 'percent' => 0];
                }
                $link_clicks[$link]['count']++;
            }
        }

        // Výpočet procent
        if ($total_ratings > 0) {
            foreach ($rating_distribution as $rating => &$data) {
                $data['percent'] = ($data['count'] / $total_ratings) * 100;
            }

            $positive_percent = ($positive_count / $total_ratings) * 100;
            $negative_percent = ($negative_count / $total_ratings) * 100;

            foreach ($link_clicks as $link => &$data) {
                $data['percent'] = ($data['count'] / $total_ratings) * 100;
            }
        } else {
            $positive_percent = 0;
            $negative_percent = 0;
        }

        $average_rating = $total_ratings > 0 ? $sum_ratings / $total_ratings : 0;

        return [
            'total_ratings' => $total_ratings,
            'average_rating' => $average_rating,
            'positive_count' => $positive_count,
            'positive_percent' => $positive_percent,
            'negative_count' => $negative_count,
            'negative_percent' => $negative_percent,
            'rating_distribution' => $rating_distribution,
            'link_clicks' => $link_clicks,
        ];
    }

    /**
     * Zpracování AJAX požadavků
     */
    private function processAjaxRequest()
    {
        $action = Tools::getValue('action');

        switch ($action) {
            case 'delete_all_ratings':
                $this->deleteAllRatings();
                break;
            default:
                $this->sendAjaxResponse(['success' => false, 'message' => 'Neznámá akce']);
                break;
        }
    }

    /**
     * Smazání všech recenzí
     */
    private function deleteAllRatings()
    {
        if (Tools::getValue('confirm') !== 'yes') {
            $this->sendAjaxResponse(['success' => false, 'message' => 'Chybí potvrzení']);
            return;
        }

        try {
            $count_before = Db::getInstance()->getValue("SELECT COUNT(*) FROM `" . _DB_PREFIX_ . "mezistranka_hodnoceni_stats`");
            $result = Db::getInstance()->execute("DELETE FROM `" . _DB_PREFIX_ . "mezistranka_hodnoceni_stats`");

            if ($result) {
                $this->sendAjaxResponse([
                    'success' => true,
                    'message' => "Úspěšně smazáno $count_before recenzí"
                ]);
            } else {
                $this->sendAjaxResponse(['success' => false, 'message' => 'Chyba při mazání z databáze']);
            }
        } catch (Exception $e) {
            $this->sendAjaxResponse(['success' => false, 'message' => 'Chyba: ' . $e->getMessage()]);
        }
    }

    /**
     * Odeslání AJAX odpovědi
     */
    private function sendAjaxResponse($data)
    {
        header('Content-Type: application/json');
        echo json_encode($data);
        exit;
    }

    /**
     * Export statistik do CSV
     */
    public function exportStatisticsCSV()
    {
        $stats = $this->getStatistics();

        if (empty($stats)) {
            $this->errors[] = $this->l('Nejsou k dispozici žádné statistiky pro export.');
            return false;
        }

        $filename = 'statistiky-hodnoceni-' . date('Y-m-d-H-i-s') . '.csv';

        $headers = [
            $this->l('ID'),
            $this->l('Hodnocení (1-5)'),
            $this->l('Odkaz'),
            $this->l('ID zákazníka'),
            $this->l('Email zákazníka'),
            $this->l('IP adresa'),
            $this->l('Prohlížeč'),
            $this->l('Datum a čas')
        ];

        $data = [];
        foreach ($stats as $stat) {
            $data[] = [
                $stat['id_stat'],
                $stat['rating'],
                $stat['clicked_link'] ? $stat['clicked_link'] : $this->l('Žádný'),
                $stat['id_customer'] ? $stat['id_customer'] : $this->l('Nepřihlášený'),
                $stat['customer_email'] ? $stat['customer_email'] : '-',
                $stat['ip_address'],
                $stat['user_agent'],
                $stat['date_add']
            ];
        }

        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename=' . $filename);

        $output = fopen('php://output', 'w');
        fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
        fputcsv($output, $headers, ';');

        foreach ($data as $row) {
            fputcsv($output, $row, ';');
        }

        fclose($output);
        exit;
    }
}
