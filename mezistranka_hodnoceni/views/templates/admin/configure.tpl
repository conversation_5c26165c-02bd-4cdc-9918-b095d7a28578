{if $admin_css}
<style>
{$admin_css nofilter}
</style>
{/if}

{*
* 2007-2023 PrestaShop
*
* NOTICE OF LICENSE
*
* This source file is subject to the Academic Free License (AFL 3.0)
* that is bundled with this package in the file LICENSE.txt.
* It is also available through the world-wide-web at this URL:
* http://opensource.org/licenses/afl-3.0.php
* If you did not receive a copy of the license and are unable to
* obtain it through the world-wide-web, please send an email
* to <EMAIL> so we can send you a copy immediately.
*
* DISCLAIMER
*
* Do not edit or add to this file if you wish to upgrade PrestaShop to newer
* versions in the future. If you wish to customize PrestaShop for your
* needs please refer to http://www.prestashop.com for more information.
*
*  <AUTHOR> SA <<EMAIL>>
*  @copyright 2007-2023 PrestaShop SA
*  @license   http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
*  International Registered Trademark & Property of PrestaShop SA
*}

<div class="bootstrap panel">
    <div class="panel-heading">
        <i class="icon icon-star"></i> {l s='Mezistranka hodnoceni' mod='mezistranka_hodnoceni'}
    </div>
    
<div class="alert alert-info">
    <p><strong>{l s='Instrukce pro vložení hodnocení do CMS stránky:' mod='mezistranka_hodnoceni'}</strong></p>
    <p>{l s='Pro vložení komponenty hodnocení stačí do obsahu CMS stránky přidat shortcode:' mod='mezistranka_hodnoceni'} <code>{ldelim}mezistrankahodnoceni{rdelim}</code></p>
    <p>{l s='Po uložení stránky se komponenta automaticky zobrazí bez nutnosti dalších kroků.' mod='mezistranka_hodnoceni'}</p>
<p>Pokud komponenta hodnocení není viditelná, klikněte na tlačítko níže pro manuální aktualizaci všech stránek:</p>
    <p class="text-center">
        <a href="{$update_shortcode_btn|escape:'html':'UTF-8'}" class="btn btn-primary">
            <i class="icon icon-refresh"></i> {l s='Aktualizovat všechny stránky se shortcodem' mod='mezistranka_hodnoceni'}
        </a>
    </p>
</div>

    <ul class="nav nav-tabs" role="tablist">
        <li class="nav-item {if $current_tab == 'configuration'}active{/if}">
            <a class="nav-link" href="{$config_url|escape:'html':'UTF-8'}" role="tab">
                <i class="icon icon-cog"></i> {l s='Konfigurace' mod='mezistranka_hodnoceni'}
            </a>
        </li>
        <li class="nav-item {if $current_tab == 'statistics'}active{/if}">
            <a class="nav-link" href="{$statistics_url|escape:'html':'UTF-8'}" role="tab">
                <i class="icon icon-bar-chart"></i> {l s='Statistiky hodnocení' mod='mezistranka_hodnoceni'}
            </a>
        </li>
    </ul>

    <div class="tab-content">
        {* Záložka konfigurace *}
        <div class="tab-pane {if $current_tab == 'configuration'}active{/if}" id="configuration">
            <div class="panel-body">
                <p>
                    <strong>{l s='Nastavení modulu mezistránky pro hodnocení zákazníků' mod='mezistranka_hodnoceni'}</strong><br />
                    {l s='Tento modul zobrazuje mezistránku s hvězdičkovým hodnocením.' mod='mezistranka_hodnoceni'}<br />
                    {l s='Pokud zákazník udělí 4-5 hvězdiček, nabídne externí recenzní služby.' mod='mezistranka_hodnoceni'}<br />
                    {l s='Pokud zákazník udělí 1-3 hvězdičky, nabídne pouze kontaktní formulář.' mod='mezistranka_hodnoceni'}
                </p>
                
<form id="module_form" class="defaultForm form-horizontal" action="{$current_url|default:''|escape:'html':'UTF-8'}" method="post">
    <div class="panel">
        <div class="panel-heading">
            <i class="icon icon-cog"></i> {l s='Nastavení hodnotícího formuláře' mod='mezistranka_hodnoceni'}
        </div>
        <div class="form-wrapper">
            <div class="form-group">
                <label class="control-label col-lg-3">{l s='Nadpis formuláře' mod='mezistranka_hodnoceni'}</label>
                <div class="col-lg-9">
                    <input type="text" name="MEZISTRANKAHODNOCENI_TITLE" value="{$MEZISTRANKAHODNOCENI_TITLE|escape:'html':'UTF-8'}" class="form-control" />
                </div>
            </div>
            
            <div class="form-group">
                <label class="control-label col-lg-3">{l s='Nadpis pro pozitivní hodnocení' mod='mezistranka_hodnoceni'}</label>
                <div class="col-lg-9">
                    <input type="text" name="MEZISTRANKAHODNOCENI_POSITIVE_TITLE" value="{$MEZISTRANKAHODNOCENI_POSITIVE_TITLE|escape:'html':'UTF-8'}" class="form-control" />
                </div>
            </div>
            
            <div class="form-group">
                <label class="control-label col-lg-3">{l s='Text pro pozitivní hodnocení' mod='mezistranka_hodnoceni'}</label>
                <div class="col-lg-9">
                    <textarea name="MEZISTRANKAHODNOCENI_POSITIVE_TEXT" class="form-control" rows="3">{$MEZISTRANKAHODNOCENI_POSITIVE_TEXT|escape:'html':'UTF-8'}</textarea>
                </div>
            </div>
            
            <div class="form-group">
                <label class="control-label col-lg-3">{l s='Nadpis pro negativní hodnocení' mod='mezistranka_hodnoceni'}</label>
                <div class="col-lg-9">
                    <input type="text" name="MEZISTRANKAHODNOCENI_NEGATIVE_TITLE" value="{$MEZISTRANKAHODNOCENI_NEGATIVE_TITLE|escape:'html':'UTF-8'}" class="form-control" />
                </div>
            </div>
            
            <div class="form-group">
                <label class="control-label col-lg-3">{l s='Text pro negativní hodnocení' mod='mezistranka_hodnoceni'}</label>
                <div class="col-lg-9">
                    <textarea name="MEZISTRANKAHODNOCENI_NEGATIVE_TEXT" class="form-control" rows="3">{$MEZISTRANKAHODNOCENI_NEGATIVE_TEXT|escape:'html':'UTF-8'}</textarea>
                </div>
            </div>
            
            <div class="form-group">
                <label class="control-label col-lg-3">{l s='Odkaz Google' mod='mezistranka_hodnoceni'}</label>
                <div class="col-lg-9">
                    <input type="text" name="MEZISTRANKAHODNOCENI_LINK_GOOGLE" value="{$MEZISTRANKAHODNOCENI_LINK_GOOGLE|escape:'html':'UTF-8'}" class="form-control" />
                </div>
            </div>
            
            <div class="form-group">
                <label class="control-label col-lg-3">{l s='Odkaz Seznam' mod='mezistranka_hodnoceni'}</label>
                <div class="col-lg-9">
                    <input type="text" name="MEZISTRANKAHODNOCENI_LINK_SEZNAM" value="{$MEZISTRANKAHODNOCENI_LINK_SEZNAM|escape:'html':'UTF-8'}" class="form-control" />
                </div>
            </div>
            
            <div class="form-group">
                <label class="control-label col-lg-3">{l s='Odkaz Heureka' mod='mezistranka_hodnoceni'}</label>
                <div class="col-lg-9">
                    <input type="text" name="MEZISTRANKAHODNOCENI_LINK_HEUREKA" value="{$MEZISTRANKAHODNOCENI_LINK_HEUREKA|escape:'html':'UTF-8'}" class="form-control" />
                </div>
            </div>
            
            <div class="form-group">
                <label class="control-label col-lg-3">{l s='Telefon' mod='mezistranka_hodnoceni'}</label>
                <div class="col-lg-9">
                    <input type="text" name="MEZISTRANKAHODNOCENI_PHONE" value="{$MEZISTRANKAHODNOCENI_PHONE|escape:'html':'UTF-8'}" class="form-control" />
                </div>
            </div>
            
            <div class="form-group">
                <label class="control-label col-lg-3">{l s='E-mail' mod='mezistranka_hodnoceni'}</label>
                <div class="col-lg-9">
                    <input type="text" name="MEZISTRANKAHODNOCENI_EMAIL" value="{$MEZISTRANKAHODNOCENI_EMAIL|escape:'html':'UTF-8'}" class="form-control" />
                </div>
            </div>
        </div>
        <div class="panel-footer">
            <button type="submit" name="submit{$name|default:'mezistranka_hodnoceni'}" class="btn btn-default pull-right">
                <i class="process-icon-save"></i> {l s='Uložit' mod='mezistranka_hodnoceni'}
            </button>
        </div>
    </div>
</form>
            </div>
        </div>
        
        {* Záložka statistiky *}
<div class="tab-pane {if $current_tab == 'statistics'}active{/if}" id="statistics">
    <div class="panel-body">
        <div class="row">
            <div class="col-md-12">
                <h3>{l s='Statistiky hodnocení zákazníků' mod='mezistranka_hodnoceni'}</h3>
                <hr>
            </div>
        </div>
        
        <div class="row mb-3">
            <div class="col-md-12 text-right">
                <a href="{$export_url|escape:'html':'UTF-8'}" class="btn btn-primary" id="export-stats-btn">
                    <i class="icon icon-download"></i> {l s='Exportovat statistiky (CSV)' mod='mezistranka_hodnoceni'}
                </a>
                <button type="button" class="btn btn-danger" id="delete-all-ratings-btn" data-toggle="modal" data-target="#deleteConfirmModal">
                    <i class="icon icon-trash"></i> {l s='Smazat všechny recenze' mod='mezistranka_hodnoceni'}
                </button>
            </div>
        </div>
        
        {if empty($statistics)}
            <div class="alert alert-info">
                {l s='Zatím nejsou k dispozici žádné statistiky hodnocení.' mod='mezistranka_hodnoceni'}
            </div>
        {else}
            {* 1. část - Hlavní souhrn statistik *}
            <div class="row stats-summary-container stats-summary-main">
                <div class="col-md-12">
                    <div class="stats-summary stats-summary-row">
                        <div class="stats-box stats-box-custom stats-box-purple stats-total-ratings">
                            <div class="stats-box-title">{l s='Celkem hodnocení' mod='mezistranka_hodnoceni'}</div>
                            <div class="stats-box-value">{$total_ratings}</div>
                        </div>
                        
                        <div class="stats-box stats-box-custom stats-box-blue stats-average-rating">
                            <div class="stats-box-title">{l s='Průměrné hodnocení' mod='mezistranka_hodnoceni'}</div>
                            <div class="stats-box-value">{$average_rating|string_format:"%.1f"}</div>
                            <div class="stats-rating-stars stats-average-stars">
                                {for $i=1 to 5}
                                    {if $i <= $average_rating}
                                        <i class="icon icon-star"></i>
                                    {elseif $i <= $average_rating+0.5}
                                        <i class="icon icon-star-half-o"></i>
                                    {else}
                                        <i class="icon icon-star-o"></i>
                                    {/if}
                                {/for}
                            </div>
                        </div>
                        
                        <div class="stats-box stats-box-custom stats-box-green stats-positive-ratings">
                            <div class="stats-box-title">{l s='Pozitivní hodnocení (4-5)' mod='mezistranka_hodnoceni'}</div>
                            <div class="stats-box-value">{$positive_count}</div>
                            <div class="stats-box-percent stats-positive-percent">{$positive_percent|string_format:"%.1f"}%</div>
                        </div>
                        
                        <div class="stats-box stats-box-custom stats-box-orange stats-negative-ratings">
                            <div class="stats-box-title">{l s='Negativní hodnocení (1-3)' mod='mezistranka_hodnoceni'}</div>
                            <div class="stats-box-value">{$negative_count}</div>
                            <div class="stats-box-percent stats-negative-percent">{$negative_percent|string_format:"%.1f"}%</div>
                        </div>
                    </div>
                </div>
            </div>
            
            {* 2. a 3. část - Rozložení hodnocení a Konverze na odkazy *}
            <div class="row stats-details-container">
                {* 2. část - Rozložení hodnocení *}
                <div class="col-md-6 stats-distribution-column">
                    <div class="stats-panel stats-rating-distribution-panel">
                        <div class="stats-panel-heading">
                            <i class="icon icon-bar-chart"></i> {l s='Rozložení hodnocení' mod='mezistranka_hodnoceni'}
                        </div>
                        <div class="stats-panel-body stats-distribution-body">
                            <div class="rating-distribution stats-rating-distribution">
                                {foreach from=$rating_distribution key=rating item=data}
                                    <div class="rating-row stats-rating-row">
                                        <div class="rating-stars-block stats-rating-stars-block">
                                            {for $i=1 to 5}
                                                {if $i <= $rating}<i class="icon icon-star"></i>{else}<i class="icon icon-star-o"></i>{/if}
                                            {/for}
                                        </div>
                                        <div class="rating-progress-container stats-rating-progress-container">
                                            <div class="rating-progress-bar stats-rating-progress-bar" style="width: {$data.percent}%"></div>
                                        </div>
                                        <div class="rating-percentage stats-rating-percentage">{$data.percent|string_format:"%.1f"}%</div>
                                        <div class="rating-count stats-rating-count">({$data.count})</div>
                                    </div>
                                {/foreach}
                            </div>
                        </div>
                    </div>
                </div>
                
                {* 3. část - Konverze na odkazy *}
                {if !empty($link_clicks)}
                <div class="col-md-6 stats-links-column">
                    <div class="stats-panel stats-links-conversion-panel">
                        <div class="stats-panel-heading">
                            <i class="icon icon-link"></i> {l s='Konverze na odkazy' mod='mezistranka_hodnoceni'}
                        </div>
                        <div class="stats-panel-body stats-links-body">
                            <table class="stats-table stats-links-table">
                                <thead>
                                    <tr>
                                        <th>{l s='Odkaz' mod='mezistranka_hodnoceni'}</th>
                                        <th class="text-center">{l s='Počet kliknutí' mod='mezistranka_hodnoceni'}</th>
                                        <th class="text-center">{l s='Konverzní poměr' mod='mezistranka_hodnoceni'}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {foreach from=$link_clicks key=link item=data}
                                        <tr>
                                            <td>{$link}</td>
                                            <td class="text-center">{$data.count}</td>
                                            <td class="text-center">{$data.percent|string_format:"%.1f"}%</td>
                                        </tr>
                                    {/foreach}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                {/if}
            </div>
            
            {* 4. část - Detail hodnocení *}
            <div class="stats-detail-container stats-full-detail">
                <div class="stats-detail-title">
                    <i class="icon icon-list"></i> {l s='Detail hodnocení' mod='mezistranka_hodnoceni'}
                </div>
                
                <div class="filter-container stats-filter-container">
                    <div class="row">
                        <div class="col-md-4">
                            <label class="filter-label">{l s='Filtrovat podle hodnocení:' mod='mezistranka_hodnoceni'}</label>
                            <select class="form-control rating-filter stats-rating-filter">
                                <option value="all">{l s='Všechna hodnocení' mod='mezistranka_hodnoceni'}</option>
                                <option value="5">★★★★★ (5/5)</option>
                                <option value="4">★★★★☆ (4/5)</option>
                                <option value="3">★★★☆☆ (3/5)</option>
                                <option value="2">★★☆☆☆ (2/5)</option>
                                <option value="1">★☆☆☆☆ (1/5)</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <div class="table-responsive stats-table-responsive">
                    <table class="table table-striped stats-table stats-full-detail-table">
                        <thead>
                            <tr>
                                <th>{l s='ID' mod='mezistranka_hodnoceni'}</th>
                                <th>{l s='Hodnocení' mod='mezistranka_hodnoceni'}</th>
                                <th>{l s='Odkaz' mod='mezistranka_hodnoceni'}</th>
                                <th>{l s='Zákazník' mod='mezistranka_hodnoceni'}</th>
                                <th>{l s='IP adresa' mod='mezistranka_hodnoceni'}</th>
                                <th>{l s='Datum' mod='mezistranka_hodnoceni'}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {foreach from=$statistics item=stat}
                                <tr class="stat-row" data-rating="{$stat.rating}">
                                    <td>{$stat.id_stat}</td>
                                    <td class="rating-stars-table stats-rating-stars-table">
                                        {for $i=1 to 5}
                                            {if $i <= $stat.rating}<i class="icon icon-star"></i>{else}<i class="icon icon-star-o"></i>{/if}
                                        {/for}
                                        <span class="ml-1">({$stat.rating}/5)</span>
                                    </td>
                                    <td>{if $stat.clicked_link}{$stat.clicked_link}{else}<em>{l s='Žádný' mod='mezistranka_hodnoceni'}</em>{/if}</td>
                                    <td>
                                        {if $stat.id_customer}
                                            <a href="{$link->getAdminLink('AdminCustomers')|escape:'html':'UTF-8'}&id_customer={$stat.id_customer}&viewcustomer" target="_blank">
                                                {$stat.firstname} {$stat.lastname} ({$stat.customer_email})
                                            </a>
                                        {else}
                                            <em>{l s='Nepřihlášený' mod='mezistranka_hodnoceni'}</em>
                                        {/if}
                                    </td>
                                    <td>{$stat.ip_address}</td>
                                    <td>{$stat.date_add}</td>
                                </tr>
                            {/foreach}
                        </tbody>
                    </table>
                </div>
            </div>
        {/if}
    </div>
</div>

{* Modal pro potvrzení smazání všech recenzí *}
<div class="modal fade" id="deleteConfirmModal" tabindex="-1" role="dialog" aria-labelledby="deleteConfirmModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Zavřít">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="deleteConfirmModalLabel">
                    <i class="icon icon-exclamation-triangle"></i> {l s='Potvrzení smazání' mod='mezistranka_hodnoceni'}
                </h4>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <strong>{l s='Varování!' mod='mezistranka_hodnoceni'}</strong>
                    <p>{l s='Chystáte se smazat VŠECHNY recenze zákazníků. Tato akce je nevratná!' mod='mezistranka_hodnoceni'}</p>
                    <p><strong>{l s='Celkem recenzí k smazání:' mod='mezistranka_hodnoceni'} <span id="total-ratings-count">{$total_ratings|default:0}</span></strong></p>
                </div>
                <div class="form-group">
                    <label for="confirm-delete-text">{l s='Pro potvrzení napište "SMAZAT":' mod='mezistranka_hodnoceni'}</label>
                    <input type="text" class="form-control" id="confirm-delete-text" placeholder="SMAZAT">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">
                    <i class="icon icon-times"></i> {l s='Zrušit' mod='mezistranka_hodnoceni'}
                </button>
                <button type="button" class="btn btn-danger" id="confirm-delete-btn" disabled>
                    <i class="icon icon-trash"></i> {l s='Smazat všechny recenze' mod='mezistranka_hodnoceni'}
                </button>
            </div>
        </div>
    </div>
</div>

{* JavaScript pro kontrolu smazání *}
<script>
$(document).ready(function() {
    var confirmText = 'SMAZAT';
    var $confirmInput = $('#confirm-delete-text');
    var $confirmBtn = $('#confirm-delete-btn');
    
    // Kontrola textu pro potvrzení
    $confirmInput.on('input', function() {
        if ($(this).val() === confirmText) {
            $confirmBtn.prop('disabled', false).removeClass('btn-default').addClass('btn-danger');
        } else {
            $confirmBtn.prop('disabled', true).removeClass('btn-danger').addClass('btn-default');
        }
    });
    
    // Akce pro smazání
    $confirmBtn.on('click', function() {
        if ($confirmInput.val() === confirmText) {
            // Zobrazení loading stavu
            $confirmBtn.prop('disabled', true).html('<i class="icon icon-spinner icon-spin"></i> Mažu...');
            
            // AJAX požadavek na smazání
            $.ajax({
                url: '{$delete_ratings_url|escape:"javascript":"UTF-8"}',
                type: 'POST',
                data: {
                    action: 'delete_all_ratings',
                    confirm: 'yes'
                },
                success: function(response) {
                    if (response.success) {
                        // Úspěšné smazání
                        $('#deleteConfirmModal').modal('hide');
                        location.reload();
                    } else {
                        // Chyba
                        alert('Chyba při mazání: ' + (response.message || 'Neznámá chyba'));
                        $confirmBtn.prop('disabled', false).html('<i class="icon icon-trash"></i> Smazat všechny recenze');
                    }
                },
                error: function() {
                    alert('Chyba při komunikaci se serverem');
                    $confirmBtn.prop('disabled', false).html('<i class="icon icon-trash"></i> Smazat všechny recenze');
                }
            });
        }
    });
    
    // Reset při zavření modalu
    $('#deleteConfirmModal').on('hidden.bs.modal', function() {
        $confirmInput.val('');
        $confirmBtn.prop('disabled', true).removeClass('btn-danger').addClass('btn-default')
            .html('<i class="icon icon-trash"></i> Smazat všechny recenze');
    });
});
</script>