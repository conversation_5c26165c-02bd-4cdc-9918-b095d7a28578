<?php
if (!defined('_PS_VERSION_')) {
    exit;
}

class Mezistranka_Hodnoceni extends Module
{
    public function __construct()
    {
        $this->name = 'mezistranka_hodnoceni';
        $this->tab = 'front_office_features';
        $this->version = '1.0.1';
        $this->author = 'ChatGPT';
        $this->need_instance = 0;
        $this->bootstrap = true;
        $this->ps_versions_compliancy = ['min' => '1.7', 'max' => '8.99.99'];

        parent::__construct();

        $this->displayName = $this->l('Mezistranka hodnoceni');
        $this->description = $this->l('Vlastni mezistranka pro filtrovani hodnoceni zakazniku.');
    }

    public function install()
    {
        // Vytvoření tabulky pro statistiky hodnocení
        $sql = "CREATE TABLE IF NOT EXISTS `" . _DB_PREFIX_ . "mezistranka_hodnoceni_stats` (
            `id_stat` int(11) NOT NULL AUTO_INCREMENT,
            `rating` int(1) NOT NULL,
            `clicked_link` varchar(255) DEFAULT NULL,
            `id_customer` int(11) DEFAULT NULL,
            `customer_email` varchar(255) DEFAULT NULL,
            `ip_address` varchar(45) DEFAULT NULL,
            `user_agent` varchar(255) DEFAULT NULL,
            `date_add` datetime NOT NULL,
            PRIMARY KEY (`id_stat`)
        ) ENGINE=" . _MYSQL_ENGINE_ . " DEFAULT CHARSET=utf8;";

        if (!Db::getInstance()->execute($sql)) {
            return false;
        }

        // Přidání výchozích hodnot konfigurace
    Configuration::updateValue('MEZISTRANKAHODNOCENI_TITLE', 'Jak jste spokojeni s našimi službami?');
    Configuration::updateValue('MEZISTRANKAHODNOCENI_POSITIVE_TITLE', 'Děkujeme za vaše pozitivní hodnocení!');
    Configuration::updateValue('MEZISTRANKAHODNOCENI_POSITIVE_TEXT', 'Moc nás těší, že jste spokojeni s našimi službami. Pomohli byste nám sdílet vaši dobrou zkušenost i na jiných platformách?');
    Configuration::updateValue('MEZISTRANKAHODNOCENI_NEGATIVE_TITLE', 'Mrzí nás, že nejste spokojeni');
    Configuration::updateValue('MEZISTRANKAHODNOCENI_NEGATIVE_TEXT', 'Vaše zpětná vazba je pro nás velmi důležitá. Rádi bychom věděli, jak můžeme naše služby zlepšit. Prosím, kontaktujte nás přímo a pomůžeme najít řešení.');
    Configuration::updateValue('MEZISTRANKAHODNOCENI_LINK_GOOGLE', 'https://www.google.com/search?q=moje+firma');
    Configuration::updateValue('MEZISTRANKAHODNOCENI_LINK_SEZNAM', 'https://www.firmy.cz/firma/moje-firma');
    Configuration::updateValue('MEZISTRANKAHODNOCENI_LINK_HEUREKA', 'https://obchody.heureka.cz/moje-firma/recenze');
    Configuration::updateValue('MEZISTRANKAHODNOCENI_PHONE', '+420 123 456 789');
    Configuration::updateValue('MEZISTRANKAHODNOCENI_EMAIL', '<EMAIL>');

        // V případě, že se instaluje poprvé, aktualizujeme všechny existující CMS stránky
        $this->updateAllCmsWithShortcode();

        // Vytvoření admin tabu v sekci "Vlastní moduly"
        $this->createTab();

        return parent::install() &&
               $this->registerHook('displayHeader') &&
               $this->registerHook('actionFrontControllerSetMedia') &&
               $this->registerHook('moduleRoutes') &&
               $this->registerHook('displayBackOfficeHeader') &&
               $this->registerHook('actionAdminControllerSetMedia') &&
               $this->registerHook('actionCmsObjectSave') &&
               $this->registerHook('actionObjectCmsUpdateAfter') &&
               $this->registerHook('displayFooter') &&
               // Nový hook pro filtrování obsahu CMS stránek
               $this->registerHook('filterCmsContent') &&
               $this->registerHook('displayContent');
    }

    /**
     * Hook volaný po každém uložení CMS stránky v administraci
     */
    public function hookActionCmsObjectSave($params)
{
    if (isset($params['object']) && $params['object'] instanceof CMS) {
        // Okamžitá aktualizace obsahu
        $cms = $params['object'];
        $languages = Language::getLanguages(false);
        
        foreach ($languages as $lang) {
            $id_lang = (int)$lang['id_lang'];
            
            if (isset($cms->content[$id_lang]) && strpos($cms->content[$id_lang], '{mezistrankahodnoceni}') !== false) {
                $ratingHtml = $this->getRatingContent();
                $content = str_replace('{mezistrankahodnoceni}', $ratingHtml, $cms->content[$id_lang]);
                
                // Aktualizace obsahu přímým SQL dotazem pro okamžitou změnu
                $sql = "UPDATE `" . _DB_PREFIX_ . "cms_lang` 
                        SET `content` = '" . pSQL($content, true) . "' 
                        WHERE `id_cms` = " . (int)$cms->id . " 
                        AND `id_lang` = " . $id_lang;
                
                Db::getInstance()->execute($sql);
                
                // Vyčištění cache
                if (method_exists('Tools', 'clearSmartyCache')) {
                    Tools::clearSmartyCache();
                }
                if (method_exists('Tools', 'clearCache')) {
                    Tools::clearCache();
                }
            }
        }
    }
    
    return true;
}
    
    /**
     * Hook volaný po každé aktualizaci CMS stránky
     */
    public function hookActionObjectCmsUpdateAfter($params)
    {
    if (isset($params['object']) && $params['object'] instanceof CMS) {
        // Okamžitá aktualizace obsahu
        $cms = $params['object'];
        $languages = Language::getLanguages(false);
        
        foreach ($languages as $lang) {
            $id_lang = (int)$lang['id_lang'];
            
            if (isset($cms->content[$id_lang]) && strpos($cms->content[$id_lang], '{mezistrankahodnoceni}') !== false) {
                $ratingHtml = $this->getRatingContent();
                $content = str_replace('{mezistrankahodnoceni}', $ratingHtml, $cms->content[$id_lang]);
                
                // Aktualizace obsahu přímým SQL dotazem pro okamžitou změnu
                $sql = "UPDATE `" . _DB_PREFIX_ . "cms_lang` 
                        SET `content` = '" . pSQL($content, true) . "' 
                        WHERE `id_cms` = " . (int)$cms->id . " 
                        AND `id_lang` = " . $id_lang;
                
                Db::getInstance()->execute($sql);
                
                // Vyčištění cache
                if (method_exists('Tools', 'clearSmartyCache')) {
                    Tools::clearSmartyCache();
                }
                if (method_exists('Tools', 'clearCache')) {
                    Tools::clearCache();
                }
            }
        }
    }
    
    return true;
}
    
    /**
     * Zpracuje shortcode v CMS stránce a uloží upravenou verzi do databáze
     */
    private function processShortcodeInCms($params)
    {
        // Získání HTML obsahu formuláře hodnocení
        $ratingHtml = $this->getRatingContent();
        $ratingHtml = addslashes($ratingHtml); // Escapování pro SQL
        
        // Aktualizace všech CMS stránek, které obsahují shortcode
        $sql = "UPDATE `" . _DB_PREFIX_ . "cms_lang` 
                SET `content` = REPLACE(`content`, '{mezistrankahodnoceni}', '" . $ratingHtml . "')
                WHERE `content` LIKE '%{mezistrankahodnoceni}%'";
        
        Db::getInstance()->execute($sql);
        
        // Vyčištění cache
        if (method_exists('Tools', 'clearAllCache')) {
            Tools::clearAllCache();
        }
        
        return true;
    }

    /**
     * Aktualizace všech CMS stránek se shortcodem
     */
    public function updateAllCmsWithShortcode()
    {
        // Získání HTML obsahu formuláře hodnocení
        $ratingHtml = $this->getRatingContent();
        $ratingHtml = addslashes($ratingHtml); // Escapování pro SQL
        
        // Aktualizace všech CMS stránek, které obsahují shortcode
        $sql = "UPDATE `" . _DB_PREFIX_ . "cms_lang` 
                SET `content` = REPLACE(`content`, '{mezistrankahodnoceni}', '" . $ratingHtml . "')
                WHERE `content` LIKE '%{mezistrankahodnoceni}%'";
        
        $result = Db::getInstance()->execute($sql);
        
        // Vyčištění cache
        if (method_exists('Tools', 'clearAllCache')) {
            Tools::clearAllCache();
        }
        
        return $result;
    }

    /**
     * Získá obsah hodnocení
     */
    public function getRatingContent()
{
    // Načtení konfiguračních hodnot
    $this->context->smarty->assign([
        'page_title' => $this->l('Ohodnoťte nás'),
        'module_dir' => $this->_path,
        'hodnoceni_title' => Configuration::get('MEZISTRANKAHODNOCENI_TITLE'),
        'hodnoceni_positive_title' => Configuration::get('MEZISTRANKAHODNOCENI_POSITIVE_TITLE'),
        'hodnoceni_positive_text' => Configuration::get('MEZISTRANKAHODNOCENI_POSITIVE_TEXT'),
        'hodnoceni_negative_title' => Configuration::get('MEZISTRANKAHODNOCENI_NEGATIVE_TITLE'),
        'hodnoceni_negative_text' => Configuration::get('MEZISTRANKAHODNOCENI_NEGATIVE_TEXT'),
        'hodnoceni_link_google' => html_entity_decode(Configuration::get('MEZISTRANKAHODNOCENI_LINK_GOOGLE')),
        'hodnoceni_link_seznam' => html_entity_decode(Configuration::get('MEZISTRANKAHODNOCENI_LINK_SEZNAM')),
        'hodnoceni_link_heureka' => html_entity_decode(Configuration::get('MEZISTRANKAHODNOCENI_LINK_HEUREKA')),
        'hodnoceni_phone' => Configuration::get('MEZISTRANKAHODNOCENI_PHONE'),
        'hodnoceni_email' => Configuration::get('MEZISTRANKAHODNOCENI_EMAIL')
    ]);
    
    // Získání HTML obsahu formuláře hodnocení
    return $this->fetch('module:' . $this->name . '/views/templates/front/rating_content.tpl');
}

    /**
     * Nový hook pro filtrování obsahu CMS stránek - doplňuje dynamické nahrazení
     */
    public function hookFilterCmsContent($params)
    {
        if (isset($params['object']) && $params['object'] instanceof CMSCore) {
            $content = $params['object']->content;
            
            // Pokud obsahuje shortcode, nahradíme ho
            if (strpos($content, '{mezistrankahodnoceni}') !== false) {
                $ratingHtml = $this->getRatingContent();
                $content = str_replace('{mezistrankahodnoceni}', $ratingHtml, $content);
                $params['object']->content = $content;
            }
        }
        
        return $params;
    }
    
    /**
     * Hook pro filtrování obsahu - univerzální řešení
     */
    public function hookDisplayContent($params)
    {
        if (isset($params['content'])) {
            $content = $params['content'];
            
            // Pokud obsahuje shortcode, nahradíme ho
            if (strpos($content, '{mezistrankahodnoceni}') !== false) {
                $ratingHtml = $this->getRatingContent();
                $content = str_replace('{mezistrankahodnoceni}', $ratingHtml, $content);
                $params['content'] = $content;
            }
        }
        
        return $params;
    }

    public function hookDisplayHeader()
{
    // Registrace CSS a JS
    $this->context->controller->registerStylesheet(
        'module-mezistranka-css',
        'modules/'.$this->name.'/views/css/mezistranka_hodnoceni.css',
        ['media' => 'all', 'priority' => 150]
    );
    $this->context->controller->registerJavascript(
        'module-mezistranka-js',
        'modules/'.$this->name.'/views/js/mezistranka_hodnoceni.js',
        ['position' => 'bottom', 'priority' => 150]
    );
    
    // Dynamické nahrazení shortcode na všech stránkách
    if ($this->context->controller instanceof CmsController) {
        $id_cms = (int)Tools::getValue('id_cms');
        if ($id_cms > 0) {
            $sql = "SELECT `content` FROM `" . _DB_PREFIX_ . "cms_lang` 
                    WHERE `id_cms` = " . $id_cms . " 
                    AND `id_lang` = " . (int)$this->context->language->id;
            
            $content = Db::getInstance()->getValue($sql);
            
            if (strpos($content, '{mezistrankahodnoceni}') !== false) {
                $ratingHtml = $this->getRatingContent();
                $updated_content = str_replace('{mezistrankahodnoceni}', $ratingHtml, $content);
                
                $sql = "UPDATE `" . _DB_PREFIX_ . "cms_lang` 
                        SET `content` = '" . pSQL($updated_content, true) . "' 
                        WHERE `id_cms` = " . $id_cms . " 
                        AND `id_lang` = " . (int)$this->context->language->id;
                
                Db::getInstance()->execute($sql);
            }
        }
    }
    
    // Přidání JavaScript pro inicializaci na všech stránkách
    return '<script>
        document.addEventListener("DOMContentLoaded", function() {
            console.log("Mezistranka hodnoceni - DOM načten");
            
            // Kontrola, zda existuje starContainer
            var starContainer = document.getElementById("starContainer");
            if (starContainer) {
                console.log("✅ StarContainer nalezen, spouštím inicializaci");
                
                // Spuštění inicializace s malým zpožděním
                setTimeout(function() {
                    if (typeof initializeRatingModule === "function") {
                        try {
                            initializeRatingModule();
                            console.log("✅ Inicializace spuštěna");
                        } catch (e) {
                            console.error("❌ Chyba při inicializaci:", e);
                        }
                    } else {
                        console.error("❌ Funkce initializeRatingModule není dostupná");
                    }
                }, 500);
            } else {
                console.log("ℹ️ StarContainer nenalezen - možná není na této stránce");
            }
        });
    </script>';
}
    
    public function hookModuleRoutes()
    {
        return [
            'module-mezistranka_hodnoceni-rating' => [
                'controller' => 'rating',
                'rule' => 'ohodnoceni',
                'keywords' => [],
                'params' => [
                    'fc' => 'module',
                    'module' => 'mezistranka_hodnoceni',
                    'controller' => 'rating',
                ]
            ]
        ];
    }

    public function hookActionFrontControllerSetMedia()
    {
        // Vždy načíst CSS a JS pro hodnocení (podmínku lze přidat později)
        $this->context->controller->registerStylesheet(
            'module-mezistranka-css',
            'modules/'.$this->name.'/views/css/mezistranka_hodnoceni.css',
            ['media' => 'all', 'priority' => 150]
        );
        $this->context->controller->registerJavascript(
            'module-mezistranka-js',
            'modules/'.$this->name.'/views/js/mezistranka_hodnoceni.js',
            ['position' => 'bottom', 'priority' => 150]
        );
    }

    /**
     * Hook pro admin CSS a JS
     */
    public function hookDisplayBackOfficeHeader()
    {
        if (Tools::getValue('configure') === $this->name) {
            $adminCssUri = __PS_BASE_URI__ . 'modules/' . $this->name . '/views/css/admin.css';
            $this->context->controller->addCSS($adminCssUri);
            $this->context->controller->addJS($this->_path . 'views/js/admin.js');
        }
    }

    public function hookActionAdminControllerSetMedia()
    {
        if (Tools::getValue('configure') === $this->name) {
            $this->context->controller->addCSS($this->_path . 'views/css/admin.css');
        }
    }
    
    /**
     * Hook pro footer - záložní řešení pro případ, že by hooky pro CMS nefungovaly
     * Vylepšená verze s dynamickým nahrazením
     */
    public function hookDisplayFooter()
    {
        // Nejprve ověříme, zda jsme na CMS stránce
        if ($this->context->controller instanceof CmsController) {
            $cms_id = (int)Tools::getValue('id_cms');
            
            if ($cms_id > 0) {
                // Získáme obsah aktuální stránky
                $sql = "SELECT `content` FROM `" . _DB_PREFIX_ . "cms_lang` 
                        WHERE `id_cms` = " . $cms_id . "
                        AND `id_lang` = " . (int)$this->context->language->id;
                
                $content = Db::getInstance()->getValue($sql);
                
                // Pokud stránka obsahuje shortcode, vložíme JS pro nahrazení
                if (strpos($content, '{mezistrankahodnoceni}') !== false) {
                    // Načtení formuláře hodnocení
                    $ratingHtml = $this->getRatingContent();
                    
                    return '<script>
                        document.addEventListener("DOMContentLoaded", function() {
                            // Najděte všechny výskyty textu {mezistrankahodnoceni} v obsahu
                            var elements = document.querySelectorAll(".cms-content, .page-content, #cms, #content");
                            
                            elements.forEach(function(element) {
                                if (element && element.innerHTML && element.innerHTML.includes("{mezistrankahodnoceni}")) {
                                    // Nahradit shortcode obsahem komponenty
                                    element.innerHTML = element.innerHTML.replace(/{mezistrankahodnoceni}/g, ' . json_encode($ratingHtml) . ');
                                    
                                    // Inicializace modulu po nahrazení
                                    if (typeof initializeRatingModule === "function") {
                                        setTimeout(function() {
                                            initializeRatingModule();
                                        }, 100);
                                    }
                                }
                            });
                        });
                    </script>';
                }
            }
        }
        
        return '';
    }

    /**
     * Zobrazení obsahu administrace modulu
     */
    public function getContent()
    {
        $output = '';

        // Přidejte tuto část pro zpracování konfiguračních hodnot
    if (Tools::isSubmit('submit'.$this->name)) {
        // Kontrola a uložení hodnot konfigurace
        Configuration::updateValue('MEZISTRANKAHODNOCENI_TITLE', Tools::getValue('MEZISTRANKAHODNOCENI_TITLE'));
        Configuration::updateValue('MEZISTRANKAHODNOCENI_POSITIVE_TITLE', Tools::getValue('MEZISTRANKAHODNOCENI_POSITIVE_TITLE'));
        Configuration::updateValue('MEZISTRANKAHODNOCENI_POSITIVE_TEXT', Tools::getValue('MEZISTRANKAHODNOCENI_POSITIVE_TEXT'));
        Configuration::updateValue('MEZISTRANKAHODNOCENI_NEGATIVE_TITLE', Tools::getValue('MEZISTRANKAHODNOCENI_NEGATIVE_TITLE'));
        Configuration::updateValue('MEZISTRANKAHODNOCENI_NEGATIVE_TEXT', Tools::getValue('MEZISTRANKAHODNOCENI_NEGATIVE_TEXT'));
        Configuration::updateValue('MEZISTRANKAHODNOCENI_LINK_GOOGLE', Tools::getValue('MEZISTRANKAHODNOCENI_LINK_GOOGLE'));
        Configuration::updateValue('MEZISTRANKAHODNOCENI_LINK_SEZNAM', Tools::getValue('MEZISTRANKAHODNOCENI_LINK_SEZNAM'));
        Configuration::updateValue('MEZISTRANKAHODNOCENI_LINK_HEUREKA', Tools::getValue('MEZISTRANKAHODNOCENI_LINK_HEUREKA'));
        Configuration::updateValue('MEZISTRANKAHODNOCENI_PHONE', Tools::getValue('MEZISTRANKAHODNOCENI_PHONE'));
        Configuration::updateValue('MEZISTRANKAHODNOCENI_EMAIL', Tools::getValue('MEZISTRANKAHODNOCENI_EMAIL'));

        // Aktualizace všech stránek se shortcodem po změně nastavení
        $this->updateAllCmsWithShortcode();

        $output .= $this->displayConfirmation($this->l('Nastavení bylo úspěšně uloženo a všechny stránky se shortcodem byly aktualizovány.'));
    }

        // Pokud bylo posláno tlačítko pro aktualizaci všech stránek s shortcodem
        if (Tools::isSubmit('update_shortcodes')) {
            if ($this->updateAllCmsWithShortcode()) {
                $output .= $this->displayConfirmation($this->l('Všechny stránky se shortcodem byly aktualizovány.'));
            } else {
                $output .= $this->displayError($this->l('Nastala chyba při aktualizaci stránek.'));
            }
        }

        // Aktualizace tlačítko pro manuální spuštění
        $current_controller = Tools::getValue('controller');
        if ($current_controller == 'AdminMezistrankaHodnoceni') {
            $update_shortcode_btn = $this->context->link->getAdminLink('AdminMezistrankaHodnoceni') . '&update_shortcodes=1';
        } else {
            $update_shortcode_btn = $this->context->link->getAdminLink('AdminModules') . '&configure=' . $this->name . '&update_shortcodes=1';
        }
        $this->context->smarty->assign('update_shortcode_btn', $update_shortcode_btn);

        // Zpracování AJAX požadavků
        if (Tools::isSubmit('ajax') && Tools::getValue('ajax') == 1) {
            $this->processAjaxRequest();
            exit;
        }

        // Načtení CSS přímo do proměnné
        $adminCss = '';
        $cssFile = dirname(__FILE__) . '/views/css/admin.css';
        if (file_exists($cssFile)) {
            $adminCss = file_get_contents($cssFile);
        }

        // Přiřazení CSS do šablony
        $current_controller = Tools::getValue('controller');
        if ($current_controller == 'AdminMezistrankaHodnoceni') {
            $base_url = $this->context->link->getAdminLink('AdminMezistrankaHodnoceni');
        } else {
            $base_url = $this->context->link->getAdminLink('AdminModules') . '&configure=' . $this->name;
        }

        $this->context->smarty->assign([
            'module_dir' => $this->_path,
            'current_tab' => Tools::isSubmit('statistics_tab') ? 'statistics' : 'configuration',
            'statistics' => $this->getStatistics(),
            'ps_version' => _PS_VERSION_,
            'export_url' => $base_url . '&export_stats=1',
            'config_url' => $base_url,
            'current_url' => $base_url,
            'name' => $this->name,
            'delete_ratings_url' => $base_url . '&ajax=1',
            'statistics_url' => $base_url . '&statistics_tab=1',
            'admin_css' => $adminCss, // Přidáme CSS jako proměnnou
        ]);

        // Zpracování exportu statistik
        if (Tools::isSubmit('export_stats')) {
            $this->exportStatisticsCSV();
        }

        // Zjištění aktuální záložky
        $current_tab = 'configuration';
        if (Tools::isSubmit('statistics_tab')) {
            $current_tab = 'statistics';
        }

        // Zpracování statistik
        $statistics = [];
        $statSummary = [];

        if ($current_tab == 'statistics') {
            // Načtení statistik
            $statistics = $this->getStatistics();

            // Příprava souhrnných statistik
            $total_ratings = count($statistics);
            $sum_ratings = 0;
            $positive_count = 0;
            $negative_count = 0;
            $rating_distribution = [
                1 => ['count' => 0, 'percent' => 0],
                2 => ['count' => 0, 'percent' => 0],
                3 => ['count' => 0, 'percent' => 0],
                4 => ['count' => 0, 'percent' => 0],
                5 => ['count' => 0, 'percent' => 0],
            ];
            $link_clicks = [];

            // Zpracování dat
            foreach ($statistics as $stat) {
                $rating = (int)$stat['rating'];
                $sum_ratings += $rating;

                // Počítání pozitivních a negativních hodnocení
                if ($rating >= 4) {
                    $positive_count++;
                } else {
                    $negative_count++;
                }

                // Distribuce hodnocení
                if (isset($rating_distribution[$rating])) {
                    $rating_distribution[$rating]['count']++;
                }

                // Počítání kliknutí na odkazy
                if (!empty($stat['clicked_link'])) {
                    $link = $stat['clicked_link'];
                    if (!isset($link_clicks[$link])) {
                        $link_clicks[$link] = ['count' => 0, 'percent' => 0];
                    }
                    $link_clicks[$link]['count']++;
                }
            }

            // Výpočet procent pro distribuci hodnocení
            if ($total_ratings > 0) {
                foreach ($rating_distribution as $rating => &$data) {
                    $data['percent'] = ($data['count'] / $total_ratings) * 100;
                }

                // Procenta pro pozitivní a negativní hodnocení
                $positive_percent = ($positive_count / $total_ratings) * 100;
                $negative_percent = ($negative_count / $total_ratings) * 100;

                // Procenta pro konverze na odkazy
                foreach ($link_clicks as $link => &$data) {
                    $data['percent'] = ($data['count'] / $total_ratings) * 100;
                }
            } else {
                $positive_percent = 0;
                $negative_percent = 0;
            }

            // Průměrné hodnocení
            $average_rating = $total_ratings > 0 ? $sum_ratings / $total_ratings : 0;

            // Příprava souhrnných statistik pro šablonu
            $statSummary = [
                'total_ratings' => $total_ratings,
                'average_rating' => $average_rating,
                'positive_count' => $positive_count,
                'positive_percent' => $positive_percent,
                'negative_count' => $negative_count,
                'negative_percent' => $negative_percent,
                'rating_distribution' => $rating_distribution,
                'link_clicks' => $link_clicks,
            ];
        }

        // Přiřazení proměnných do šablony
        $this->context->smarty->assign([
            'module_dir' => $this->_path,
            'current_tab' => $current_tab,
            'statistics' => $statistics,
            'ps_version' => _PS_VERSION_,
            'export_url' => $base_url . '&export_stats=1',
            'config_url' => $base_url,
            'statistics_url' => $base_url . '&statistics_tab=1',
        ]);

        // Přidání statistických dat, pokud jsou k dispozici
        if (!empty($statSummary)) {
            $this->context->smarty->assign($statSummary);
        }

// Přidání konfiguračních hodnot do šablony
$this->context->smarty->assign([
    'MEZISTRANKAHODNOCENI_TITLE' => Configuration::get('MEZISTRANKAHODNOCENI_TITLE'),
    'MEZISTRANKAHODNOCENI_POSITIVE_TITLE' => Configuration::get('MEZISTRANKAHODNOCENI_POSITIVE_TITLE'),
    'MEZISTRANKAHODNOCENI_POSITIVE_TEXT' => Configuration::get('MEZISTRANKAHODNOCENI_POSITIVE_TEXT'),
    'MEZISTRANKAHODNOCENI_NEGATIVE_TITLE' => Configuration::get('MEZISTRANKAHODNOCENI_NEGATIVE_TITLE'),
    'MEZISTRANKAHODNOCENI_NEGATIVE_TEXT' => Configuration::get('MEZISTRANKAHODNOCENI_NEGATIVE_TEXT'),
    'MEZISTRANKAHODNOCENI_LINK_GOOGLE' => Configuration::get('MEZISTRANKAHODNOCENI_LINK_GOOGLE'),
    'MEZISTRANKAHODNOCENI_LINK_SEZNAM' => Configuration::get('MEZISTRANKAHODNOCENI_LINK_SEZNAM'),
    'MEZISTRANKAHODNOCENI_LINK_HEUREKA' => Configuration::get('MEZISTRANKAHODNOCENI_LINK_HEUREKA'),
    'MEZISTRANKAHODNOCENI_PHONE' => Configuration::get('MEZISTRANKAHODNOCENI_PHONE'),
    'MEZISTRANKAHODNOCENI_EMAIL' => Configuration::get('MEZISTRANKAHODNOCENI_EMAIL')
]);

        // Zobrazení šablony
        return $output . $this->display(__FILE__, 'views/templates/admin/configure.tpl');
    }

    /**
     * Získá statistiky hodnocení z databáze
     */
    private function getStatistics()
    {
        $sql = "SELECT s.*, COALESCE(c.firstname, '') as firstname, COALESCE(c.lastname, '') as lastname
                FROM `" . _DB_PREFIX_ . "mezistranka_hodnoceni_stats` s
                LEFT JOIN `" . _DB_PREFIX_ . "customer` c ON (s.id_customer = c.id_customer)
                ORDER BY s.date_add DESC";

        return Db::getInstance()->executeS($sql);
    }

    /**
     * Zpracování AJAX požadavků
     */
    private function processAjaxRequest()
    {
        $action = Tools::getValue('action');

        switch ($action) {
            case 'delete_all_ratings':
                $this->deleteAllRatings();
                break;
            default:
                $this->sendAjaxResponse(['success' => false, 'message' => 'Neznámá akce']);
                break;
        }
    }

    /**
     * Smazání všech recenzí
     */
    private function deleteAllRatings()
    {
        // Kontrola potvrzení
        if (Tools::getValue('confirm') !== 'yes') {
            $this->sendAjaxResponse(['success' => false, 'message' => 'Chybí potvrzení']);
            return;
        }

        try {
            // Počet recenzí před smazáním
            $count_before = Db::getInstance()->getValue("SELECT COUNT(*) FROM `" . _DB_PREFIX_ . "mezistranka_hodnoceni_stats`");

            // Smazání všech recenzí
            $result = Db::getInstance()->execute("DELETE FROM `" . _DB_PREFIX_ . "mezistranka_hodnoceni_stats`");

            if ($result) {
                $this->sendAjaxResponse([
                    'success' => true,
                    'message' => "Úspěšně smazáno $count_before recenzí"
                ]);
            } else {
                $this->sendAjaxResponse(['success' => false, 'message' => 'Chyba při mazání z databáze']);
            }
        } catch (Exception $e) {
            $this->sendAjaxResponse(['success' => false, 'message' => 'Chyba: ' . $e->getMessage()]);
        }
    }

    /**
     * Odeslání AJAX odpovědi
     */
    private function sendAjaxResponse($data)
    {
        header('Content-Type: application/json');
        echo json_encode($data);
        exit;
    }

    /**
     * Exportuje statistiky hodnocení do CSV souboru
     */
    public function exportStatisticsCSV()
    {
        $stats = $this->getStatistics();

        if (empty($stats)) {
            $this->context->controller->errors[] = $this->l('Nejsou k dispozici žádné statistiky pro export.');
            return false;
        }

        $filename = 'statistiky-hodnoceni-' . date('Y-m-d-H-i-s') . '.csv';

        // Hlavičky pro CSV
        $headers = [
            $this->l('ID'),
            $this->l('Hodnocení (1-5)'),
            $this->l('Odkaz'),
            $this->l('ID zákazníka'),
            $this->l('Email zákazníka'),
            $this->l('IP adresa'),
            $this->l('Prohlížeč'),
            $this->l('Datum a čas')
        ];

        // Příprava dat
        $data = [];
        foreach ($stats as $stat) {
            $data[] = [
                $stat['id_stat'],
                $stat['rating'],
                $stat['clicked_link'] ? $stat['clicked_link'] : $this->l('Žádný'),
                $stat['id_customer'] ? $stat['id_customer'] : $this->l('Nepřihlášený'),
                $stat['customer_email'] ? $stat['customer_email'] : '-',
                $stat['ip_address'],
                $stat['user_agent'],
                $stat['date_add']
            ];
        }

        // Nastavení hlaviček pro stahování
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename=' . $filename);

        // Otevření výstupního streamu
        $output = fopen('php://output', 'w');

        // Výpis BOM pro správné zobrazení diakritiky v Excelu
        fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

        // Výpis hlaviček
        fputcsv($output, $headers, ';');

        // Výpis dat
        foreach ($data as $row) {
            fputcsv($output, $row, ';');
        }

        fclose($output);
        exit;
    }



    public function uninstall()
    {
        // Odstranění admin tabu
        $this->removeTab();

        // Odstranění tabulky statistik při odinstalaci
        // Poznámka: Můžete toto zakomentovat, pokud chcete zachovat data i po odinstalaci
        $sql = "DROP TABLE IF EXISTS `" . _DB_PREFIX_ . "mezistranka_hodnoceni_stats`";
        Db::getInstance()->execute($sql);

        return parent::uninstall();
    }

    /**
     * Vytvoří admin tab v sekci "Vlastní moduly"
     */
    private function createTab()
    {
        require_once(_PS_MODULE_DIR_ . $this->name . '/classes/CustomModulesTabManager.php');
        return CustomModulesTabManager::createModuleTab(
            'AdminMezistrankaHodnoceni',
            'Mezistranka hodnocení',
            $this->name
        );
    }

    /**
     * Odstraní admin tab
     */
    private function removeTab()
    {
        require_once(_PS_MODULE_DIR_ . $this->name . '/classes/CustomModulesTabManager.php');
        return CustomModulesTabManager::removeModuleTab('AdminMezistrankaHodnoceni');
    }
}