<?php
if (!defined('_PS_VERSION_')) {
    exit;
}

class Mezistranka_Hodnoceni extends Module
{
    public function __construct()
    {
        $this->name = 'mezistranka_hodnoceni';
        $this->tab = 'front_office_features';
        $this->version = '1.0.1';
        $this->author = 'ChatGPT';
        $this->need_instance = 0;
        $this->bootstrap = true;
        $this->ps_versions_compliancy = ['min' => '1.7', 'max' => '8.99.99'];

        parent::__construct();

        $this->displayName = $this->l('Mezistranka hodnoceni');
        $this->description = $this->l('Vlastni mezistranka pro filtrovani hodnoceni zakazniku.');
    }

    public function install()
    {
        // Vytvoření tabulky pro statistiky hodnocení
        $sql = "CREATE TABLE IF NOT EXISTS `" . _DB_PREFIX_ . "mezistranka_hodnoceni_stats` (
            `id_stat` int(11) NOT NULL AUTO_INCREMENT,
            `rating` int(1) NOT NULL,
            `clicked_link` varchar(255) DEFAULT NULL,
            `id_customer` int(11) DEFAULT NULL,
            `customer_email` varchar(255) DEFAULT NULL,
            `ip_address` varchar(45) DEFAULT NULL,
            `user_agent` varchar(255) DEFAULT NULL,
            `date_add` datetime NOT NULL,
            PRIMARY KEY (`id_stat`)
        ) ENGINE=" . _MYSQL_ENGINE_ . " DEFAULT CHARSET=utf8;";

        if (!Db::getInstance()->execute($sql)) {
            return false;
        }

        // Přidání výchozích hodnot konfigurace
    Configuration::updateValue('MEZISTRANKAHODNOCENI_TITLE', 'Jak jste spokojeni s našimi službami?');
    Configuration::updateValue('MEZISTRANKAHODNOCENI_POSITIVE_TITLE', 'Děkujeme za vaše pozitivní hodnocení!');
    Configuration::updateValue('MEZISTRANKAHODNOCENI_POSITIVE_TEXT', 'Moc nás těší, že jste spokojeni s našimi službami. Pomohli byste nám sdílet vaši dobrou zkušenost i na jiných platformách?');
    Configuration::updateValue('MEZISTRANKAHODNOCENI_NEGATIVE_TITLE', 'Mrzí nás, že nejste spokojeni');
    Configuration::updateValue('MEZISTRANKAHODNOCENI_NEGATIVE_TEXT', 'Vaše zpětná vazba je pro nás velmi důležitá. Rádi bychom věděli, jak můžeme naše služby zlepšit. Prosím, kontaktujte nás přímo a pomůžeme najít řešení.');
    Configuration::updateValue('MEZISTRANKAHODNOCENI_LINK_GOOGLE', 'https://www.google.com/search?q=moje+firma');
    Configuration::updateValue('MEZISTRANKAHODNOCENI_LINK_SEZNAM', 'https://www.firmy.cz/firma/moje-firma');
    Configuration::updateValue('MEZISTRANKAHODNOCENI_LINK_HEUREKA', 'https://obchody.heureka.cz/moje-firma/recenze');
    Configuration::updateValue('MEZISTRANKAHODNOCENI_PHONE', '+420 123 456 789');
    Configuration::updateValue('MEZISTRANKAHODNOCENI_EMAIL', '<EMAIL>');

        // V případě, že se instaluje poprvé, aktualizujeme všechny existující CMS stránky
        $this->updateAllCmsWithShortcode();

        // Vytvoření admin tabu v sekci "Vlastní moduly"
        $this->createTab();

        return parent::install() &&
               $this->registerHook('displayHeader') &&
               $this->registerHook('actionFrontControllerSetMedia') &&
               $this->registerHook('moduleRoutes') &&
               $this->registerHook('displayBackOfficeHeader') &&
               $this->registerHook('actionAdminControllerSetMedia') &&
               $this->registerHook('actionCmsObjectSave') &&
               $this->registerHook('actionObjectCmsUpdateAfter') &&
               $this->registerHook('displayFooter') &&
               // Nový hook pro filtrování obsahu CMS stránek
               $this->registerHook('filterCmsContent') &&
               $this->registerHook('displayContent');
    }

    /**
     * Hook volaný po každém uložení CMS stránky v administraci
     */
    public function hookActionCmsObjectSave($params)
{
    if (isset($params['object']) && $params['object'] instanceof CMS) {
        // Okamžitá aktualizace obsahu
        $cms = $params['object'];
        $languages = Language::getLanguages(false);
        
        foreach ($languages as $lang) {
            $id_lang = (int)$lang['id_lang'];
            
            if (isset($cms->content[$id_lang]) && strpos($cms->content[$id_lang], '{mezistrankahodnoceni}') !== false) {
                $ratingHtml = $this->getRatingContent();
                $content = str_replace('{mezistrankahodnoceni}', $ratingHtml, $cms->content[$id_lang]);
                
                // Aktualizace obsahu přímým SQL dotazem pro okamžitou změnu
                $sql = "UPDATE `" . _DB_PREFIX_ . "cms_lang` 
                        SET `content` = '" . pSQL($content, true) . "' 
                        WHERE `id_cms` = " . (int)$cms->id . " 
                        AND `id_lang` = " . $id_lang;
                
                Db::getInstance()->execute($sql);
                
                // Vyčištění cache
                if (method_exists('Tools', 'clearSmartyCache')) {
                    Tools::clearSmartyCache();
                }
                if (method_exists('Tools', 'clearCache')) {
                    Tools::clearCache();
                }
            }
        }
    }
    
    return true;
}
    
    /**
     * Hook volaný po každé aktualizaci CMS stránky
     */
    public function hookActionObjectCmsUpdateAfter($params)
    {
    if (isset($params['object']) && $params['object'] instanceof CMS) {
        // Okamžitá aktualizace obsahu
        $cms = $params['object'];
        $languages = Language::getLanguages(false);
        
        foreach ($languages as $lang) {
            $id_lang = (int)$lang['id_lang'];
            
            if (isset($cms->content[$id_lang]) && strpos($cms->content[$id_lang], '{mezistrankahodnoceni}') !== false) {
                $ratingHtml = $this->getRatingContent();
                $content = str_replace('{mezistrankahodnoceni}', $ratingHtml, $cms->content[$id_lang]);
                
                // Aktualizace obsahu přímým SQL dotazem pro okamžitou změnu
                $sql = "UPDATE `" . _DB_PREFIX_ . "cms_lang` 
                        SET `content` = '" . pSQL($content, true) . "' 
                        WHERE `id_cms` = " . (int)$cms->id . " 
                        AND `id_lang` = " . $id_lang;
                
                Db::getInstance()->execute($sql);
                
                // Vyčištění cache
                if (method_exists('Tools', 'clearSmartyCache')) {
                    Tools::clearSmartyCache();
                }
                if (method_exists('Tools', 'clearCache')) {
                    Tools::clearCache();
                }
            }
        }
    }
    
    return true;
}
    
    /**
     * Zpracuje shortcode v CMS stránce a uloží upravenou verzi do databáze
     */
    private function processShortcodeInCms($params)
    {
        // Získání HTML obsahu formuláře hodnocení
        $ratingHtml = $this->getRatingContent();
        $ratingHtml = addslashes($ratingHtml); // Escapování pro SQL
        
        // Aktualizace všech CMS stránek, které obsahují shortcode
        $sql = "UPDATE `" . _DB_PREFIX_ . "cms_lang` 
                SET `content` = REPLACE(`content`, '{mezistrankahodnoceni}', '" . $ratingHtml . "')
                WHERE `content` LIKE '%{mezistrankahodnoceni}%'";
        
        Db::getInstance()->execute($sql);
        
        // Vyčištění cache
        if (method_exists('Tools', 'clearAllCache')) {
            Tools::clearAllCache();
        }
        
        return true;
    }

    /**
     * Aktualizace všech CMS stránek se shortcodem
     */
    public function updateAllCmsWithShortcode()
    {
        // Získání HTML obsahu formuláře hodnocení
        $ratingHtml = $this->getRatingContent();
        $ratingHtml = addslashes($ratingHtml); // Escapování pro SQL
        
        // Aktualizace všech CMS stránek, které obsahují shortcode
        $sql = "UPDATE `" . _DB_PREFIX_ . "cms_lang` 
                SET `content` = REPLACE(`content`, '{mezistrankahodnoceni}', '" . $ratingHtml . "')
                WHERE `content` LIKE '%{mezistrankahodnoceni}%'";
        
        $result = Db::getInstance()->execute($sql);
        
        // Vyčištění cache
        if (method_exists('Tools', 'clearAllCache')) {
            Tools::clearAllCache();
        }
        
        return $result;
    }

    /**
     * Získá obsah hodnocení
     */
    public function getRatingContent()
{
    // Načtení konfiguračních hodnot
    $this->context->smarty->assign([
        'page_title' => $this->l('Ohodnoťte nás'),
        'module_dir' => $this->_path,
        'hodnoceni_title' => Configuration::get('MEZISTRANKAHODNOCENI_TITLE'),
        'hodnoceni_positive_title' => Configuration::get('MEZISTRANKAHODNOCENI_POSITIVE_TITLE'),
        'hodnoceni_positive_text' => Configuration::get('MEZISTRANKAHODNOCENI_POSITIVE_TEXT'),
        'hodnoceni_negative_title' => Configuration::get('MEZISTRANKAHODNOCENI_NEGATIVE_TITLE'),
        'hodnoceni_negative_text' => Configuration::get('MEZISTRANKAHODNOCENI_NEGATIVE_TEXT'),
        'hodnoceni_link_google' => html_entity_decode(Configuration::get('MEZISTRANKAHODNOCENI_LINK_GOOGLE')),
        'hodnoceni_link_seznam' => html_entity_decode(Configuration::get('MEZISTRANKAHODNOCENI_LINK_SEZNAM')),
        'hodnoceni_link_heureka' => html_entity_decode(Configuration::get('MEZISTRANKAHODNOCENI_LINK_HEUREKA')),
        'hodnoceni_phone' => Configuration::get('MEZISTRANKAHODNOCENI_PHONE'),
        'hodnoceni_email' => Configuration::get('MEZISTRANKAHODNOCENI_EMAIL')
    ]);
    
    // Získání HTML obsahu formuláře hodnocení
    return $this->fetch('module:' . $this->name . '/views/templates/front/rating_content.tpl');
}

    /**
     * Nový hook pro filtrování obsahu CMS stránek - doplňuje dynamické nahrazení
     */
    public function hookFilterCmsContent($params)
    {
        if (isset($params['object']) && $params['object'] instanceof CMSCore) {
            $content = $params['object']->content;
            
            // Pokud obsahuje shortcode, nahradíme ho
            if (strpos($content, '{mezistrankahodnoceni}') !== false) {
                $ratingHtml = $this->getRatingContent();
                $content = str_replace('{mezistrankahodnoceni}', $ratingHtml, $content);
                $params['object']->content = $content;
            }
        }
        
        return $params;
    }
    
    /**
     * Hook pro filtrování obsahu - univerzální řešení
     */
    public function hookDisplayContent($params)
    {
        if (isset($params['content'])) {
            $content = $params['content'];
            
            // Pokud obsahuje shortcode, nahradíme ho
            if (strpos($content, '{mezistrankahodnoceni}') !== false) {
                $ratingHtml = $this->getRatingContent();
                $content = str_replace('{mezistrankahodnoceni}', $ratingHtml, $content);
                $params['content'] = $content;
            }
        }
        
        return $params;
    }

    public function hookDisplayHeader()
{
    // Registrace CSS a JS
    $this->context->controller->registerStylesheet(
        'module-mezistranka-css',
        'modules/'.$this->name.'/views/css/mezistranka_hodnoceni.css',
        ['media' => 'all', 'priority' => 150]
    );
    $this->context->controller->registerJavascript(
        'module-mezistranka-js',
        'modules/'.$this->name.'/views/js/mezistranka_hodnoceni.js',
        ['position' => 'bottom', 'priority' => 150]
    );
    
    // Dynamické nahrazení shortcode na všech stránkách
    if ($this->context->controller instanceof CmsController) {
        $id_cms = (int)Tools::getValue('id_cms');
        if ($id_cms > 0) {
            $sql = "SELECT `content` FROM `" . _DB_PREFIX_ . "cms_lang` 
                    WHERE `id_cms` = " . $id_cms . " 
                    AND `id_lang` = " . (int)$this->context->language->id;
            
            $content = Db::getInstance()->getValue($sql);
            
            if (strpos($content, '{mezistrankahodnoceni}') !== false) {
                $ratingHtml = $this->getRatingContent();
                $updated_content = str_replace('{mezistrankahodnoceni}', $ratingHtml, $content);
                
                $sql = "UPDATE `" . _DB_PREFIX_ . "cms_lang` 
                        SET `content` = '" . pSQL($updated_content, true) . "' 
                        WHERE `id_cms` = " . $id_cms . " 
                        AND `id_lang` = " . (int)$this->context->language->id;
                
                Db::getInstance()->execute($sql);
            }
        }
    }
    
    // Přidání JavaScript pro inicializaci na všech stránkách
    return '<script>
        document.addEventListener("DOMContentLoaded", function() {
            console.log("Mezistranka hodnoceni - DOM načten");
            
            // Kontrola, zda existuje starContainer
            var starContainer = document.getElementById("starContainer");
            if (starContainer) {
                console.log("✅ StarContainer nalezen, spouštím inicializaci");
                
                // Spuštění inicializace s malým zpožděním
                setTimeout(function() {
                    if (typeof initializeRatingModule === "function") {
                        try {
                            initializeRatingModule();
                            console.log("✅ Inicializace spuštěna");
                        } catch (e) {
                            console.error("❌ Chyba při inicializaci:", e);
                        }
                    } else {
                        console.error("❌ Funkce initializeRatingModule není dostupná");
                    }
                }, 500);
            } else {
                console.log("ℹ️ StarContainer nenalezen - možná není na této stránce");
            }
        });
    </script>';
}
    
    public function hookModuleRoutes()
    {
        return [
            'module-mezistranka_hodnoceni-rating' => [
                'controller' => 'rating',
                'rule' => 'ohodnoceni',
                'keywords' => [],
                'params' => [
                    'fc' => 'module',
                    'module' => 'mezistranka_hodnoceni',
                    'controller' => 'rating',
                ]
            ]
        ];
    }

    public function hookActionFrontControllerSetMedia()
    {
        // Vždy načíst CSS a JS pro hodnocení (podmínku lze přidat později)
        $this->context->controller->registerStylesheet(
            'module-mezistranka-css',
            'modules/'.$this->name.'/views/css/mezistranka_hodnoceni.css',
            ['media' => 'all', 'priority' => 150]
        );
        $this->context->controller->registerJavascript(
            'module-mezistranka-js',
            'modules/'.$this->name.'/views/js/mezistranka_hodnoceni.js',
            ['position' => 'bottom', 'priority' => 150]
        );
    }

    /**
     * Hook pro admin CSS a JS
     */
    public function hookDisplayBackOfficeHeader()
    {
        if (Tools::getValue('configure') === $this->name) {
            $adminCssUri = __PS_BASE_URI__ . 'modules/' . $this->name . '/views/css/admin.css';
            $this->context->controller->addCSS($adminCssUri);
            $this->context->controller->addJS($this->_path . 'views/js/admin.js');
        }
    }

    public function hookActionAdminControllerSetMedia()
    {
        if (Tools::getValue('configure') === $this->name) {
            $this->context->controller->addCSS($this->_path . 'views/css/admin.css');
        }
    }
    
    /**
     * Hook pro footer - záložní řešení pro případ, že by hooky pro CMS nefungovaly
     * Vylepšená verze s dynamickým nahrazením
     */
    public function hookDisplayFooter()
    {
        // Nejprve ověříme, zda jsme na CMS stránce
        if ($this->context->controller instanceof CmsController) {
            $cms_id = (int)Tools::getValue('id_cms');
            
            if ($cms_id > 0) {
                // Získáme obsah aktuální stránky
                $sql = "SELECT `content` FROM `" . _DB_PREFIX_ . "cms_lang` 
                        WHERE `id_cms` = " . $cms_id . "
                        AND `id_lang` = " . (int)$this->context->language->id;
                
                $content = Db::getInstance()->getValue($sql);
                
                // Pokud stránka obsahuje shortcode, vložíme JS pro nahrazení
                if (strpos($content, '{mezistrankahodnoceni}') !== false) {
                    // Načtení formuláře hodnocení
                    $ratingHtml = $this->getRatingContent();
                    
                    return '<script>
                        document.addEventListener("DOMContentLoaded", function() {
                            // Najděte všechny výskyty textu {mezistrankahodnoceni} v obsahu
                            var elements = document.querySelectorAll(".cms-content, .page-content, #cms, #content");
                            
                            elements.forEach(function(element) {
                                if (element && element.innerHTML && element.innerHTML.includes("{mezistrankahodnoceni}")) {
                                    // Nahradit shortcode obsahem komponenty
                                    element.innerHTML = element.innerHTML.replace(/{mezistrankahodnoceni}/g, ' . json_encode($ratingHtml) . ');
                                    
                                    // Inicializace modulu po nahrazení
                                    if (typeof initializeRatingModule === "function") {
                                        setTimeout(function() {
                                            initializeRatingModule();
                                        }, 100);
                                    }
                                }
                            });
                        });
                    </script>';
                }
            }
        }
        
        return '';
    }

    /**
     * Přesměrování na admin controller místo getContent()
     */
    public function getContent()
    {
        // Přesměrování na nový admin controller
        $admin_link = $this->context->link->getAdminLink('AdminMezistrankaHodnoceni');
        Tools::redirectAdmin($admin_link);
    }



    public function uninstall()
    {
        // Odstranění admin tabu
        $this->removeTab();

        // Odstranění tabulky statistik při odinstalaci
        // Poznámka: Můžete toto zakomentovat, pokud chcete zachovat data i po odinstalaci
        $sql = "DROP TABLE IF EXISTS `" . _DB_PREFIX_ . "mezistranka_hodnoceni_stats`";
        Db::getInstance()->execute($sql);

        return parent::uninstall();
    }

    /**
     * Vytvoří admin tab v sekci "Vlastní moduly"
     */
    private function createTab()
    {
        require_once(_PS_MODULE_DIR_ . $this->name . '/classes/CustomModulesTabManager.php');
        return CustomModulesTabManager::createModuleTab(
            'AdminMezistrankaHodnoceni',
            'Mezistranka hodnocení',
            $this->name
        );
    }

    /**
     * Odstraní admin tab
     */
    private function removeTab()
    {
        require_once(_PS_MODULE_DIR_ . $this->name . '/classes/CustomModulesTabManager.php');
        return CustomModulesTabManager::removeModuleTab('AdminMezistrankaHodnoceni');
    }
}