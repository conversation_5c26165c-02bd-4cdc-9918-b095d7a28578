# Zařazení modulu Mezistranka hodnocení do sekce "Vlastní moduly"

Tento dokument popisuje, jak zařadit modul **Mezistranka hodnocení** do sekce "Vlastní moduly" v administraci PrestaShop.

## Co bylo provedeno

### 1. Přidání CustomModulesTabManager
- Zkopírován soubor `CustomModulesTabManager.php` do složky `classes/`
- Tento soubor spravuje vytváření a správu vlastní sekce modulů

### 2. Vytvoření admin controlleru
- Vytvořen `controllers/admin/AdminMezistrankaHodnoceniController.php`
- Controller poskytuje stejnou funkcionalnost jako původní `getContent()` metoda
- Obsahuje záložky pro Konfiguraci a Statistiky
- Plně funkční administrace s formuláři a přehledy

### 3. Úprava hlavního modulu
- Přidány metody `createTab()` a `removeTab()` pro správu admin tabu
- Metoda `getContent()` nyní přesměrovává na nový admin controller
- Při instalaci se automaticky vytvoří tab v sekci "Vlastní moduly"
- Při odinstalaci se tab automaticky odstraní

### 4. Automatické skripty
- `setup-custom-modules-section.php` - skript pro nastavení modulu do vlastní sekce
- `test-module-setup.php` - test skript pro ověření správného nastavení

## Jak spustit nastavení

### Automatické nastavení (doporučeno)

1. **Spusťte setup skript:**
   ```
   http://vase-domena.cz/modules/mezistranka_hodnoceni/setup-custom-modules-section.php
   ```

2. **Postupujte podle instrukcí na stránce:**
   - Klikněte na "Vytvořit sekci" (pokud sekce neexistuje)
   - Klikněte na "Přesunout do vlastní sekce" (pokud tab není ve správné sekci)

3. **Ověřte nastavení:**
   ```
   http://vase-domena.cz/test-module-setup.php
   ```

### Manuální nastavení

Pokud automatické skripty nefungují, můžete nastavení provést ručně:

1. **Zkontrolujte soubory:**
   - `classes/CustomModulesTabManager.php` - musí existovat
   - `controllers/admin/AdminMezistrankaHodnoceniController.php` - musí existovat

2. **Vytvořte tab ručně v databázi:**
   ```sql
   -- Najděte ID sekce "Vlastní moduly"
   SELECT id_tab FROM ps_tab WHERE class_name = 'AdminCustomModules';
   
   -- Pokud neexistuje, vytvořte ji
   INSERT INTO ps_tab (id_parent, position, module, class_name, active, icon) 
   VALUES (0, 10, '', 'AdminCustomModules', 1, 'extension');
   
   -- Přidejte název sekce
   INSERT INTO ps_tab_lang (id_tab, id_lang, name) 
   VALUES (LAST_INSERT_ID(), 1, 'Vlastní moduly');
   
   -- Vytvořte tab pro modul
   INSERT INTO ps_tab (id_parent, position, module, class_name, active) 
   VALUES ([ID_VLASTNI_MODULY], 1, 'mezistranka_hodnoceni', 'AdminMezistrankaHodnoceni', 1);
   
   -- Přidejte název tabu
   INSERT INTO ps_tab_lang (id_tab, id_lang, name) 
   VALUES (LAST_INSERT_ID(), 1, 'Mezistranka hodnocení');
   ```

## Výsledek

Po úspěšném nastavení:

### ✅ Administrace modulu je dostupná v menu:
```
Vlastní moduly → Mezistranka hodnocení
```

### ✅ Funkcionalita je 1:1 stejná jako dříve:
- **Záložka Konfigurace:** Nastavení textů, odkazů, kontaktů
- **Záložka Statistiky:** Přehled hodnocení, export do CSV
- **Všechny funkce:** Aktualizace shortcodů, mazání dat, atd.

### ✅ Výhody nového řešení:
- Čistší organizace administrace
- Snadnější správa více vlastních modulů
- Profesionálnější vzhled
- Lepší uživatelská zkušenost

## Řešení problémů

### Problém: Tab se nezobrazuje v menu
**Řešení:** 
1. Zkontrolujte, zda je tab aktivní v databázi
2. Vyčistěte cache PrestaShop
3. Zkontrolujte oprávnění uživatele

### Problém: Chyba 404 při přístupu na administraci
**Řešení:**
1. Zkontrolujte, zda existuje soubor `AdminMezistrankaHodnoceniController.php`
2. Zkontrolujte oprávnění souborů
3. Vyčistěte cache

### Problém: Sekce "Vlastní moduly" neexistuje
**Řešení:**
1. Spusťte `setup-custom-modules-section.php`
2. Klikněte na "Vytvořit sekci"
3. Nebo vytvořte sekci ručně v databázi (viz výše)

## Podpora

Pro další podporu nebo dotazy kontaktujte vývojáře modulu.

---

**Autor:** Miroslav Urbánek  
**Verze:** 1.0.0  
**Datum:** 2024-08-19
