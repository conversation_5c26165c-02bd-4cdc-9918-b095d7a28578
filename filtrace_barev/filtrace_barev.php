<?php
/**
 * Modul pro filtraci barev - zobrazování správných obrázků variant
 * 
 * <AUTHOR> Agent
 * @version 1.0.0
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

class Filtrace_Barev extends Module
{
    public function __construct()
    {
        $this->name = 'filtrace_barev';
        $this->tab = 'front_office_features';
        $this->version = '1.0.0';
        $this->author = 'Augment Agent';
        $this->need_instance = 0;
        $this->ps_versions_compliancy = array('min' => '1.7', 'max' => _PS_VERSION_);
        $this->bootstrap = true;

        parent::__construct();

        $this->displayName = $this->l('Filtrace barev');
        $this->description = $this->l('Zobrazuje správné obrázky variant produktů při filtraci podle barvy.');
        $this->confirmUninstall = $this->l('Opravdu chcete odinstalovat modul filtrace barev?');
    }

    public function install()
    {
        return parent::install() &&
            $this->registerHook('displayHeader') &&
            $this->registerHook('actionFrontControllerSetMedia');
    }

    public function uninstall()
    {
        return parent::uninstall();
    }

    /**
     * Přidá CSS a JavaScript do hlavičky
     */
    public function hookDisplayHeader()
    {
        // Pouze na stránkách kategorií
        if ($this->context->controller instanceof CategoryController) {
            $enabled = Configuration::get('FILTRACE_BAREV_ENABLED', true);
            $debug = Configuration::get('FILTRACE_BAREV_DEBUG', false);

            // Vždy nastavíme konfiguraci do JavaScript
            $js_config = '
            <script type="text/javascript">
                window.FILTRACE_BAREV_DEBUG = ' . ($debug ? 'true' : 'false') . ';
                window.FILTRACE_BAREV_ENABLED = ' . ($enabled ? 'true' : 'false') . ';
            </script>';

            if ($enabled) {
                $this->context->controller->addCSS($this->_path . 'views/css/filtrace_barev.css');
                $this->context->controller->addJS($this->_path . 'views/js/filtrace_barev.js');
            }

            return $js_config;
        }
    }

    /**
     * Přidá media soubory
     */
    public function hookActionFrontControllerSetMedia()
    {
        if ($this->context->controller instanceof CategoryController) {
            $enabled = Configuration::get('FILTRACE_BAREV_ENABLED', true);
            
            if ($enabled) {
                $this->context->controller->registerJavascript(
                    'filtrace-barev-js',
                    'modules/' . $this->name . '/views/js/filtrace_barev.js',
                    ['position' => 'bottom', 'priority' => 150]
                );
            }
        }
    }

    /**
     * Konfigurace modulu
     */
    public function getContent()
    {
        $output = '';

        if (Tools::isSubmit('submit' . $this->name)) {
            // Uložíme hodnoty
            Configuration::updateValue('FILTRACE_BAREV_ENABLED', (bool)Tools::getValue('FILTRACE_BAREV_ENABLED'));
            Configuration::updateValue('FILTRACE_BAREV_DEBUG', (bool)Tools::getValue('FILTRACE_BAREV_DEBUG'));

            $output .= $this->displayConfirmation($this->l('Nastavení uloženo'));
        }

        return $output . $this->displayForm();
    }

    /**
     * Formulář pro konfiguraci
     */
    public function displayForm()
    {
        $default_lang = (int)Configuration::get('PS_LANG_DEFAULT');

        $fields_form[0]['form'] = array(
            'legend' => array(
                'title' => $this->l('Nastavení filtrace barev'),
            ),
            'input' => array(
                array(
                    'type' => 'switch',
                    'label' => $this->l('Povolit filtraci barev'),
                    'name' => 'FILTRACE_BAREV_ENABLED',
                    'is_bool' => true,
                    'desc' => $this->l('Zapne/vypne funkcionalitu filtrace barev'),
                    'values' => array(
                        array(
                            'id' => 'active_on',
                            'value' => true,
                            'label' => $this->l('Povoleno')
                        ),
                        array(
                            'id' => 'active_off',
                            'value' => false,
                            'label' => $this->l('Zakázáno')
                        )
                    ),
                ),
                array(
                    'type' => 'switch',
                    'label' => $this->l('Debug režim'),
                    'name' => 'FILTRACE_BAREV_DEBUG',
                    'is_bool' => true,
                    'desc' => $this->l('Zapne debug informace v konzoli prohlížeče'),
                    'values' => array(
                        array(
                            'id' => 'debug_on',
                            'value' => true,
                            'label' => $this->l('Zapnuto')
                        ),
                        array(
                            'id' => 'debug_off',
                            'value' => false,
                            'label' => $this->l('Vypnuto')
                        )
                    ),
                ),
            ),
            'submit' => array(
                'title' => $this->l('Uložit'),
                'class' => 'btn btn-default pull-right'
            )
        );

        $helper = new HelperForm();
        $helper->module = $this;
        $helper->name_controller = $this->name;
        $helper->token = Tools::getAdminTokenLite('AdminModules');
        $helper->currentIndex = AdminController::$currentIndex . '&configure=' . $this->name;

        $helper->default_form_language = $default_lang;
        $helper->allow_employee_form_lang = $default_lang;

        $helper->title = $this->displayName;
        $helper->show_toolbar = true;
        $helper->toolbar_scroll = true;
        $helper->submit_action = 'submit' . $this->name;
        $helper->toolbar_btn = array(
            'save' => array(
                'desc' => $this->l('Uložit'),
                'href' => AdminController::$currentIndex . '&configure=' . $this->name . '&save' . $this->name .
                '&token=' . Tools::getAdminTokenLite('AdminModules'),
            ),
            'back' => array(
                'href' => AdminController::$currentIndex . '&token=' . Tools::getAdminTokenLite('AdminModules'),
                'desc' => $this->l('Zpět na seznam modulů')
            )
        );

        $helper->fields_value['FILTRACE_BAREV_ENABLED'] = Configuration::get('FILTRACE_BAREV_ENABLED', true);
        $helper->fields_value['FILTRACE_BAREV_DEBUG'] = Configuration::get('FILTRACE_BAREV_DEBUG', false);

        // Přidáme diagnostické informace
        $diagnostic_info = $this->getDiagnosticInfo();
        
        return $helper->generateForm($fields_form) . $diagnostic_info;
    }

    /**
     * Diagnostické informace
     */
    private function getDiagnosticInfo()
    {
        $html = '<div class="panel">';
        $html .= '<div class="panel-heading"><i class="icon-cogs"></i> ' . $this->l('Diagnostika') . '</div>';
        $html .= '<div class="panel-body">';

        // Kontrola souborů
        $files_to_check = array(
            'AJAX endpoint' => _PS_MODULE_DIR_ . $this->name . '/ajax/get_variant_image_basic.php',
            'Product template' => _PS_THEME_DIR_ . 'templates/catalog/_partials/miniatures/product.tpl',
            'Variant links template' => _PS_THEME_DIR_ . 'templates/catalog/_partials/variant-links.tpl'
        );

        $html .= '<h4>' . $this->l('Kontrola souborů') . '</h4>';
        $html .= '<table class="table">';
        $html .= '<thead><tr><th>' . $this->l('Soubor') . '</th><th>' . $this->l('Stav') . '</th></tr></thead>';
        $html .= '<tbody>';

        foreach ($files_to_check as $name => $path) {
            $exists = file_exists($path);
            $status = $exists ? 
                '<span class="label label-success">OK</span>' : 
                '<span class="label label-danger">Chybí</span>';
            
            $html .= '<tr><td>' . $name . '</td><td>' . $status . '</td></tr>';
        }

        $html .= '</tbody></table>';

        // Odkazy na diagnostické nástroje
        $html .= '<h4>' . $this->l('Diagnostické nástroje') . '</h4>';
        $html .= '<div class="btn-group">';
        $html .= '<a href="' . $this->_path . 'ajax/production_test.php" target="_blank" class="btn btn-default">' . $this->l('Test na produkci') . '</a>';
        $html .= '<a href="' . $this->_path . 'ajax/test_javascript.html" target="_blank" class="btn btn-default">' . $this->l('Test JavaScript') . '</a>';
        $html .= '<a href="' . $this->_path . 'ajax/diagnostika.php?test=all" target="_blank" class="btn btn-default">' . $this->l('Kompletní diagnostika') . '</a>';
        $html .= '<a href="' . $this->_path . 'ajax/troubleshooting.html" target="_blank" class="btn btn-default">' . $this->l('Řešení problémů') . '</a>';
        $html .= '</div>';

        $html .= '</div></div>';

        return $html;
    }
}
?>
