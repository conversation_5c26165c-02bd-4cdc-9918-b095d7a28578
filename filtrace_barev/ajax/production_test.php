<?php
/**
 * Test funkcionality na produkci - simuluje reálné použití
 */

// Načtení PrestaShop frameworku
require_once dirname(__FILE__) . '/../../../config/config.inc.php';
require_once dirname(__FILE__) . '/../../../init.php';

header('Content-Type: text/html; charset=utf-8');

?>
<!DOCTYPE html>
<html>
<head>
    <title>Test na produkci</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .test-section { margin: 20px 0; padding: 15px; border: 2px solid #ddd; border-radius: 5px; }
        .success { border-color: #28a745; background-color: #f8fff9; }
        .error { border-color: #dc3545; background-color: #fff8f8; }
        .warning { border-color: #ffc107; background-color: #fffdf0; }
        .info { border-color: #17a2b8; background-color: #f0f9ff; }
        .status { padding: 8px 12px; border-radius: 3px; font-weight: bold; }
        .status.ok { background-color: #d4edda; color: #155724; }
        .status.fail { background-color: #f8d7da; color: #721c24; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
        .file-check { margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 3px; }
        .demo-section { background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .image-demo { display: flex; gap: 20px; margin: 15px 0; }
        .image-box { text-align: center; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
        .image-box img { max-width: 200px; height: auto; }
        button { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <h1>🔍 Test funkcionality na produkci</h1>
    
    <?php
    
    // Testovací data
    $test_product_id = 2821;
    $test_variant_id = 7605;
    
    echo "<div class='info test-section'>";
    echo "<h2>📋 Testovací scénář</h2>";
    echo "<p><strong>Cíl:</strong> Ověřit, že při filtraci podle barvy se zobrazují správné obrázky variant</p>";
    echo "<p><strong>Test produkt:</strong> Dering stínítko (ID: $test_product_id)</p>";
    echo "<p><strong>Test varianta:</strong> Červená (ID: $test_variant_id)</p>";
    echo "</div>";
    
    // 1. Kontrola souborů
    echo "<div class='test-section'>";
    echo "<h2>1. 📁 Kontrola nahraných souborů</h2>";
    
    $files_to_check = [
        'AJAX endpoint' => [
            'path' => dirname(__FILE__) . '/get_variant_image_basic.php',
            'critical' => true
        ],
        'Product template (lokální)' => [
            'path' => dirname(__FILE__) . '/../product.tpl',
            'critical' => false
        ],
        'Product template (Electech)' => [
            'path' => _PS_ROOT_DIR_ . '/themes/Electech/templates/catalog/_partials/miniatures/product.tpl',
            'critical' => true
        ],
        'Variant links (Electech)' => [
            'path' => _PS_ROOT_DIR_ . '/themes/Electech/templates/catalog/_partials/variant-links.tpl',
            'critical' => true
        ]
    ];
    
    $critical_files_ok = true;
    
    foreach ($files_to_check as $name => $info) {
        echo "<div class='file-check'>";
        if (file_exists($info['path'])) {
            echo "<span class='status ok'>✅ OK</span> $name";
            
            // Pro product.tpl zkontrolujeme, zda obsahuje správný endpoint
            if (strpos($name, 'Product template') !== false) {
                $content = file_get_contents($info['path']);
                if (strpos($content, 'get_variant_image_basic.php') !== false) {
                    echo " <small>(obsahuje správný endpoint)</small>";
                } else {
                    echo " <small style='color: red;'>(NEOBSAHUJE správný endpoint!)</small>";
                    if ($info['critical']) $critical_files_ok = false;
                }
            }
        } else {
            echo "<span class='status fail'>❌ CHYBÍ</span> $name";
            echo "<br><small>Cesta: " . $info['path'] . "</small>";
            if ($info['critical']) $critical_files_ok = false;
        }
        echo "</div>";
    }
    
    if (!$critical_files_ok) {
        echo "<div class='error test-section'>";
        echo "<h3>⚠️ Kritické soubory chybí!</h3>";
        echo "<p>Některé důležité soubory nejsou na místě. Nahrajte je prosím na server.</p>";
        echo "</div>";
    }
    
    // 2. Test AJAX endpointu
    echo "</div>";
    echo "<div class='test-section'>";
    echo "<h2>2. 🔧 Test AJAX endpointu</h2>";
    
    $_GET['id_product'] = $test_product_id;
    $_GET['id_product_attribute'] = $test_variant_id;
    
    ob_start();
    if (file_exists(dirname(__FILE__) . '/get_variant_image_basic.php')) {
        include dirname(__FILE__) . '/get_variant_image_basic.php';
    } else {
        echo json_encode(['success' => false, 'error' => 'AJAX endpoint not found']);
    }
    $ajax_output = ob_get_clean();
    
    echo "<h4>Výstup AJAX:</h4>";
    echo "<pre>" . htmlspecialchars($ajax_output) . "</pre>";
    
    $ajax_data = json_decode($ajax_output, true);
    $ajax_works = false;
    
    if ($ajax_data && $ajax_data['success']) {
        $returned_image_id = $ajax_data['debug']['variant_image_id'];
        
        if ($returned_image_id == '31794') {
            echo "<div class='success test-section'>";
            echo "<h3>✅ AJAX endpoint funguje správně!</h3>";
            echo "<p>Vrací správný obrázek: $returned_image_id (červená varianta)</p>";
            echo "</div>";
            $ajax_works = true;
        } else {
            echo "<div class='error test-section'>";
            echo "<h3>❌ AJAX endpoint vrací nesprávný obrázek</h3>";
            echo "<p>Vrací: $returned_image_id, očekáváno: 31794</p>";
            echo "</div>";
        }
    } else {
        echo "<div class='error test-section'>";
        echo "<h3>❌ AJAX endpoint nefunguje</h3>";
        echo "<p>Chyba: " . (isset($ajax_data['error']) ? $ajax_data['error'] : 'Neznámá chyba') . "</p>";
        echo "</div>";
    }
    
    // 3. Simulace JavaScript logiky
    echo "</div>";
    echo "<div class='test-section'>";
    echo "<h2>3. 🎯 Simulace JavaScript logiky</h2>";
    
    echo "<div class='demo-section'>";
    echo "<h4>Test detekce barevných filtrů z URL:</h4>";
    
    $test_urls = [
        'Bez filtru' => '?category=123',
        'Červený filtr' => '?q=barva-cervena/kategorie-123',
        'Modrý filtr' => '?q=barva-modra/kategorie-123',
        'Více filtrů' => '?q=barva-cervena/velikost-m/kategorie-123'
    ];
    
    foreach ($test_urls as $name => $url) {
        echo "<p><strong>$name:</strong> <code>$url</code></p>";
        
        // Simulujeme JavaScript logiku
        parse_str(parse_url($url, PHP_URL_QUERY), $params);
        $q = isset($params['q']) ? $params['q'] : '';
        
        if ($q) {
            $parts = explode('/', $q);
            $color_filters = [];
            foreach ($parts as $part) {
                if (strpos(strtolower($part), 'barva-') === 0) {
                    $color_filters[] = substr($part, 6);
                }
            }
            
            if ($color_filters) {
                echo "<span class='status ok'>Detekováno: " . implode(', ', $color_filters) . "</span>";
            } else {
                echo "<span class='status fail'>Žádné barvy</span>";
            }
        } else {
            echo "<span class='status fail'>Žádný filtr</span>";
        }
        echo "<br>";
    }
    echo "</div>";
    
    // 4. Vizuální demo
    if ($ajax_works && $ajax_data) {
        echo "</div>";
        echo "<div class='test-section'>";
        echo "<h2>4. 👁️ Vizuální demo</h2>";
        
        echo "<div class='image-demo'>";
        
        // Cover obrázek
        $context = Context::getContext();
        $product = new Product($test_product_id, false, $context->language->id);
        $cover = Product::getCover($test_product_id);
        $link = new Link();
        
        if ($cover) {
            $cover_url = 'https:' . $link->getImageLink(
                $product->link_rewrite,
                $cover['id_image'],
                ImageType::getFormattedName('home_default')
            );
            
            echo "<div class='image-box'>";
            echo "<h4>❌ Před opravou</h4>";
            echo "<img src='$cover_url' alt='Cover obrázek'>";
            echo "<p>Cover obrázek (ID: " . $cover['id_image'] . ")</p>";
            echo "<p>Zobrazoval se vždy, bez ohledu na filtr</p>";
            echo "</div>";
        }
        
        // Nový obrázek
        echo "<div class='image-box'>";
        echo "<h4>✅ Po opravě</h4>";
        echo "<img src='" . $ajax_data['image_url'] . "' alt='Obrázek varianty'>";
        echo "<p>Obrázek varianty (ID: " . $ajax_data['debug']['variant_image_id'] . ")</p>";
        echo "<p>Zobrazuje se podle vybraného filtru</p>";
        echo "</div>";
        
        echo "</div>";
    }
    
    // 5. Finální doporučení
    echo "</div>";
    echo "<div class='test-section'>";
    echo "<h2>5. 📝 Finální doporučení</h2>";
    
    if ($critical_files_ok && $ajax_works) {
        echo "<div class='success test-section'>";
        echo "<h3>🎉 Vše je připraveno!</h3>";
        echo "<p>Filtrace barev by měla fungovat na produkci.</p>";
        echo "</div>";
        
        echo "<div class='info test-section'>";
        echo "<h4>Jak otestovat na produkci:</h4>";
        echo "<ol>";
        echo "<li>Jděte na kategorii s produkty, které mají barevné varianty</li>";
        echo "<li>Aplikujte filtr 'Červená' (nebo jiná barva)</li>";
        echo "<li>Zkontrolujte, zda se obrázky produktů změnily na odpovídající barvu</li>";
        echo "<li>Pokud ne, otevřete konzoli prohlížeče (F12) a hledejte chyby</li>";
        echo "</ol>";
        echo "</div>";
        
    } else {
        echo "<div class='error test-section'>";
        echo "<h3>❌ Ještě není hotovo</h3>";
        echo "<p>Opravte prosím výše uvedené problémy před testováním na produkci.</p>";
        echo "</div>";
    }
    
    ?>
    
    <div class="demo-section">
        <h3>🔗 Užitečné odkazy</h3>
        <ul>
            <li><a href="test_all_endpoints.php">Test všech AJAX endpointů</a></li>
            <li><a href="find_red_variants.php">Najít červené varianty</a></li>
            <li><a href="diagnostika.php?test=all">Kompletní diagnostika</a></li>
            <li><a href="get_variant_image_basic.php?id_product=<?php echo $test_product_id; ?>&id_product_attribute=<?php echo $test_variant_id; ?>" target="_blank">Přímý test AJAX</a></li>
        </ul>
    </div>
    
    <script>
    // Jednoduchý test JavaScript logiky
    function testJavaScriptLogic() {
        console.log('=== Test JavaScript logiky ===');
        
        // Test normalizace
        function normalizeText(text) {
            if (!text) return '';
            return text.toLowerCase()
                .normalize("NFD").replace(/[\u0300-\u036f]/g, "")
                .replace(/\s+/g, '-');
        }
        
        const testColors = ['Červená', 'Modrá', 'Zelená'];
        testColors.forEach(color => {
            console.log(color + ' -> ' + normalizeText(color));
        });
        
        // Test detekce URL
        const testUrl = '?q=barva-cervena/kategorie-123';
        const urlParams = new URLSearchParams(testUrl.substring(1));
        const qParam = urlParams.get('q');
        console.log('URL parametr q:', qParam);
        
        if (qParam) {
            const qParts = qParam.split('/');
            const colorFilters = [];
            qParts.forEach(part => {
                if (part.toLowerCase().startsWith('barva-')) {
                    colorFilters.push(normalizeText(part.substring(6)));
                }
            });
            console.log('Detekované barvy:', colorFilters);
        }
        
        alert('Test dokončen - výsledky v konzoli (F12)');
    }
    </script>
    
    <div class="demo-section">
        <button onclick="testJavaScriptLogic()">🧪 Test JavaScript logiky</button>
        <p><small>Klikněte pro test JavaScript funkcí v konzoli prohlížeče</small></p>
    </div>
</body>
</html>
