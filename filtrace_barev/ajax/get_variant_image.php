<?php
/**
 * AJAX endpoint pro získání obrázku varianty produktu.
 */

// Dočasně vypneme hlášení chyb pro diagnostiku, aby <PERSON>v<PERSON><PERSON><PERSON>ly JSON výstup
error_reporting(0);
ini_set('display_errors', 0);

// Kontrola, zda je to AJAX request - pro produkci odkomentovat
// if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) || strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) !== 'xmlhttprequest') {
//     header('HTTP/1.0 403 Forbidden');
//     exit('Access denied');
// }

// Načtení PrestaShop frameworku
require_once dirname(__FILE__) . '/../../../config/config.inc.php';
require_once dirname(__FILE__) . '/../../../init.php';

// Získání a validace parametrů
$id_product = (int) Tools::getValue('id_product');
$id_product_attribute = (int) Tools::getValue('id_product_attribute');

if (!$id_product || !$id_product_attribute) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'error' => 'Missing parameters']);
    exit; // Zajistíme okamžité ukončení
}

try {
    $context = Context::getContext();
    $product = new Product($id_product, false, $context->language->id);

    // Zkontrolujeme, zda produkt existuje
    if (!Validate::isLoadedObject($product)) {
        throw new Exception('Product not found');
    }

    // Získáme obrázky přiřazené k této konkrétní variantě
    $variant_image_id = 0;

    try {
        // Použijeme správnou metodu pro získání obrázků variant
        $sql = "SELECT pai.id_image, i.position FROM `" . _DB_PREFIX_ . "product_attribute_image` pai LEFT JOIN `" . _DB_PREFIX_ . "image` i ON pai.id_image = i.id_image WHERE pai.id_product_attribute = " . (int)$id_product_attribute . " ORDER BY i.position ASC LIMIT 1";

        $result = Db::getInstance()->getRow($sql);
        if ($result && isset($result['id_image'])) {
            $variant_image_id = $result['id_image'];
        }
    } catch (Exception $e) {
        // Pokud tabulka neexistuje nebo je chyba v SQL, zkusíme alternativní metodu
        error_log("SQL chyba v get_variant_image.php: " . $e->getMessage());

        // Alternativní metoda - použijeme jednodušší přístup
        $images = Image::getImages($context->language->id, $id_product);
        if ($images && count($images) > 0) {
            // Pokud nemůžeme najít specifický obrázek varianty, použijeme první dostupný
            $variant_image_id = $images[0]['id_image'];
        }
    }
    
    // Pokud varianta nemá vlastní obrázek, použijeme hlavní obrázek produktu jako fallback
    if (!$variant_image_id) {
         $cover = Product::getCover($id_product);
         if ($cover && isset($cover['id_image'])) {
            $variant_image_id = $cover['id_image'];
         }
    }

    if ($variant_image_id) {
        $link = new Link();
        // Získáme URL pro náhledový obrázek (např. home_default)
        $image_url = $link->getImageLink(
            $product->link_rewrite,
            $variant_image_id,
            ImageType::getFormattedName('home_default')
        );

        // Získáme URL pro velký obrázek
        $large_image_url = $link->getImageLink(
            $product->link_rewrite,
            $variant_image_id,
            ImageType::getFormattedName('large_default')
        );

        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'image_url' => (strpos($image_url, 'http') === 0) ? $image_url : 'https:' . $image_url,
            'large_image_url' => (strpos($large_image_url, 'http') === 0) ? $large_image_url : 'https:' . $large_image_url,
            'debug' => [
                'id_product' => $id_product,
                'id_product_attribute' => $id_product_attribute,
                'variant_image_id' => $variant_image_id,
                'product_name' => $product->name
            ]
        ]);
        exit; // Zajistíme okamžité ukončení
    } else {
        throw new Exception('Image not found for this variant');
    }

} catch (Exception $e) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'error' => $e->getMessage()]);
    exit; // Zajistíme okamžité ukončení
}

// Poslední exit; je redundantní, pokud všechny cesty výše mají exit;, ale neškodné.
// exit;