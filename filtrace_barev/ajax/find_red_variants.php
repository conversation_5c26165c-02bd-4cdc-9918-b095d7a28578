<?php
/**
 * Skript pro nalezení červených variant s o<PERSON><PERSON><PERSON><PERSON><PERSON>
 */

// Načtení PrestaShop frameworku
require_once dirname(__FILE__) . '/../../../config/config.inc.php';
require_once dirname(__FILE__) . '/../../../init.php';

header('Content-Type: text/html; charset=utf-8');

?>
<!DOCTYPE html>
<html>
<head>
    <title>Nalezení červených variant</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .red-variant { background-color: #ffe6e6; }
        .has-image { background-color: #e6ffe6; }
    </style>
</head>
<body>
    <h1><PERSON><PERSON><PERSON><PERSON> variant s obr<PERSON><PERSON><PERSON></h1>
    
    <?php
    
    $context = Context::getContext();
    
    // Najdeme všechny červené varianty
    $sql = "SELECT 
                p.id_product,
                pl.name as product_name,
                pa.id_product_attribute,
                pa.reference,
                al.name as color_name,
                a.color as color_code,
                COUNT(pai.id_image) as image_count,
                GROUP_CONCAT(pai.id_image) as image_ids
            FROM " . _DB_PREFIX_ . "product p
            LEFT JOIN " . _DB_PREFIX_ . "product_lang pl ON (p.id_product = pl.id_product AND pl.id_lang = " . (int)$context->language->id . ")
            LEFT JOIN " . _DB_PREFIX_ . "product_attribute pa ON p.id_product = pa.id_product
            LEFT JOIN " . _DB_PREFIX_ . "product_attribute_combination pac ON pa.id_product_attribute = pac.id_product_attribute
            LEFT JOIN " . _DB_PREFIX_ . "attribute a ON pac.id_attribute = a.id_attribute
            LEFT JOIN " . _DB_PREFIX_ . "attribute_lang al ON (a.id_attribute = al.id_attribute AND al.id_lang = " . (int)$context->language->id . ")
            LEFT JOIN " . _DB_PREFIX_ . "product_attribute_image pai ON pa.id_product_attribute = pai.id_product_attribute
            WHERE p.active = 1 
            AND (al.name LIKE '%červen%' OR al.name LIKE '%red%' OR a.color LIKE '%E84C3D%' OR a.color LIKE '%red%')
            GROUP BY pa.id_product_attribute
            HAVING image_count > 0
            ORDER BY p.id_product, pa.id_product_attribute
            LIMIT 20";
    
    $red_variants = Db::getInstance()->executeS($sql);
    
    if ($red_variants) {
        echo "<h2>Nalezeno " . count($red_variants) . " červených variant s obrázky:</h2>";
        
        echo "<table>";
        echo "<tr><th>ID produktu</th><th>Název produktu</th><th>ID varianty</th><th>Název barvy</th><th>Kód barvy</th><th>Počet obrázků</th><th>ID obrázků</th><th>Test</th></tr>";
        
        foreach ($red_variants as $variant) {
            $class = 'red-variant';
            if ($variant['image_count'] > 0) {
                $class .= ' has-image';
            }
            
            echo "<tr class='$class'>";
            echo "<td>" . $variant['id_product'] . "</td>";
            echo "<td>" . htmlspecialchars($variant['product_name']) . "</td>";
            echo "<td>" . $variant['id_product_attribute'] . "</td>";
            echo "<td>" . htmlspecialchars($variant['color_name']) . "</td>";
            echo "<td style='background-color: " . $variant['color_code'] . "'>" . $variant['color_code'] . "</td>";
            echo "<td>" . $variant['image_count'] . "</td>";
            echo "<td>" . $variant['image_ids'] . "</td>";
            echo "<td>";
            echo "<a href='test_variant_fix.php?id_product=" . $variant['id_product'] . "&id_product_attribute=" . $variant['id_product_attribute'] . "'>Test</a> | ";
            echo "<a href='test_ajax.html?id_product=" . $variant['id_product'] . "&id_product_attribute=" . $variant['id_product_attribute'] . "'>AJAX</a>";
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Ukážeme první variantu jako příklad
        if (count($red_variants) > 0) {
            $first_variant = $red_variants[0];
            echo "<h3>Příklad pro testování:</h3>";
            echo "<p><strong>Produkt:</strong> " . htmlspecialchars($first_variant['product_name']) . "</p>";
            echo "<p><strong>ID produktu:</strong> " . $first_variant['id_product'] . "</p>";
            echo "<p><strong>ID varianty:</strong> " . $first_variant['id_product_attribute'] . "</p>";
            echo "<p><strong>Barva:</strong> " . htmlspecialchars($first_variant['color_name']) . "</p>";
            
            echo "<p><strong>Rychlé testy:</strong></p>";
            echo "<ul>";
            echo "<li><a href='test_variant_fix.php?id_product=" . $first_variant['id_product'] . "&id_product_attribute=" . $first_variant['id_product_attribute'] . "'>Detailní test opravy</a></li>";
            echo "<li><a href='test_ajax.html?id_product=" . $first_variant['id_product'] . "&id_product_attribute=" . $first_variant['id_product_attribute'] . "'>AJAX tester</a></li>";
            echo "<li><a href='diagnostika.php?test=ajax&id_product=" . $first_variant['id_product'] . "&id_product_attribute=" . $first_variant['id_product_attribute'] . "'>Diagnostika AJAX</a></li>";
            echo "</ul>";
        }
        
    } else {
        echo "<p>❌ Nebyly nalezeny žádné červené varianty s obrázky.</p>";
        
        // Zkusíme najít alespoň nějaké červené varianty bez obrázků
        $sql_no_images = "SELECT 
                            p.id_product,
                            pl.name as product_name,
                            pa.id_product_attribute,
                            al.name as color_name,
                            a.color as color_code
                        FROM " . _DB_PREFIX_ . "product p
                        LEFT JOIN " . _DB_PREFIX_ . "product_lang pl ON (p.id_product = pl.id_product AND pl.id_lang = " . (int)$context->language->id . ")
                        LEFT JOIN " . _DB_PREFIX_ . "product_attribute pa ON p.id_product = pa.id_product
                        LEFT JOIN " . _DB_PREFIX_ . "product_attribute_combination pac ON pa.id_product_attribute = pac.id_product_attribute
                        LEFT JOIN " . _DB_PREFIX_ . "attribute a ON pac.id_attribute = a.id_attribute
                        LEFT JOIN " . _DB_PREFIX_ . "attribute_lang al ON (a.id_attribute = al.id_attribute AND al.id_lang = " . (int)$context->language->id . ")
                        WHERE p.active = 1 
                        AND (al.name LIKE '%červen%' OR al.name LIKE '%red%')
                        ORDER BY p.id_product, pa.id_product_attribute
                        LIMIT 10";
        
        $red_variants_no_images = Db::getInstance()->executeS($sql_no_images);
        
        if ($red_variants_no_images) {
            echo "<h3>Červené varianty bez specifických obrázků:</h3>";
            echo "<table>";
            echo "<tr><th>ID produktu</th><th>Název produktu</th><th>ID varianty</th><th>Název barvy</th><th>Test</th></tr>";
            
            foreach ($red_variants_no_images as $variant) {
                echo "<tr>";
                echo "<td>" . $variant['id_product'] . "</td>";
                echo "<td>" . htmlspecialchars($variant['product_name']) . "</td>";
                echo "<td>" . $variant['id_product_attribute'] . "</td>";
                echo "<td>" . htmlspecialchars($variant['color_name']) . "</td>";
                echo "<td><a href='test_variant_fix.php?id_product=" . $variant['id_product'] . "&id_product_attribute=" . $variant['id_product_attribute'] . "'>Test</a></td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    }
    
    ?>
    
    <h2>Další nástroje:</h2>
    <ul>
        <li><a href="diagnostika.php?test=all">Kompletní diagnostika</a></li>
        <li><a href="test_ajax.html">AJAX tester</a></li>
        <li><a href="test_variant_fix.php">Test opravy variant</a></li>
    </ul>
</body>
</html>
