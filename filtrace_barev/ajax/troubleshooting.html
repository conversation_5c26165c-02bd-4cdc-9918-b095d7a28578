<!DOCTYPE html>
<html>
<head>
    <title>Řešení problémů - Filtrace barev</title>
    <meta charset="utf-8">
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            line-height: 1.6; 
            background-color: #f8f9fa;
        }
        .container { 
            max-width: 1000px; 
            margin: 0 auto; 
            background: white; 
            padding: 30px; 
            border-radius: 10px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .step { 
            margin: 20px 0; 
            padding: 20px; 
            border: 2px solid #ddd; 
            border-radius: 8px; 
            background: #fafafa;
        }
        .step.critical { border-color: #dc3545; background: #fff5f5; }
        .step.important { border-color: #ffc107; background: #fffef0; }
        .step.info { border-color: #17a2b8; background: #f0f9ff; }
        .step.success { border-color: #28a745; background: #f8fff9; }
        
        .checklist { list-style: none; padding: 0; }
        .checklist li { 
            margin: 10px 0; 
            padding: 10px; 
            background: white; 
            border-radius: 5px; 
            border-left: 4px solid #007bff;
        }
        .checklist li:before { content: "☐ "; font-size: 18px; margin-right: 10px; }
        .checklist li.done:before { content: "✅ "; }
        .checklist li.done { background: #f8fff9; border-left-color: #28a745; }
        
        .code { 
            background: #f1f3f4; 
            padding: 15px; 
            border-radius: 5px; 
            font-family: monospace; 
            margin: 10px 0;
            border-left: 4px solid #007bff;
        }
        
        .warning { 
            background: #fff3cd; 
            border: 1px solid #ffeaa7; 
            padding: 15px; 
            border-radius: 5px; 
            margin: 15px 0;
        }
        
        .tip { 
            background: #d1ecf1; 
            border: 1px solid #bee5eb; 
            padding: 15px; 
            border-radius: 5px; 
            margin: 15px 0;
        }
        
        h1 { color: #333; text-align: center; }
        h2 { color: #007bff; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
        h3 { color: #495057; }
        
        .file-path { 
            background: #e9ecef; 
            padding: 5px 10px; 
            border-radius: 3px; 
            font-family: monospace; 
            font-size: 14px;
        }
        
        .status-good { color: #28a745; font-weight: bold; }
        .status-bad { color: #dc3545; font-weight: bold; }
        .status-warning { color: #ffc107; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Řešení problémů - Filtrace barev</h1>
        
        <div class="warning">
            <strong>⚠️ Problém:</strong> AJAX endpoint funguje, ale na produkci se stále nezobrazují správné obrázky variant při filtraci podle barvy.
        </div>
        
        <div class="step critical">
            <h2>🚨 Krok 1: Kritická kontrola souborů</h2>
            <p>Nejdříve ověřte, že jsou všechny soubory správně nahrané:</p>
            
            <ul class="checklist">
                <li>AJAX endpoint: <span class="file-path">/modules/filtrace_barev/ajax/get_variant_image_basic.php</span></li>
                <li>Product template: <span class="file-path">/themes/Electech/templates/catalog/_partials/miniatures/product.tpl</span></li>
                <li>Variant links: <span class="file-path">/themes/Electech/templates/catalog/_partials/variant-links.tpl</span></li>
            </ul>
            
            <div class="tip">
                <strong>💡 Tip:</strong> Spusťte <a href="production_test.php" target="_blank">production_test.php</a> pro automatickou kontrolu souborů.
            </div>
        </div>
        
        <div class="step important">
            <h2>⚠️ Krok 2: Kontrola product.tpl</h2>
            <p>Nejčastější problém je, že product.tpl neobsahuje správný endpoint. Zkontrolujte:</p>
            
            <div class="code">
// V souboru product.tpl hledejte tento řádek:
const endpointUrl = `${prestashop.urls.base_url}modules/${moduleDirectoryName}/ajax/get_variant_image_basic.php?id_product=${productId}&id_product_attribute=${attributeId}`;

// NESMÍ být:
get_variant_image.php
get_variant_image_simple.php

// MUSÍ být:
get_variant_image_basic.php
            </div>
            
            <ul class="checklist">
                <li>Otevřete product.tpl v editoru</li>
                <li>Najděte řádek s "endpointUrl"</li>
                <li>Ověřte, že obsahuje "get_variant_image_basic.php"</li>
                <li>Uložte a nahrajte na server</li>
            </ul>
        </div>
        
        <div class="step info">
            <h2>🔍 Krok 3: Test v prohlížeči</h2>
            <p>Otestujte funkcionalitu přímo v prohlížeči:</p>
            
            <ul class="checklist">
                <li>Jděte na kategorii s produkty</li>
                <li>Otevřete konzoli prohlížeče (F12)</li>
                <li>Aplikujte barevný filtr (např. "Červená")</li>
                <li>Sledujte konzoli - hledejte chyby nebo AJAX volání</li>
                <li>Zkontrolujte, zda se URL změnila (obsahuje "barva-cervena")</li>
            </ul>
            
            <div class="tip">
                <strong>💡 Co hledat v konzoli:</strong>
                <ul>
                    <li>Chyby JavaScriptu (červené)</li>
                    <li>AJAX volání na get_variant_image_basic.php</li>
                    <li>Odpovědi s image_url obsahující správné ID obrázku</li>
                </ul>
            </div>
        </div>
        
        <div class="step info">
            <h2>🎯 Krok 4: Kontrola URL parametrů</h2>
            <p>Ověřte, že filtrace správně generuje URL:</p>
            
            <div class="code">
// Správná URL po aplikaci červeného filtru:
https://vase-domena.cz/kategorie?q=barva-cervena/kategorie-123

// Nebo podobně:
https://vase-domena.cz/kategorie?q=barva-cervena

// NESMÍ být prázdné 'q' nebo bez 'barva-':
https://vase-domena.cz/kategorie?q=cervena  ❌
https://vase-domena.cz/kategorie?color=red   ❌
            </div>
            
            <ul class="checklist">
                <li>Aplikujte barevný filtr</li>
                <li>Zkontrolujte URL v adresním řádku</li>
                <li>Ověřte, že obsahuje "q=barva-nazev"</li>
                <li>Pokud ne, problém je ve faceted search modulu</li>
            </ul>
        </div>
        
        <div class="step info">
            <h2>🔧 Krok 5: Ruční test AJAX</h2>
            <p>Otestujte AJAX endpoint přímo:</p>
            
            <div class="code">
// Otevřete v novém okně:
https://vase-domena.cz/modules/filtrace_barev/ajax/get_variant_image_basic.php?id_product=2821&id_product_attribute=7605

// Měli byste vidět JSON s:
{
  "success": true,
  "image_url": "https:czimg-dev1.www2.peterman.cz/31794-home_default_default/dering-stinitko.jpg",
  "debug": {
    "variant_image_id": "31794"
  }
}
            </div>
            
            <ul class="checklist">
                <li>Otevřete AJAX endpoint v prohlížeči</li>
                <li>Ověřte, že vrací success: true</li>
                <li>Zkontrolujte variant_image_id (mělo by být 31794)</li>
                <li>Klikněte na image_url - měl by se zobrazit červený obrázek</li>
            </ul>
        </div>
        
        <div class="step success">
            <h2>✅ Krok 6: Pokud vše funguje</h2>
            <p>Pokud všechny předchozí kroky prošly, ale stále to nefunguje:</p>
            
            <ul class="checklist">
                <li>Vymažte cache prohlížeče (Ctrl+F5)</li>
                <li>Zkuste jiný prohlížeč</li>
                <li>Ověřte, že používáte správné téma (Electech)</li>
                <li>Zkontrolujte, zda není aktivní nějaký cache modul</li>
                <li>Restartujte webserver (pokud máte přístup)</li>
            </ul>
        </div>
        
        <div class="step critical">
            <h2>🆘 Pokud nic nepomáhá</h2>
            <p>Poslední možnosti:</p>
            
            <ul class="checklist">
                <li>Zkontrolujte error log serveru</li>
                <li>Ověřte oprávnění souborů (644 pro PHP, 755 pro složky)</li>
                <li>Zkuste nahrát soubory znovu</li>
                <li>Kontaktujte správce serveru</li>
            </ul>
            
            <div class="warning">
                <strong>🔍 Debug informace:</strong><br>
                Pokud potřebujete pomoc, připravte tyto informace:
                <ul>
                    <li>Výstup z production_test.php</li>
                    <li>Screenshot konzole prohlížeče s chybami</li>
                    <li>URL stránky kde testujete</li>
                    <li>Verze PrestaShop a tématu</li>
                </ul>
            </div>
        </div>
        
        <div class="step info">
            <h2>🔗 Užitečné odkazy</h2>
            <ul>
                <li><a href="production_test.php" target="_blank">🧪 Test na produkci</a></li>
                <li><a href="test_all_endpoints.php" target="_blank">🔧 Test všech endpointů</a></li>
                <li><a href="find_red_variants.php" target="_blank">🔍 Najít červené varianty</a></li>
                <li><a href="diagnostika.php?test=all" target="_blank">📊 Kompletní diagnostika</a></li>
                <li><a href="get_variant_image_basic.php?id_product=2821&id_product_attribute=7605" target="_blank">⚡ Přímý test AJAX</a></li>
            </ul>
        </div>
    </div>
</body>
</html>
