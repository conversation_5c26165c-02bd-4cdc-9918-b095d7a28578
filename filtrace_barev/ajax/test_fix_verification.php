<?php
/**
 * Test ověření opravy AJAX endpointu
 */

// Načtení PrestaShop frameworku
require_once dirname(__FILE__) . '/../../../config/config.inc.php';
require_once dirname(__FILE__) . '/../../../init.php';

header('Content-Type: text/html; charset=utf-8');

$test_product_id = 2821;
$test_variant_id = 7605;

?>
<!DOCTYPE html>
<html>
<head>
    <title>Test ověření opravy</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border: 1px solid #ddd; }
        .success { background-color: #d4edda; }
        .error { background-color: #f8d7da; }
        .warning { background-color: #fff3cd; }
        .info { background-color: #d1ecf1; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
        .image-preview { margin: 10px 0; }
        .image-preview img { max-width: 200px; border: 1px solid #ddd; margin: 5px; }
    </style>
</head>
<body>
    <h1>Test ověření opravy AJAX endpointu</h1>
    
    <h2>Testovací data:</h2>
    <ul>
        <li><strong>Produkt ID:</strong> <?php echo $test_product_id; ?> (Dering stínítko)</li>
        <li><strong>Varianta ID:</strong> <?php echo $test_variant_id; ?></li>
        <li><strong>Očekávané obrázky varianty:</strong> 31794, 31795</li>
        <li><strong>Cover obrázek (nesprávný):</strong> 31792</li>
    </ul>
    
    <?php
    
    // Test 1: Přímý SQL dotaz
    echo "<h2>Test 1: Přímý SQL dotaz</h2>";
    
    try {
        $sql = "SELECT pai.id_image, i.position FROM `" . _DB_PREFIX_ . "product_attribute_image` pai LEFT JOIN `" . _DB_PREFIX_ . "image` i ON pai.id_image = i.id_image WHERE pai.id_product_attribute = " . (int)$test_variant_id . " ORDER BY i.position ASC";

        $variant_images = Db::getInstance()->executeS($sql);
        
        if ($variant_images) {
            echo "<div class='test-result success'>";
            echo "✅ SQL dotaz úspěšný - nalezeno " . count($variant_images) . " obrázků";
            echo "<br>Obrázky: ";
            foreach ($variant_images as $img) {
                echo $img['id_image'] . " (pozice " . $img['position'] . "), ";
            }
            echo "</div>";
            
            $expected_image_id = $variant_images[0]['id_image'];
        } else {
            echo "<div class='test-result error'>❌ SQL dotaz nevrátil žádné výsledky</div>";
            $expected_image_id = null;
        }
    } catch (Exception $e) {
        echo "<div class='test-result error'>❌ SQL chyba: " . $e->getMessage() . "</div>";
        $expected_image_id = null;
    }
    
    // Test 2: Původní AJAX endpoint
    echo "<h2>Test 2: Původní AJAX endpoint</h2>";
    
    $_GET['id_product'] = $test_product_id;
    $_GET['id_product_attribute'] = $test_variant_id;
    
    ob_start();
    include dirname(__FILE__) . '/get_variant_image.php';
    $original_output = ob_get_clean();
    
    echo "<h4>Výstup původního endpointu:</h4>";
    echo "<pre>" . htmlspecialchars($original_output) . "</pre>";
    
    $original_data = json_decode($original_output, true);
    if ($original_data && $original_data['success']) {
        $original_image_id = $original_data['debug']['variant_image_id'];
        
        if ($expected_image_id && $original_image_id == $expected_image_id) {
            echo "<div class='test-result success'>✅ Původní endpoint vrací správný obrázek: $original_image_id</div>";
        } else {
            echo "<div class='test-result error'>❌ Původní endpoint vrací nesprávný obrázek: $original_image_id (očekáváno: $expected_image_id)</div>";
        }
    } else {
        echo "<div class='test-result error'>❌ Původní endpoint selhal</div>";
    }
    
    // Test 3: Nový AJAX endpoint
    echo "<h2>Test 3: Nový AJAX endpoint</h2>";
    
    ob_start();
    include dirname(__FILE__) . '/get_variant_image_simple.php';
    $simple_output = ob_get_clean();
    
    echo "<h4>Výstup nového endpointu:</h4>";
    echo "<pre>" . htmlspecialchars($simple_output) . "</pre>";
    
    $simple_data = json_decode($simple_output, true);
    if ($simple_data && $simple_data['success']) {
        $simple_image_id = $simple_data['debug']['variant_image_id'];
        $method_used = $simple_data['debug']['method_used'];
        
        if ($expected_image_id && $simple_image_id == $expected_image_id) {
            echo "<div class='test-result success'>✅ Nový endpoint vrací správný obrázek: $simple_image_id (metoda: $method_used)</div>";
        } else {
            echo "<div class='test-result error'>❌ Nový endpoint vrací nesprávný obrázek: $simple_image_id (očekáváno: $expected_image_id, metoda: $method_used)</div>";
        }
        
        // Zobrazíme náhled obrázků
        echo "<div class='image-preview'>";
        echo "<h4>Náhled obrázků:</h4>";
        echo "<p><strong>Vrácený obrázek:</strong></p>";
        echo "<img src='" . $simple_data['image_url'] . "' alt='Vrácený obrázek' title='ID: $simple_image_id'>";
        
        if ($expected_image_id && $simple_image_id != $expected_image_id) {
            // Ukážeme i správný obrázek pro porovnání
            $context = Context::getContext();
            $product = new Product($test_product_id, false, $context->language->id);
            $link = new Link();
            
            $correct_image_url = $link->getImageLink(
                $product->link_rewrite,
                $expected_image_id,
                ImageType::getFormattedName('home_default')
            );
            $correct_image_url = 'https:' . $correct_image_url;
            
            echo "<p><strong>Správný obrázek (ID $expected_image_id):</strong></p>";
            echo "<img src='$correct_image_url' alt='Správný obrázek' title='ID: $expected_image_id'>";
        }
        echo "</div>";
        
    } else {
        echo "<div class='test-result error'>❌ Nový endpoint selhal</div>";
    }
    
    // Shrnutí
    echo "<h2>Shrnutí</h2>";
    
    if ($expected_image_id) {
        echo "<div class='test-result info'>";
        echo "<strong>Očekávaný výsledek:</strong> Obrázek ID $expected_image_id<br>";
        
        if (isset($original_image_id)) {
            echo "<strong>Původní endpoint:</strong> " . ($original_image_id == $expected_image_id ? "✅ Správně" : "❌ Nesprávně") . " (ID $original_image_id)<br>";
        }
        
        if (isset($simple_image_id)) {
            echo "<strong>Nový endpoint:</strong> " . ($simple_image_id == $expected_image_id ? "✅ Správně" : "❌ Nesprávně") . " (ID $simple_image_id)<br>";
        }
        echo "</div>";
        
        if ((isset($original_image_id) && $original_image_id == $expected_image_id) || 
            (isset($simple_image_id) && $simple_image_id == $expected_image_id)) {
            echo "<div class='test-result success'>";
            echo "<h3>🎉 OPRAVA ÚSPĚŠNÁ!</h3>";
            echo "Alespoň jeden z endpointů nyní vrací správný obrázek varianty.";
            echo "</div>";
        } else {
            echo "<div class='test-result error'>";
            echo "<h3>❌ Oprava zatím neúspěšná</h3>";
            echo "Oba endpointy stále vracejí nesprávný obrázek.";
            echo "</div>";
        }
    }
    
    ?>
    
    <h2>Další kroky</h2>
    <ul>
        <li><a href="test_ajax.html?id_product=<?php echo $test_product_id; ?>&id_product_attribute=<?php echo $test_variant_id; ?>">Interaktivní AJAX test</a></li>
        <li><a href="find_red_variants.php">Najít další červené varianty pro testování</a></li>
        <li><a href="diagnostika.php?test=all">Kompletní diagnostika</a></li>
    </ul>
</body>
</html>
