<!DOCTYPE html>
<html>
<head>
    <title><PERSON><PERSON><PERSON>ý test filtrace barev</title>
    <meta charset="utf-8">
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            line-height: 1.6; 
            background: #f8f9fa;
        }
        .container { 
            max-width: 800px; 
            margin: 0 auto; 
            background: white; 
            padding: 30px; 
            border-radius: 10px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .step { 
            margin: 20px 0; 
            padding: 20px; 
            border: 2px solid #ddd; 
            border-radius: 8px; 
            background: #fafafa;
        }
        .success { border-color: #28a745; background: #f8fff9; }
        .error { border-color: #dc3545; background: #fff5f5; }
        .info { border-color: #17a2b8; background: #f0f9ff; }
        
        .big-button { 
            display: inline-block;
            padding: 15px 30px; 
            background: #007bff; 
            color: white; 
            text-decoration: none;
            border-radius: 5px; 
            font-size: 18px;
            margin: 10px;
            text-align: center;
        }
        .big-button:hover { background: #0056b3; color: white; text-decoration: none; }
        
        .checklist { list-style: none; padding: 0; }
        .checklist li { 
            margin: 10px 0; 
            padding: 10px; 
            background: white; 
            border-radius: 5px; 
            border-left: 4px solid #007bff;
        }
        .checklist li:before { content: "☐ "; font-size: 18px; margin-right: 10px; }
        
        .code { 
            background: #f1f3f4; 
            padding: 15px; 
            border-radius: 5px; 
            font-family: monospace; 
            margin: 10px 0;
            border-left: 4px solid #007bff;
        }
        
        h1 { color: #333; text-align: center; }
        h2 { color: #007bff; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Rychlý test filtrace barev</h1>
        
        <div class="info step">
            <h2>✅ JavaScript logika funguje!</h2>
            <p>Testy ukázaly, že JavaScript správně:</p>
            <ul>
                <li>✅ Normalizuje "Červená" → "cervena"</li>
                <li>✅ Detekuje "Barva-Červená" z URL</li>
                <li>✅ Porovnává varianty správně</li>
            </ul>
        </div>
        
        <div class="step">
            <h2>🔧 Kroky k dokončení</h2>
            
            <ul class="checklist">
                <li>Nahrajte opravený modul na server</li>
                <li>Nainstalujte modul v administraci</li>
                <li>Zapněte "Povolit filtraci barev" a "Debug režim"</li>
                <li>Otestujte na kategorii s produkty</li>
            </ul>
        </div>
        
        <div class="step">
            <h2>📁 Soubory k nahrání</h2>
            <div class="code">
/modules/filtrace_barev/filtrace_barev.php (opravený - ukládání konfigurace)
/modules/filtrace_barev/views/js/filtrace_barev.js
/modules/filtrace_barev/views/css/filtrace_barev.css
/modules/filtrace_barev/ajax/ (všechny diagnostické soubory)
/themes/Electech/templates/catalog/_partials/miniatures/product.tpl (s debug)
            </div>
        </div>
        
        <div class="step">
            <h2>🧪 Test na produkci</h2>
            <p>Po nahrání a instalaci modulu:</p>
            
            <ol>
                <li><strong>Jděte na kategorii s produkty</strong> (např. slunečníky)</li>
                <li><strong>Otevřete konzoli prohlížeče</strong> (F12)</li>
                <li><strong>Aplikujte červený filtr</strong></li>
                <li><strong>Sledujte konzoli</strong> - měli byste vidět:</li>
            </ol>
            
            <div class="code">
[Filtrace Barev] URL parametr q: Barva-Červená
[Filtrace Barev] Nalezen barevný filtr: cervena
[Filtrace Barev] ✅ Nalezena shoda! Varianta: Červená, ID: 7605
[Filtrace Barev] ✅ Obrázek produktu 2821 aktualizován
            </div>
        </div>
        
        <div class="success step">
            <h2>🎯 Očekávaný výsledek</h2>
            <p>Po aplikaci červeného filtru by se měly:</p>
            <ul>
                <li>✅ Zobrazit červené obrázky produktů</li>
                <li>✅ Změnit obrázky z cover na varianty</li>
                <li>✅ Vypsat debug informace v konzoli</li>
            </ul>
        </div>
        
        <div class="step">
            <h2>🔗 Užitečné odkazy</h2>
            <div style="text-align: center;">
                <a href="production_test.php" class="big-button">🧪 Test produkce</a>
                <a href="test_javascript.html" class="big-button">🔧 Test JavaScript</a>
                <a href="diagnostika.php?test=all" class="big-button">📊 Diagnostika</a>
                <a href="troubleshooting.html" class="big-button">🆘 Řešení problémů</a>
            </div>
        </div>
        
        <div class="info step">
            <h2>💡 Tipy pro debugging</h2>
            <ul>
                <li><strong>Konzole prohlížeče:</strong> Sledujte zprávy "[Filtrace Barev]"</li>
                <li><strong>Network tab:</strong> Zkontrolujte AJAX volání na get_variant_image_basic.php</li>
                <li><strong>Elements tab:</strong> Ověřte, že se mění src atribut obrázků</li>
                <li><strong>Vymazání cache:</strong> Ctrl+F5 pro obnovení bez cache</li>
            </ul>
        </div>
        
        <div class="error step" id="troubleshooting" style="display: none;">
            <h2>❌ Pokud stále nefunguje</h2>
            <p>Nejčastější problémy:</p>
            <ul>
                <li><strong>Modul není nainstalován:</strong> Zkontrolujte v administraci</li>
                <li><strong>Debug není zapnutý:</strong> Zapněte v konfiguraci modulu</li>
                <li><strong>JavaScript se nenačítá:</strong> Zkontrolujte konzoli na chyby</li>
                <li><strong>Špatné téma:</strong> Ověřte, že používáte Electech téma</li>
                <li><strong>Cache:</strong> Vymažte cache PrestaShop i prohlížeče</li>
            </ul>
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
            <button onclick="document.getElementById('troubleshooting').style.display='block'" 
                    style="padding: 10px 20px; background: #dc3545; color: white; border: none; border-radius: 3px;">
                ❌ Stále nefunguje? Klikněte zde
            </button>
        </div>
    </div>
    
    <script>
    // Automatická kontrola, zda je na stránce s filtry
    if (window.location.search.includes('q=')) {
        const banner = document.createElement('div');
        banner.style.cssText = 'position: fixed; top: 0; left: 0; right: 0; background: #28a745; color: white; padding: 10px; text-align: center; z-index: 9999;';
        banner.innerHTML = '✅ Detekován filtr v URL! Zkontrolujte konzoli (F12) pro debug informace.';
        document.body.appendChild(banner);
        
        setTimeout(() => banner.remove(), 5000);
    }
    </script>
</body>
</html>
