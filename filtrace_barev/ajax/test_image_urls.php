<?php
/**
 * Test různých metod generování URL obrázků
 */

// Načtení PrestaShop frameworku
require_once dirname(__FILE__) . '/../../../config/config.inc.php';
require_once dirname(__FILE__) . '/../../../init.php';

header('Content-Type: text/html; charset=utf-8');

$test_product_id = 2821;
$test_variant_id = 7605;
$test_image_id = 31794;

?>
<!DOCTYPE html>
<html>
<head>
    <title>Test URL obrázků</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .test-section { margin: 20px 0; padding: 15px; border: 2px solid #ddd; border-radius: 5px; }
        .success { border-color: #28a745; background-color: #f8fff9; }
        .error { border-color: #dc3545; background-color: #fff8f8; }
        .info { border-color: #17a2b8; background-color: #f0f9ff; }
        .url-test { margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 3px; }
        .url-working { border-left: 4px solid #28a745; }
        .url-broken { border-left: 4px solid #dc3545; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; font-size: 12px; }
        .image-test { margin: 10px 0; text-align: center; }
        .image-test img { max-width: 150px; border: 1px solid #ddd; margin: 5px; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>🔍 Test URL obrázků</h1>
    
    <div class="info test-section">
        <h2>📋 Testovací data</h2>
        <ul>
            <li><strong>Produkt ID:</strong> <?php echo $test_product_id; ?></li>
            <li><strong>Varianta ID:</strong> <?php echo $test_variant_id; ?></li>
            <li><strong>Obrázek ID:</strong> <?php echo $test_image_id; ?></li>
            <li><strong>Správná cesta:</strong> <code>img/p/3/1/7/9/4/31794-home_default.jpg</code></li>
        </ul>
    </div>
    
    <?php
    
    $context = Context::getContext();
    $product = new Product($test_product_id, false, $context->language->id);
    $base_url = $context->shop->getBaseURL(true);
    
    echo "<div class='test-section'>";
    echo "<h2>🧪 Test různých metod generování URL</h2>";
    
    // Metoda 1: Link::getImageLink (původní)
    $link = new Link();
    $url1 = $link->getImageLink(
        $product->link_rewrite,
        $test_image_id,
        ImageType::getFormattedName('home_default')
    );
    if (strpos($url1, 'http') !== 0) {
        $url1 = $base_url . ltrim($url1, '/');
    }
    
    // Metoda 2: Ruční generování cesty
    function generateImagePath($image_id) {
        $image_id = (int)$image_id;
        $path = '';
        $id_str = (string)$image_id;
        for ($i = 0; $i < strlen($id_str); $i++) {
            $path .= $id_str[$i] . '/';
        }
        return $path;
    }
    
    $image_path = generateImagePath($test_image_id);
    $url2 = $base_url . 'img/p/' . $image_path . $test_image_id . '-home_default.jpg';
    
    // Metoda 3: Image::getExistingImgPath
    $image = new Image($test_image_id);
    $url3 = '';
    if (Validate::isLoadedObject($image)) {
        try {
            $existing_path = $image->getExistingImgPath();
            if ($existing_path) {
                $url3 = $base_url . 'img/p/' . $existing_path . '-home_default.jpg';
            }
        } catch (Exception $e) {
            $url3 = 'Chyba: ' . $e->getMessage();
        }
    }
    
    // Metoda 4: Známá správná cesta
    $url4 = $base_url . 'img/p/3/1/7/9/4/31794-home_default.jpg';
    
    $methods = [
        'Link::getImageLink (původní)' => $url1,
        'Ruční generování cesty' => $url2,
        'Image::getExistingImgPath' => $url3,
        'Známá správná cesta' => $url4
    ];
    
    echo "<table>";
    echo "<tr><th>Metoda</th><th>Vygenerovaná URL</th><th>Test</th></tr>";
    
    foreach ($methods as $method => $url) {
        echo "<tr>";
        echo "<td>$method</td>";
        echo "<td><code>" . htmlspecialchars($url) . "</code></td>";
        echo "<td>";
        if (strpos($url, 'http') === 0) {
            echo "<a href='$url' target='_blank'>Test načítání</a>";
        } else {
            echo "Nevalidní URL";
        }
        echo "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "</div>";
    
    // Test AJAX endpointů
    echo "<div class='test-section'>";
    echo "<h2>🔧 Test AJAX endpointů</h2>";
    
    $endpoints = [
        'Původní basic' => 'get_variant_image_basic.php',
        'Opravený fixed' => 'get_variant_image_fixed.php'
    ];
    
    foreach ($endpoints as $name => $file) {
        echo "<h3>$name ($file)</h3>";
        
        $_GET['id_product'] = $test_product_id;
        $_GET['id_product_attribute'] = $test_variant_id;
        
        ob_start();
        if (file_exists(dirname(__FILE__) . '/' . $file)) {
            include dirname(__FILE__) . '/' . $file;
        } else {
            echo json_encode(['success' => false, 'error' => 'File not found']);
        }
        $output = ob_get_clean();
        
        echo "<h4>Výstup:</h4>";
        echo "<pre>" . htmlspecialchars($output) . "</pre>";
        
        $data = json_decode($output, true);
        if ($data && $data['success']) {
            $ajax_url = $data['image_url'];
            echo "<h4>Test načítání:</h4>";
            echo "<div class='image-test'>";
            echo "<img src='$ajax_url' alt='Test $name' onload=\"this.parentNode.style.borderColor='#28a745'\" onerror=\"this.parentNode.style.borderColor='#dc3545'\" style='border: 2px solid #ddd;'>";
            echo "<br><small>$ajax_url</small>";
            echo "</div>";
        }
    }
    
    echo "</div>";
    
    ?>
    
    <div class="test-section">
        <h2>🎯 Interpretace výsledků</h2>
        <ul>
            <li><strong>Zelený rámeček:</strong> Obrázek se načetl správně ✅</li>
            <li><strong>Červený rámeček:</strong> Obrázek se nenačetl ❌</li>
            <li><strong>Správná struktura:</strong> <code>img/p/3/1/7/9/4/31794-home_default.jpg</code></li>
        </ul>
    </div>
    
    <div class="success test-section">
        <h2>🚀 Doporučení</h2>
        <p>Použijte metodu, která generuje funkční URL a aktualizujte JavaScript aby používal správný endpoint.</p>
        
        <h3>Aktualizace JavaScript:</h3>
        <pre>
// V filtrace_barev.js změňte:
const endpointUrl = `${prestashop.urls.base_url}modules/${CONFIG.moduleDirectory}/ajax/get_variant_image_fixed.php?id_product=${productId}&id_product_attribute=${attributeId}`;
        </pre>
    </div>
    
    <script>
    // Automatické testování načítání obrázků
    window.addEventListener('load', function() {
        const images = document.querySelectorAll('.image-test img');
        images.forEach(img => {
            img.addEventListener('load', function() {
                console.log('✅ Obrázek se načetl:', this.src);
                this.style.borderColor = '#28a745';
            });
            img.addEventListener('error', function() {
                console.log('❌ Obrázek se nenačetl:', this.src);
                this.style.borderColor = '#dc3545';
            });
        });
    });
    </script>
</body>
</html>
