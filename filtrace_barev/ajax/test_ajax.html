<!DOCTYPE html>
<html>
<head>
    <title>Test AJAX endpointu pro filtrace barev</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .form-group { margin: 10px 0; }
        label { display: inline-block; width: 150px; }
        input[type="number"] { width: 100px; }
        button { padding: 10px 20px; margin: 10px 5px; }
        .result { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
        .image-preview { margin: 10px 0; }
        .image-preview img { max-width: 200px; border: 1px solid #ddd; }
    </style>
</head>
<body>
    <h1>Test AJAX endpointu pro filtrace barev</h1>
    
    <form id="testForm">
        <div class="form-group">
            <label for="id_product">ID produktu:</label>
            <input type="number" id="id_product" name="id_product" value="" required>
        </div>
        
        <div class="form-group">
            <label for="id_product_attribute">ID varianty:</label>
            <input type="number" id="id_product_attribute" name="id_product_attribute" value="" required>
        </div>
        
        <div class="form-group">
            <button type="submit">Testovat AJAX</button>
            <button type="button" onclick="loadExampleData()">Načíst ukázková data</button>
            <button type="button" onclick="clearResults()">Vymazat výsledky</button>
        </div>
    </form>
    
    <div id="results"></div>
    
    <div class="form-group">
        <h2>Návod k použití:</h2>
        <ol>
            <li>Nahrajte tento soubor na server do složky <code>/modules/filtrace_barev/ajax/</code></li>
            <li>Otevřete v prohlížeči: <code>https://vase-domena.cz/modules/filtrace_barev/ajax/test_ajax.html</code></li>
            <li>Zadejte ID produktu a ID varianty, které chcete testovat</li>
            <li>Klikněte na "Testovat AJAX"</li>
            <li>Zkontrolujte výsledky - měli byste vidět URL obrázků</li>
        </ol>
        
        <h3>Jak najít ID produktu a varianty:</h3>
        <ul>
            <li>Použijte diagnostický nástroj: <code>/modules/filtrace_barev/ajax/diagnostika.php</code></li>
            <li>Nebo se podívejte do administrace PrestaShop → Katalog → Produkty</li>
            <li>ID varianty najdete v sekci "Kombinace" u konkrétního produktu</li>
        </ul>
    </div>
    
    <script>
    document.getElementById('testForm').addEventListener('submit', function(e) {
        e.preventDefault();
        testAjax();
    });
    
    async function testAjax() {
        const idProduct = document.getElementById('id_product').value;
        const idProductAttribute = document.getElementById('id_product_attribute').value;
        
        if (!idProduct || !idProductAttribute) {
            showResult('Chyba: Zadejte ID produktu i ID varianty', 'error');
            return;
        }
        
        showResult('Testování...', 'info');
        
        try {
            // Určíme URL endpointu
            const baseUrl = window.location.origin + window.location.pathname.replace('/test_ajax.html', '');
            const endpointUrl = `${baseUrl}/get_variant_image.php?id_product=${idProduct}&id_product_attribute=${idProductAttribute}`;
            
            console.log('Volám endpoint:', endpointUrl);
            
            const response = await fetch(endpointUrl);
            const responseText = await response.text();
            
            console.log('Response status:', response.status);
            console.log('Response text:', responseText);
            
            if (!response.ok) {
                throw new Error(`Server vrátil status ${response.status}: ${responseText}`);
            }
            
            // Pokusíme se parsovat JSON
            let data;
            try {
                data = JSON.parse(responseText);
            } catch (parseError) {
                throw new Error(`Neplatný JSON: ${responseText}`);
            }
            
            if (data.success) {
                let resultHtml = `
                    <h3>✅ Úspěch!</h3>
                    <p><strong>URL obrázku:</strong> <a href="${data.image_url}" target="_blank">${data.image_url}</a></p>
                `;
                
                if (data.large_image_url) {
                    resultHtml += `<p><strong>URL velkého obrázku:</strong> <a href="${data.large_image_url}" target="_blank">${data.large_image_url}</a></p>`;
                }
                
                resultHtml += `
                    <div class="image-preview">
                        <h4>Náhled obrázku:</h4>
                        <img src="${data.image_url}" alt="Náhled varianty" onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                        <p style="display:none; color:red;">Obrázek se nepodařilo načíst</p>
                    </div>
                `;
                
                resultHtml += `<pre>${JSON.stringify(data, null, 2)}</pre>`;
                
                showResult(resultHtml, 'success');
            } else {
                showResult(`❌ Chyba: ${data.error}<br><pre>${JSON.stringify(data, null, 2)}</pre>`, 'error');
            }
            
        } catch (error) {
            console.error('Chyba při volání AJAX:', error);
            showResult(`❌ Chyba při volání AJAX: ${error.message}`, 'error');
        }
    }
    
    function showResult(message, type) {
        const resultsDiv = document.getElementById('results');
        const className = type === 'error' ? 'error' : (type === 'success' ? 'success' : '');
        resultsDiv.innerHTML = `<div class="result ${className}">${message}</div>`;
    }
    
    function loadExampleData() {
        // Ukázková data - upravte podle vašich produktů
        document.getElementById('id_product').value = '1';
        document.getElementById('id_product_attribute').value = '1';
        showResult('Načtena ukázková data. Upravte ID podle vašich produktů.', 'info');
    }
    
    function clearResults() {
        document.getElementById('results').innerHTML = '';
    }
    
    // Automatické načtení z URL parametrů
    window.addEventListener('load', function() {
        const urlParams = new URLSearchParams(window.location.search);
        const idProduct = urlParams.get('id_product');
        const idProductAttribute = urlParams.get('id_product_attribute');
        
        if (idProduct) {
            document.getElementById('id_product').value = idProduct;
        }
        if (idProductAttribute) {
            document.getElementById('id_product_attribute').value = idProductAttribute;
        }
        
        // Pokud jsou oba parametry, automaticky spustíme test
        if (idProduct && idProductAttribute) {
            testAjax();
        }
    });
    </script>
</body>
</html>
