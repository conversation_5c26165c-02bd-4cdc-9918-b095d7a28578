<!DOCTYPE html>
<html>
<head>
    <title>Test JavaScript logiky</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .test-section { margin: 20px 0; padding: 15px; border: 2px solid #ddd; border-radius: 5px; }
        .success { border-color: #28a745; background-color: #f8fff9; }
        .error { border-color: #dc3545; background-color: #fff8f8; }
        .info { border-color: #17a2b8; background-color: #f0f9ff; }
        .code { background: #f1f3f4; padding: 15px; border-radius: 5px; font-family: monospace; margin: 10px 0; }
        .result { background: #e9ecef; padding: 10px; border-radius: 3px; margin: 10px 0; }
        button { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>🧪 Test JavaScript logiky pro filtrace barev</h1>
    
    <div class="info test-section">
        <h2>📋 Účel testu</h2>
        <p>Tento test ověří, zda JavaScript správně detekuje a normalizuje barevné filtry z URL.</p>
        <p><strong>Problém:</strong> URL obsahuje <code>?q=Barva-Červená</code>, ale JavaScript hledá <code>barva-cervena</code></p>
    </div>
    
    <div class="test-section">
        <h2>1. Test normalizace textu</h2>
        <p>Funkce <code>normalizeText()</code> by měla převést "Červená" na "cervena":</p>
        
        <div class="code">
function normalizeText(text) {
    if (!text) return '';
    return text.toLowerCase()
        .normalize("NFD").replace(/[\u0300-\u036f]/g, "")
        .replace(/\s+/g, '-');
}
        </div>
        
        <button onclick="testNormalization()">🧪 Test normalizace</button>
        <div id="normalization-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>2. Test detekce URL parametrů</h2>
        <p>Test různých formátů URL:</p>
        
        <button onclick="testUrlDetection()">🔍 Test URL detekce</button>
        <div id="url-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>3. Test s reálnou URL</h2>
        <p>Aktuální URL: <code id="current-url"></code></p>
        <p>Parametr 'q': <code id="current-q"></code></p>
        
        <button onclick="testCurrentUrl()">🎯 Test aktuální URL</button>
        <div id="current-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>4. Simulace s testovací URL</h2>
        <p>Zadejte URL pro test:</p>
        <input type="text" id="test-url" value="https://czimg-dev1.www2.peterman.cz/31-slunecniky?q=Barva-Červená" style="width: 100%; padding: 10px; margin: 10px 0;">
        <button onclick="testCustomUrl()">🧪 Test vlastní URL</button>
        <div id="custom-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>5. Test porovnání variant</h2>
        <p>Simulace porovnání názvů variant s filtry:</p>
        
        <button onclick="testVariantMatching()">🔄 Test porovnání variant</button>
        <div id="variant-result" class="result"></div>
    </div>
    
    <script>
    // Kopie funkcí z product.tpl
    function normalizeText(text) {
        if (!text) return '';
        return text.toLowerCase()
            .normalize("NFD").replace(/[\u0300-\u036f]/g, "")
            .replace(/\s+/g, '-');
    }
    
    function getActiveColorFilters(urlString = null) {
        const filters = new Set();
        const url = urlString || window.location.href;
        const urlParams = new URLSearchParams(url.split('?')[1] || '');
        const qParam = urlParams.get('q');
        
        if (qParam) {
            const qParts = qParam.split('/');
            qParts.forEach(part => {
                // Normalizujeme část pro porovnání
                const normalizedPart = normalizeText(part);
                // Hledáme "barva-" na začátku (po normalizaci)
                if (normalizedPart.startsWith('barva-')) {
                    // Extrahujeme název barvy a normalizujeme ho
                    const colorName = normalizedPart.substring(6);
                    filters.add(colorName);
                }
            });
        }
        return Array.from(filters);
    }
    
    function testNormalization() {
        const testCases = [
            'Červená',
            'Modrá', 
            'Zelená',
            'Žlutá',
            'Černá',
            'Bílá',
            'Růžová',
            'Fialová',
            'Barva-Červená'
        ];
        
        let html = '<h4>Výsledky normalizace:</h4><table><tr><th>Původní</th><th>Normalizováno</th></tr>';
        
        testCases.forEach(text => {
            const normalized = normalizeText(text);
            html += `<tr><td>${text}</td><td>${normalized}</td></tr>`;
        });
        
        html += '</table>';
        document.getElementById('normalization-result').innerHTML = html;
    }
    
    function testUrlDetection() {
        const testUrls = [
            'https://example.com/kategorie?q=Barva-Červená',
            'https://example.com/kategorie?q=barva-cervena',
            'https://example.com/kategorie?q=Barva-Červená/kategorie-123',
            'https://example.com/kategorie?q=barva-modra/velikost-m',
            'https://example.com/kategorie?q=cervena',
            'https://example.com/kategorie?color=red',
            'https://example.com/kategorie'
        ];
        
        let html = '<h4>Test detekce URL:</h4><table><tr><th>URL</th><th>Parametr q</th><th>Detekované filtry</th></tr>';
        
        testUrls.forEach(url => {
            const urlParams = new URLSearchParams(url.split('?')[1] || '');
            const qParam = urlParams.get('q') || 'žádný';
            const filters = getActiveColorFilters(url);
            
            html += `<tr><td>${url}</td><td>${qParam}</td><td>${filters.join(', ') || 'žádné'}</td></tr>`;
        });
        
        html += '</table>';
        document.getElementById('url-result').innerHTML = html;
    }
    
    function testCurrentUrl() {
        document.getElementById('current-url').textContent = window.location.href;
        
        const urlParams = new URLSearchParams(window.location.search);
        const qParam = urlParams.get('q');
        document.getElementById('current-q').textContent = qParam || 'žádný';
        
        const filters = getActiveColorFilters();
        
        let html = '<h4>Analýza aktuální URL:</h4>';
        html += `<p><strong>Celá URL:</strong> ${window.location.href}</p>`;
        html += `<p><strong>Parametr q:</strong> ${qParam || 'žádný'}</p>`;
        html += `<p><strong>Detekované filtry:</strong> ${filters.join(', ') || 'žádné'}</p>`;
        
        if (filters.length > 0) {
            html += '<p class="success">✅ Filtry byly úspěšně detekovány!</p>';
        } else {
            html += '<p class="error">❌ Žádné filtry nebyly detekovány</p>';
        }
        
        document.getElementById('current-result').innerHTML = html;
    }
    
    function testCustomUrl() {
        const testUrl = document.getElementById('test-url').value;
        const filters = getActiveColorFilters(testUrl);
        
        const urlParams = new URLSearchParams(testUrl.split('?')[1] || '');
        const qParam = urlParams.get('q');
        
        let html = '<h4>Analýza vlastní URL:</h4>';
        html += `<p><strong>URL:</strong> ${testUrl}</p>`;
        html += `<p><strong>Parametr q:</strong> ${qParam || 'žádný'}</p>`;
        html += `<p><strong>Detekované filtry:</strong> ${filters.join(', ') || 'žádné'}</p>`;
        
        if (filters.length > 0) {
            html += '<p style="color: green;">✅ Filtry byly úspěšně detekovány!</p>';
        } else {
            html += '<p style="color: red;">❌ Žádné filtry nebyly detekovány</p>';
        }
        
        document.getElementById('custom-result').innerHTML = html;
    }
    
    function testVariantMatching() {
        const activeFilters = ['cervena', 'modra']; // Simulované aktivní filtry
        const variantNames = [
            'Červená',
            'Modrá',
            'Zelená', 
            'Žlutá',
            'Černá',
            'Red',
            'Blue'
        ];
        
        let html = '<h4>Test porovnání variant:</h4>';
        html += `<p><strong>Aktivní filtry:</strong> ${activeFilters.join(', ')}</p>`;
        html += '<table><tr><th>Název varianty</th><th>Normalizováno</th><th>Shoda</th></tr>';
        
        variantNames.forEach(variantName => {
            const normalized = normalizeText(variantName);
            const matches = activeFilters.includes(normalized);
            const matchText = matches ? '✅ ANO' : '❌ NE';
            
            html += `<tr><td>${variantName}</td><td>${normalized}</td><td>${matchText}</td></tr>`;
        });
        
        html += '</table>';
        document.getElementById('variant-result').innerHTML = html;
    }
    
    // Automaticky spustíme test aktuální URL při načtení
    window.addEventListener('load', function() {
        testCurrentUrl();
    });
    </script>
    
    <div class="info test-section">
        <h2>📝 Interpretace výsledků</h2>
        <ul>
            <li><strong>Normalizace:</strong> "Červená" by měla být převedena na "cervena"</li>
            <li><strong>URL detekce:</strong> "Barva-Červená" by měla být detekována jako filtr "cervena"</li>
            <li><strong>Porovnání variant:</strong> Varianta "Červená" by měla odpovídat filtru "cervena"</li>
        </ul>
        
        <p><strong>Pokud testy selžou:</strong> JavaScript logika potřebuje úpravu pro správnou detekci vašeho formátu URL.</p>
    </div>
</body>
</html>
