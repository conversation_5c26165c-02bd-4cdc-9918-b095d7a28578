/**
 * JavaScript pro filtraci barev - zobrazování správných obrázků variant
 * 
 * <AUTHOR> Agent
 * @version 1.0.0
 */

(function() {
    'use strict';

    // Kontrola, zda je modul povolen
    if (typeof window.FILTRACE_BAREV_ENABLED !== 'undefined' && !window.FILTRACE_BAREV_ENABLED) {
        console.log('[Filtrace <PERSON>ev] Modul je vypnutý - ukončuji');
        return;
    }

    // Konfigurace
    const CONFIG = {
        debug: window.FILTRACE_BAREV_DEBUG || window.prestashop?.debug || false,
        moduleDirectory: 'filtrace_barev',
        selectors: {
            productMiniature: '.js-product-miniature',
            variantLinks: '.variant-links a[data-variant-name]',
            productImage: '.product-thumbnail img',
            productLink: '.product-thumbnail a'
        }
    };

    /**
     * Debug log
     */
    function debugLog(...args) {
        if (CONFIG.debug) {
            console.log('[Filt<PERSON>]', ...args);
        }
    }

    /**
     * Normalizace textu pro porovnání
     */
    function normalizeText(text) {
        if (!text) return '';
        return text.toLowerCase()
            .normalize("NFD").replace(/[\u0300-\u036f]/g, "")
            .replace(/\s+/g, '-');
    }

    /**
     * Získání aktivních barevných filtrů z URL
     */
    function getActiveColorFilters() {
        const filters = new Set();
        const urlParams = new URLSearchParams(window.location.search);
        const qParam = urlParams.get('q');
        
        debugLog('URL parametr q:', qParam);
        
        if (qParam) {
            const qParts = qParam.split('/');
            qParts.forEach(part => {
                // Normalizujeme část pro porovnání
                const normalizedPart = normalizeText(part);
                debugLog('Zpracovávám část URL:', part, '-> normalizováno:', normalizedPart);
                
                // Hledáme "barva-" na začátku (po normalizaci)
                if (normalizedPart.startsWith('barva-')) {
                    // Extrahujeme název barvy a normalizujeme ho
                    const colorName = normalizedPart.substring(6);
                    filters.add(colorName);
                    debugLog('Nalezen barevný filtr:', colorName);
                }
            });
        }
        
        const result = Array.from(filters);
        debugLog('Všechny detekované filtry:', result);
        return result;
    }

    /**
     * AJAX volání pro získání obrázku varianty
     */
    async function loadVariantImage(productId, attributeId) {
        const endpointUrl = `${prestashop.urls.base_url}modules/${CONFIG.moduleDirectory}/ajax/get_variant_image_fixed.php?id_product=${productId}&id_product_attribute=${attributeId}`;

        debugLog('AJAX volání:', endpointUrl);
        
        try {
            const response = await fetch(endpointUrl);
            const data = await response.json();
            
            debugLog('AJAX odpověď:', data);
            
            if (data.success) {
                return {
                    imageUrl: data.image_url,
                    largeImageUrl: data.large_image_url
                };
            } else {
                debugLog('AJAX chyba:', data.error);
                return null;
            }
        } catch (error) {
            debugLog('AJAX selhalo:', error);
            return null;
        }
    }

    /**
     * Aktualizace obrázku produktu
     */
    function updateProductImage(productElement, imageUrl, largeImageUrl, productUrl) {
        const imgElement = productElement.querySelector(CONFIG.selectors.productImage);
        const fliperImgElement = productElement.querySelector('.fliper_image');
        const linkElement = productElement.querySelector(CONFIG.selectors.productLink);

        if (imgElement && imageUrl) {
            debugLog('Aktualizuji hlavní obrázek:', imageUrl);

            // Ověříme, že URL je validní
            if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
                imgElement.src = imageUrl;
                imgElement.srcset = imageUrl;
                imgElement.setAttribute('data-src', imageUrl);

                // Aktualizujeme také data-full-size-image-url pokud existuje
                if (largeImageUrl) {
                    imgElement.setAttribute('data-full-size-image-url', largeImageUrl);
                }
            } else {
                debugLog('❌ Nevalidní URL obrázku:', imageUrl);
            }
        }

        // Aktualizujeme také fliper obrázek pokud existuje
        if (fliperImgElement && imageUrl) {
            debugLog('Aktualizuji fliper obrázek:', imageUrl);

            // Ověříme, že URL je validní
            if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
                fliperImgElement.src = imageUrl;
                fliperImgElement.setAttribute('data-src', imageUrl);

                if (largeImageUrl) {
                    fliperImgElement.setAttribute('data-full-size-image-url', largeImageUrl);
                }
            } else {
                debugLog('❌ Nevalidní URL fliper obrázku:', imageUrl);
            }
        }

        // Aktualizujeme URL produktu pokud je k dispozici
        if (linkElement && productUrl) {
            debugLog('Aktualizuji URL produktu:', productUrl);
            linkElement.href = productUrl;
        }
    }

    /**
     * Hlavní funkce pro aktualizaci obrázků produktů
     */
    async function updateProductImages() {
        const activeFilters = getActiveColorFilters();
        
        debugLog('=== Spouštím filtraci barev ===');
        debugLog('URL:', window.location.href);
        debugLog('Aktivní filtry:', activeFilters);
        
        // Pokud nejsou aktivní filtry, ukončíme
        if (activeFilters.length === 0) {
            debugLog('Žádné aktivní barevné filtry - ukončuji');
            return;
        }

        const productMiniatures = document.querySelectorAll(CONFIG.selectors.productMiniature + ':not(.variant-logic-initialized)');
        debugLog('Nalezeno produktů k zpracování:', productMiniatures.length);

        for (const productElement of productMiniatures) {
            const productId = productElement.dataset.idProduct;
            const currentAttributeId = productElement.dataset.idProductAttribute;

            debugLog(`Zpracovávám produkt ${productId}, aktuální varianta ${currentAttributeId}`);

            if (!productId) continue;

            let targetAttributeId = null;
            let targetVariantUrl = null;

            // Hledáme variantu odpovídající aktivnímu filtru
            const variants = productElement.querySelectorAll(CONFIG.selectors.variantLinks);
            debugLog(`Produkt ${productId} má ${variants.length} variant`);

            for (const variant of variants) {
                const variantName = variant.dataset.variantName;
                const normalizedVariantName = normalizeText(variantName);
                
                debugLog(`Varianta: "${variantName}" -> normalizováno: "${normalizedVariantName}"`);
                
                if (activeFilters.includes(normalizedVariantName)) {
                    targetAttributeId = variant.dataset.variantAttributeId;
                    targetVariantUrl = variant.dataset.variantUrl;
                    debugLog(`✅ Nalezena shoda! Varianta: ${variantName}, ID: ${targetAttributeId}`);
                    break;
                }
            }

            if (!targetAttributeId) {
                debugLog(`❌ Žádná varianta neodpovídá aktivním filtrům pro produkt ${productId}`);
                // Označíme jako zpracovaný i když jsme nenašli shodu
                productElement.classList.add('variant-logic-initialized');
                continue;
            }

            // Zkontrolujeme, zda má správný obrázek i když má správnou variantu
            const currentImg = productElement.querySelector(CONFIG.selectors.productImage);
            const currentImageSrc = currentImg ? currentImg.src : '';

            if (currentAttributeId === targetAttributeId) {
                debugLog(`Produkt ${productId} už má správnou variantu ${targetAttributeId}`);

                // Ale zkontrolujeme, zda má i správný obrázek
                debugLog(`Aktuální obrázek: ${currentImageSrc}`);

                // Pokud obrázek neobsahuje ID cílové varianty, musíme ho aktualizovat
                const imageData = await loadVariantImage(productId, targetAttributeId);
                if (imageData && imageData.imageUrl !== currentImageSrc) {
                    debugLog(`🔄 Aktualizuji obrázek i přes správnou variantu`);
                    updateProductImage(
                        productElement,
                        imageData.imageUrl,
                        imageData.largeImageUrl,
                        targetVariantUrl
                    );
                } else {
                    debugLog(`✅ Obrázek je už správný`);
                }

                productElement.classList.add('variant-logic-initialized');
                continue;
            }

            // Načteme obrázek varianty přes AJAX
            debugLog(`Načítám obrázek pro variantu ${targetAttributeId}`);
            const imageData = await loadVariantImage(productId, targetAttributeId);
            
            if (imageData) {
                updateProductImage(
                    productElement, 
                    imageData.imageUrl, 
                    imageData.largeImageUrl, 
                    targetVariantUrl
                );
                
                // Aktualizujeme data atribut pro budoucí reference
                productElement.dataset.idProductAttribute = targetAttributeId;
                debugLog(`✅ Obrázek produktu ${productId} aktualizován na variantu ${targetAttributeId}`);
            } else {
                debugLog(`❌ Nepodařilo se načíst obrázek pro variantu ${targetAttributeId}`);
            }

            // Označíme jako zpracovaný
            productElement.classList.add('variant-logic-initialized');
        }

        debugLog('=== Filtrace barev dokončena ===');
    }

    /**
     * Inicializace po načtení DOM
     */
    function init() {
        debugLog('Inicializuji filtraci barev');
        
        // Spustíme při načtení stránky
        updateProductImages();
        
        // Spustíme při změně URL (AJAX navigace)
        let lastUrl = location.href;
        new MutationObserver(() => {
            const url = location.href;
            if (url !== lastUrl) {
                lastUrl = url;
                debugLog('URL se změnila, spouštím filtraci znovu');
                setTimeout(updateProductImages, 500); // Malé zpoždění pro načtení obsahu
            }
        }).observe(document, { subtree: true, childList: true });
        
        // Spustíme při popstate (tlačítko zpět/vpřed)
        window.addEventListener('popstate', () => {
            debugLog('Popstate event, spouštím filtraci znovu');
            setTimeout(updateProductImages, 500);
        });
    }

    // Spustíme po načtení DOM
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

    // Export pro debugging
    window.FiltraceBarev = {
        updateProductImages,
        getActiveColorFilters,
        normalizeText,
        debugLog
    };

})();
