/**
 * CSS pro filtraci barev
 * 
 * <AUTHOR> Agent
 * @version 1.0.0
 */

/* Animace při z<PERSON> obrázku */
.js-product-miniature .product-thumbnail img {
    transition: opacity 0.3s ease-in-out;
}

.js-product-miniature.variant-updating .product-thumbnail img {
    opacity: 0.7;
}

/* Debug indikátor */
.js-product-miniature.variant-logic-initialized::after {
    content: "✓";
    position: absolute;
    top: 5px;
    right: 5px;
    background: #28a745;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    z-index: 10;
    opacity: 0;
    transition: opacity 0.3s ease;
}

/* Zobrazit debug indikátor pouze v debug režimu */
body.filtrace-barev-debug .js-product-miniature.variant-logic-initialized::after {
    opacity: 1;
}

/* Loading indikátor */
.js-product-miniature.variant-loading .product-thumbnail::before {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 30px;
    height: 30px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 10;
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Zvýraznění aktivních variant */
.variant-links a[data-variant-name].active-variant {
    border: 2px solid #007bff;
    box-shadow: 0 0 5px rgba(0, 123, 255, 0.5);
}
