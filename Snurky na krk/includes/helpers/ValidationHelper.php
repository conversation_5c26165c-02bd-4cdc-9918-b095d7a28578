<?php

/**
 * Helper třída pro validaci dat
 * Obsahuje různé validační funkce pro formuláře a vstupní data
 */
class ValidationHelper
{
    /**
     * Validuje povinné pole
     */
    public static function required($value, $fieldName = 'Pole')
    {
        if (empty($value) || (is_string($value) && trim($value) === '')) {
            return "$fieldName je povinné";
        }
        return true;
    }
    
    /**
     * Validuje minimální délku
     */
    public static function minLength($value, $min, $fieldName = 'Pole')
    {
        if (strlen($value) < $min) {
            return "$fieldName musí mít alespoň $min znaků";
        }
        return true;
    }
    
    /**
     * Validuje maximální délku
     */
    public static function maxLength($value, $max, $fieldName = 'Pole')
    {
        if (strlen($value) > $max) {
            return "$fieldName může mít maximálně $max znaků";
        }
        return true;
    }
    
    /**
     * Validuje email
     */
    public static function email($email, $fieldName = 'Email')
    {
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            return "$fieldName není ve správném formátu";
        }
        return true;
    }
    
    /**
     * Validuje telefonní číslo
     */
    public static function phone($phone, $fieldName = 'Telefon')
    {
        // Odstraň mezery a pomlčky
        $cleanPhone = preg_replace('/[\s\-]/', '', $phone);
        
        // České telefonní číslo
        if (!preg_match('/^(\+420)?[0-9]{9}$/', $cleanPhone)) {
            return "$fieldName není ve správném formátu (např. +420123456789)";
        }
        return true;
    }
    
    /**
     * Validuje URL
     */
    public static function url($url, $fieldName = 'URL')
    {
        if (!filter_var($url, FILTER_VALIDATE_URL)) {
            return "$fieldName není ve správném formátu";
        }
        return true;
    }
    
    /**
     * Validuje číslo
     */
    public static function numeric($value, $fieldName = 'Pole')
    {
        if (!is_numeric($value)) {
            return "$fieldName musí být číslo";
        }
        return true;
    }
    
    /**
     * Validuje celé číslo
     */
    public static function integer($value, $fieldName = 'Pole')
    {
        if (!filter_var($value, FILTER_VALIDATE_INT)) {
            return "$fieldName musí být celé číslo";
        }
        return true;
    }
    
    /**
     * Validuje minimální hodnotu
     */
    public static function min($value, $min, $fieldName = 'Pole')
    {
        if ($value < $min) {
            return "$fieldName musí být alespoň $min";
        }
        return true;
    }
    
    /**
     * Validuje maximální hodnotu
     */
    public static function max($value, $max, $fieldName = 'Pole')
    {
        if ($value > $max) {
            return "$fieldName může být maximálně $max";
        }
        return true;
    }
    
    /**
     * Validuje datum
     */
    public static function date($date, $format = 'Y-m-d', $fieldName = 'Datum')
    {
        $d = DateTime::createFromFormat($format, $date);
        if (!$d || $d->format($format) !== $date) {
            return "$fieldName není ve správném formátu";
        }
        return true;
    }
    
    /**
     * Validuje regulární výraz
     */
    public static function pattern($value, $pattern, $message, $fieldName = 'Pole')
    {
        if (!preg_match($pattern, $value)) {
            return $message ?: "$fieldName není ve správném formátu";
        }
        return true;
    }
    
    /**
     * Validuje výběr z možností
     */
    public static function inArray($value, $options, $fieldName = 'Pole')
    {
        if (!in_array($value, $options)) {
            return "$fieldName obsahuje neplatnou hodnotu";
        }
        return true;
    }
    
    /**
     * Validuje shodu dvou polí (např. hesla)
     */
    public static function matches($value1, $value2, $fieldName = 'Pole')
    {
        if ($value1 !== $value2) {
            return "$fieldName se neshodují";
        }
        return true;
    }
    
    /**
     * Validuje sílu hesla
     */
    public static function passwordStrength($password, $fieldName = 'Heslo')
    {
        $errors = [];
        
        if (strlen($password) < 8) {
            $errors[] = "$fieldName musí mít alespoň 8 znaků";
        }
        
        if (!preg_match('/[a-z]/', $password)) {
            $errors[] = "$fieldName musí obsahovat alespoň jedno malé písmeno";
        }
        
        if (!preg_match('/[A-Z]/', $password)) {
            $errors[] = "$fieldName musí obsahovat alespoň jedno velké písmeno";
        }
        
        if (!preg_match('/[0-9]/', $password)) {
            $errors[] = "$fieldName musí obsahovat alespoň jednu číslici";
        }
        
        return empty($errors) ? true : $errors;
    }
    
    /**
     * Validuje soubor
     */
    public static function file($file, $allowedTypes = [], $maxSize = null, $fieldName = 'Soubor')
    {
        if (!isset($file['tmp_name']) || !is_uploaded_file($file['tmp_name'])) {
            return "$fieldName nebyl nahrán";
        }
        
        // Ověř typ souboru
        if (!empty($allowedTypes)) {
            $finfo = finfo_open(FILEINFO_MIME_TYPE);
            $mimeType = finfo_file($finfo, $file['tmp_name']);
            finfo_close($finfo);
            
            if (!in_array($mimeType, $allowedTypes)) {
                return "$fieldName má nepovolený typ";
            }
        }
        
        // Ověř velikost
        if ($maxSize && $file['size'] > $maxSize) {
            $maxSizeMB = round($maxSize / 1024 / 1024, 1);
            return "$fieldName je příliš velký (max. {$maxSizeMB}MB)";
        }
        
        return true;
    }
    
    /**
     * Validuje obrázek
     */
    public static function image($file, $maxSize = 5242880, $fieldName = 'Obrázek') // 5MB default
    {
        $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        return self::file($file, $allowedTypes, $maxSize, $fieldName);
    }
    
    /**
     * Spustí validaci podle pravidel
     */
    public static function validate($data, $rules)
    {
        $errors = [];
        
        foreach ($rules as $field => $fieldRules) {
            $value = isset($data[$field]) ? $data[$field] : '';
            $fieldName = isset($fieldRules['name']) ? $fieldRules['name'] : $field;
            
            foreach ($fieldRules as $rule => $params) {
                if ($rule === 'name') continue;
                
                $result = null;
                
                switch ($rule) {
                    case 'required':
                        if ($params) {
                            $result = self::required($value, $fieldName);
                        }
                        break;
                        
                    case 'min_length':
                        $result = self::minLength($value, $params, $fieldName);
                        break;
                        
                    case 'max_length':
                        $result = self::maxLength($value, $params, $fieldName);
                        break;
                        
                    case 'email':
                        if ($params && !empty($value)) {
                            $result = self::email($value, $fieldName);
                        }
                        break;
                        
                    case 'phone':
                        if ($params && !empty($value)) {
                            $result = self::phone($value, $fieldName);
                        }
                        break;
                        
                    case 'numeric':
                        if ($params && !empty($value)) {
                            $result = self::numeric($value, $fieldName);
                        }
                        break;
                        
                    case 'min':
                        if (!empty($value)) {
                            $result = self::min($value, $params, $fieldName);
                        }
                        break;
                        
                    case 'max':
                        if (!empty($value)) {
                            $result = self::max($value, $params, $fieldName);
                        }
                        break;
                }
                
                if ($result !== true && $result !== null) {
                    if (is_array($result)) {
                        $errors[$field] = array_merge($errors[$field] ?? [], $result);
                    } else {
                        $errors[$field][] = $result;
                    }
                }
            }
        }
        
        return $errors;
    }
}
