<?php

/**
 * Helper třída pro formátování dat
 * Obsahuje funkce pro formátování cen, dat, textů atd.
 */
class FormattingHelper
{
    /**
     * Formátuje cenu pro zobrazení
     */
    public static function formatPrice($price, $currency = 'Kč', $decimals = 0)
    {
        return number_format($price, $decimals, ',', ' ') . ' ' . $currency;
    }
    
    /**
     * Formátuje datum pro zobrazení
     */
    public static function formatDate($date, $format = 'd.m.Y')
    {
        if (is_string($date)) {
            $date = new DateTime($date);
        }
        
        if (!$date instanceof DateTime) {
            return '';
        }
        
        return $date->format($format);
    }
    
    /**
     * Formátuje datum a čas
     */
    public static function formatDateTime($datetime, $format = 'd.m.Y H:i')
    {
        return self::formatDate($datetime, $format);
    }
    
    /**
     * Formátuje relativní čas (před X minutami)
     */
    public static function formatRelativeTime($datetime)
    {
        if (is_string($datetime)) {
            $datetime = new DateTime($datetime);
        }
        
        if (!$datetime instanceof DateTime) {
            return '';
        }
        
        $now = new DateTime();
        $diff = $now->diff($datetime);
        
        if ($diff->days > 7) {
            return self::formatDate($datetime);
        } elseif ($diff->days > 0) {
            return $diff->days === 1 ? 'včera' : "před {$diff->days} dny";
        } elseif ($diff->h > 0) {
            return $diff->h === 1 ? 'před hodinou' : "před {$diff->h} hodinami";
        } elseif ($diff->i > 0) {
            return $diff->i === 1 ? 'před minutou' : "před {$diff->i} minutami";
        } else {
            return 'právě teď';
        }
    }
    
    /**
     * Zkrátí text na zadaný počet znaků
     */
    public static function truncate($text, $length = 100, $suffix = '...')
    {
        if (mb_strlen($text) <= $length) {
            return $text;
        }
        
        return mb_substr($text, 0, $length) . $suffix;
    }
    
    /**
     * Zkrátí text na celá slova
     */
    public static function truncateWords($text, $words = 20, $suffix = '...')
    {
        $wordArray = explode(' ', $text);
        
        if (count($wordArray) <= $words) {
            return $text;
        }
        
        return implode(' ', array_slice($wordArray, 0, $words)) . $suffix;
    }
    
    /**
     * Převede text na URL slug
     */
    public static function slugify($text)
    {
        // Převod českých znaků
        $text = strtr($text, [
            'á' => 'a', 'č' => 'c', 'ď' => 'd', 'é' => 'e', 'ě' => 'e',
            'í' => 'i', 'ň' => 'n', 'ó' => 'o', 'ř' => 'r', 'š' => 's',
            'ť' => 't', 'ú' => 'u', 'ů' => 'u', 'ý' => 'y', 'ž' => 'z',
            'Á' => 'A', 'Č' => 'C', 'Ď' => 'D', 'É' => 'E', 'Ě' => 'E',
            'Í' => 'I', 'Ň' => 'N', 'Ó' => 'O', 'Ř' => 'R', 'Š' => 'S',
            'Ť' => 'T', 'Ú' => 'U', 'Ů' => 'U', 'Ý' => 'Y', 'Ž' => 'Z'
        ]);
        
        // Převod na malá písmena
        $text = strtolower($text);
        
        // Nahrazení nealfanumerických znaků pomlčkami
        $text = preg_replace('/[^a-z0-9]+/', '-', $text);
        
        // Odstranění pomlček ze začátku a konce
        $text = trim($text, '-');
        
        return $text;
    }
    
    /**
     * Formátuje telefonní číslo
     */
    public static function formatPhone($phone)
    {
        // Odstraň všechny nealfanumerické znaky
        $phone = preg_replace('/[^0-9+]/', '', $phone);
        
        // České číslo s +420
        if (preg_match('/^\+420([0-9]{9})$/', $phone, $matches)) {
            $number = $matches[1];
            return '+420 ' . substr($number, 0, 3) . ' ' . substr($number, 3, 3) . ' ' . substr($number, 6, 3);
        }
        
        // České číslo bez +420
        if (preg_match('/^([0-9]{9})$/', $phone, $matches)) {
            $number = $matches[1];
            return substr($number, 0, 3) . ' ' . substr($number, 3, 3) . ' ' . substr($number, 6, 3);
        }
        
        return $phone;
    }
    
    /**
     * Formátuje velikost souboru
     */
    public static function formatFileSize($bytes)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }
    
    /**
     * Formátuje číslo s tisícovými oddělovači
     */
    public static function formatNumber($number, $decimals = 0)
    {
        return number_format($number, $decimals, ',', ' ');
    }
    
    /**
     * Formátuje procenta
     */
    public static function formatPercent($value, $decimals = 1)
    {
        return number_format($value, $decimals, ',', ' ') . ' %';
    }
    
    /**
     * Převede newlines na HTML br tagy
     */
    public static function nl2br($text)
    {
        return nl2br(htmlspecialchars($text));
    }
    
    /**
     * Zvýrazní hledaný text
     */
    public static function highlight($text, $search, $class = 'highlight')
    {
        if (empty($search)) {
            return $text;
        }
        
        return preg_replace(
            '/(' . preg_quote($search, '/') . ')/i',
            '<span class="' . $class . '">$1</span>',
            $text
        );
    }
    
    /**
     * Formátuje status objednávky
     */
    public static function formatOrderStatus($status)
    {
        $statuses = [
            'pending' => 'Čeká na zpracování',
            'processing' => 'Zpracovává se',
            'shipped' => 'Odesláno',
            'delivered' => 'Doručeno',
            'cancelled' => 'Zrušeno'
        ];
        
        return isset($statuses[$status]) ? $statuses[$status] : $status;
    }
    
    /**
     * Formátuje CSS třídu pro status
     */
    public static function getStatusClass($status)
    {
        $classes = [
            'pending' => 'status-pending',
            'processing' => 'status-processing',
            'shipped' => 'status-shipped',
            'delivered' => 'status-delivered',
            'cancelled' => 'status-cancelled'
        ];
        
        return isset($classes[$status]) ? $classes[$status] : 'status-default';
    }
    
    /**
     * Vytvoří breadcrumb navigaci
     */
    public static function breadcrumb($items, $separator = ' / ')
    {
        $breadcrumb = [];
        
        foreach ($items as $item) {
            if (is_array($item) && isset($item['url']) && isset($item['title'])) {
                $breadcrumb[] = '<a href="' . htmlspecialchars($item['url']) . '">' . htmlspecialchars($item['title']) . '</a>';
            } else {
                $breadcrumb[] = htmlspecialchars($item);
            }
        }
        
        return implode($separator, $breadcrumb);
    }
    
    /**
     * Formátuje seznam jako HTML
     */
    public static function formatList($items, $type = 'ul', $class = '')
    {
        if (empty($items)) {
            return '';
        }
        
        $classAttr = $class ? ' class="' . htmlspecialchars($class) . '"' : '';
        $html = "<{$type}{$classAttr}>";
        
        foreach ($items as $item) {
            $html .= '<li>' . htmlspecialchars($item) . '</li>';
        }
        
        $html .= "</{$type}>";
        
        return $html;
    }
    
    /**
     * Escapuje HTML
     */
    public static function escape($string)
    {
        return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
    }
    
    /**
     * Formátuje adresu
     */
    public static function formatAddress($address)
    {
        $parts = [];
        
        if (!empty($address['street'])) {
            $parts[] = $address['street'];
        }
        
        if (!empty($address['city']) && !empty($address['zip'])) {
            $parts[] = $address['zip'] . ' ' . $address['city'];
        } elseif (!empty($address['city'])) {
            $parts[] = $address['city'];
        }
        
        if (!empty($address['country'])) {
            $parts[] = $address['country'];
        }
        
        return implode(', ', $parts);
    }
}
