<?php
/**
 * Cache manager pro optimalizaci výkonu
 * Cachuje JSON data a výsledky dotazů
 * 
 * <AUTHOR> Team
 * @version 1.0
 */

if (!defined('SNURKY_INIT')) {
    die('<PERSON>římý přístup není povolen');
}

class CacheManager
{
    private $cacheDir;
    private $defaultTtl;
    
    public function __construct($cacheDir = null, $defaultTtl = 3600)
    {
        $this->cacheDir = $cacheDir ?: ROOT_PATH . '/data/cache';
        $this->defaultTtl = $defaultTtl;
        
        // Vytvoř cache adresář pokud neexistuje
        $this->ensureCacheDir();
    }
    
    /**
     * Uloží data do cache
     */
    public function set($key, $data, $ttl = null)
    {
        $ttl = $ttl ?: $this->defaultTtl;
        $cacheFile = $this->getCacheFile($key);
        
        $cacheData = [
            'data' => $data,
            'expires' => time() + $ttl,
            'created' => time()
        ];
        
        $success = file_put_contents(
            $cacheFile, 
            json_encode($cacheData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)
        );
        
        return $success !== false;
    }
    
    /**
     * Načte data z cache
     */
    public function get($key, $default = null)
    {
        $cacheFile = $this->getCacheFile($key);
        
        if (!file_exists($cacheFile)) {
            return $default;
        }
        
        $content = file_get_contents($cacheFile);
        if (!$content) {
            return $default;
        }
        
        $cacheData = json_decode($content, true);
        if (!$cacheData || !isset($cacheData['expires'])) {
            return $default;
        }
        
        // Kontrola expirace
        if (time() > $cacheData['expires']) {
            $this->delete($key);
            return $default;
        }
        
        return $cacheData['data'];
    }
    
    /**
     * Kontroluje zda klíč existuje v cache a není expirovaný
     */
    public function has($key)
    {
        return $this->get($key) !== null;
    }
    
    /**
     * Smaže klíč z cache
     */
    public function delete($key)
    {
        $cacheFile = $this->getCacheFile($key);
        
        if (file_exists($cacheFile)) {
            return unlink($cacheFile);
        }
        
        return true;
    }
    
    /**
     * Vyčistí celou cache
     */
    public function clear()
    {
        $files = glob($this->cacheDir . '/*.cache');
        $deleted = 0;
        
        foreach ($files as $file) {
            if (unlink($file)) {
                $deleted++;
            }
        }
        
        return $deleted;
    }
    
    /**
     * Vyčistí expirované záznamy
     */
    public function clearExpired()
    {
        $files = glob($this->cacheDir . '/*.cache');
        $deleted = 0;
        
        foreach ($files as $file) {
            $content = file_get_contents($file);
            if (!$content) continue;
            
            $cacheData = json_decode($content, true);
            if (!$cacheData || !isset($cacheData['expires'])) {
                unlink($file);
                $deleted++;
                continue;
            }
            
            if (time() > $cacheData['expires']) {
                unlink($file);
                $deleted++;
            }
        }
        
        return $deleted;
    }
    
    /**
     * Získá informace o cache
     */
    public function getStats()
    {
        $files = glob($this->cacheDir . '/*.cache');
        $totalSize = 0;
        $validEntries = 0;
        $expiredEntries = 0;
        
        foreach ($files as $file) {
            $totalSize += filesize($file);
            
            $content = file_get_contents($file);
            if (!$content) continue;
            
            $cacheData = json_decode($content, true);
            if (!$cacheData || !isset($cacheData['expires'])) {
                $expiredEntries++;
                continue;
            }
            
            if (time() > $cacheData['expires']) {
                $expiredEntries++;
            } else {
                $validEntries++;
            }
        }
        
        return [
            'total_files' => count($files),
            'valid_entries' => $validEntries,
            'expired_entries' => $expiredEntries,
            'total_size' => $totalSize,
            'total_size_formatted' => $this->formatBytes($totalSize)
        ];
    }
    
    /**
     * Cache s callback funkcí
     */
    public function remember($key, $callback, $ttl = null)
    {
        $data = $this->get($key);
        
        if ($data === null) {
            $data = call_user_func($callback);
            $this->set($key, $data, $ttl);
        }
        
        return $data;
    }
    
    /**
     * Invaliduje cache na základě tagu
     */
    public function invalidateTag($tag)
    {
        $files = glob($this->cacheDir . '/*_' . $tag . '_*.cache');
        $deleted = 0;
        
        foreach ($files as $file) {
            if (unlink($file)) {
                $deleted++;
            }
        }
        
        return $deleted;
    }
    
    /**
     * Pomocné metody
     */
    
    private function getCacheFile($key)
    {
        $safeKey = preg_replace('/[^a-zA-Z0-9_-]/', '_', $key);
        return $this->cacheDir . '/' . $safeKey . '.cache';
    }
    
    private function ensureCacheDir()
    {
        if (!is_dir($this->cacheDir)) {
            mkdir($this->cacheDir, 0755, true);
        }
        
        // Vytvoř .htaccess pro ochranu cache adresáře
        $htaccessFile = $this->cacheDir . '/.htaccess';
        if (!file_exists($htaccessFile)) {
            file_put_contents($htaccessFile, "Deny from all\n");
        }
    }
    
    private function formatBytes($bytes, $precision = 2)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
}
