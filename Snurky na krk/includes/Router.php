<?php
/**
 * Router třída pro SnurkyNaKrk.cz
 * Jednoduchý router pro směrování URL na controllery
 * 
 * <AUTHOR> Team
 * @version 1.0
 */

// Zabránění přímému přístupu
if (!defined('SNURKY_INIT')) {
    die('Přímý přístup není povolen');
}

class Router {
    
    /**
     * @var array Pole s definovanými routes
     */
    private $routes = [];
    
    /**
     * @var array Pole s parametry z URL
     */
    private $params = [];
    
    /**
     * Přidání nové route
     * 
     * @param string $method HTTP metoda (GET, POST, PUT, DELETE)
     * @param string $pattern URL pattern (např. /produkty/{id})
     * @param string $handler Handler ve formátu "Controller@method"
     */
    public function addRoute($method, $pattern, $handler) {
        $this->routes[] = [
            'method' => strtoupper($method),
            'pattern' => $pattern,
            'handler' => $handler
        ];
    }
    
    /**
     * Spuštění routeru - najde odpovídající route a spustí handler
     * 
     * @param string $method HTTP metoda
     * @param string $uri Požadovaná URI
     */
    public function dispatch($method, $uri) {
        $method = strtoupper($method);
        
        // Normalizace URI (odstranění koncového lomítka)
        $uri = rtrim($uri, '/');
        if (empty($uri)) {
            $uri = '/';
        }
        
        foreach ($this->routes as $route) {
            if ($route['method'] === $method && $this->matchRoute($route['pattern'], $uri)) {
                $this->executeHandler($route['handler']);
                return;
            }
        }
        
        // Pokud nebyla nalezena žádná route, zobrazíme 404
        $this->show404();
    }
    
    /**
     * Kontrola, zda URI odpovídá pattern
     * 
     * @param string $pattern Pattern route
     * @param string $uri Požadovaná URI
     * @return bool True pokud odpovídá
     */
    private function matchRoute($pattern, $uri) {
        // Normalizace pattern
        $pattern = rtrim($pattern, '/');
        if (empty($pattern)) {
            $pattern = '/';
        }
        
        // Převod pattern na regex
        $regex = preg_replace('/\{([^}]+)\}/', '([^/]+)', $pattern);
        $regex = '#^' . $regex . '$#';
        
        if (preg_match($regex, $uri, $matches)) {
            // Uložení parametrů
            array_shift($matches); // Odstranění celého match
            $this->params = $matches;
            return true;
        }
        
        return false;
    }
    
    /**
     * Spuštění handleru
     * 
     * @param string $handler Handler ve formátu "Controller@method"
     */
    private function executeHandler($handler) {
        list($controllerName, $methodName) = explode('@', $handler);
        
        // Kontrola existence controlleru
        if (!class_exists($controllerName)) {
            throw new Exception("Controller '$controllerName' neexistuje");
        }
        
        $controller = new $controllerName();
        
        // Kontrola existence metody
        if (!method_exists($controller, $methodName)) {
            throw new Exception("Metoda '$methodName' neexistuje v controlleru '$controllerName'");
        }
        
        // Spuštění metody s parametry
        call_user_func_array([$controller, $methodName], $this->params);
    }
    
    /**
     * Zobrazení 404 stránky
     */
    private function show404() {
        http_response_code(404);
        
        if (file_exists(ROOT_PATH . '/404.php')) {
            include ROOT_PATH . '/404.php';
        } else {
            echo "<h1>404 - Stránka nenalezena</h1>";
            echo "<p>Požadovaná stránka neexistuje.</p>";
        }
        exit;
    }
    
    /**
     * Získání parametrů z URL
     * 
     * @return array Pole parametrů
     */
    public function getParams() {
        return $this->params;
    }
    
    /**
     * Získání konkrétního parametru
     * 
     * @param int $index Index parametru
     * @param mixed $default Výchozí hodnota
     * @return mixed Hodnota parametru
     */
    public function getParam($index, $default = null) {
        return isset($this->params[$index]) ? $this->params[$index] : $default;
    }
    
    /**
     * Přesměrování na jinou URL
     * 
     * @param string $url URL pro přesměrování
     * @param int $code HTTP kód (výchozí 302)
     */
    public static function redirect($url, $code = 302) {
        http_response_code($code);
        header("Location: $url");
        exit;
    }
    
    /**
     * Získání aktuální URL
     * 
     * @return string Aktuální URL
     */
    public static function getCurrentUrl() {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'];
        $uri = $_SERVER['REQUEST_URI'];
        
        return $protocol . '://' . $host . $uri;
    }
    
    /**
     * Generování URL pro route
     * 
     * @param string $pattern Pattern route
     * @param array $params Parametry pro nahrazení
     * @return string Vygenerovaná URL
     */
    public static function url($pattern, $params = []) {
        $url = $pattern;
        
        foreach ($params as $key => $value) {
            $url = str_replace('{' . $key . '}', $value, $url);
        }
        
        return $url;
    }
}
