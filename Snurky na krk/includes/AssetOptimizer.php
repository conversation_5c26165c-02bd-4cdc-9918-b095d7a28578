<?php
/**
 * Optimalizace CSS a JavaScript souborů
 * Minifikace, kombinování a cache busting
 * 
 * <AUTHOR> Team
 * @version 1.0
 */

if (!defined('SNURKY_INIT')) {
    die('<PERSON>římý přístup není povolen');
}

class AssetOptimizer
{
    private $assetsDir;
    private $cacheDir;
    private $cache;
    
    public function __construct()
    {
        $this->assetsDir = ROOT_PATH . '/assets';
        $this->cacheDir = ROOT_PATH . '/data/cache/assets';
        $this->cache = new CacheManager();
        
        $this->ensureCacheDir();
    }
    
    /**
     * Optimalizuje CSS soubory
     */
    public function optimizeCSS($files, $outputName = 'combined.min.css')
    {
        $cacheKey = 'css_' . md5(implode('|', $files) . $outputName);
        $outputFile = $this->cacheDir . '/' . $outputName;
        
        // Kontrola cache
        if ($this->isCacheValid($cacheKey, $files)) {
            return $this->getAssetUrl($outputName);
        }
        
        $combinedCSS = '';
        
        foreach ($files as $file) {
            $filePath = $this->assetsDir . '/' . ltrim($file, '/');
            
            if (!file_exists($filePath)) {
                error_log("CSS soubor nenalezen: $filePath");
                continue;
            }
            
            $css = file_get_contents($filePath);
            $css = $this->minifyCSS($css);
            $combinedCSS .= $css . "\n";
        }
        
        // Uložení optimalizovaného CSS
        file_put_contents($outputFile, $combinedCSS);
        
        // Uložení do cache
        $this->cache->set($cacheKey, [
            'files' => $files,
            'output' => $outputName,
            'created' => time()
        ], 86400); // 24 hodin
        
        return $this->getAssetUrl($outputName);
    }
    
    /**
     * Optimalizuje JavaScript soubory
     */
    public function optimizeJS($files, $outputName = 'combined.min.js')
    {
        $cacheKey = 'js_' . md5(implode('|', $files) . $outputName);
        $outputFile = $this->cacheDir . '/' . $outputName;
        
        // Kontrola cache
        if ($this->isCacheValid($cacheKey, $files)) {
            return $this->getAssetUrl($outputName);
        }
        
        $combinedJS = '';
        
        foreach ($files as $file) {
            $filePath = $this->assetsDir . '/' . ltrim($file, '/');
            
            if (!file_exists($filePath)) {
                error_log("JS soubor nenalezen: $filePath");
                continue;
            }
            
            $js = file_get_contents($filePath);
            $js = $this->minifyJS($js);
            $combinedJS .= $js . ";\n";
        }
        
        // Uložení optimalizovaného JS
        file_put_contents($outputFile, $combinedJS);
        
        // Uložení do cache
        $this->cache->set($cacheKey, [
            'files' => $files,
            'output' => $outputName,
            'created' => time()
        ], 86400); // 24 hodin
        
        return $this->getAssetUrl($outputName);
    }
    
    /**
     * Minifikuje CSS
     */
    private function minifyCSS($css)
    {
        // Odstranění komentářů
        $css = preg_replace('!/\*[^*]*\*+([^/][^*]*\*+)*/!', '', $css);
        
        // Odstranění zbytečných mezer a nových řádků
        $css = preg_replace('/\s+/', ' ', $css);
        
        // Odstranění mezer kolem speciálních znaků
        $css = preg_replace('/\s*([{}:;,>+~])\s*/', '$1', $css);
        
        // Odstranění posledního středníku před }
        $css = preg_replace('/;+}/', '}', $css);
        
        // Odstranění zbytečných mezer
        $css = trim($css);
        
        return $css;
    }
    
    /**
     * Minifikuje JavaScript (základní)
     */
    private function minifyJS($js)
    {
        // Odstranění jednořádkových komentářů
        $js = preg_replace('/\/\/.*$/m', '', $js);
        
        // Odstranění víceřádkových komentářů
        $js = preg_replace('/\/\*[\s\S]*?\*\//', '', $js);
        
        // Odstranění zbytečných mezer a nových řádků
        $js = preg_replace('/\s+/', ' ', $js);
        
        // Odstranění mezer kolem operátorů
        $js = preg_replace('/\s*([=+\-*\/{}();,])\s*/', '$1', $js);
        
        return trim($js);
    }
    
    /**
     * Kontroluje zda je cache platná
     */
    private function isCacheValid($cacheKey, $files)
    {
        $cacheData = $this->cache->get($cacheKey);
        
        if (!$cacheData) {
            return false;
        }
        
        $outputFile = $this->cacheDir . '/' . $cacheData['output'];
        
        if (!file_exists($outputFile)) {
            return false;
        }
        
        // Kontrola zda se některý ze zdrojových souborů nezměnil
        $outputTime = filemtime($outputFile);
        
        foreach ($files as $file) {
            $filePath = $this->assetsDir . '/' . ltrim($file, '/');
            
            if (file_exists($filePath) && filemtime($filePath) > $outputTime) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * Získá URL pro asset s cache busting
     */
    private function getAssetUrl($filename)
    {
        $filePath = $this->cacheDir . '/' . $filename;
        $timestamp = file_exists($filePath) ? filemtime($filePath) : time();
        
        return '/data/cache/assets/' . $filename . '?v=' . $timestamp;
    }
    
    /**
     * Vyčistí cache assetů
     */
    public function clearCache()
    {
        $files = glob($this->cacheDir . '/*');
        $deleted = 0;
        
        foreach ($files as $file) {
            if (is_file($file) && unlink($file)) {
                $deleted++;
            }
        }
        
        return $deleted;
    }
    
    /**
     * Získá statistiky cache
     */
    public function getCacheStats()
    {
        $files = glob($this->cacheDir . '/*');
        $totalSize = 0;
        
        foreach ($files as $file) {
            if (is_file($file)) {
                $totalSize += filesize($file);
            }
        }
        
        return [
            'files_count' => count($files),
            'total_size' => $totalSize,
            'total_size_formatted' => $this->formatBytes($totalSize)
        ];
    }
    
    /**
     * Pomocné metody
     */
    
    private function ensureCacheDir()
    {
        if (!is_dir($this->cacheDir)) {
            mkdir($this->cacheDir, 0755, true);
        }
        
        // Vytvoř .htaccess pro povolení přístupu k assetům
        $htaccessFile = $this->cacheDir . '/.htaccess';
        if (!file_exists($htaccessFile)) {
            $content = "# Povolení přístupu k optimalizovaným assetům\n";
            $content .= "Order allow,deny\n";
            $content .= "Allow from all\n";
            $content .= "\n";
            $content .= "# Gzip komprese\n";
            $content .= "<IfModule mod_deflate.c>\n";
            $content .= "    AddOutputFilterByType DEFLATE text/css application/javascript\n";
            $content .= "</IfModule>\n";
            $content .= "\n";
            $content .= "# Cache headers\n";
            $content .= "<IfModule mod_expires.c>\n";
            $content .= "    ExpiresActive on\n";
            $content .= "    ExpiresByType text/css \"access plus 1 month\"\n";
            $content .= "    ExpiresByType application/javascript \"access plus 1 month\"\n";
            $content .= "</IfModule>\n";
            
            file_put_contents($htaccessFile, $content);
        }
    }
    
    private function formatBytes($bytes, $precision = 2)
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
}
