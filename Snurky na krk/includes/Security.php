<?php

/**
 * Bezpečnostní třída pro ochranu aplikace
 * Obsahuje funkce pro CSRF ochranu, XSS prevenci, validaci a sanitizaci
 */
class Security
{
    private $sessionManager;
    
    public function __construct()
    {
        $this->sessionManager = new SessionManager();
    }
    
    /**
     * Vygeneruje CSRF token
     */
    public function generateCSRFToken()
    {
        if (!$this->sessionManager->has('csrf_token')) {
            $token = bin2hex(random_bytes(32));
            $this->sessionManager->set('csrf_token', $token);
        }
        
        return $this->sessionManager->get('csrf_token');
    }
    
    /**
     * Ověří CSRF token
     */
    public function validateCSRFToken($token)
    {
        $sessionToken = $this->sessionManager->get('csrf_token');
        
        if (!$sessionToken || !$token) {
            return false;
        }
        
        return hash_equals($sessionToken, $token);
    }
    
    /**
     * Sanitizuje vstupní data proti XSS
     */
    public function sanitizeInput($data)
    {
        if (is_array($data)) {
            return array_map([$this, 'sanitizeInput'], $data);
        }
        
        // Odstraň HTML tagy a speciální znaky
        $data = trim($data);
        $data = stripslashes($data);
        $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
        
        return $data;
    }
    
    /**
     * Validuje email adresu
     */
    public function validateEmail($email)
    {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }
    
    /**
     * Validuje telefonní číslo (české formáty)
     */
    public function validatePhone($phone)
    {
        // Odstraň mezery a pomlčky
        $phone = preg_replace('/[\s\-]/', '', $phone);
        
        // České telefonní číslo: +420xxxxxxxxx nebo xxxxxxxxx
        $pattern = '/^(\+420)?[0-9]{9}$/';
        
        return preg_match($pattern, $phone);
    }
    
    /**
     * Validuje URL
     */
    public function validateUrl($url)
    {
        return filter_var($url, FILTER_VALIDATE_URL) !== false;
    }
    
    /**
     * Ověří, zda je soubor validní obrázek
     */
    public function isValidImageFile($file)
    {
        if (!isset($file['tmp_name']) || !is_uploaded_file($file['tmp_name'])) {
            return false;
        }
        
        // Povolené MIME typy
        $allowedMimes = [
            'image/jpeg',
            'image/png',
            'image/gif',
            'image/webp'
        ];
        
        // Ověř MIME typ
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);
        
        if (!in_array($mimeType, $allowedMimes)) {
            return false;
        }
        
        // Ověř příponu
        $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        
        if (!in_array($extension, $allowedExtensions)) {
            return false;
        }
        
        // Ověř velikost (max 5MB)
        if ($file['size'] > 5 * 1024 * 1024) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Vygeneruje bezpečné heslo
     */
    public function generatePassword($length = 12)
    {
        $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
        $password = '';
        
        for ($i = 0; $i < $length; $i++) {
            $password .= $chars[random_int(0, strlen($chars) - 1)];
        }
        
        return $password;
    }
    
    /**
     * Hashuje heslo
     */
    public function hashPassword($password)
    {
        return password_hash($password, PASSWORD_DEFAULT);
    }
    
    /**
     * Ověří heslo proti hashi
     */
    public function verifyPassword($password, $hash)
    {
        return password_verify($password, $hash);
    }
    
    /**
     * Ověří sílu hesla
     */
    public function validatePasswordStrength($password)
    {
        $errors = [];
        
        if (strlen($password) < 8) {
            $errors[] = 'Heslo musí mít alespoň 8 znaků';
        }
        
        if (!preg_match('/[a-z]/', $password)) {
            $errors[] = 'Heslo musí obsahovat alespoň jedno malé písmeno';
        }
        
        if (!preg_match('/[A-Z]/', $password)) {
            $errors[] = 'Heslo musí obsahovat alespoň jedno velké písmeno';
        }
        
        if (!preg_match('/[0-9]/', $password)) {
            $errors[] = 'Heslo musí obsahovat alespoň jednu číslici';
        }
        
        return empty($errors) ? true : $errors;
    }
    
    /**
     * Sanitizuje název souboru
     */
    public function sanitizeFilename($filename)
    {
        // Odstraň nebezpečné znaky
        $filename = preg_replace('/[^a-zA-Z0-9\-_\.]/', '', $filename);
        
        // Omeź délku
        if (strlen($filename) > 100) {
            $extension = pathinfo($filename, PATHINFO_EXTENSION);
            $name = pathinfo($filename, PATHINFO_FILENAME);
            $filename = substr($name, 0, 96 - strlen($extension)) . '.' . $extension;
        }
        
        return $filename;
    }
    
    /**
     * Ověří, zda je IP adresa v povoleném rozsahu
     */
    public function isAllowedIP($ip, $allowedIPs = [])
    {
        if (empty($allowedIPs)) {
            return true;
        }
        
        return in_array($ip, $allowedIPs);
    }
    
    /**
     * Získá IP adresu klienta
     */
    public function getClientIP()
    {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                
                // Pro X-Forwarded-For může být více IP oddělených čárkou
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
}
