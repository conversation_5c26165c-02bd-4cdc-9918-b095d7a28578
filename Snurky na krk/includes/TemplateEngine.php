<?php

/**
 * Jednoduchý template engine pro renderování šablon
 * Podporuje layout systém a předávání dat do šablon
 */
class TemplateEngine
{
    private $templateDir;
    private $layoutDir;
    private $data = [];
    private $assetOptimizer;
    
    public function __construct()
    {
        $this->templateDir = ROOT_PATH . '/templates/';
        $this->layoutDir = ROOT_PATH . '/templates/layouts/';
        $this->assetOptimizer = new AssetOptimizer();
    }
    
    /**
     * Renderuje šablonu bez layoutu
     */
    public function render($template, $data = [])
    {
        $this->data = array_merge($this->data, $data);
        
        $templateFile = $this->templateDir . $template . '.php';
        
        if (!file_exists($templateFile)) {
            throw new Exception("Šablona '$template' nebyla nalezena: $templateFile");
        }
        
        // Extrahuj data do proměnných
        extract($this->data);
        
        // Zachyť výstup
        ob_start();
        include $templateFile;
        $content = ob_get_clean();
        
        return $content;
    }
    
    /**
     * Ren<PERSON>uje šablonu s layoutem
     */
    public function renderWithLayout($template, $data = [], $layout = 'main')
    {
        // Renderuj obsah šablony
        $content = $this->render($template, $data);
        
        // Přidej obsah do dat pro layout
        $this->data['content'] = $content;
        
        // Renderuj layout
        $layoutFile = $this->layoutDir . $layout . '.php';
        
        if (!file_exists($layoutFile)) {
            throw new Exception("Layout '$layout' nebyl nalezen: $layoutFile");
        }
        
        // Extrahuj data do proměnných
        extract($this->data);
        
        // Zachyť výstup
        ob_start();
        include $layoutFile;
        $output = ob_get_clean();
        
        return $output;
    }
    
    /**
     * Nastaví globální data pro všechny šablony
     */
    public function setGlobalData($key, $value)
    {
        $this->data[$key] = $value;
    }
    
    /**
     * Získá globální data
     */
    public function getGlobalData($key = null)
    {
        if ($key === null) {
            return $this->data;
        }
        
        return isset($this->data[$key]) ? $this->data[$key] : null;
    }
    
    /**
     * Escapuje HTML pro bezpečný výstup
     */
    public function escape($string)
    {
        return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
    }
    
    /**
     * Formátuje datum pro zobrazení
     */
    public function formatDate($date, $format = 'd.m.Y')
    {
        if (is_string($date)) {
            $date = new DateTime($date);
        }
        
        return $date->format($format);
    }
    
    /**
     * Formátuje cenu pro zobrazení
     */
    public function formatPrice($price)
    {
        return number_format($price, 0, ',', ' ') . ' Kč';
    }
    
    /**
     * Zkrátí text na zadaný počet znaků
     */
    public function truncate($text, $length = 100, $suffix = '...')
    {
        if (mb_strlen($text) <= $length) {
            return $text;
        }
        
        return mb_substr($text, 0, $length) . $suffix;
    }
    
    /**
     * Vytvoří URL s parametry
     */
    public function url($path, $params = [])
    {
        $url = rtrim(BASE_URL, '/') . '/' . ltrim($path, '/');
        
        if (!empty($params)) {
            $url .= '?' . http_build_query($params);
        }
        
        return $url;
    }
    
    /**
     * Vygeneruje CSRF token pro formuláře
     */
    public function csrfToken()
    {
        $security = new Security();
        return $security->generateCSRFToken();
    }
    
    /**
     * Vygeneruje CSRF input field
     */
    public function csrfField()
    {
        $token = $this->csrfToken();
        return '<input type="hidden" name="csrf_token" value="' . $this->escape($token) . '">';
    }
    
    /**
     * Includuje partial šablonu
     */
    public function partial($template, $data = [])
    {
        $partialFile = $this->templateDir . 'partials/' . $template . '.php';
        
        if (!file_exists($partialFile)) {
            throw new Exception("Partial '$template' nebyl nalezen: $partialFile");
        }
        
        // Sloučí data s aktuálními daty
        $partialData = array_merge($this->data, $data);
        extract($partialData);
        
        include $partialFile;
    }

    /**
     * Získá aktuální URL
     */
    public function getCurrentUrl()
    {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
        $uri = $_SERVER['REQUEST_URI'] ?? '/';

        return $protocol . '://' . $host . $uri;
    }

    /**
     * Generuje CSRF token pro formuláře
     */
    public function csrf()
    {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }

        return '<input type="hidden" name="csrf_token" value="' . $this->escape($_SESSION['csrf_token']) . '">';
    }

    /**
     * Kontroluje, zda je aktuální stránka aktivní (pro navigaci)
     */
    public function isCurrentPage($path)
    {
        $currentPath = $_SERVER['REQUEST_URI'] ?? '/';
        $currentPath = strtok($currentPath, '?'); // Odstraní query parametry

        return $currentPath === $path || strpos($currentPath, $path) === 0;
    }

    /**
     * Získá flash zprávu ze session
     */
    public function getFlashMessage()
    {
        if (isset($_SESSION['flash_message'])) {
            $message = $_SESSION['flash_message'];
            unset($_SESSION['flash_message']);
            return $message;
        }

        return null;
    }

    /**
     * Renderuje šablonu s automatickou volbou layoutu
     */
    public function layout($layout)
    {
        // Tato metoda se používá v šablonách pro specifikaci layoutu
        // Samotné renderování se děje v kontroleru
        $this->data['_layout'] = $layout;
    }

    /**
     * Formátuje status pro zobrazení
     */
    public function formatStatus($status)
    {
        $statusMap = [
            'active' => ['text' => 'Aktivní', 'class' => 'success'],
            'inactive' => ['text' => 'Neaktivní', 'class' => 'secondary'],
            'pending' => ['text' => 'Čeká na schválení', 'class' => 'warning'],
            'rejected' => ['text' => 'Zamítnuto', 'class' => 'danger'],
            'completed' => ['text' => 'Dokončeno', 'class' => 'success'],
            'cancelled' => ['text' => 'Zrušeno', 'class' => 'danger']
        ];

        $statusInfo = $statusMap[$status] ?? ['text' => $status, 'class' => 'secondary'];

        return [
            'text' => $statusInfo['text'],
            'class' => $statusInfo['class'],
            'badge' => '<span class="badge bg-' . $statusInfo['class'] . '">' . $statusInfo['text'] . '</span>'
        ];
    }

    /**
     * Generuje breadcrumb navigaci
     */
    public function breadcrumb($items)
    {
        if (empty($items)) {
            return '';
        }

        $html = '<nav aria-label="breadcrumb"><ol class="breadcrumb">';

        $lastIndex = count($items) - 1;
        foreach ($items as $index => $item) {
            if ($index === $lastIndex) {
                $html .= '<li class="breadcrumb-item active" aria-current="page">' . $this->escape($item['text']) . '</li>';
            } else {
                $html .= '<li class="breadcrumb-item"><a href="' . $this->escape($item['url']) . '">' . $this->escape($item['text']) . '</a></li>';
            }
        }

        $html .= '</ol></nav>';

        return $html;
    }

    /**
     * Generuje optimalizované CSS tagy
     */
    public function optimizedCSS($files, $outputName = null)
    {
        if (!is_array($files)) {
            $files = [$files];
        }

        $outputName = $outputName ?: 'combined_' . md5(implode('|', $files)) . '.min.css';
        $optimizedUrl = $this->assetOptimizer->optimizeCSS($files, $outputName);

        return '<link rel="stylesheet" href="' . $this->escape($optimizedUrl) . '">';
    }

    /**
     * Generuje optimalizované JavaScript tagy
     */
    public function optimizedJS($files, $outputName = null)
    {
        if (!is_array($files)) {
            $files = [$files];
        }

        $outputName = $outputName ?: 'combined_' . md5(implode('|', $files)) . '.min.js';
        $optimizedUrl = $this->assetOptimizer->optimizeJS($files, $outputName);

        return '<script src="' . $this->escape($optimizedUrl) . '"></script>';
    }

    /**
     * Generuje asset URL s cache busting
     */
    public function asset($path)
    {
        $fullPath = ROOT_PATH . '/assets/' . ltrim($path, '/');
        $timestamp = file_exists($fullPath) ? filemtime($fullPath) : time();

        return '/assets/' . ltrim($path, '/') . '?v=' . $timestamp;
    }
}
