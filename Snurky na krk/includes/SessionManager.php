<?php

/**
 * Správa sessions pro aplikaci
 * Bezpečné ukládání session dat, timeout handling, autentizace
 */
class SessionManager
{
    private $sessionTimeout;
    private $isStarted = false;
    
    public function __construct($timeout = 3600) // 1 hodina default
    {
        $this->sessionTimeout = $timeout;
        $this->startSession();
    }
    
    /**
     * Spustí session s bezpečnostními nastaveními
     */
    private function startSession()
    {
        if ($this->isStarted || session_status() === PHP_SESSION_ACTIVE) {
            return;
        }
        
        // Bezpečnostní nastavení session
        ini_set('session.cookie_httponly', 1);
        ini_set('session.cookie_secure', isset($_SERVER['HTTPS']));
        ini_set('session.use_strict_mode', 1);
        ini_set('session.cookie_samesite', 'Strict');
        
        session_start();
        $this->isStarted = true;
        
        // Ověř timeout
        $this->checkTimeout();
        
        // Regeneruj session ID pro bezpečnost
        $this->regenerateSessionId();
    }
    
    /**
     * Ověří timeout session
     */
    private function checkTimeout()
    {
        if (isset($_SESSION['last_activity'])) {
            if (time() - $_SESSION['last_activity'] > $this->sessionTimeout) {
                $this->destroy();
                return;
            }
        }
        
        $_SESSION['last_activity'] = time();
    }
    
    /**
     * Regeneruje session ID pro bezpečnost
     */
    private function regenerateSessionId()
    {
        if (!isset($_SESSION['regenerated'])) {
            session_regenerate_id(true);
            $_SESSION['regenerated'] = time();
        } elseif (time() - $_SESSION['regenerated'] > 300) { // každých 5 minut
            session_regenerate_id(true);
            $_SESSION['regenerated'] = time();
        }
    }
    
    /**
     * Nastaví hodnotu do session
     */
    public function set($key, $value)
    {
        $_SESSION[$key] = $value;
    }
    
    /**
     * Získá hodnotu ze session
     */
    public function get($key, $default = null)
    {
        return isset($_SESSION[$key]) ? $_SESSION[$key] : $default;
    }
    
    /**
     * Ověří, zda klíč existuje v session
     */
    public function has($key)
    {
        return isset($_SESSION[$key]);
    }
    
    /**
     * Odstraní hodnotu ze session
     */
    public function remove($key)
    {
        if (isset($_SESSION[$key])) {
            unset($_SESSION[$key]);
        }
    }
    
    /**
     * Vymaže celou session
     */
    public function clear()
    {
        $_SESSION = [];
    }
    
    /**
     * Zničí session
     */
    public function destroy()
    {
        if ($this->isStarted) {
            $_SESSION = [];
            
            if (ini_get("session.use_cookies")) {
                $params = session_get_cookie_params();
                setcookie(session_name(), '', time() - 42000,
                    $params["path"], $params["domain"],
                    $params["secure"], $params["httponly"]
                );
            }
            
            session_destroy();
            $this->isStarted = false;
        }
    }
    
    /**
     * Přihlásí uživatele (admin)
     */
    public function login($username, $userData = [])
    {
        $this->set('logged_in', true);
        $this->set('username', $username);
        $this->set('login_time', time());
        
        foreach ($userData as $key => $value) {
            $this->set('user_' . $key, $value);
        }
        
        // Regeneruj session ID po přihlášení
        session_regenerate_id(true);
    }
    
    /**
     * Odhlásí uživatele
     */
    public function logout()
    {
        $this->remove('logged_in');
        $this->remove('username');
        $this->remove('login_time');
        
        // Odstraň všechna user_ data
        foreach ($_SESSION as $key => $value) {
            if (strpos($key, 'user_') === 0) {
                $this->remove($key);
            }
        }
        
        // Regeneruj session ID po odhlášení
        session_regenerate_id(true);
    }
    
    /**
     * Ověří, zda je uživatel přihlášen
     */
    public function isLoggedIn()
    {
        return $this->get('logged_in', false) === true;
    }
    
    /**
     * Získá jméno přihlášeného uživatele
     */
    public function getUsername()
    {
        return $this->get('username');
    }
    
    /**
     * Získá data přihlášeného uživatele
     */
    public function getUserData($key = null)
    {
        if ($key === null) {
            $userData = [];
            foreach ($_SESSION as $sessionKey => $value) {
                if (strpos($sessionKey, 'user_') === 0) {
                    $userData[substr($sessionKey, 5)] = $value;
                }
            }
            return $userData;
        }
        
        return $this->get('user_' . $key);
    }
    
    /**
     * Nastaví flash zprávu
     */
    public function setFlashMessage($message, $type = 'info')
    {
        if (!isset($_SESSION['flash_messages'])) {
            $_SESSION['flash_messages'] = [];
        }
        
        $_SESSION['flash_messages'][] = [
            'message' => $message,
            'type' => $type,
            'timestamp' => time()
        ];
    }
    
    /**
     * Získá a vymaže flash zprávy
     */
    public function getFlashMessages()
    {
        $messages = $this->get('flash_messages', []);
        $this->remove('flash_messages');

        return $messages;
    }

    /**
     * Získá první flash zprávu (pro kompatibilitu)
     */
    public function getFlashMessage()
    {
        $messages = $this->get('flash_messages', []);
        if (!empty($messages)) {
            $message = $messages[0];
            // Odstraníme pouze první zprávu
            array_shift($_SESSION['flash_messages']);
            if (empty($_SESSION['flash_messages'])) {
                unset($_SESSION['flash_messages']);
            }
            return $message;
        }
        return null;
    }
    
    /**
     * Ověří, zda jsou flash zprávy
     */
    public function hasFlashMessages()
    {
        return !empty($this->get('flash_messages', []));
    }
    
    /**
     * Získá ID session
     */
    public function getSessionId()
    {
        return session_id();
    }
    
    /**
     * Získá informace o session
     */
    public function getSessionInfo()
    {
        return [
            'id' => $this->getSessionId(),
            'started' => $this->isStarted,
            'last_activity' => $this->get('last_activity'),
            'login_time' => $this->get('login_time'),
            'logged_in' => $this->isLoggedIn(),
            'username' => $this->getUsername(),
            'timeout' => $this->sessionTimeout
        ];
    }

    /**
     * Přihlášení administrátora
     */
    public function loginAdmin($username)
    {
        $this->regenerateId();

        $this->set('admin_logged_in', true);
        $this->set('admin_username', $username);
        $this->set('admin_login_time', time());
        $this->set('last_activity', time());

        return true;
    }

    /**
     * Odhlášení administrátora
     */
    public function logoutAdmin()
    {
        $this->remove('admin_logged_in');
        $this->remove('admin_username');
        $this->remove('admin_login_time');

        return true;
    }

    /**
     * Ověří, zda je admin přihlášen
     */
    public function isAdminLoggedIn()
    {
        if (!$this->get('admin_logged_in')) {
            return false;
        }

        // Kontrola timeout
        $lastActivity = $this->get('last_activity');
        if ($lastActivity && (time() - $lastActivity) > $this->sessionTimeout) {
            $this->logoutAdmin();
            return false;
        }

        // Aktualizace last activity
        $this->set('last_activity', time());

        return true;
    }

    /**
     * Získá username administrátora
     */
    public function getAdminUsername()
    {
        return $this->get('admin_username');
    }
}
