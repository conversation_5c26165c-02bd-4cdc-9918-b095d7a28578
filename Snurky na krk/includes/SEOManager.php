<?php
/**
 * SEO Manager pro optimalizaci vyhledávačů
 * Generuje meta tagy, strukturovaná data a další SEO prvky
 * 
 * <AUTHOR> Team
 * @version 1.0
 */

if (!defined('SNURKY_INIT')) {
    die('Přímý přístup není povolen');
}

class SEOManager
{
    private $settings;
    private $cache;
    
    public function __construct()
    {
        $settingsModel = new SettingsModel();
        $this->settings = $settingsModel->getSettings();
        $this->cache = new CacheManager();
    }
    
    /**
     * Generuje meta tagy pro stránku
     */
    public function generateMetaTags($page, $data = [])
    {
        $meta = $this->getPageMeta($page, $data);
        
        $html = '';
        
        // Základní meta tagy
        $html .= '<title>' . htmlspecialchars($meta['title']) . '</title>' . "\n";
        $html .= '<meta name="description" content="' . htmlspecialchars($meta['description']) . '">' . "\n";
        $html .= '<meta name="keywords" content="' . htmlspecialchars($meta['keywords']) . '">' . "\n";
        $html .= '<meta name="robots" content="' . htmlspecialchars($meta['robots']) . '">' . "\n";
        $html .= '<link rel="canonical" href="' . htmlspecialchars($meta['canonical']) . '">' . "\n";
        
        // Open Graph tagy
        $html .= '<meta property="og:title" content="' . htmlspecialchars($meta['title']) . '">' . "\n";
        $html .= '<meta property="og:description" content="' . htmlspecialchars($meta['description']) . '">' . "\n";
        $html .= '<meta property="og:url" content="' . htmlspecialchars($meta['canonical']) . '">' . "\n";
        $html .= '<meta property="og:type" content="' . htmlspecialchars($meta['og_type']) . '">' . "\n";
        $html .= '<meta property="og:site_name" content="' . htmlspecialchars($this->settings['site_settings']['site_name']) . '">' . "\n";
        
        if (!empty($meta['image'])) {
            $html .= '<meta property="og:image" content="' . htmlspecialchars($meta['image']) . '">' . "\n";
            $html .= '<meta property="og:image:alt" content="' . htmlspecialchars($meta['image_alt']) . '">' . "\n";
        }
        
        // Twitter Card tagy
        $html .= '<meta name="twitter:card" content="summary_large_image">' . "\n";
        $html .= '<meta name="twitter:title" content="' . htmlspecialchars($meta['title']) . '">' . "\n";
        $html .= '<meta name="twitter:description" content="' . htmlspecialchars($meta['description']) . '">' . "\n";
        
        if (!empty($meta['image'])) {
            $html .= '<meta name="twitter:image" content="' . htmlspecialchars($meta['image']) . '">' . "\n";
        }
        
        return $html;
    }
    
    /**
     * Generuje strukturovaná data JSON-LD
     */
    public function generateStructuredData($type, $data = [])
    {
        $structuredData = [];
        
        switch ($type) {
            case 'organization':
                $structuredData = $this->getOrganizationData();
                break;
                
            case 'product':
                $structuredData = $this->getProductData($data);
                break;
                
            case 'breadcrumb':
                $structuredData = $this->getBreadcrumbData($data);
                break;
                
            case 'website':
                $structuredData = $this->getWebsiteData();
                break;
        }
        
        if (!empty($structuredData)) {
            return '<script type="application/ld+json">' . "\n" . 
                   json_encode($structuredData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES) . 
                   "\n" . '</script>' . "\n";
        }
        
        return '';
    }
    
    /**
     * Získá meta data pro konkrétní stránku
     */
    private function getPageMeta($page, $data = [])
    {
        $baseUrl = $this->settings['site_settings']['site_url'];
        $siteName = $this->settings['site_settings']['site_name'];
        
        $meta = [
            'title' => $siteName,
            'description' => $this->settings['site_settings']['site_description'],
            'keywords' => $this->settings['site_settings']['site_keywords'],
            'robots' => 'index, follow',
            'canonical' => $baseUrl,
            'og_type' => 'website',
            'image' => $baseUrl . '/assets/images/logo.png',
            'image_alt' => $siteName . ' logo'
        ];
        
        switch ($page) {
            case 'home':
                $meta['title'] = $siteName . ' - ' . $this->settings['site_settings']['site_description'];
                break;
                
            case 'products':
                $meta['title'] = 'Produkty - ' . $siteName;
                $meta['description'] = 'Prohlédněte si naši nabídku kvalitních šňůrek na krk. Bavlna, hedvábí a další materiály.';
                $meta['keywords'] = 'šňůrky na krk, produkty, bavlna, hedvábí, móda';
                break;
                
            case 'product':
                if (isset($data['product'])) {
                    $product = $data['product'];
                    $meta['title'] = $product['name'] . ' - ' . $siteName;
                    $meta['description'] = $product['description'] ?? 'Kvalitní šňůrka na krk ' . $product['name'];
                    $meta['keywords'] = $product['name'] . ', šňůrka na krk, ' . ($product['category'] ?? '');
                    $meta['og_type'] = 'product';
                    $meta['canonical'] = $baseUrl . '/produkt/' . $product['id'];
                    
                    if (!empty($product['images'][0])) {
                        $meta['image'] = $baseUrl . '/' . $product['images'][0];
                        $meta['image_alt'] = $product['name'];
                    }
                }
                break;
                
            case 'dealers':
                $meta['title'] = 'Obchodníci - ' . $siteName;
                $meta['description'] = 'Najděte si nejbližšího obchodníka s našimi produkty. Mapa obchodníků po celé České republice.';
                $meta['keywords'] = 'obchodníci, prodejci, mapa obchodů, kde koupit';
                $meta['canonical'] = $baseUrl . '/obchodnici';
                break;
                
            case 'contact':
                $meta['title'] = 'Kontakt - ' . $siteName;
                $meta['description'] = 'Kontaktujte nás. Telefon, email, adresa a kontaktní formulář.';
                $meta['keywords'] = 'kontakt, telefon, email, adresa, formulář';
                $meta['canonical'] = $baseUrl . '/kontakt';
                break;
                
            case 'about':
                $meta['title'] = 'O nás - ' . $siteName;
                $meta['description'] = 'Seznamte se s naší firmou, historií a hodnotami. Kvalitní šňůrky na krk od roku 2020.';
                $meta['keywords'] = 'o nás, historie, firma, hodnoty, kvalita';
                $meta['canonical'] = $baseUrl . '/o-nas';
                break;
        }
        
        return $meta;
    }
    
    /**
     * Strukturovaná data pro organizaci
     */
    private function getOrganizationData()
    {
        $settings = $this->settings;
        
        return [
            '@context' => 'https://schema.org',
            '@type' => 'Organization',
            'name' => $settings['business_settings']['company_name'],
            'url' => $settings['site_settings']['site_url'],
            'logo' => $settings['site_settings']['site_url'] . '/assets/images/logo.png',
            'contactPoint' => [
                '@type' => 'ContactPoint',
                'telephone' => $settings['site_settings']['phone'],
                'contactType' => 'customer service',
                'email' => $settings['site_settings']['contact_email']
            ],
            'address' => [
                '@type' => 'PostalAddress',
                'streetAddress' => $settings['site_settings']['address']['street'],
                'addressLocality' => $settings['site_settings']['address']['city'],
                'postalCode' => $settings['site_settings']['address']['postal_code'],
                'addressCountry' => 'CZ'
            ]
        ];
    }
    
    /**
     * Strukturovaná data pro produkt
     */
    private function getProductData($product)
    {
        if (empty($product)) {
            return [];
        }
        
        $data = [
            '@context' => 'https://schema.org',
            '@type' => 'Product',
            'name' => $product['name'],
            'description' => $product['description'] ?? '',
            'category' => $product['category'] ?? 'Šňůrky na krk',
            'brand' => [
                '@type' => 'Brand',
                'name' => $this->settings['site_settings']['site_name']
            ]
        ];
        
        if (!empty($product['images'][0])) {
            $data['image'] = $this->settings['site_settings']['site_url'] . '/' . $product['images'][0];
        }
        
        if (isset($product['price'])) {
            $data['offers'] = [
                '@type' => 'Offer',
                'price' => $product['price'],
                'priceCurrency' => 'CZK',
                'availability' => isset($product['in_stock']) && $product['in_stock'] > 0 ? 
                    'https://schema.org/InStock' : 'https://schema.org/OutOfStock'
            ];
        }
        
        return $data;
    }
    
    /**
     * Strukturovaná data pro breadcrumb
     */
    private function getBreadcrumbData($breadcrumbs)
    {
        if (empty($breadcrumbs)) {
            return [];
        }
        
        $items = [];
        foreach ($breadcrumbs as $index => $breadcrumb) {
            $items[] = [
                '@type' => 'ListItem',
                'position' => $index + 1,
                'name' => $breadcrumb['text'],
                'item' => isset($breadcrumb['url']) ? 
                    $this->settings['site_settings']['site_url'] . $breadcrumb['url'] : null
            ];
        }
        
        return [
            '@context' => 'https://schema.org',
            '@type' => 'BreadcrumbList',
            'itemListElement' => $items
        ];
    }
    
    /**
     * Strukturovaná data pro website
     */
    private function getWebsiteData()
    {
        return [
            '@context' => 'https://schema.org',
            '@type' => 'WebSite',
            'name' => $this->settings['site_settings']['site_name'],
            'url' => $this->settings['site_settings']['site_url'],
            'description' => $this->settings['site_settings']['site_description'],
            'potentialAction' => [
                '@type' => 'SearchAction',
                'target' => $this->settings['site_settings']['site_url'] . '/produkty?search={search_term_string}',
                'query-input' => 'required name=search_term_string'
            ]
        ];
    }
}
