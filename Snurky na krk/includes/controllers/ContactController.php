<?php

/**
 * Kontroler pro kontaktní stránku
 * Zobrazuje kontaktní formulář a zpracovává zprávy
 */
class ContactController extends BaseController
{
    private $settingsModel;
    
    public function __construct()
    {
        parent::__construct();
        $this->settingsModel = new SettingsModel();
    }
    
    /**
     * Zobrazí kontaktní stránku
     */
    public function index()
    {
        try {
            // Získej kontaktní informace z nastavení
            $settings = $this->settingsModel->getAll();
            $contactInfo = $settings['contact'] ?? [];
            
            echo $this->renderWithLayout('contact/index', [
                'title' => 'Kontakt - Šňůrky na krk',
                'contactInfo' => $contactInfo,
                'breadcrumb' => [
                    ['url' => '/', 'title' => 'Domů'],
                    'Kontakt'
                ]
            ]);
            
        } catch (Exception $e) {
            $this->setFlashMessage('Chyba při načítání kontaktní stránky: ' . $e->getMessage(), 'error');
            echo $this->renderWithLayout('error/500', [
                'title' => 'Chyba',
                'message' => 'Nepodařilo se načíst kontaktní stránku'
            ]);
        }
    }
    
    /**
     * Zpracuje kontaktní formulář
     */
    public function send()
    {
        try {
            // Ověř CSRF token
            $postData = $this->getPostData(['csrf_token']);
            if (!$this->validateCSRF($postData['csrf_token'])) {
                throw new Exception('Neplatný CSRF token');
            }
            
            // Validace dat
            $rules = [
                'name' => [
                    'name' => 'Jméno',
                    'required' => true,
                    'min_length' => 2,
                    'max_length' => 100
                ],
                'email' => [
                    'name' => 'Email',
                    'required' => true,
                    'email' => true
                ],
                'phone' => [
                    'name' => 'Telefon',
                    'required' => false,
                    'phone' => true
                ],
                'subject' => [
                    'name' => 'Předmět',
                    'required' => true,
                    'min_length' => 5,
                    'max_length' => 200
                ],
                'message' => [
                    'name' => 'Zpráva',
                    'required' => true,
                    'min_length' => 10,
                    'max_length' => 2000
                ]
            ];
            
            $errors = ValidationHelper::validate($postData, $rules);
            
            if (!empty($errors)) {
                $this->setFlashMessage('Formulář obsahuje chyby', 'error');
                
                // Získej kontaktní informace pro znovu zobrazení
                $settings = $this->settingsModel->getAll();
                $contactInfo = $settings['contact'] ?? [];
                
                echo $this->renderWithLayout('contact/index', [
                    'title' => 'Kontakt - Šňůrky na krk',
                    'contactInfo' => $contactInfo,
                    'errors' => $errors,
                    'formData' => $postData,
                    'breadcrumb' => [
                        ['url' => '/', 'title' => 'Domů'],
                        'Kontakt'
                    ]
                ]);
                return;
            }
            
            // Uložení zprávy do logu
            $this->logContactMessage($postData);
            
            // Odeslání emailu (pokud je nakonfigurován)
            $this->sendContactEmail($postData);
            
            $this->setFlashMessage('Vaše zpráva byla úspěšně odeslána. Odpovíme vám co nejdříve.', 'success');
            $this->redirect('/kontakt');
            
        } catch (Exception $e) {
            $this->setFlashMessage('Chyba při odesílání zprávy: ' . $e->getMessage(), 'error');
            $this->redirect('/kontakt');
        }
    }
    
    /**
     * Uloží kontaktní zprávu do logu
     */
    private function logContactMessage($data)
    {
        $logFile = ROOT_PATH . '/data/contact_messages.log';
        
        $timestamp = date('Y-m-d H:i:s');
        $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
        
        $logEntry = sprintf(
            "[%s] IP: %s\n" .
            "Jméno: %s\n" .
            "Email: %s\n" .
            "Telefon: %s\n" .
            "Předmět: %s\n" .
            "Zpráva: %s\n" .
            "User-Agent: %s\n" .
            "%s\n\n",
            $timestamp,
            $ip,
            $data['name'],
            $data['email'],
            $data['phone'] ?? 'neuvedeno',
            $data['subject'],
            $data['message'],
            $userAgent,
            str_repeat('-', 80)
        );
        
        // Zajisti, že adresář existuje
        $logDir = dirname($logFile);
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
    }
    
    /**
     * Odešle kontaktní email (pokud je nakonfigurován)
     */
    private function sendContactEmail($data)
    {
        try {
            $settings = $this->settingsModel->getAll();
            $emailSettings = $settings['email'] ?? [];
            
            // Pokud není email nakonfigurován, přeskoč
            if (empty($emailSettings['contact_email'])) {
                return;
            }
            
            $to = $emailSettings['contact_email'];
            $subject = 'Nová zpráva z kontaktního formuláře: ' . $data['subject'];
            
            $message = "Nová zpráva z kontaktního formuláře:\n\n";
            $message .= "Jméno: " . $data['name'] . "\n";
            $message .= "Email: " . $data['email'] . "\n";
            $message .= "Telefon: " . ($data['phone'] ?? 'neuvedeno') . "\n";
            $message .= "Předmět: " . $data['subject'] . "\n\n";
            $message .= "Zpráva:\n" . $data['message'] . "\n\n";
            $message .= "---\n";
            $message .= "Odesláno: " . date('d.m.Y H:i:s') . "\n";
            $message .= "IP adresa: " . ($_SERVER['REMOTE_ADDR'] ?? 'unknown') . "\n";
            
            $headers = [
                'From: ' . ($emailSettings['from_email'] ?? '<EMAIL>'),
                'Reply-To: ' . $data['email'],
                'X-Mailer: PHP/' . phpversion(),
                'Content-Type: text/plain; charset=UTF-8'
            ];
            
            mail($to, $subject, $message, implode("\r\n", $headers));
            
        } catch (Exception $e) {
            // Loguj chybu, ale nepřerušuj proces
            error_log('Chyba při odesílání kontaktního emailu: ' . $e->getMessage());
        }
    }
    
    /**
     * Zobrazí stránku s informacemi o společnosti
     */
    public function about()
    {
        try {
            $settings = $this->settingsModel->getAll();
            $companyInfo = $settings['company'] ?? [];
            
            echo $this->renderWithLayout('contact/about', [
                'title' => 'O nás - Šňůrky na krk',
                'companyInfo' => $companyInfo,
                'breadcrumb' => [
                    ['url' => '/', 'title' => 'Domů'],
                    'O nás'
                ]
            ]);
            
        } catch (Exception $e) {
            $this->setFlashMessage('Chyba při načítání stránky: ' . $e->getMessage(), 'error');
            echo $this->renderWithLayout('error/500', [
                'title' => 'Chyba',
                'message' => 'Nepodařilo se načíst stránku'
            ]);
        }
    }
    
    /**
     * Zobrazí obchodní podmínky
     */
    public function terms()
    {
        try {
            $settings = $this->settingsModel->getAll();
            $termsContent = $settings['legal']['terms'] ?? '';
            
            echo $this->renderWithLayout('contact/terms', [
                'title' => 'Obchodní podmínky - Šňůrky na krk',
                'termsContent' => $termsContent,
                'breadcrumb' => [
                    ['url' => '/', 'title' => 'Domů'],
                    'Obchodní podmínky'
                ]
            ]);
            
        } catch (Exception $e) {
            $this->setFlashMessage('Chyba při načítání stránky: ' . $e->getMessage(), 'error');
            echo $this->renderWithLayout('error/500', [
                'title' => 'Chyba',
                'message' => 'Nepodařilo se načíst obchodní podmínky'
            ]);
        }
    }
    
    /**
     * Zobrazí zásady ochrany osobních údajů
     */
    public function privacy()
    {
        try {
            $settings = $this->settingsModel->getAll();
            $privacyContent = $settings['legal']['privacy'] ?? '';
            
            echo $this->renderWithLayout('contact/privacy', [
                'title' => 'Ochrana osobních údajů - Šňůrky na krk',
                'privacyContent' => $privacyContent,
                'breadcrumb' => [
                    ['url' => '/', 'title' => 'Domů'],
                    'Ochrana osobních údajů'
                ]
            ]);
            
        } catch (Exception $e) {
            $this->setFlashMessage('Chyba při načítání stránky: ' . $e->getMessage(), 'error');
            echo $this->renderWithLayout('error/500', [
                'title' => 'Chyba',
                'message' => 'Nepodařilo se načíst zásady ochrany osobních údajů'
            ]);
        }
    }
}
