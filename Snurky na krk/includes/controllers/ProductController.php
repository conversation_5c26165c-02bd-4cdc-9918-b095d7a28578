<?php

/**
 * Kontroler pro správu produktů (šňůrek)
 * Zobrazuje produkty, kategorie, detail produktu
 */
class ProductController extends BaseController
{
    private $productModel;
    
    public function __construct()
    {
        parent::__construct();
        $this->productModel = new ProductModel();
    }
    
    /**
     * Zobrazí seznam všech produktů
     */
    public function index()
    {
        try {
            $params = $this->getGetData(['category', 'search', 'sort', 'page']);
            
            // Získej produkty s filtrováním
            $products = $this->productModel->getAll();
            
            // Filtrování podle kategorie
            if (!empty($params['category'])) {
                $products = array_filter($products, function($product) use ($params) {
                    return $product['category'] === $params['category'];
                });
            }
            
            // Vyhledávání
            if (!empty($params['search'])) {
                $search = strtolower($params['search']);
                $products = array_filter($products, function($product) use ($search) {
                    return strpos(strtolower($product['name']), $search) !== false ||
                           strpos(strtolower($product['description']), $search) !== false;
                });
            }
            
            // Řazení
            $sort = $params['sort'] ?? 'name';
            switch ($sort) {
                case 'price_asc':
                    usort($products, function($a, $b) { return $a['price'] <=> $b['price']; });
                    break;
                case 'price_desc':
                    usort($products, function($a, $b) { return $b['price'] <=> $a['price']; });
                    break;
                case 'name':
                default:
                    usort($products, function($a, $b) { return $a['name'] <=> $b['name']; });
                    break;
            }
            
            // Získej kategorie pro filtr
            $categories = $this->productModel->getCategories();
            
            // Stránkování
            $page = max(1, intval($params['page'] ?? 1));
            $perPage = 12;
            $total = count($products);
            $totalPages = ceil($total / $perPage);
            $offset = ($page - 1) * $perPage;
            $products = array_slice($products, $offset, $perPage);
            
            echo $this->renderWithLayout('products/index', [
                'title' => 'Produkty - Šňůrky na krk',
                'products' => $products,
                'categories' => $categories,
                'currentCategory' => $params['category'] ?? '',
                'currentSearch' => $params['search'] ?? '',
                'currentSort' => $sort,
                'pagination' => [
                    'current' => $page,
                    'total' => $totalPages,
                    'per_page' => $perPage,
                    'total_items' => $total
                ]
            ]);
            
        } catch (Exception $e) {
            $this->setFlashMessage('Chyba při načítání produktů: ' . $e->getMessage(), 'error');
            echo $this->renderWithLayout('error/500', [
                'title' => 'Chyba',
                'message' => 'Nepodařilo se načíst produkty'
            ]);
        }
    }
    
    /**
     * Zobrazí detail produktu
     */
    public function detail($id)
    {
        try {
            $product = $this->productModel->getById($id);
            
            if (!$product) {
                http_response_code(404);
                echo $this->renderWithLayout('error/404', [
                    'title' => 'Produkt nenalezen',
                    'message' => 'Požadovaný produkt nebyl nalezen'
                ]);
                return;
            }
            
            // Získej podobné produkty ze stejné kategorie
            $relatedProducts = $this->productModel->getByCategory($product['category'], 4);
            $relatedProducts = array_filter($relatedProducts, function($p) use ($id) {
                return $p['id'] !== $id;
            });
            
            echo $this->renderWithLayout('products/detail', [
                'title' => $product['name'] . ' - Šňůrky na krk',
                'product' => $product,
                'relatedProducts' => array_slice($relatedProducts, 0, 3),
                'breadcrumb' => [
                    ['url' => '/', 'title' => 'Domů'],
                    ['url' => '/produkty', 'title' => 'Produkty'],
                    $product['name']
                ]
            ]);
            
        } catch (Exception $e) {
            $this->setFlashMessage('Chyba při načítání produktu: ' . $e->getMessage(), 'error');
            echo $this->renderWithLayout('error/500', [
                'title' => 'Chyba',
                'message' => 'Nepodařilo se načíst detail produktu'
            ]);
        }
    }
    
    /**
     * Zobrazí produkty podle kategorie
     */
    public function category($categorySlug)
    {
        try {
            // Najdi kategorii podle slug
            $categories = $this->productModel->getCategories();
            $category = null;
            
            foreach ($categories as $cat) {
                if (FormattingHelper::slugify($cat['name']) === $categorySlug) {
                    $category = $cat;
                    break;
                }
            }
            
            if (!$category) {
                http_response_code(404);
                echo $this->renderWithLayout('error/404', [
                    'title' => 'Kategorie nenalezena',
                    'message' => 'Požadovaná kategorie nebyla nalezena'
                ]);
                return;
            }
            
            // Získej produkty z kategorie
            $products = $this->productModel->getByCategory($category['name']);
            
            // Řazení
            $params = $this->getGetData(['sort', 'page']);
            $sort = $params['sort'] ?? 'name';
            
            switch ($sort) {
                case 'price_asc':
                    usort($products, function($a, $b) { return $a['price'] <=> $b['price']; });
                    break;
                case 'price_desc':
                    usort($products, function($a, $b) { return $b['price'] <=> $a['price']; });
                    break;
                case 'name':
                default:
                    usort($products, function($a, $b) { return $a['name'] <=> $b['name']; });
                    break;
            }
            
            // Stránkování
            $page = max(1, intval($params['page'] ?? 1));
            $perPage = 12;
            $total = count($products);
            $totalPages = ceil($total / $perPage);
            $offset = ($page - 1) * $perPage;
            $products = array_slice($products, $offset, $perPage);
            
            echo $this->renderWithLayout('products/category', [
                'title' => $category['name'] . ' - Šňůrky na krk',
                'category' => $category,
                'products' => $products,
                'categories' => $categories,
                'currentSort' => $sort,
                'pagination' => [
                    'current' => $page,
                    'total' => $totalPages,
                    'per_page' => $perPage,
                    'total_items' => $total
                ],
                'breadcrumb' => [
                    ['url' => '/', 'title' => 'Domů'],
                    ['url' => '/produkty', 'title' => 'Produkty'],
                    $category['name']
                ]
            ]);
            
        } catch (Exception $e) {
            $this->setFlashMessage('Chyba při načítání kategorie: ' . $e->getMessage(), 'error');
            echo $this->renderWithLayout('error/500', [
                'title' => 'Chyba',
                'message' => 'Nepodařilo se načíst kategorii'
            ]);
        }
    }
    
    /**
     * API endpoint pro vyhledávání produktů (AJAX)
     */
    public function search()
    {
        try {
            $query = $this->sanitizeInput($_GET['q'] ?? '');
            
            if (strlen($query) < 2) {
                $this->jsonResponse(['products' => []]);
                return;
            }
            
            $products = $this->productModel->search($query, 10);
            
            // Formátuj produkty pro JSON
            $formattedProducts = array_map(function($product) {
                return [
                    'id' => $product['id'],
                    'name' => $product['name'],
                    'price' => $product['price'],
                    'image' => $product['image'],
                    'url' => '/produkty/' . $product['id']
                ];
            }, $products);
            
            $this->jsonResponse(['products' => $formattedProducts]);
            
        } catch (Exception $e) {
            $this->jsonResponse(['error' => 'Chyba při vyhledávání'], 500);
        }
    }
}
