<?php
/**
 * Administrační kontroler pro dashboard
 * 
 * <AUTHOR> Team
 * @version 1.0
 */

if (!defined('SNURKY_INIT')) {
    die('Přímý přístup není povolen');
}

class AdminDashboardController extends BaseController
{
    protected $sessionManager;
    protected $productModel;
    protected $merchantModel;
    protected $orderModel;
    
    public function __construct()
    {
        parent::__construct();
        $this->sessionManager = new SessionManager();
        $this->productModel = new ProductModel();
        $this->merchantModel = new MerchantModel();
        $this->orderModel = new OrderModel();
        
        // Kontrola přihlášení
        $this->requireAdminAuth();
    }
    
    /**
     * Zobrazí hlavní dashboard
     */
    public function index()
    {
        try {
            // Získání statistik
            $stats = $this->getStatistics();
            
            // Získání posledních aktivit
            $recentActivities = $this->getRecentActivities();
            
            $this->render('admin/dashboard', [
                'title' => 'Dashboard - Administrace',
                'stats' => $stats,
                'recent_activities' => $recentActivities
            ]);
            
        } catch (Exception $e) {
            $this->handleError($e, 'Chyba při načítání dashboardu');
        }
    }
    
    /**
     * Získá statistiky pro dashboard
     */
    private function getStatistics()
    {
        $products = $this->productModel->getAll();
        $merchants = $this->merchantModel->getAll();
        $orders = $this->orderModel->getAll();
        
        // Základní počty
        $stats = [
            'products_total' => count($products),
            'products_active' => count(array_filter($products, function($p) { return $p['active']; })),
            'products_out_of_stock' => count(array_filter($products, function($p) { return $p['stock'] <= 0; })),
            'merchants_total' => count($merchants),
            'merchants_active' => count(array_filter($merchants, function($m) { return $m['status'] === 'active'; })),
            'merchants_pending' => count(array_filter($merchants, function($m) { return $m['status'] === 'pending'; })),
            'orders_total' => count($orders),
            'orders_pending' => count(array_filter($orders, function($o) { return $o['status'] === 'pending'; })),
            'orders_completed' => count(array_filter($orders, function($o) { return $o['status'] === 'completed'; }))
        ];
        
        // Výpočet celkových tržeb
        $totalRevenue = 0;
        $monthlyRevenue = 0;
        $currentMonth = date('Y-m');
        
        foreach ($orders as $order) {
            if ($order['status'] === 'completed') {
                $totalRevenue += $order['total_amount'];
                
                if (strpos($order['created_at'], $currentMonth) === 0) {
                    $monthlyRevenue += $order['total_amount'];
                }
            }
        }
        
        $stats['revenue_total'] = $totalRevenue;
        $stats['revenue_monthly'] = $monthlyRevenue;
        
        return $stats;
    }
    
    /**
     * Získá poslední aktivity
     */
    private function getRecentActivities()
    {
        $activities = [];
        
        // Poslední objednávky
        $orders = $this->orderModel->getAll();
        usort($orders, function($a, $b) {
            return strtotime($b['created_at']) - strtotime($a['created_at']);
        });
        
        foreach (array_slice($orders, 0, 5) as $order) {
            $activities[] = [
                'type' => 'order',
                'title' => 'Nová objednávka #' . $order['order_number'],
                'description' => 'Celkem: ' . number_format($order['total_amount'], 0, ',', ' ') . ' Kč',
                'time' => $order['created_at'],
                'status' => $order['status']
            ];
        }
        
        // Poslední registrace obchodníků
        $merchants = $this->merchantModel->getAll();
        usort($merchants, function($a, $b) {
            return strtotime($b['created_at']) - strtotime($a['created_at']);
        });
        
        foreach (array_slice($merchants, 0, 3) as $merchant) {
            $activities[] = [
                'type' => 'merchant',
                'title' => 'Nová registrace obchodníka',
                'description' => $merchant['company_name'],
                'time' => $merchant['created_at'],
                'status' => $merchant['status']
            ];
        }
        
        // Seřazení podle času
        usort($activities, function($a, $b) {
            return strtotime($b['time']) - strtotime($a['time']);
        });
        
        return array_slice($activities, 0, 10);
    }
    
    /**
     * Kontrola admin autentizace
     */
    private function requireAdminAuth()
    {
        if (!$this->sessionManager->isAdminLoggedIn()) {
            header('Location: /admin/login');
            exit;
        }
    }
    
    /**
     * Zpracování chyb
     */
    private function handleError($exception, $message)
    {
        error_log("Admin Dashboard Error: " . $exception->getMessage());
        
        $this->render('admin/error', [
            'title' => 'Chyba',
            'message' => $message,
            'error' => DEBUG_MODE ? $exception->getMessage() : null
        ]);
    }
}
