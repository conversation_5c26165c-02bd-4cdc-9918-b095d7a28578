<?php

/**
 * Základní třída pro všechny kontrolery
 * Poskytuje společné funkce pro všechny kontrolery
 */
class BaseController
{
    protected $templateEngine;
    protected $security;
    protected $sessionManager;
    
    public function __construct()
    {
        $this->templateEngine = new TemplateEngine();
        $this->security = new Security();
        $this->sessionManager = new SessionManager();
    }
    
    /**
     * Renderuje šablonu s daty
     */
    protected function render($template, $data = [])
    {
        // Automatická detekce admin layoutu
        $layout = defined('ADMIN_AREA') ? 'admin' : 'main';

        // Přidání globálních dat pro admin
        if (defined('ADMIN_AREA')) {
            $data = array_merge($data, $this->getAdminGlobalData());
        }

        echo $this->templateEngine->renderWithLayout($template, $data, $layout);
    }

    /**
     * Renderuje šablonu s layoutem
     */
    protected function renderWithLayout($template, $data = [], $layout = 'main')
    {
        // Přidání globálních dat pro admin
        if (defined('ADMIN_AREA')) {
            $data = array_merge($data, $this->getAdminGlobalData());
        }

        echo $this->templateEngine->renderWithLayout($template, $data, $layout);
    }

    /**
     * Získá globální data pro admin
     */
    protected function getAdminGlobalData()
    {
        return [
            'admin_user' => $_SESSION['admin_username'] ?? null,
            'is_admin_logged_in' => $this->sessionManager->isAdminLoggedIn(),
            'flash_messages' => $this->sessionManager->getFlashMessages()
        ];
    }
    
    /**
     * Přesměruje na jinou URL
     */
    protected function redirect($url, $statusCode = 302)
    {
        header("Location: $url", true, $statusCode);
        exit;
    }
    
    /**
     * Vrátí JSON odpověď
     */
    protected function jsonResponse($data, $statusCode = 200)
    {
        http_response_code($statusCode);
        header('Content-Type: application/json');
        echo json_encode($data);
        exit;
    }
    
    /**
     * Ověří CSRF token
     */
    protected function validateCSRF($token)
    {
        return $this->security->validateCSRFToken($token);
    }
    
    /**
     * Sanitizuje vstupní data
     */
    protected function sanitizeInput($data)
    {
        return $this->security->sanitizeInput($data);
    }
    
    /**
     * Ověří, zda je uživatel přihlášen (pro admin)
     */
    protected function requireAuth()
    {
        if (!$this->sessionManager->isLoggedIn()) {
            $this->redirect('/admin');
        }
    }
    
    /**
     * Získá POST data s validací
     */
    protected function getPostData($required = [])
    {
        $data = [];
        
        foreach ($_POST as $key => $value) {
            $data[$key] = $this->sanitizeInput($value);
        }
        
        // Ověř povinná pole
        foreach ($required as $field) {
            if (empty($data[$field])) {
                throw new InvalidArgumentException("Povinné pole '$field' není vyplněno");
            }
        }
        
        return $data;
    }
    
    /**
     * Získá GET parametry s validací
     */
    protected function getGetData($allowed = [])
    {
        $data = [];
        
        foreach ($_GET as $key => $value) {
            if (empty($allowed) || in_array($key, $allowed)) {
                $data[$key] = $this->sanitizeInput($value);
            }
        }
        
        return $data;
    }
    
    /**
     * Nastaví flash zprávu
     */
    protected function setFlashMessage($message, $type = 'info')
    {
        $this->sessionManager->setFlashMessage($message, $type);
    }
    
    /**
     * Získá flash zprávy
     */
    protected function getFlashMessages()
    {
        return $this->sessionManager->getFlashMessages();
    }

    /**
     * Kontrola admin autentizace
     */
    protected function requireAdminAuth()
    {
        if (!$this->sessionManager) {
            $this->sessionManager = new SessionManager();
        }

        if (!$this->sessionManager->isAdminLoggedIn()) {
            header('Location: /admin/login');
            exit;
        }
    }

    /**
     * Zpracování chyby pro admin rozhraní
     */
    protected function handleError(Exception $e, $userMessage = 'Došlo k chybě')
    {
        // Logování chyby
        error_log("Controller Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine());

        // Nastavení flash zprávy
        if ($this->sessionManager) {
            $this->sessionManager->setFlashMessage('error', $userMessage);
        }

        // V debug režimu zobrazíme detailní chybu
        if (DEBUG_MODE) {
            throw $e;
        }

        // Přesměrování na error stránku nebo předchozí stránku
        $referer = $_SERVER['HTTP_REFERER'] ?? '/admin/dashboard';
        header("Location: $referer");
        exit;
    }
}
