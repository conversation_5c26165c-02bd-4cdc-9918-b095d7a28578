<?php
/**
 * Administrační kontroler pro nastavení
 * 
 * <AUTHOR> Team
 * @version 1.0
 */

if (!defined('SNURKY_INIT')) {
    die('Přímý přístup není povolen');
}

class AdminSettingsController extends BaseController
{
    protected $sessionManager;
    protected $settingsModel;
    protected $security;
    protected $validationHelper;
    
    public function __construct()
    {
        parent::__construct();
        $this->sessionManager = new SessionManager();
        $this->settingsModel = new SettingsModel();
        $this->security = new Security();
        $this->validationHelper = new ValidationHelper();
        
        // Kontrola přihlášení
        $this->requireAdminAuth();
    }
    
    /**
     * Zobrazí formulář nastavení
     */
    public function index()
    {
        try {
            $settings = $this->settingsModel->getSettings();
            
            $this->render('admin/settings/index', [
                'title' => 'Nastavení aplikace',
                'settings' => $settings,
                'csrf_token' => $this->security->generateCSRFToken()
            ]);
            
        } catch (Exception $e) {
            $this->handleError($e, 'Chyba při načítání nastavení');
        }
    }
    
    /**
     * Aktualizace nastavení
     */
    public function update()
    {
        try {
            // Ověření CSRF tokenu
            if (!$this->security->verifyCSRFToken($_POST['csrf_token'] ?? '')) {
                throw new Exception('Neplatný CSRF token');
            }
            
            $currentSettings = $this->settingsModel->getSettings();
            
            // Validace a aktualizace jednotlivých sekcí
            $updatedSettings = $currentSettings;
            
            // Webové nastavení
            if (isset($_POST['website'])) {
                $updatedSettings['website'] = $this->validateWebsiteSettings($_POST['website']);
            }
            
            // Obchodní nastavení
            if (isset($_POST['shop'])) {
                $updatedSettings['shop'] = $this->validateShopSettings($_POST['shop']);
            }
            
            // Email nastavení
            if (isset($_POST['email'])) {
                $updatedSettings['email'] = $this->validateEmailSettings($_POST['email']);
            }
            
            // Admin nastavení (pouze změna hesla)
            if (isset($_POST['admin_password']) && !empty($_POST['admin_password'])) {
                $this->updateAdminPassword($_POST['admin_password'], $_POST['admin_password_confirm'] ?? '');
            }
            
            // Uložení nastavení
            $this->settingsModel->updateSettings($updatedSettings);
            
            $this->sessionManager->setFlashMessage('success', 'Nastavení bylo úspěšně aktualizováno');
            header('Location: /admin/nastaveni');
            exit;
            
        } catch (Exception $e) {
            $settings = $this->settingsModel->getSettings();
            
            $this->render('admin/settings/index', [
                'title' => 'Nastavení aplikace',
                'settings' => $settings,
                'error' => $e->getMessage(),
                'csrf_token' => $this->security->generateCSRFToken(),
                'old_data' => $_POST
            ]);
        }
    }
    
    /**
     * Validace webových nastavení
     */
    private function validateWebsiteSettings($data)
    {
        $rules = [
            'name' => 'required|min:3|max:100',
            'description' => 'required|min:10|max:500',
            'keywords' => 'required|min:10|max:200',
            'contact_email' => 'required|email',
            'contact_phone' => 'required|min:9|max:20',
            'address' => 'required|min:10|max:200'
        ];
        
        return $this->validationHelper->validate($data, $rules);
    }
    
    /**
     * Validace obchodních nastavení
     */
    private function validateShopSettings($data)
    {
        $rules = [
            'currency' => 'required|in:CZK,EUR,USD',
            'tax_rate' => 'required|numeric|min:0|max:100',
            'shipping_cost' => 'required|numeric|min:0',
            'free_shipping_limit' => 'required|numeric|min:0',
            'default_commission' => 'required|numeric|min:0|max:100'
        ];
        
        $validatedData = $this->validationHelper->validate($data, $rules);
        
        // Přidání boolean hodnot
        $validatedData['enable_orders'] = isset($data['enable_orders']) ? true : false;
        $validatedData['enable_registration'] = isset($data['enable_registration']) ? true : false;
        
        return $validatedData;
    }
    
    /**
     * Validace emailových nastavení
     */
    private function validateEmailSettings($data)
    {
        $rules = [
            'smtp_host' => 'required|min:3|max:100',
            'smtp_port' => 'required|integer|min:1|max:65535',
            'smtp_username' => 'required|email',
            'smtp_password' => 'required|min:6',
            'from_email' => 'required|email',
            'from_name' => 'required|min:3|max:100'
        ];
        
        $validatedData = $this->validationHelper->validate($data, $rules);
        
        // Přidání boolean hodnot
        $validatedData['smtp_secure'] = isset($data['smtp_secure']) ? true : false;
        
        return $validatedData;
    }
    
    /**
     * Aktualizace admin hesla
     */
    private function updateAdminPassword($newPassword, $confirmPassword)
    {
        if ($newPassword !== $confirmPassword) {
            throw new Exception('Hesla se neshodují');
        }
        
        if (strlen($newPassword) < 8) {
            throw new Exception('Heslo musí mít alespoň 8 znaků');
        }
        
        // Získání aktuálních nastavení
        $settings = $this->settingsModel->getSettings();
        
        // Aktualizace hesla pro prvního admin uživatele
        if (isset($settings['admin']['users'][0])) {
            $settings['admin']['users'][0]['password'] = $this->security->hashPassword($newPassword);
            $this->settingsModel->updateSettings($settings);
        }
    }
    
    /**
     * Kontrola admin autentizace
     */
    private function requireAdminAuth()
    {
        if (!$this->sessionManager->isAdminLoggedIn()) {
            header('Location: /admin/login');
            exit;
        }
    }
    
    /**
     * Zpracování chyb
     */
    private function handleError($exception, $message)
    {
        error_log("Admin Settings Error: " . $exception->getMessage());
        
        $this->render('admin/error', [
            'title' => 'Chyba',
            'message' => $message,
            'error' => DEBUG_MODE ? $exception->getMessage() : null
        ]);
    }
}
