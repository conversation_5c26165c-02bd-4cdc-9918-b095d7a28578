<?php

/**
 * Kontroler pro správu obchodníků
 * Zobrazuje seznam obchodníků, registraci nových obchodníků
 */
class DealerController extends BaseController
{
    private $merchantModel;
    
    public function __construct()
    {
        parent::__construct();
        $this->merchantModel = new MerchantModel();
    }
    
    /**
     * Zobrazí seznam obchodníků
     */
    public function index()
    {
        try {
            $params = $this->getGetData(['region', 'search', 'page']);
            
            // Získej všechny obchodníky
            $merchants = $this->merchantModel->getAll();
            
            // Filtr pouze aktivních obchodníků
            $merchants = array_filter($merchants, function($merchant) {
                return $merchant['status'] === 'active';
            });
            
            // Filtrování podle regionu
            if (!empty($params['region'])) {
                $merchants = array_filter($merchants, function($merchant) use ($params) {
                    return stripos($merchant['address']['region'] ?? '', $params['region']) !== false;
                });
            }
            
            // Vyhledávání
            if (!empty($params['search'])) {
                $search = strtolower($params['search']);
                $merchants = array_filter($merchants, function($merchant) use ($search) {
                    return strpos(strtolower($merchant['business_name']), $search) !== false ||
                           strpos(strtolower($merchant['address']['city'] ?? ''), $search) !== false ||
                           strpos(strtolower($merchant['address']['region'] ?? ''), $search) !== false;
                });
            }
            
            // Řazení podle názvu
            usort($merchants, function($a, $b) {
                return $a['business_name'] <=> $b['business_name'];
            });
            
            // Získej regiony pro filtr
            $regions = [];
            foreach ($merchants as $merchant) {
                $region = $merchant['address']['region'] ?? '';
                if ($region && !in_array($region, $regions)) {
                    $regions[] = $region;
                }
            }
            sort($regions);
            
            // Stránkování
            $page = max(1, intval($params['page'] ?? 1));
            $perPage = 12;
            $total = count($merchants);
            $totalPages = ceil($total / $perPage);
            $offset = ($page - 1) * $perPage;
            $merchants = array_slice($merchants, $offset, $perPage);
            
            echo $this->renderWithLayout('dealers/index', [
                'title' => 'Obchodníci - Šňůrky na krk',
                'merchants' => $merchants,
                'regions' => $regions,
                'currentRegion' => $params['region'] ?? '',
                'currentSearch' => $params['search'] ?? '',
                'pagination' => [
                    'current' => $page,
                    'total' => $totalPages,
                    'per_page' => $perPage,
                    'total_items' => $total
                ]
            ]);
            
        } catch (Exception $e) {
            $this->setFlashMessage('Chyba při načítání obchodníků: ' . $e->getMessage(), 'error');
            echo $this->renderWithLayout('error/500', [
                'title' => 'Chyba',
                'message' => 'Nepodařilo se načíst seznam obchodníků'
            ]);
        }
    }
    
    /**
     * Zobrazí detail obchodníka
     */
    public function detail($id)
    {
        try {
            $merchant = $this->merchantModel->getById($id);
            
            if (!$merchant || $merchant['status'] !== 'active') {
                http_response_code(404);
                echo $this->renderWithLayout('error/404', [
                    'title' => 'Obchodník nenalezen',
                    'message' => 'Požadovaný obchodník nebyl nalezen'
                ]);
                return;
            }
            
            echo $this->renderWithLayout('dealers/detail', [
                'title' => $merchant['business_name'] . ' - Obchodníci',
                'merchant' => $merchant,
                'breadcrumb' => [
                    ['url' => '/', 'title' => 'Domů'],
                    ['url' => '/obchodnici', 'title' => 'Obchodníci'],
                    $merchant['business_name']
                ]
            ]);
            
        } catch (Exception $e) {
            $this->setFlashMessage('Chyba při načítání obchodníka: ' . $e->getMessage(), 'error');
            echo $this->renderWithLayout('error/500', [
                'title' => 'Chyba',
                'message' => 'Nepodařilo se načíst detail obchodníka'
            ]);
        }
    }
    
    /**
     * Zobrazí formulář pro registraci obchodníka
     */
    public function register()
    {
        echo $this->renderWithLayout('dealers/register', [
            'title' => 'Registrace obchodníka - Šňůrky na krk',
            'breadcrumb' => [
                ['url' => '/', 'title' => 'Domů'],
                ['url' => '/obchodnici', 'title' => 'Obchodníci'],
                'Registrace'
            ]
        ]);
    }
    
    /**
     * Zpracuje registraci obchodníka
     */
    public function processRegister()
    {
        try {
            // Ověř CSRF token
            $postData = $this->getPostData(['csrf_token']);
            if (!$this->validateCSRF($postData['csrf_token'])) {
                throw new Exception('Neplatný CSRF token');
            }
            
            // Validace dat
            $rules = [
                'business_name' => [
                    'name' => 'Název firmy',
                    'required' => true,
                    'min_length' => 2,
                    'max_length' => 100
                ],
                'contact_person' => [
                    'name' => 'Kontaktní osoba',
                    'required' => true,
                    'min_length' => 2,
                    'max_length' => 100
                ],
                'email' => [
                    'name' => 'Email',
                    'required' => true,
                    'email' => true
                ],
                'phone' => [
                    'name' => 'Telefon',
                    'required' => true,
                    'phone' => true
                ],
                'street' => [
                    'name' => 'Ulice',
                    'required' => true,
                    'max_length' => 100
                ],
                'city' => [
                    'name' => 'Město',
                    'required' => true,
                    'max_length' => 50
                ],
                'zip' => [
                    'name' => 'PSČ',
                    'required' => true,
                    'max_length' => 10
                ],
                'region' => [
                    'name' => 'Kraj',
                    'required' => true,
                    'max_length' => 50
                ],
                'website' => [
                    'name' => 'Webové stránky',
                    'required' => false,
                    'max_length' => 200
                ],
                'description' => [
                    'name' => 'Popis',
                    'required' => false,
                    'max_length' => 1000
                ]
            ];
            
            $errors = ValidationHelper::validate($postData, $rules);
            
            // Ověř, zda email již neexistuje
            if (empty($errors['email'])) {
                $existingMerchant = $this->merchantModel->getByEmail($postData['email']);
                if ($existingMerchant) {
                    $errors['email'][] = 'Email je již registrován';
                }
            }
            
            if (!empty($errors)) {
                $this->setFlashMessage('Formulář obsahuje chyby', 'error');
                echo $this->renderWithLayout('dealers/register', [
                    'title' => 'Registrace obchodníka - Šňůrky na krk',
                    'errors' => $errors,
                    'formData' => $postData,
                    'breadcrumb' => [
                        ['url' => '/', 'title' => 'Domů'],
                        ['url' => '/obchodnici', 'title' => 'Obchodníci'],
                        'Registrace'
                    ]
                ]);
                return;
            }
            
            // Vytvoř nového obchodníka
            $merchantData = [
                'business_name' => $postData['business_name'],
                'contact_person' => $postData['contact_person'],
                'email' => $postData['email'],
                'phone' => $postData['phone'],
                'address' => [
                    'street' => $postData['street'],
                    'city' => $postData['city'],
                    'zip' => $postData['zip'],
                    'region' => $postData['region']
                ],
                'website' => $postData['website'] ?? '',
                'description' => $postData['description'] ?? '',
                'status' => 'pending', // Čeká na schválení
                'commission_rate' => 10.0 // Výchozí provize
            ];
            
            $merchantId = $this->merchantModel->create($merchantData);
            
            $this->setFlashMessage('Registrace byla úspěšně odeslána. Obchodník bude aktivován po schválení administrátorem.', 'success');
            $this->redirect('/obchodnici');
            
        } catch (Exception $e) {
            $this->setFlashMessage('Chyba při registraci: ' . $e->getMessage(), 'error');
            $this->redirect('/obchodnici/registrace');
        }
    }
    
    /**
     * API endpoint pro vyhledávání obchodníků (AJAX)
     */
    public function search()
    {
        try {
            $query = $this->sanitizeInput($_GET['q'] ?? '');
            
            if (strlen($query) < 2) {
                $this->jsonResponse(['merchants' => []]);
                return;
            }
            
            $merchants = $this->merchantModel->search($query, 10);
            
            // Filtr pouze aktivních
            $merchants = array_filter($merchants, function($merchant) {
                return $merchant['status'] === 'active';
            });
            
            // Formátuj obchodníky pro JSON
            $formattedMerchants = array_map(function($merchant) {
                return [
                    'id' => $merchant['id'],
                    'business_name' => $merchant['business_name'],
                    'city' => $merchant['address']['city'] ?? '',
                    'region' => $merchant['address']['region'] ?? '',
                    'url' => '/obchodnici/' . $merchant['id']
                ];
            }, $merchants);
            
            $this->jsonResponse(['merchants' => array_values($formattedMerchants)]);
            
        } catch (Exception $e) {
            $this->jsonResponse(['error' => 'Chyba při vyhledávání'], 500);
        }
    }
}
