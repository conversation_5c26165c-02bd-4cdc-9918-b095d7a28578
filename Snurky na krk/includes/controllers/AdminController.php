<?php

/**
 * Kontroler pro administrační rozhraní
 * Správa přihlášení, dashboard a základní admin funkce
 */
class AdminController extends BaseController
{
    private $settingsModel;
    private $productModel;
    private $merchantModel;
    private $orderModel;
    
    public function __construct()
    {
        parent::__construct();
        $this->settingsModel = new SettingsModel();
        $this->productModel = new ProductModel();
        $this->merchantModel = new MerchantModel();
        $this->orderModel = new OrderModel();
    }
    
    /**
     * Zobrazí přihlašovací formulář
     */
    public function login()
    {
        // Pokud je už <PERSON>, přesměruj na dashboard
        if ($this->sessionManager->isLoggedIn()) {
            $this->redirect('/admin/dashboard');
        }
        
        echo $this->render('admin/login', [
            'title' => 'Přihlášení do administrace'
        ]);
    }
    
    /**
     * Zpracuje přihlášení
     */
    public function processLogin()
    {
        try {
            // Ověř CSRF token
            $postData = $this->getPostData(['csrf_token']);
            if (!$this->validateCSRF($postData['csrf_token'])) {
                throw new Exception('Neplatný CSRF token');
            }
            
            $username = $postData['username'] ?? '';
            $password = $postData['password'] ?? '';
            
            if (empty($username) || empty($password)) {
                throw new Exception('Vyplňte všechna pole');
            }
            
            // Získej admin údaje z nastavení
            $settings = $this->settingsModel->getAll();
            $adminSettings = $settings['admin'] ?? [];
            
            if (empty($adminSettings['username']) || empty($adminSettings['password_hash'])) {
                throw new Exception('Admin účet není nakonfigurován');
            }
            
            // Ověř přihlašovací údaje
            if ($username !== $adminSettings['username'] || 
                !$this->security->verifyPassword($password, $adminSettings['password_hash'])) {
                
                // Loguj neúspěšný pokus
                $this->logLoginAttempt($username, false);
                throw new Exception('Nesprávné přihlašovací údaje');
            }
            
            // Úspěšné přihlášení
            $this->sessionManager->login($username, [
                'role' => 'admin',
                'last_login' => date('Y-m-d H:i:s')
            ]);
            
            // Loguj úspěšné přihlášení
            $this->logLoginAttempt($username, true);
            
            $this->setFlashMessage('Úspěšně přihlášen', 'success');
            $this->redirect('/admin/dashboard');
            
        } catch (Exception $e) {
            $this->setFlashMessage($e->getMessage(), 'error');
            echo $this->render('admin/login', [
                'title' => 'Přihlášení do administrace',
                'error' => $e->getMessage(),
                'username' => $username ?? ''
            ]);
        }
    }
    
    /**
     * Odhlásí uživatele
     */
    public function logout()
    {
        $this->sessionManager->logout();
        $this->setFlashMessage('Úspěšně odhlášen', 'success');
        $this->redirect('/admin');
    }
    
    /**
     * Zobrazí dashboard
     */
    public function dashboard()
    {
        $this->requireAuth();
        
        try {
            // Získej statistiky
            $stats = $this->getDashboardStats();
            
            echo $this->render('admin/dashboard', [
                'title' => 'Dashboard - Administrace',
                'stats' => $stats,
                'user' => [
                    'username' => $this->sessionManager->getUsername(),
                    'last_login' => $this->sessionManager->getUserData('last_login')
                ]
            ]);
            
        } catch (Exception $e) {
            $this->setFlashMessage('Chyba při načítání dashboardu: ' . $e->getMessage(), 'error');
            echo $this->render('admin/error', [
                'title' => 'Chyba',
                'message' => 'Nepodařilo se načíst dashboard'
            ]);
        }
    }
    
    /**
     * Získá statistiky pro dashboard
     */
    private function getDashboardStats()
    {
        $stats = [];
        
        try {
            // Produkty
            $products = $this->productModel->getAll();
            $stats['products'] = [
                'total' => count($products),
                'active' => count(array_filter($products, function($p) { return $p['status'] === 'active'; })),
                'out_of_stock' => count(array_filter($products, function($p) { return $p['stock'] <= 0; }))
            ];
            
            // Obchodníci
            $merchants = $this->merchantModel->getAll();
            $stats['merchants'] = [
                'total' => count($merchants),
                'active' => count(array_filter($merchants, function($m) { return $m['status'] === 'active'; })),
                'pending' => count(array_filter($merchants, function($m) { return $m['status'] === 'pending'; }))
            ];
            
            // Objednávky
            $orders = $this->orderModel->getAll();
            $stats['orders'] = [
                'total' => count($orders),
                'pending' => count(array_filter($orders, function($o) { return $o['status'] === 'pending'; })),
                'this_month' => count(array_filter($orders, function($o) {
                    return date('Y-m', strtotime($o['created_at'])) === date('Y-m');
                }))
            ];
            
            // Tržby
            $totalRevenue = 0;
            $monthRevenue = 0;
            foreach ($orders as $order) {
                if ($order['status'] === 'delivered') {
                    $totalRevenue += $order['total_amount'];
                    if (date('Y-m', strtotime($order['created_at'])) === date('Y-m')) {
                        $monthRevenue += $order['total_amount'];
                    }
                }
            }
            
            $stats['revenue'] = [
                'total' => $totalRevenue,
                'this_month' => $monthRevenue
            ];
            
            // Poslední aktivity
            $stats['recent_orders'] = array_slice(
                array_reverse(array_slice($orders, -5)), 0, 5
            );
            
            $stats['recent_merchants'] = array_slice(
                array_reverse(array_slice($merchants, -5)), 0, 5
            );
            
        } catch (Exception $e) {
            error_log('Chyba při získávání statistik: ' . $e->getMessage());
        }
        
        return $stats;
    }
    
    /**
     * Loguje pokus o přihlášení
     */
    private function logLoginAttempt($username, $success)
    {
        $logFile = ROOT_PATH . '/data/admin_login.log';
        
        $timestamp = date('Y-m-d H:i:s');
        $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
        $status = $success ? 'SUCCESS' : 'FAILED';
        
        $logEntry = sprintf(
            "[%s] %s - Username: %s | IP: %s | User-Agent: %s\n",
            $timestamp,
            $status,
            $username,
            $ip,
            $userAgent
        );
        
        // Zajisti, že adresář existuje
        $logDir = dirname($logFile);
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
    }
    
    /**
     * Zobrazí nastavení administrace
     */
    public function settings()
    {
        $this->requireAuth();
        
        try {
            $settings = $this->settingsModel->getAll();
            
            echo $this->render('admin/settings', [
                'title' => 'Nastavení - Administrace',
                'settings' => $settings
            ]);
            
        } catch (Exception $e) {
            $this->setFlashMessage('Chyba při načítání nastavení: ' . $e->getMessage(), 'error');
            $this->redirect('/admin/dashboard');
        }
    }
    
    /**
     * Uloží nastavení administrace
     */
    public function saveSettings()
    {
        $this->requireAuth();
        
        try {
            // Ověř CSRF token
            $postData = $this->getPostData(['csrf_token']);
            if (!$this->validateCSRF($postData['csrf_token'])) {
                throw new Exception('Neplatný CSRF token');
            }
            
            // Aktualizuj nastavení
            $settings = $this->settingsModel->getAll();
            
            // Admin nastavení
            if (!empty($postData['admin_username'])) {
                $settings['admin']['username'] = $postData['admin_username'];
            }
            
            if (!empty($postData['admin_password'])) {
                $settings['admin']['password_hash'] = $this->security->hashPassword($postData['admin_password']);
            }
            
            // Kontaktní informace
            $settings['contact'] = [
                'email' => $postData['contact_email'] ?? '',
                'phone' => $postData['contact_phone'] ?? '',
                'address' => $postData['contact_address'] ?? ''
            ];
            
            // Email nastavení
            $settings['email'] = [
                'contact_email' => $postData['email_contact'] ?? '',
                'from_email' => $postData['email_from'] ?? ''
            ];
            
            // Uložení
            $this->settingsModel->updateAll($settings);
            
            $this->setFlashMessage('Nastavení bylo úspěšně uloženo', 'success');
            $this->redirect('/admin/nastaveni');
            
        } catch (Exception $e) {
            $this->setFlashMessage('Chyba při ukládání nastavení: ' . $e->getMessage(), 'error');
            $this->redirect('/admin/nastaveni');
        }
    }
}
