<?php

/**
 * HomeController pro SnurkyNaKrk.cz
 * Controller pro hlavní stránku a základn<PERSON> stránky
 */
class HomeController extends BaseController
{
    private $productModel;
    private $settingsModel;

    public function __construct()
    {
        parent::__construct();
        $this->productModel = new ProductModel();
        $this->settingsModel = new SettingsModel();
    }

    /**
     * Zobrazení hlavní stránky
     */
    public function index()
    {
        try {
            // Získej nejnovější produkty pro homepage
            $featuredProducts = $this->productModel->getFeatured(6);

            // Získej nastavení webu
            $settings = $this->settingsModel->getAll();
            $webSettings = $settings['web'] ?? [];

            echo $this->renderWithLayout('home/index', [
                'title' => $webSettings['title'] ?? 'Šňůrky na krk',
                'description' => $webSettings['description'] ?? '',
                'featuredProducts' => $featuredProducts,
                'webSettings' => $webSettings
            ]);

        } catch (Exception $e) {
            // Fallback pro případ chyby
            echo $this->renderWithLayout('home/index', [
                'title' => 'Šňůrky na krk',
                'description' => 'Kvalitní šňůrky na krk pro každou příležitost',
                'featuredProducts' => [],
                'webSettings' => [],
                'error' => DEBUG_MODE ? $e->getMessage() : null
            ]);
        }
    }

    /**
     * Zobrazení stránky "O nás"
     */
    public function about()
    {
        try {
            // Získej nastavení webu
            $settings = $this->settingsModel->getAll();
            $webSettings = $settings['web'] ?? [];

            echo $this->renderWithLayout('home/about', [
                'title' => 'O nás - Šňůrky na krk',
                'description' => 'Seznamte se s naší firmou, historií a hodnotami. Jsme rodinná firma s dlouholetou tradicí ve výrobě kvalitních šňůrek na krk.',
                'webSettings' => $webSettings,
                'breadcrumb' => [
                    ['url' => '/', 'title' => 'Domů'],
                    'O nás'
                ]
            ]);

        } catch (Exception $e) {
            // Fallback pro případ chyby
            echo $this->renderWithLayout('home/about', [
                'title' => 'O nás - Šňůrky na krk',
                'description' => 'Seznamte se s naší firmou',
                'webSettings' => [],
                'error' => DEBUG_MODE ? $e->getMessage() : null,
                'breadcrumb' => [
                    ['url' => '/', 'title' => 'Domů'],
                    'O nás'
                ]
            ]);
        }
    }
}
