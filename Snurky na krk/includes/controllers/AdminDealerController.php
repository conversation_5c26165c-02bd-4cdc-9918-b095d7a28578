<?php
/**
 * Administračn<PERSON> kontroler pro správu obchodn<PERSON>
 * 
 * <AUTHOR> Team
 * @version 1.0
 */

if (!defined('SNURKY_INIT')) {
    die('<PERSON><PERSON>ímý přístup není povolen');
}

class AdminDealerController extends BaseController
{
    protected $sessionManager;
    protected $merchantModel;
    protected $security;
    protected $validationHelper;
    
    public function __construct()
    {
        parent::__construct();
        $this->sessionManager = new SessionManager();
        $this->merchantModel = new MerchantModel();
        $this->security = new Security();
        $this->validationHelper = new ValidationHelper();
        
        // Kontrola přihlášení
        $this->requireAdminAuth();
    }
    
    /**
     * Seznam všech obchodníků
     */
    public function index()
    {
        try {
            $merchants = $this->merchantModel->getAll();
            
            // Filtrování podle statusu
            $selectedStatus = $_GET['status'] ?? '';
            if ($selectedStatus && $selectedStatus !== 'all') {
                $merchants = array_filter($merchants, function($merchant) use ($selectedStatus) {
                    return $merchant['status'] === $selectedStatus;
                });
            }
            
            // Vyhledávání
            $search = $_GET['search'] ?? '';
            if ($search) {
                $merchants = array_filter($merchants, function($merchant) use ($search) {
                    return stripos($merchant['company_name'], $search) !== false ||
                           stripos($merchant['contact_person'], $search) !== false ||
                           stripos($merchant['email'], $search) !== false;
                });
            }
            
            $this->render('admin/dealers/index', [
                'title' => 'Správa obchodníků',
                'merchants' => $merchants,
                'selected_status' => $selectedStatus,
                'search' => $search
            ]);
            
        } catch (Exception $e) {
            $this->handleError($e, 'Chyba při načítání obchodníků');
        }
    }
    
    /**
     * Formulář pro vytvoření nového obchodníka
     */
    public function create()
    {
        try {
            $this->render('admin/dealers/create', [
                'title' => 'Nový obchodník',
                'csrf_token' => $this->security->generateCSRFToken()
            ]);
            
        } catch (Exception $e) {
            $this->handleError($e, 'Chyba při načítání formuláře');
        }
    }
    
    /**
     * Uložení nového obchodníka
     */
    public function store()
    {
        try {
            // Ověření CSRF tokenu
            if (!$this->security->verifyCSRFToken($_POST['csrf_token'] ?? '')) {
                throw new Exception('Neplatný CSRF token');
            }
            
            // Validace dat
            $data = $this->validateMerchantData($_POST);
            
            // Vytvoření obchodníka
            $merchantId = $this->merchantModel->create($data);
            
            $this->sessionManager->setFlashMessage('success', 'Obchodník byl úspěšně vytvořen');
            header('Location: /admin/obchodnici');
            exit;
            
        } catch (Exception $e) {
            $this->render('admin/dealers/create', [
                'title' => 'Nový obchodník',
                'error' => $e->getMessage(),
                'csrf_token' => $this->security->generateCSRFToken(),
                'old_data' => $_POST
            ]);
        }
    }
    
    /**
     * Formulář pro editaci obchodníka
     */
    public function edit($id)
    {
        try {
            $merchant = $this->merchantModel->getById($id);
            if (!$merchant) {
                throw new Exception('Obchodník nebyl nalezen');
            }
            
            $this->render('admin/dealers/edit', [
                'title' => 'Editace obchodníka: ' . $merchant['company_name'],
                'merchant' => $merchant,
                'csrf_token' => $this->security->generateCSRFToken()
            ]);
            
        } catch (Exception $e) {
            $this->handleError($e, 'Chyba při načítání obchodníka');
        }
    }
    
    /**
     * Aktualizace obchodníka
     */
    public function update($id)
    {
        try {
            $merchant = $this->merchantModel->getById($id);
            if (!$merchant) {
                throw new Exception('Obchodník nebyl nalezen');
            }
            
            // Ověření CSRF tokenu
            if (!$this->security->verifyCSRFToken($_POST['csrf_token'] ?? '')) {
                throw new Exception('Neplatný CSRF token');
            }
            
            // Validace dat
            $data = $this->validateMerchantData($_POST);
            
            // Aktualizace obchodníka
            $this->merchantModel->update($id, $data);
            
            $this->sessionManager->setFlashMessage('success', 'Obchodník byl úspěšně aktualizován');
            header('Location: /admin/obchodnici');
            exit;
            
        } catch (Exception $e) {
            $this->render('admin/dealers/edit', [
                'title' => 'Editace obchodníka',
                'merchant' => $merchant ?? null,
                'error' => $e->getMessage(),
                'csrf_token' => $this->security->generateCSRFToken(),
                'old_data' => $_POST
            ]);
        }
    }
    
    /**
     * Smazání obchodníka
     */
    public function delete($id)
    {
        try {
            // Ověření CSRF tokenu
            if (!$this->security->verifyCSRFToken($_POST['csrf_token'] ?? '')) {
                throw new Exception('Neplatný CSRF token');
            }
            
            $merchant = $this->merchantModel->getById($id);
            if (!$merchant) {
                throw new Exception('Obchodník nebyl nalezen');
            }
            
            // Smazání obchodníka
            $this->merchantModel->delete($id);
            
            $this->sessionManager->setFlashMessage('success', 'Obchodník byl úspěšně smazán');
            
        } catch (Exception $e) {
            $this->sessionManager->setFlashMessage('error', $e->getMessage());
        }
        
        header('Location: /admin/obchodnici');
        exit;
    }
    
    /**
     * Validace dat obchodníka
     */
    private function validateMerchantData($data)
    {
        $rules = [
            'company_name' => 'required|min:3|max:100',
            'contact_person' => 'required|min:3|max:100',
            'email' => 'required|email',
            'phone' => 'required|min:9|max:20',
            'address' => 'required|min:10',
            'city' => 'required|min:2|max:50',
            'postal_code' => 'required|min:5|max:10',
            'ico' => 'required|min:8|max:8',
            'commission_rate' => 'required|numeric|min:0|max:100'
        ];
        
        $validatedData = $this->validationHelper->validate($data, $rules);
        
        // Přidání statusu
        $validatedData['status'] = $data['status'] ?? 'pending';
        
        // Validace statusu
        $allowedStatuses = ['pending', 'active', 'inactive', 'rejected'];
        if (!in_array($validatedData['status'], $allowedStatuses)) {
            throw new Exception('Neplatný status obchodníka');
        }
        
        return $validatedData;
    }
    
    /**
     * Kontrola admin autentizace
     */
    private function requireAdminAuth()
    {
        if (!$this->sessionManager->isAdminLoggedIn()) {
            header('Location: /admin/login');
            exit;
        }
    }
    
    /**
     * Zpracování chyb
     */
    private function handleError($exception, $message)
    {
        error_log("Admin Dealer Error: " . $exception->getMessage());
        
        $this->render('admin/error', [
            'title' => 'Chyba',
            'message' => $message,
            'error' => DEBUG_MODE ? $exception->getMessage() : null
        ]);
    }
}
