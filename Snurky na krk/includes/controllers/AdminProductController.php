<?php
/**
 * Administračn<PERSON> kontroler pro správu produktů
 * 
 * <AUTHOR> Team
 * @version 1.0
 */

if (!defined('SNURKY_INIT')) {
    die('Přímý přístup není povolen');
}

class AdminProductController extends BaseController
{
    protected $sessionManager;
    protected $productModel;
    protected $security;
    protected $validationHelper;
    
    public function __construct()
    {
        parent::__construct();
        $this->sessionManager = new SessionManager();
        $this->productModel = new ProductModel();
        $this->security = new Security();
        $this->validationHelper = new ValidationHelper();
        
        // Kontrola přihlášení
        $this->requireAdminAuth();
    }
    
    /**
     * Seznam všech produktů
     */
    public function index()
    {
        try {
            $products = $this->productModel->getAll();
            $categories = $this->productModel->getCategories();
            
            // Filtrování podle kategorie
            $selectedCategory = $_GET['category'] ?? '';
            if ($selectedCategory && $selectedCategory !== 'all') {
                $products = array_filter($products, function($product) use ($selectedCategory) {
                    return $product['category'] === $selectedCategory;
                });
            }
            
            // Vyhledávání
            $search = $_GET['search'] ?? '';
            if ($search) {
                $products = array_filter($products, function($product) use ($search) {
                    return stripos($product['name'], $search) !== false ||
                           stripos($product['description'], $search) !== false;
                });
            }
            
            $this->render('admin/products/index', [
                'title' => 'Správa produktů',
                'products' => $products,
                'categories' => $categories,
                'selected_category' => $selectedCategory,
                'search' => $search
            ]);
            
        } catch (Exception $e) {
            $this->handleError($e, 'Chyba při načítání produktů');
        }
    }
    
    /**
     * Formulář pro vytvoření nového produktu
     */
    public function create()
    {
        try {
            $categories = $this->productModel->getCategories();
            
            $this->render('admin/products/create', [
                'title' => 'Nový produkt',
                'categories' => $categories,
                'csrf_token' => $this->security->generateCSRFToken()
            ]);
            
        } catch (Exception $e) {
            $this->handleError($e, 'Chyba při načítání formuláře');
        }
    }
    
    /**
     * Uložení nového produktu
     */
    public function store()
    {
        try {
            // Ověření CSRF tokenu
            if (!$this->security->verifyCSRFToken($_POST['csrf_token'] ?? '')) {
                throw new Exception('Neplatný CSRF token');
            }
            
            // Validace dat
            $data = $this->validateProductData($_POST);
            
            // Zpracování nahraného obrázku
            if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
                $data['image'] = $this->handleImageUpload($_FILES['image']);
            }
            
            // Vytvoření produktu
            $productId = $this->productModel->create($data);
            
            $this->sessionManager->setFlashMessage('success', 'Produkt byl úspěšně vytvořen');
            header('Location: /admin/produkty');
            exit;
            
        } catch (Exception $e) {
            $categories = $this->productModel->getCategories();
            
            $this->render('admin/products/create', [
                'title' => 'Nový produkt',
                'categories' => $categories,
                'error' => $e->getMessage(),
                'csrf_token' => $this->security->generateCSRFToken(),
                'old_data' => $_POST
            ]);
        }
    }
    
    /**
     * Formulář pro editaci produktu
     */
    public function edit($id)
    {
        try {
            $product = $this->productModel->getById($id);
            if (!$product) {
                throw new Exception('Produkt nebyl nalezen');
            }
            
            $categories = $this->productModel->getCategories();
            
            $this->render('admin/products/edit', [
                'title' => 'Editace produktu: ' . $product['name'],
                'product' => $product,
                'categories' => $categories,
                'csrf_token' => $this->security->generateCSRFToken()
            ]);
            
        } catch (Exception $e) {
            $this->handleError($e, 'Chyba při načítání produktu');
        }
    }
    
    /**
     * Aktualizace produktu
     */
    public function update($id)
    {
        try {
            $product = $this->productModel->getById($id);
            if (!$product) {
                throw new Exception('Produkt nebyl nalezen');
            }
            
            // Ověření CSRF tokenu
            if (!$this->security->verifyCSRFToken($_POST['csrf_token'] ?? '')) {
                throw new Exception('Neplatný CSRF token');
            }
            
            // Validace dat
            $data = $this->validateProductData($_POST);
            
            // Zpracování nahraného obrázku
            if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
                $data['image'] = $this->handleImageUpload($_FILES['image']);
                
                // Smazání starého obrázku
                if ($product['image'] && file_exists(UPLOAD_PATH . '/' . $product['image'])) {
                    unlink(UPLOAD_PATH . '/' . $product['image']);
                }
            }
            
            // Aktualizace produktu
            $this->productModel->update($id, $data);
            
            $this->sessionManager->setFlashMessage('success', 'Produkt byl úspěšně aktualizován');
            header('Location: /admin/produkty');
            exit;
            
        } catch (Exception $e) {
            $categories = $this->productModel->getCategories();
            
            $this->render('admin/products/edit', [
                'title' => 'Editace produktu',
                'product' => $product ?? null,
                'categories' => $categories,
                'error' => $e->getMessage(),
                'csrf_token' => $this->security->generateCSRFToken(),
                'old_data' => $_POST
            ]);
        }
    }
    
    /**
     * Smazání produktu
     */
    public function delete($id)
    {
        try {
            // Ověření CSRF tokenu
            if (!$this->security->verifyCSRFToken($_POST['csrf_token'] ?? '')) {
                throw new Exception('Neplatný CSRF token');
            }
            
            $product = $this->productModel->getById($id);
            if (!$product) {
                throw new Exception('Produkt nebyl nalezen');
            }
            
            // Smazání obrázku
            if ($product['image'] && file_exists(UPLOAD_PATH . '/' . $product['image'])) {
                unlink(UPLOAD_PATH . '/' . $product['image']);
            }
            
            // Smazání produktu
            $this->productModel->delete($id);
            
            $this->sessionManager->setFlashMessage('success', 'Produkt byl úspěšně smazán');
            
        } catch (Exception $e) {
            $this->sessionManager->setFlashMessage('error', $e->getMessage());
        }
        
        header('Location: /admin/produkty');
        exit;
    }
    
    /**
     * Validace dat produktu
     */
    private function validateProductData($data)
    {
        $rules = [
            'name' => 'required|min:3|max:100',
            'description' => 'required|min:10',
            'category' => 'required',
            'price' => 'required|numeric|min:0',
            'stock' => 'required|integer|min:0'
        ];
        
        $validatedData = $this->validationHelper->validate($data, $rules);
        
        // Přidání dalších polí
        $validatedData['active'] = isset($data['active']) ? 1 : 0;
        $validatedData['featured'] = isset($data['featured']) ? 1 : 0;
        
        return $validatedData;
    }
    
    /**
     * Zpracování nahraného obrázku
     */
    private function handleImageUpload($file)
    {
        $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        $maxSize = 5 * 1024 * 1024; // 5MB
        
        if (!in_array($file['type'], $allowedTypes)) {
            throw new Exception('Nepovolený typ souboru. Povolené jsou: JPG, PNG, GIF, WebP');
        }
        
        if ($file['size'] > $maxSize) {
            throw new Exception('Soubor je příliš velký. Maximum je 5MB');
        }
        
        $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
        $filename = 'product_' . uniqid() . '.' . $extension;
        $uploadPath = UPLOAD_PATH . '/' . $filename;
        
        if (!move_uploaded_file($file['tmp_name'], $uploadPath)) {
            throw new Exception('Chyba při nahrávání souboru');
        }
        
        return $filename;
    }
    
    /**
     * Kontrola admin autentizace
     */
    private function requireAdminAuth()
    {
        if (!$this->sessionManager->isAdminLoggedIn()) {
            header('Location: /admin/login');
            exit;
        }
    }
    
    /**
     * Zpracování chyb
     */
    private function handleError($exception, $message)
    {
        error_log("Admin Product Error: " . $exception->getMessage());
        
        $this->render('admin/error', [
            'title' => 'Chyba',
            'message' => $message,
            'error' => DEBUG_MODE ? $exception->getMessage() : null
        ]);
    }
}
