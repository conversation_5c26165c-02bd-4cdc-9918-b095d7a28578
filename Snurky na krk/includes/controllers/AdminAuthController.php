<?php
/**
 * Administrační kontroler pro autentizaci
 * 
 * <AUTHOR> Team
 * @version 1.0
 */

if (!defined('SNURKY_INIT')) {
    die('Přím<PERSON> přístup není povolen');
}

class AdminAuthController extends BaseController
{
    protected $sessionManager;
    protected $security;
    
    public function __construct()
    {
        parent::__construct();
        $this->sessionManager = new SessionManager();
        $this->security = new Security();
    }
    
    /**
     * Zobrazí přihlašovací formulář
     */
    public function loginForm()
    {
        // Pokud je už <PERSON>, přesměruj na dashboard
        if ($this->sessionManager->isAdminLoggedIn()) {
            header('Location: /admin/dashboard');
            exit;
        }
        
        $this->render('admin/login', [
            'title' => 'Přihlášení do administrace',
            'csrf_token' => $this->security->generateCSRFToken()
        ]);
    }
    
    /**
     * Zpracuje přihlášení
     */
    public function login()
    {
        try {
            // Ověření CSRF tokenu
            if (!$this->security->verifyCSRFToken($_POST['csrf_token'] ?? '')) {
                throw new Exception('Neplatný CSRF token');
            }
            
            $username = trim($_POST['username'] ?? '');
            $password = $_POST['password'] ?? '';
            
            // Validace vstupů
            if (empty($username) || empty($password)) {
                throw new Exception('Vyplňte všechna pole');
            }
            
            // Ověření přihlašovacích údajů
            if ($this->verifyCredentials($username, $password)) {
                // Úspěšné přihlášení
                $this->sessionManager->loginAdmin($username);
                
                // Přesměrování na dashboard
                header('Location: /admin/dashboard');
                exit;
            } else {
                throw new Exception('Neplatné přihlašovací údaje');
            }
            
        } catch (Exception $e) {
            // Zobrazení chyby
            $this->render('admin/login', [
                'title' => 'Přihlášení do administrace',
                'error' => $e->getMessage(),
                'csrf_token' => $this->security->generateCSRFToken(),
                'username' => $username ?? ''
            ]);
        }
    }
    
    /**
     * Odhlášení administrátora
     */
    public function logout()
    {
        $this->sessionManager->logoutAdmin();
        header('Location: /admin/login');
        exit;
    }
    
    /**
     * Ověří přihlašovací údaje
     */
    private function verifyCredentials($username, $password)
    {
        // Načtení nastavení s admin údaji
        $settingsModel = new SettingsModel();
        $settings = $settingsModel->getSettings();
        
        $adminUsers = $settings['admin']['users'] ?? [];
        
        foreach ($adminUsers as $user) {
            if ($user['username'] === $username && 
                $this->security->verifyPassword($password, $user['password'])) {
                return true;
            }
        }
        
        return false;
    }
}
