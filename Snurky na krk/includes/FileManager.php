<?php
/**
 * <PERSON><PERSON><PERSON><PERSON><PERSON> souborů a uploadů
 * 
 * <AUTHOR> Team
 * @version 1.0
 */

if (!defined('SNURKY_INIT')) {
    die('<PERSON><PERSON>ímý přístup není povolen');
}

class FileManager
{
    private $uploadDir;
    private $allowedImageTypes;
    private $allowedDocTypes;
    private $maxFileSize;
    private $security;
    
    public function __construct()
    {
        $this->uploadDir = UPLOAD_DIR;
        $this->allowedImageTypes = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'];
        $this->allowedDocTypes = ['pdf', 'doc', 'docx', 'txt'];
        $this->maxFileSize = 5 * 1024 * 1024; // 5MB
        $this->security = new Security();
        
        // Vytvoř upload adresář pokud neexistuje
        $this->ensureUploadDir();
    }
    
    /**
     * Nahraje soubor
     */
    public function uploadFile($file, $subdir = '', $options = [])
    {
        try {
            // Validace souboru
            $this->validateFile($file, $options);
            
            // Určení cílového adresáře
            $targetDir = $this->uploadDir;
            if ($subdir) {
                $targetDir .= '/' . trim($subdir, '/');
                $this->ensureDirectory($targetDir);
            }
            
            // Generování unikátního názvu
            $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
            $filename = $this->generateUniqueFilename($extension);
            $targetPath = $targetDir . '/' . $filename;
            
            // Upload souboru
            if (!move_uploaded_file($file['tmp_name'], $targetPath)) {
                throw new Exception('Nepodařilo se nahrát soubor');
            }
            
            // Optimalizace obrázku pokud je to obrázek
            if ($this->isImage($extension)) {
                $this->optimizeImage($targetPath, $options);
            }
            
            // Vrácení relativní cesty
            $relativePath = str_replace(ROOT_DIR, '', $targetPath);
            return $relativePath;
            
        } catch (Exception $e) {
            throw new Exception('Chyba při uploadu souboru: ' . $e->getMessage());
        }
    }
    
    /**
     * Validuje nahrávaný soubor
     */
    private function validateFile($file, $options = [])
    {
        // Kontrola chyb uploadu
        if ($file['error'] !== UPLOAD_ERR_OK) {
            $errors = [
                UPLOAD_ERR_INI_SIZE => 'Soubor je příliš velký (server limit)',
                UPLOAD_ERR_FORM_SIZE => 'Soubor je příliš velký (form limit)',
                UPLOAD_ERR_PARTIAL => 'Soubor byl nahrán pouze částečně',
                UPLOAD_ERR_NO_FILE => 'Nebyl vybrán žádný soubor',
                UPLOAD_ERR_NO_TMP_DIR => 'Chybí dočasný adresář',
                UPLOAD_ERR_CANT_WRITE => 'Nepodařilo se zapsat soubor',
                UPLOAD_ERR_EXTENSION => 'Upload byl zastaven rozšířením'
            ];
            
            throw new Exception($errors[$file['error']] ?? 'Neznámá chyba uploadu');
        }
        
        // Kontrola velikosti
        $maxSize = $options['max_size'] ?? $this->maxFileSize;
        if ($file['size'] > $maxSize) {
            throw new Exception('Soubor je příliš velký. Maximum: ' . $this->formatFileSize($maxSize));
        }
        
        // Kontrola typu souboru
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        $allowedTypes = $options['allowed_types'] ?? array_merge($this->allowedImageTypes, $this->allowedDocTypes);
        
        if (!in_array($extension, $allowedTypes)) {
            throw new Exception('Nepodporovaný typ souboru. Povolené: ' . implode(', ', $allowedTypes));
        }
        
        // Kontrola MIME typu
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);
        
        $allowedMimes = [
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'png' => 'image/png',
            'gif' => 'image/gif',
            'webp' => 'image/webp',
            'svg' => 'image/svg+xml',
            'pdf' => 'application/pdf',
            'doc' => 'application/msword',
            'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'txt' => 'text/plain'
        ];
        
        if (isset($allowedMimes[$extension]) && $mimeType !== $allowedMimes[$extension]) {
            throw new Exception('Neplatný MIME typ souboru');
        }
        
        // Bezpečnostní kontrola obsahu
        $this->scanFileContent($file['tmp_name']);
    }
    
    /**
     * Optimalizuje obrázek
     */
    private function optimizeImage($imagePath, $options = [])
    {
        $extension = strtolower(pathinfo($imagePath, PATHINFO_EXTENSION));
        
        if (!$this->isImage($extension) || $extension === 'svg') {
            return; // SVG neoptimalizujeme
        }
        
        $maxWidth = $options['max_width'] ?? 1200;
        $maxHeight = $options['max_height'] ?? 1200;
        $quality = $options['quality'] ?? 85;
        
        try {
            // Načtení obrázku
            switch ($extension) {
                case 'jpg':
                case 'jpeg':
                    $image = imagecreatefromjpeg($imagePath);
                    break;
                case 'png':
                    $image = imagecreatefrompng($imagePath);
                    break;
                case 'gif':
                    $image = imagecreatefromgif($imagePath);
                    break;
                case 'webp':
                    $image = imagecreatefromwebp($imagePath);
                    break;
                default:
                    return;
            }
            
            if (!$image) {
                return;
            }
            
            // Získání rozměrů
            $originalWidth = imagesx($image);
            $originalHeight = imagesy($image);
            
            // Výpočet nových rozměrů
            $ratio = min($maxWidth / $originalWidth, $maxHeight / $originalHeight);
            
            if ($ratio < 1) {
                $newWidth = round($originalWidth * $ratio);
                $newHeight = round($originalHeight * $ratio);
                
                // Vytvoření nového obrázku
                $newImage = imagecreatetruecolor($newWidth, $newHeight);
                
                // Zachování průhlednosti pro PNG a GIF
                if ($extension === 'png' || $extension === 'gif') {
                    imagealphablending($newImage, false);
                    imagesavealpha($newImage, true);
                    $transparent = imagecolorallocatealpha($newImage, 255, 255, 255, 127);
                    imagefill($newImage, 0, 0, $transparent);
                }
                
                // Změna velikosti
                imagecopyresampled($newImage, $image, 0, 0, 0, 0, $newWidth, $newHeight, $originalWidth, $originalHeight);
                
                // Uložení optimalizovaného obrázku
                switch ($extension) {
                    case 'jpg':
                    case 'jpeg':
                        imagejpeg($newImage, $imagePath, $quality);
                        break;
                    case 'png':
                        imagepng($newImage, $imagePath, round(9 * (100 - $quality) / 100));
                        break;
                    case 'gif':
                        imagegif($newImage, $imagePath);
                        break;
                    case 'webp':
                        imagewebp($newImage, $imagePath, $quality);
                        break;
                }
                
                imagedestroy($newImage);
            }
            
            imagedestroy($image);
            
        } catch (Exception $e) {
            // Pokud optimalizace selže, ponecháme původní soubor
            error_log('Image optimization failed: ' . $e->getMessage());
        }
    }
    
    /**
     * Vytvoří thumbnail obrázku
     */
    public function createThumbnail($imagePath, $width = 150, $height = 150)
    {
        $extension = strtolower(pathinfo($imagePath, PATHINFO_EXTENSION));
        
        if (!$this->isImage($extension) || $extension === 'svg') {
            return $imagePath; // SVG vrátíme původní
        }
        
        $thumbnailPath = $this->getThumbnailPath($imagePath, $width, $height);
        
        // Pokud thumbnail už existuje, vrátíme ho
        if (file_exists($thumbnailPath)) {
            return str_replace(ROOT_DIR, '', $thumbnailPath);
        }
        
        try {
            // Načtení obrázku
            switch ($extension) {
                case 'jpg':
                case 'jpeg':
                    $image = imagecreatefromjpeg($imagePath);
                    break;
                case 'png':
                    $image = imagecreatefrompng($imagePath);
                    break;
                case 'gif':
                    $image = imagecreatefromgif($imagePath);
                    break;
                case 'webp':
                    $image = imagecreatefromwebp($imagePath);
                    break;
                default:
                    return $imagePath;
            }
            
            if (!$image) {
                return $imagePath;
            }
            
            // Získání rozměrů
            $originalWidth = imagesx($image);
            $originalHeight = imagesy($image);
            
            // Výpočet rozměrů pro crop
            $ratio = max($width / $originalWidth, $height / $originalHeight);
            $newWidth = round($originalWidth * $ratio);
            $newHeight = round($originalHeight * $ratio);
            
            // Vytvoření thumbnail
            $thumbnail = imagecreatetruecolor($width, $height);
            
            // Zachování průhlednosti
            if ($extension === 'png' || $extension === 'gif') {
                imagealphablending($thumbnail, false);
                imagesavealpha($thumbnail, true);
                $transparent = imagecolorallocatealpha($thumbnail, 255, 255, 255, 127);
                imagefill($thumbnail, 0, 0, $transparent);
            }
            
            // Crop a resize
            $srcX = ($newWidth - $width) / 2;
            $srcY = ($newHeight - $height) / 2;
            
            imagecopyresampled(
                $thumbnail, $image,
                0, 0, $srcX / $ratio, $srcY / $ratio,
                $width, $height, $originalWidth, $originalHeight
            );
            
            // Vytvoření adresáře pro thumbnail
            $this->ensureDirectory(dirname($thumbnailPath));
            
            // Uložení thumbnail
            switch ($extension) {
                case 'jpg':
                case 'jpeg':
                    imagejpeg($thumbnail, $thumbnailPath, 85);
                    break;
                case 'png':
                    imagepng($thumbnail, $thumbnailPath, 6);
                    break;
                case 'gif':
                    imagegif($thumbnail, $thumbnailPath);
                    break;
                case 'webp':
                    imagewebp($thumbnail, $thumbnailPath, 85);
                    break;
            }
            
            imagedestroy($image);
            imagedestroy($thumbnail);
            
            return str_replace(ROOT_DIR, '', $thumbnailPath);
            
        } catch (Exception $e) {
            error_log('Thumbnail creation failed: ' . $e->getMessage());
            return $imagePath;
        }
    }
    
    /**
     * Smaže soubor
     */
    public function deleteFile($filePath)
    {
        $fullPath = ROOT_DIR . '/' . ltrim($filePath, '/');
        
        if (file_exists($fullPath)) {
            if (unlink($fullPath)) {
                // Smaž i thumbnail pokud existuje
                $this->deleteThumbnails($fullPath);
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Získá informace o souboru
     */
    public function getFileInfo($filePath)
    {
        $fullPath = ROOT_DIR . '/' . ltrim($filePath, '/');
        
        if (!file_exists($fullPath)) {
            return null;
        }
        
        $info = [
            'name' => basename($fullPath),
            'size' => filesize($fullPath),
            'size_formatted' => $this->formatFileSize(filesize($fullPath)),
            'type' => strtolower(pathinfo($fullPath, PATHINFO_EXTENSION)),
            'mime_type' => mime_content_type($fullPath),
            'created' => filectime($fullPath),
            'modified' => filemtime($fullPath),
            'is_image' => $this->isImage(strtolower(pathinfo($fullPath, PATHINFO_EXTENSION)))
        ];
        
        if ($info['is_image']) {
            $imageInfo = getimagesize($fullPath);
            if ($imageInfo) {
                $info['width'] = $imageInfo[0];
                $info['height'] = $imageInfo[1];
                $info['dimensions'] = $imageInfo[0] . 'x' . $imageInfo[1];
            }
        }
        
        return $info;
    }
    
    /**
     * Pomocné metody
     */
    
    private function ensureUploadDir()
    {
        $this->ensureDirectory($this->uploadDir);
        $this->ensureDirectory($this->uploadDir . '/products');
        $this->ensureDirectory($this->uploadDir . '/thumbnails');
        $this->ensureDirectory($this->uploadDir . '/documents');
    }
    
    private function ensureDirectory($dir)
    {
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
        }
    }
    
    private function generateUniqueFilename($extension)
    {
        return uniqid() . '_' . time() . '.' . $extension;
    }
    
    private function isImage($extension)
    {
        return in_array($extension, $this->allowedImageTypes);
    }
    
    private function getThumbnailPath($imagePath, $width, $height)
    {
        $pathInfo = pathinfo($imagePath);
        $thumbnailDir = $this->uploadDir . '/thumbnails';
        return $thumbnailDir . '/' . $pathInfo['filename'] . '_' . $width . 'x' . $height . '.' . $pathInfo['extension'];
    }
    
    private function deleteThumbnails($imagePath)
    {
        $pathInfo = pathinfo($imagePath);
        $thumbnailDir = $this->uploadDir . '/thumbnails';
        $pattern = $thumbnailDir . '/' . $pathInfo['filename'] . '_*.' . $pathInfo['extension'];
        
        foreach (glob($pattern) as $thumbnail) {
            unlink($thumbnail);
        }
    }
    
    private function formatFileSize($bytes)
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }
    
    private function scanFileContent($filePath)
    {
        // Základní kontrola na škodlivý obsah
        $content = file_get_contents($filePath, false, null, 0, 1024); // Prvních 1KB
        
        $dangerousPatterns = [
            '/<\?php/i',
            '/<script/i',
            '/javascript:/i',
            '/vbscript:/i',
            '/onload=/i',
            '/onerror=/i'
        ];
        
        foreach ($dangerousPatterns as $pattern) {
            if (preg_match($pattern, $content)) {
                throw new Exception('Soubor obsahuje potenciálně nebezpečný obsah');
            }
        }
    }
}
