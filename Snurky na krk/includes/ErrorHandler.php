<?php

/**
 * Zpracování chyb a logování pro aplikaci
 * Uživatelsky přívětivé chybové stránky a debug režim
 */
class ErrorHandler
{
    private $logFile;
    private $debugMode;
    private $templateEngine;
    
    public function __construct($debugMode = false)
    {
        $this->debugMode = $debugMode;
        $this->logFile = ROOT_PATH . '/data/error.log';
        $this->templateEngine = new TemplateEngine();
        
        // Nastav error handlery
        set_error_handler([$this, 'handleError']);
        set_exception_handler([$this, 'handleException']);
        register_shutdown_function([$this, 'handleFatalError']);
    }
    
    /**
     * Zpracuje PHP chyby
     */
    public function handleError($severity, $message, $file, $line)
    {
        // Ignoruj chyby potlačené @
        if (!(error_reporting() & $severity)) {
            return false;
        }
        
        $errorType = $this->getErrorType($severity);
        $errorMessage = "[$errorType] $message in $file on line $line";
        
        $this->logError($errorMessage);
        
        if ($this->debugMode) {
            $this->displayDebugError($errorType, $message, $file, $line);
        } else {
            $this->displayUserFriendlyError();
        }
        
        return true;
    }
    
    /**
     * Zpracuje výjimky
     */
    public function handleException($exception)
    {
        $errorMessage = sprintf(
            "[EXCEPTION] %s: %s in %s on line %d\nStack trace:\n%s",
            get_class($exception),
            $exception->getMessage(),
            $exception->getFile(),
            $exception->getLine(),
            $exception->getTraceAsString()
        );
        
        $this->logError($errorMessage);
        
        if ($this->debugMode) {
            $this->displayDebugException($exception);
        } else {
            $this->displayUserFriendlyError();
        }
    }
    
    /**
     * Zpracuje fatální chyby
     */
    public function handleFatalError()
    {
        $error = error_get_last();
        
        if ($error !== null && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR])) {
            $errorType = $this->getErrorType($error['type']);
            $errorMessage = "[$errorType] {$error['message']} in {$error['file']} on line {$error['line']}";
            
            $this->logError($errorMessage);
            
            if ($this->debugMode) {
                $this->displayDebugError($errorType, $error['message'], $error['file'], $error['line']);
            } else {
                $this->displayUserFriendlyError();
            }
        }
    }
    
    /**
     * Zaloguje chybu do souboru
     */
    public function logError($message)
    {
        $timestamp = date('Y-m-d H:i:s');
        $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
        $requestUri = $_SERVER['REQUEST_URI'] ?? 'unknown';
        
        $logEntry = sprintf(
            "[%s] IP: %s | URI: %s | User-Agent: %s\n%s\n%s\n\n",
            $timestamp,
            $ip,
            $requestUri,
            $userAgent,
            $message,
            str_repeat('-', 80)
        );
        
        // Zajisti, že adresář existuje
        $logDir = dirname($this->logFile);
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        file_put_contents($this->logFile, $logEntry, FILE_APPEND | LOCK_EX);
    }
    
    /**
     * Zobrazí debug chybu (pro vývojáře)
     */
    private function displayDebugError($type, $message, $file, $line)
    {
        http_response_code(500);
        
        echo "<!DOCTYPE html>\n";
        echo "<html><head><title>Chyba aplikace</title>";
        echo "<style>body{font-family:Arial;margin:20px;} .error{background:#ffebee;border:1px solid #f44336;padding:15px;border-radius:4px;} .code{background:#f5f5f5;padding:10px;margin:10px 0;border-radius:4px;font-family:monospace;}</style>";
        echo "</head><body>";
        echo "<div class='error'>";
        echo "<h2>🚨 Chyba aplikace ($type)</h2>";
        echo "<p><strong>Zpráva:</strong> " . htmlspecialchars($message) . "</p>";
        echo "<p><strong>Soubor:</strong> " . htmlspecialchars($file) . "</p>";
        echo "<p><strong>Řádek:</strong> $line</p>";
        
        // Zobraz kód kolem chyby
        if (file_exists($file)) {
            $lines = file($file);
            $start = max(0, $line - 6);
            $end = min(count($lines), $line + 5);
            
            echo "<h3>Kód:</h3><div class='code'>";
            for ($i = $start; $i < $end; $i++) {
                $lineNum = $i + 1;
                $highlight = ($lineNum == $line) ? 'style="background:#ffcdd2;"' : '';
                echo "<div $highlight><strong>$lineNum:</strong> " . htmlspecialchars($lines[$i]) . "</div>";
            }
            echo "</div>";
        }
        
        echo "</div></body></html>";
        exit;
    }
    
    /**
     * Zobrazí debug výjimku
     */
    private function displayDebugException($exception)
    {
        http_response_code(500);
        
        echo "<!DOCTYPE html>\n";
        echo "<html><head><title>Výjimka aplikace</title>";
        echo "<style>body{font-family:Arial;margin:20px;} .error{background:#ffebee;border:1px solid #f44336;padding:15px;border-radius:4px;} .trace{background:#f5f5f5;padding:10px;margin:10px 0;border-radius:4px;font-family:monospace;white-space:pre-wrap;}</style>";
        echo "</head><body>";
        echo "<div class='error'>";
        echo "<h2>🚨 Výjimka aplikace (" . get_class($exception) . ")</h2>";
        echo "<p><strong>Zpráva:</strong> " . htmlspecialchars($exception->getMessage()) . "</p>";
        echo "<p><strong>Soubor:</strong> " . htmlspecialchars($exception->getFile()) . "</p>";
        echo "<p><strong>Řádek:</strong> " . $exception->getLine() . "</p>";
        echo "<h3>Stack trace:</h3>";
        echo "<div class='trace'>" . htmlspecialchars($exception->getTraceAsString()) . "</div>";
        echo "</div></body></html>";
        exit;
    }
    
    /**
     * Zobrazí uživatelsky přívětivou chybu
     */
    private function displayUserFriendlyError()
    {
        http_response_code(500);
        
        try {
            echo $this->templateEngine->renderWithLayout('error/500', [
                'title' => 'Chyba serveru',
                'message' => 'Omlouváme se, došlo k neočekávané chybě. Zkuste to prosím později.'
            ]);
        } catch (Exception $e) {
            // Fallback pokud selže i template
            echo "<!DOCTYPE html>\n";
            echo "<html><head><title>Chyba serveru</title></head><body>";
            echo "<h1>Chyba serveru</h1>";
            echo "<p>Omlouváme se, došlo k neočekávané chybě. Zkuste to prosím později.</p>";
            echo "</body></html>";
        }
        
        exit;
    }
    
    /**
     * Získá název typu chyby
     */
    private function getErrorType($type)
    {
        $types = [
            E_ERROR => 'FATAL ERROR',
            E_WARNING => 'WARNING',
            E_PARSE => 'PARSE ERROR',
            E_NOTICE => 'NOTICE',
            E_CORE_ERROR => 'CORE ERROR',
            E_CORE_WARNING => 'CORE WARNING',
            E_COMPILE_ERROR => 'COMPILE ERROR',
            E_COMPILE_WARNING => 'COMPILE WARNING',
            E_USER_ERROR => 'USER ERROR',
            E_USER_WARNING => 'USER WARNING',
            E_USER_NOTICE => 'USER NOTICE',
            E_STRICT => 'STRICT',
            E_RECOVERABLE_ERROR => 'RECOVERABLE ERROR',
            E_DEPRECATED => 'DEPRECATED',
            E_USER_DEPRECATED => 'USER DEPRECATED'
        ];
        
        return isset($types[$type]) ? $types[$type] : 'UNKNOWN ERROR';
    }
    
    /**
     * Nastaví debug režim
     */
    public function setDebugMode($debug)
    {
        $this->debugMode = $debug;
    }
    
    /**
     * Získá debug režim
     */
    public function isDebugMode()
    {
        return $this->debugMode;
    }
}
