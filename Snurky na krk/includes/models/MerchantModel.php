<?php

require_once 'BaseModel.php';

/**
 * Model pro pr<PERSON>ci s obchodníky
 */
class MerchantModel extends BaseModel {
    
    public function __construct() {
        parent::__construct('merchants.json');
    }
    
    /**
     * Získá všechny obchodníky (alias pro getAllMerchants)
     */
    public function getAll($activeOnly = true) {
        return $this->getAllMerchants($activeOnly);
    }

    /**
     * Získá všechny obchodníky
     */
    public function getAllMerchants($activeOnly = true) {
        $merchants = $this->data['merchants'] ?? [];
        
        if ($activeOnly) {
            $merchants = array_filter($merchants, function($merchant) {
                return isset($merchant['active']) && $merchant['active'] === true;
            });
        }
        
        return array_values($merchants);
    }
    
    /**
     * Získá obchodníka podle ID
     */
    public function getMerchantById($id) {
        $result = $this->findById('merchants', $id);
        return $result ? $result['item'] : null;
    }
    
    /**
     * Vyhledá obchodníky podle názvu nebo kontaktní osoby
     */
    public function searchMerchants($query, $activeOnly = true) {
        $merchants = $this->getAllMerchants($activeOnly);
        $query = strtolower($query);
        
        return array_filter($merchants, function($merchant) use ($query) {
            $name = strtolower($merchant['name'] ?? '');
            $contactPerson = strtolower($merchant['contact_person'] ?? '');
            $email = strtolower($merchant['email'] ?? '');
            
            return strpos($name, $query) !== false || 
                   strpos($contactPerson, $query) !== false ||
                   strpos($email, $query) !== false;
        });
    }
    
    /**
     * Přidá nového obchodníka
     */
    public function addMerchant($merchantData) {
        $requiredFields = ['name', 'contact_person', 'email', 'phone'];
        $errors = $this->validateRequired($merchantData, $requiredFields);
        
        // Validace emailu
        if (isset($merchantData['email']) && !filter_var($merchantData['email'], FILTER_VALIDATE_EMAIL)) {
            $errors[] = 'Neplatný formát emailu';
        }
        
        if (!empty($errors)) {
            throw new Exception('Chyby validace: ' . implode(', ', $errors));
        }
        
        // Kontrola duplicitního emailu
        if ($this->getMerchantByEmail($merchantData['email'])) {
            throw new Exception('Obchodník s tímto emailem již existuje');
        }
        
        // Sanitizace dat
        $merchantData = $this->sanitizeData($merchantData);
        
        // Přidání ID a časových značek
        $merchantData['id'] = $this->generateId('merchants');
        $merchantData = $this->addTimestamps($merchantData);
        
        // Výchozí hodnoty
        if (!isset($merchantData['active'])) {
            $merchantData['active'] = true;
        }
        if (!isset($merchantData['commission_rate'])) {
            $merchantData['commission_rate'] = $this->data['commission_rates']['default'] ?? 15;
        }
        if (!isset($merchantData['payment_terms'])) {
            $merchantData['payment_terms'] = '30 dní';
        }
        if (!isset($merchantData['minimum_order'])) {
            $merchantData['minimum_order'] = 500;
        }
        
        // Přidání do kolekce
        if (!isset($this->data['merchants'])) {
            $this->data['merchants'] = [];
        }
        
        $this->data['merchants'][] = $merchantData;
        $this->saveData();
        
        return $merchantData['id'];
    }
    
    /**
     * Aktualizuje obchodníka
     */
    public function updateMerchant($id, $merchantData) {
        $result = $this->findById('merchants', $id);
        
        if (!$result) {
            throw new Exception('Obchodník s ID ' . $id . ' nebyl nalezen');
        }
        
        // Validace emailu
        if (isset($merchantData['email']) && !filter_var($merchantData['email'], FILTER_VALIDATE_EMAIL)) {
            throw new Exception('Neplatný formát emailu');
        }
        
        // Kontrola duplicitního emailu (kromě aktuálního záznamu)
        if (isset($merchantData['email'])) {
            $existingMerchant = $this->getMerchantByEmail($merchantData['email']);
            if ($existingMerchant && $existingMerchant['id'] != $id) {
                throw new Exception('Obchodník s tímto emailem již existuje');
            }
        }
        
        // Sanitizace dat
        $merchantData = $this->sanitizeData($merchantData);
        
        // Zachování původního ID a created_at
        $merchantData['id'] = $id;
        if (isset($result['item']['created_at'])) {
            $merchantData['created_at'] = $result['item']['created_at'];
        }
        
        // Přidání updated_at
        $merchantData = $this->addTimestamps($merchantData, true);
        
        // Aktualizace
        $this->data['merchants'][$result['index']] = $merchantData;
        $this->saveData();
        
        return true;
    }
    
    /**
     * Smaže obchodníka
     */
    public function deleteMerchant($id) {
        $result = $this->findById('merchants', $id);
        
        if (!$result) {
            throw new Exception('Obchodník s ID ' . $id . ' nebyl nalezen');
        }
        
        // Místo smazání můžeme jen deaktivovat
        $this->data['merchants'][$result['index']]['active'] = false;
        $this->data['merchants'][$result['index']] = $this->addTimestamps($this->data['merchants'][$result['index']], true);
        
        $this->saveData();
        
        return true;
    }
    
    /**
     * Najde obchodníka podle emailu
     */
    public function getMerchantByEmail($email) {
        $merchants = $this->data['merchants'] ?? [];
        
        foreach ($merchants as $merchant) {
            if (isset($merchant['email']) && $merchant['email'] === $email) {
                return $merchant;
            }
        }
        
        return null;
    }
    
    /**
     * Získá možnosti platebních podmínek
     */
    public function getPaymentTermsOptions() {
        return $this->data['payment_terms_options'] ?? [];
    }
    
    /**
     * Získá sazby provizí
     */
    public function getCommissionRates() {
        return $this->data['commission_rates'] ?? [];
    }
    
    /**
     * Aktualizuje provizní sazbu obchodníka
     */
    public function updateCommissionRate($id, $rate) {
        $result = $this->findById('merchants', $id);
        
        if (!$result) {
            throw new Exception('Obchodník s ID ' . $id . ' nebyl nalezen');
        }
        
        if (!is_numeric($rate) || $rate < 0 || $rate > 100) {
            throw new Exception('Provizní sazba musí být číslo mezi 0 a 100');
        }
        
        $this->data['merchants'][$result['index']]['commission_rate'] = floatval($rate);
        $this->data['merchants'][$result['index']] = $this->addTimestamps($this->data['merchants'][$result['index']], true);
        
        $this->saveData();
        
        return true;
    }
}
