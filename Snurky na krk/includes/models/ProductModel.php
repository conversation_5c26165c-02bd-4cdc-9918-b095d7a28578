<?php

require_once 'BaseModel.php';

/**
 * Model pro práci s produkty (šňůrkami)
 */
class ProductModel extends BaseModel {
    
    public function __construct() {
        parent::__construct('products.json');
    }
    
    /**
     * Získá všechny produkty (alias pro getAllProducts)
     */
    public function getAll($activeOnly = true) {
        return $this->getAllProducts($activeOnly);
    }

    /**
     * Získá všechny produkty
     */
    public function getAllProducts($activeOnly = true) {
        $products = $this->data['products'] ?? [];
        
        if ($activeOnly) {
            $products = array_filter($products, function($product) {
                return isset($product['active']) && $product['active'] === true;
            });
        }
        
        return array_values($products);
    }
    
    /**
     * Získá produkt podle ID
     */
    public function getProductById($id) {
        $result = $this->findById('products', $id);
        return $result ? $result['item'] : null;
    }
    
    /**
     * Získá produkty podle kategorie
     */
    public function getProductsByCategory($categoryId, $activeOnly = true) {
        $products = $this->getAllProducts($activeOnly);
        
        return array_filter($products, function($product) use ($categoryId) {
            return isset($product['category']) && $product['category'] === $categoryId;
        });
    }
    
    /**
     * Získá doporučené produkty
     */
    public function getFeatured($limit = null) {
        return $this->getFeaturedProducts($limit);
    }

    /**
     * Získá doporučené produkty
     */
    public function getFeaturedProducts($limit = null) {
        $products = $this->getAllProducts(true);
        
        $featured = array_filter($products, function($product) {
            return isset($product['featured']) && $product['featured'] === true;
        });
        
        if ($limit) {
            $featured = array_slice($featured, 0, $limit);
        }
        
        return array_values($featured);
    }
    
    /**
     * Vyhledá produkty podle názvu nebo popisu
     */
    public function searchProducts($query, $activeOnly = true) {
        $products = $this->getAllProducts($activeOnly);
        $query = strtolower($query);
        
        return array_filter($products, function($product) use ($query) {
            $name = strtolower($product['name'] ?? '');
            $description = strtolower($product['description'] ?? '');
            
            return strpos($name, $query) !== false || strpos($description, $query) !== false;
        });
    }
    
    /**
     * Přidá nový produkt
     */
    public function addProduct($productData) {
        $requiredFields = ['name', 'price', 'category', 'material'];
        $errors = $this->validateRequired($productData, $requiredFields);
        
        if (!empty($errors)) {
            throw new Exception('Chyby validace: ' . implode(', ', $errors));
        }
        
        // Sanitizace dat
        $productData = $this->sanitizeData($productData);
        
        // Přidání ID a časových značek
        $productData['id'] = $this->generateId('products');
        $productData = $this->addTimestamps($productData);
        
        // Výchozí hodnoty
        if (!isset($productData['active'])) {
            $productData['active'] = true;
        }
        if (!isset($productData['featured'])) {
            $productData['featured'] = false;
        }
        if (!isset($productData['currency'])) {
            $productData['currency'] = 'CZK';
        }
        
        // Přidání do kolekce
        if (!isset($this->data['products'])) {
            $this->data['products'] = [];
        }
        
        $this->data['products'][] = $productData;
        $this->saveData();
        
        return $productData['id'];
    }
    
    /**
     * Aktualizuje produkt
     */
    public function updateProduct($id, $productData) {
        $result = $this->findById('products', $id);
        
        if (!$result) {
            throw new Exception('Produkt s ID ' . $id . ' nebyl nalezen');
        }
        
        // Sanitizace dat
        $productData = $this->sanitizeData($productData);
        
        // Zachování původního ID a created_at
        $productData['id'] = $id;
        if (isset($result['item']['created_at'])) {
            $productData['created_at'] = $result['item']['created_at'];
        }
        
        // Přidání updated_at
        $productData = $this->addTimestamps($productData, true);
        
        // Aktualizace
        $this->data['products'][$result['index']] = $productData;
        $this->saveData();
        
        return true;
    }
    
    /**
     * Smaže produkt
     */
    public function deleteProduct($id) {
        $result = $this->findById('products', $id);
        
        if (!$result) {
            throw new Exception('Produkt s ID ' . $id . ' nebyl nalezen');
        }
        
        unset($this->data['products'][$result['index']]);
        $this->data['products'] = array_values($this->data['products']); // Přeindexování
        $this->saveData();
        
        return true;
    }
    
    /**
     * Získá všechny kategorie
     */
    public function getAllCategories($activeOnly = true) {
        $categories = $this->data['categories'] ?? [];
        
        if ($activeOnly) {
            $categories = array_filter($categories, function($category) {
                return isset($category['active']) && $category['active'] === true;
            });
        }
        
        return array_values($categories);
    }
    
    /**
     * Aktualizuje skladové zásoby
     */
    public function updateStock($productId, $size, $color, $quantity) {
        $result = $this->findById('products', $productId);
        
        if (!$result) {
            throw new Exception('Produkt s ID ' . $productId . ' nebyl nalezen');
        }
        
        if (!isset($this->data['products'][$result['index']]['stock'][$size][$color])) {
            throw new Exception('Kombinace velikosti a barvy nebyla nalezena');
        }
        
        $this->data['products'][$result['index']]['stock'][$size][$color] = $quantity;
        $this->data['products'][$result['index']] = $this->addTimestamps($this->data['products'][$result['index']], true);
        
        $this->saveData();
        
        return true;
    }
    
    /**
     * Zkontroluje dostupnost produktu
     */
    public function checkAvailability($productId, $size, $color, $requestedQuantity) {
        $product = $this->getProductById($productId);
        
        if (!$product) {
            return false;
        }
        
        $availableQuantity = $product['stock'][$size][$color] ?? 0;
        
        return $availableQuantity >= $requestedQuantity;
    }

    /**
     * Získá produkty podle kategorie
     */
    public function getByCategory($category, $limit = null) {
        $products = $this->getAllProducts(true);

        $filtered = array_filter($products, function($product) use ($category) {
            return $product['category'] === $category;
        });

        if ($limit) {
            $filtered = array_slice($filtered, 0, $limit);
        }

        return array_values($filtered);
    }

    /**
     * Získá všechny kategorie produktů
     */
    public function getCategories() {
        $products = $this->getAllProducts(true);
        $categories = [];

        foreach ($products as $product) {
            if (!empty($product['category']) && !in_array($product['category'], $categories)) {
                $categories[] = $product['category'];
            }
        }

        sort($categories);
        return $categories;
    }

    /**
     * Vyhledá produkty podle textu
     */
    public function search($query, $limit = null) {
        $products = $this->getAllProducts(true);
        $query = strtolower(trim($query));

        if (empty($query)) {
            return [];
        }

        $results = array_filter($products, function($product) use ($query) {
            $searchText = strtolower($product['name'] . ' ' . $product['description'] . ' ' . $product['category']);
            return strpos($searchText, $query) !== false;
        });

        if ($limit) {
            $results = array_slice($results, 0, $limit);
        }

        return array_values($results);
    }
}
