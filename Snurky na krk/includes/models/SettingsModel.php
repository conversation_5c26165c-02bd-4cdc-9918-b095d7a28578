<?php

require_once 'BaseModel.php';

/**
 * Model pro pr<PERSON>ci s nastavením aplikace
 */
class SettingsModel extends BaseModel {
    
    public function __construct() {
        parent::__construct('settings.json');
    }
    
    /**
     * Získá všechna nastavení (alias pro getAllSettings)
     */
    public function getAll() {
        return $this->getAllSettings();
    }

    /**
     * Získá všechna nastavení
     */
    public function getAllSettings() {
        return $this->data;
    }
    
    /**
     * Získá konkrétní skupinu nastavení
     */
    public function getSettingsGroup($group) {
        return $this->data[$group] ?? [];
    }
    
    /**
     * Získá konkrétní nastavení
     */
    public function getSetting($group, $key = null) {
        if ($key === null) {
            return $this->getSettingsGroup($group);
        }
        
        return $this->data[$group][$key] ?? null;
    }
    
    /**
     * Aktualizuje nastavení
     */
    public function updateSetting($group, $key, $value) {
        if (!isset($this->data[$group])) {
            $this->data[$group] = [];
        }
        
        $this->data[$group][$key] = $value;
        $this->data['last_updated'] = date('c');
        
        $this->saveData();
        
        return true;
    }
    
    /**
     * Aktualizuje celou skupinu nastavení
     */
    public function updateSettingsGroup($group, $settings) {
        // Sanitizace dat
        $settings = $this->sanitizeData($settings);
        
        $this->data[$group] = $settings;
        $this->data['last_updated'] = date('c');
        
        $this->saveData();
        
        return true;
    }
    
    /**
     * Získá nastavení webu
     */
    public function getSiteSettings() {
        return $this->getSettingsGroup('site_settings');
    }
    
    /**
     * Získá obchodní nastavení
     */
    public function getBusinessSettings() {
        return $this->getSettingsGroup('business_settings');
    }
    
    /**
     * Získá nastavení zobrazení
     */
    public function getDisplaySettings() {
        return $this->getSettingsGroup('display_settings');
    }
    
    /**
     * Získá emailová nastavení
     */
    public function getEmailSettings() {
        return $this->getSettingsGroup('email_settings');
    }
    
    /**
     * Získá bezpečnostní nastavení
     */
    public function getSecuritySettings() {
        return $this->getSettingsGroup('security_settings');
    }
    
    /**
     * Získá nastavení zálohování
     */
    public function getBackupSettings() {
        return $this->getSettingsGroup('backup_settings');
    }
    
    /**
     * Zkontroluje, zda je web v režimu údržby
     */
    public function isMaintenanceMode() {
        return $this->getSetting('maintenance', 'maintenance_mode') === true;
    }
    
    /**
     * Zapne/vypne režim údržby
     */
    public function setMaintenanceMode($enabled, $message = null) {
        $this->updateSetting('maintenance', 'maintenance_mode', $enabled);
        
        if ($message !== null) {
            $this->updateSetting('maintenance', 'maintenance_message', $message);
        }
        
        return true;
    }
    
    /**
     * Získá zprávu o údržbě
     */
    public function getMaintenanceMessage() {
        return $this->getSetting('maintenance', 'maintenance_message') ?? 
               'Stránky jsou dočasně nedostupné kvůli údržbě. Zkuste to prosím později.';
    }
    
    /**
     * Validuje emailová nastavení
     */
    public function validateEmailSettings($settings) {
        $errors = [];
        
        if (isset($settings['from_email']) && !filter_var($settings['from_email'], FILTER_VALIDATE_EMAIL)) {
            $errors[] = 'Neplatný formát odesílacího emailu';
        }
        
        if (isset($settings['smtp_port']) && (!is_numeric($settings['smtp_port']) || $settings['smtp_port'] < 1 || $settings['smtp_port'] > 65535)) {
            $errors[] = 'SMTP port musí být číslo mezi 1 a 65535';
        }
        
        if (isset($settings['smtp_encryption']) && !in_array($settings['smtp_encryption'], ['none', 'ssl', 'tls'])) {
            $errors[] = 'Neplatný typ šifrování SMTP';
        }
        
        return $errors;
    }
    
    /**
     * Validuje bezpečnostní nastavení
     */
    public function validateSecuritySettings($settings) {
        $errors = [];
        
        if (isset($settings['admin_session_timeout']) && (!is_numeric($settings['admin_session_timeout']) || $settings['admin_session_timeout'] < 300)) {
            $errors[] = 'Timeout session musí být alespoň 300 sekund';
        }
        
        if (isset($settings['max_login_attempts']) && (!is_numeric($settings['max_login_attempts']) || $settings['max_login_attempts'] < 1)) {
            $errors[] = 'Maximální počet pokusů o přihlášení musí být alespoň 1';
        }
        
        if (isset($settings['password_min_length']) && (!is_numeric($settings['password_min_length']) || $settings['password_min_length'] < 6)) {
            $errors[] = 'Minimální délka hesla musí být alespoň 6 znaků';
        }
        
        return $errors;
    }
    
    /**
     * Resetuje nastavení na výchozí hodnoty
     */
    public function resetToDefaults($group = null) {
        $defaults = $this->getDefaultSettings();
        
        if ($group && isset($defaults[$group])) {
            $this->data[$group] = $defaults[$group];
        } else {
            $this->data = $defaults;
        }
        
        $this->data['last_updated'] = date('c');
        $this->saveData();
        
        return true;
    }
    
    /**
     * Získá výchozí nastavení
     */
    private function getDefaultSettings() {
        return [
            'site_settings' => [
                'site_name' => 'Snurky na krk',
                'site_description' => 'Kvalitní šňůrky na krk pro každou příležitost',
                'site_keywords' => 'šňůrky, krk, bavlna, hedvábí, móda, doplňky',
                'admin_email' => '<EMAIL>',
                'contact_email' => '<EMAIL>'
            ],
            'business_settings' => [
                'currency' => 'CZK',
                'vat_rate' => 21,
                'default_commission_rate' => 15,
                'minimum_order_amount' => 500
            ],
            'display_settings' => [
                'products_per_page' => 12,
                'featured_products_count' => 6,
                'show_prices' => true,
                'show_stock' => false,
                'enable_categories' => true,
                'enable_search' => true,
                'default_language' => 'cs',
                'date_format' => 'd.m.Y',
                'time_format' => 'H:i'
            ],
            'security_settings' => [
                'admin_session_timeout' => 3600,
                'max_login_attempts' => 5,
                'login_lockout_time' => 900,
                'enable_csrf_protection' => true,
                'password_min_length' => 8
            ],
            'maintenance' => [
                'maintenance_mode' => false,
                'maintenance_message' => 'Stránky jsou dočasně nedostupné kvůli údržbě. Zkuste to prosím později.'
            ]
        ];
    }
    
    /**
     * Exportuje nastavení do pole
     */
    public function exportSettings() {
        return $this->data;
    }
    
    /**
     * Importuje nastavení z pole
     */
    public function importSettings($settings) {
        // Validace základní struktury
        if (!is_array($settings)) {
            throw new Exception('Nastavení musí být ve formátu pole');
        }
        
        // Sanitizace dat
        $settings = $this->sanitizeData($settings);
        
        // Zachování některých kritických nastavení
        if (isset($this->data['last_updated'])) {
            $settings['last_updated'] = date('c');
        }
        
        $this->data = $settings;
        $this->saveData();
        
        return true;
    }
}
