<?php

require_once 'BaseModel.php';

/**
 * Model pro pr<PERSON><PERSON> s objednávka<PERSON>
 */
class OrderModel extends BaseModel {
    
    public function __construct() {
        parent::__construct('orders.json');
    }
    
    /**
     * Získá všechny objednávky
     */
    public function getAllOrders($limit = null, $offset = 0) {
        $orders = $this->data['orders'] ?? [];
        
        // Seřazení podle data vytvoření (nejnovější první)
        usort($orders, function($a, $b) {
            return strtotime($b['created_at']) - strtotime($a['created_at']);
        });
        
        if ($limit) {
            $orders = array_slice($orders, $offset, $limit);
        }
        
        return array_values($orders);
    }
    
    /**
     * Získá objednávku podle ID
     */
    public function getOrderById($id) {
        $result = $this->findById('orders', $id);
        return $result ? $result['item'] : null;
    }
    
    /**
     * <PERSON><PERSON><PERSON><PERSON> objednávky podle obchodníka
     */
    public function getOrdersByMerchant($merchantId, $limit = null) {
        $orders = $this->data['orders'] ?? [];
        
        $merchantOrders = array_filter($orders, function($order) use ($merchantId) {
            return isset($order['merchant_id']) && $order['merchant_id'] == $merchantId;
        });
        
        // Seřazení podle data
        usort($merchantOrders, function($a, $b) {
            return strtotime($b['created_at']) - strtotime($a['created_at']);
        });
        
        if ($limit) {
            $merchantOrders = array_slice($merchantOrders, 0, $limit);
        }
        
        return array_values($merchantOrders);
    }
    
    /**
     * Získá objednávky podle statusu
     */
    public function getOrdersByStatus($status) {
        $orders = $this->data['orders'] ?? [];
        
        return array_filter($orders, function($order) use ($status) {
            return isset($order['status']) && $order['status'] === $status;
        });
    }
    
    /**
     * Vytvoří novou objednávku
     */
    public function createOrder($orderData) {
        $requiredFields = ['merchant_id', 'items'];
        $errors = $this->validateRequired($orderData, $requiredFields);
        
        if (!empty($errors)) {
            throw new Exception('Chyby validace: ' . implode(', ', $errors));
        }
        
        // Validace položek objednávky
        if (empty($orderData['items']) || !is_array($orderData['items'])) {
            throw new Exception('Objednávka musí obsahovat alespoň jednu položku');
        }
        
        // Sanitizace dat
        $orderData = $this->sanitizeData($orderData);
        
        // Přidání ID a časových značek
        $orderData['id'] = $this->generateId('orders');
        $orderData = $this->addTimestamps($orderData);
        
        // Generování čísla objednávky
        $orderData['order_number'] = $this->generateOrderNumber();
        
        // Výchozí hodnoty
        if (!isset($orderData['status'])) {
            $orderData['status'] = 'pending';
        }
        if (!isset($orderData['payment_status'])) {
            $orderData['payment_status'] = 'pending';
        }
        if (!isset($orderData['order_date'])) {
            $orderData['order_date'] = date('c');
        }
        
        // Výpočet celkových částek
        $orderData = $this->calculateOrderTotals($orderData);
        
        // Přidání do kolekce
        if (!isset($this->data['orders'])) {
            $this->data['orders'] = [];
        }
        
        $this->data['orders'][] = $orderData;
        $this->saveData();
        
        return $orderData['id'];
    }
    
    /**
     * Aktualizuje objednávku
     */
    public function updateOrder($id, $orderData) {
        $result = $this->findById('orders', $id);
        
        if (!$result) {
            throw new Exception('Objednávka s ID ' . $id . ' nebyla nalezena');
        }
        
        // Sanitizace dat
        $orderData = $this->sanitizeData($orderData);
        
        // Zachování původního ID, order_number a created_at
        $orderData['id'] = $id;
        if (isset($result['item']['order_number'])) {
            $orderData['order_number'] = $result['item']['order_number'];
        }
        if (isset($result['item']['created_at'])) {
            $orderData['created_at'] = $result['item']['created_at'];
        }
        
        // Přepočítání celkových částek pokud se změnily položky
        if (isset($orderData['items'])) {
            $orderData = $this->calculateOrderTotals($orderData);
        }
        
        // Přidání updated_at
        $orderData = $this->addTimestamps($orderData, true);
        
        // Aktualizace
        $this->data['orders'][$result['index']] = $orderData;
        $this->saveData();
        
        return true;
    }
    
    /**
     * Změní status objednávky
     */
    public function updateOrderStatus($id, $status) {
        $result = $this->findById('orders', $id);
        
        if (!$result) {
            throw new Exception('Objednávka s ID ' . $id . ' nebyla nalezena');
        }
        
        $validStatuses = array_column($this->getOrderStatuses(), 'id');
        if (!in_array($status, $validStatuses)) {
            throw new Exception('Neplatný status objednávky');
        }
        
        $this->data['orders'][$result['index']]['status'] = $status;
        $this->data['orders'][$result['index']] = $this->addTimestamps($this->data['orders'][$result['index']], true);
        
        $this->saveData();
        
        return true;
    }
    
    /**
     * Změní status platby
     */
    public function updatePaymentStatus($id, $paymentStatus, $paymentDate = null) {
        $result = $this->findById('orders', $id);
        
        if (!$result) {
            throw new Exception('Objednávka s ID ' . $id . ' nebyla nalezena');
        }
        
        $validStatuses = array_column($this->getPaymentStatuses(), 'id');
        if (!in_array($paymentStatus, $validStatuses)) {
            throw new Exception('Neplatný status platby');
        }
        
        $this->data['orders'][$result['index']]['payment_status'] = $paymentStatus;
        
        if ($paymentStatus === 'paid' && $paymentDate) {
            $this->data['orders'][$result['index']]['payment_date'] = $paymentDate;
        }
        
        $this->data['orders'][$result['index']] = $this->addTimestamps($this->data['orders'][$result['index']], true);
        
        $this->saveData();
        
        return true;
    }
    
    /**
     * Generuje číslo objednávky
     */
    private function generateOrderNumber() {
        $year = date('Y');
        $orders = $this->data['orders'] ?? [];
        
        // Najde nejvyšší číslo objednávky pro aktuální rok
        $maxNumber = 0;
        foreach ($orders as $order) {
            if (isset($order['order_number']) && strpos($order['order_number'], "ORD-{$year}-") === 0) {
                $number = intval(substr($order['order_number'], strlen("ORD-{$year}-")));
                if ($number > $maxNumber) {
                    $maxNumber = $number;
                }
            }
        }
        
        $nextNumber = $maxNumber + 1;
        return "ORD-{$year}-" . str_pad($nextNumber, 3, '0', STR_PAD_LEFT);
    }
    
    /**
     * Vypočítá celkové částky objednávky
     */
    private function calculateOrderTotals($orderData) {
        $subtotal = 0;
        
        foreach ($orderData['items'] as &$item) {
            $item['total_price'] = $item['quantity'] * $item['unit_price'];
            $subtotal += $item['total_price'];
        }
        
        $orderData['subtotal'] = $subtotal;
        $orderData['total_amount'] = $subtotal;
        
        // Výpočet provize
        if (isset($orderData['commission_rate'])) {
            $orderData['commission_amount'] = $subtotal * ($orderData['commission_rate'] / 100);
        }
        
        return $orderData;
    }
    
    /**
     * Získá možné statusy objednávek
     */
    public function getOrderStatuses() {
        return $this->data['order_statuses'] ?? [];
    }
    
    /**
     * Získá možné statusy plateb
     */
    public function getPaymentStatuses() {
        return $this->data['payment_statuses'] ?? [];
    }
    
    /**
     * Získá statistiky objednávek
     */
    public function getOrderStatistics($merchantId = null) {
        $orders = $this->data['orders'] ?? [];
        
        if ($merchantId) {
            $orders = array_filter($orders, function($order) use ($merchantId) {
                return isset($order['merchant_id']) && $order['merchant_id'] == $merchantId;
            });
        }
        
        $stats = [
            'total_orders' => count($orders),
            'total_amount' => 0,
            'total_commission' => 0,
            'by_status' => [],
            'by_payment_status' => []
        ];
        
        foreach ($orders as $order) {
            $stats['total_amount'] += $order['total_amount'] ?? 0;
            $stats['total_commission'] += $order['commission_amount'] ?? 0;
            
            $status = $order['status'] ?? 'unknown';
            $stats['by_status'][$status] = ($stats['by_status'][$status] ?? 0) + 1;
            
            $paymentStatus = $order['payment_status'] ?? 'unknown';
            $stats['by_payment_status'][$paymentStatus] = ($stats['by_payment_status'][$paymentStatus] ?? 0) + 1;
        }
        
        return $stats;
    }
}
