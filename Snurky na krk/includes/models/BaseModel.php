<?php

/**
 * Základn<PERSON> třída pro práci s J<PERSON><PERSON> daty
 * Poskytuje společné metody pro všechny datové modely
 * Verze 2.0 s cache podporou
 */
class BaseModel {
    protected $dataFile;
    protected $data;
    protected $cache;
    protected $cacheEnabled;
    protected $cacheTtl;
    
    public function __construct($dataFile, $cacheEnabled = true, $cacheTtl = 3600) {
        $this->dataFile = DATA_PATH . '/' . $dataFile;
        $this->cacheEnabled = $cacheEnabled;
        $this->cacheTtl = $cacheTtl;

        if ($this->cacheEnabled) {
            $this->cache = new CacheManager();
        }

        $this->loadData();
    }
    
    /**
     * Načte data z JSON souboru s cache podporou
     */
    protected function loadData() {
        $cacheKey = 'model_data_' . basename($this->dataFile, '.json');

        // Pokusíme se načíst z cache
        if ($this->cacheEnabled) {
            $cachedData = $this->cache->get($cacheKey);
            if ($cachedData !== null) {
                // Kontrola zda se soubor nezměnil od uložení do cache
                $fileModTime = file_exists($this->dataFile) ? filemtime($this->dataFile) : 0;
                if (isset($cachedData['file_mod_time']) && $cachedData['file_mod_time'] >= $fileModTime) {
                    $this->data = $cachedData['data'];
                    return;
                }
            }
        }

        // Načtení ze souboru
        if (!file_exists($this->dataFile)) {
            $this->data = [];
        } else {
            $jsonContent = file_get_contents($this->dataFile);
            $this->data = json_decode($jsonContent, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new Exception('Chyba při načítání JSON dat: ' . json_last_error_msg());
            }
        }

        // Uložení do cache
        if ($this->cacheEnabled) {
            $cacheData = [
                'data' => $this->data,
                'file_mod_time' => file_exists($this->dataFile) ? filemtime($this->dataFile) : time()
            ];
            $this->cache->set($cacheKey, $cacheData, $this->cacheTtl);
        }
    }
    
    /**
     * Uloží data do JSON souboru a invaliduje cache
     */
    protected function saveData() {
        $jsonContent = json_encode($this->data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);

        if ($jsonContent === false) {
            throw new Exception('Chyba při ukládání JSON dat: ' . json_last_error_msg());
        }

        // Vytvoření záložního souboru
        if (file_exists($this->dataFile)) {
            $backupFile = $this->dataFile . '.backup.' . date('Y-m-d-H-i-s');
            copy($this->dataFile, $backupFile);
        }

        $result = file_put_contents($this->dataFile, $jsonContent, LOCK_EX);

        if ($result === false) {
            throw new Exception('Nepodařilo se uložit data do souboru: ' . $this->dataFile);
        }

        // Invalidace cache
        if ($this->cacheEnabled) {
            $cacheKey = 'model_data_' . basename($this->dataFile, '.json');
            $this->cache->delete($cacheKey);
        }

        return true;
    }

    /**
     * Vyčistí cache pro tento model
     */
    public function clearCache() {
        if ($this->cacheEnabled) {
            $cacheKey = 'model_data_' . basename($this->dataFile, '.json');
            return $this->cache->delete($cacheKey);
        }
        return true;
    }
    
    /**
     * Vygeneruje nové ID
     */
    protected function generateId($collection) {
        if (!isset($this->data[$collection]) || empty($this->data[$collection])) {
            return 1;
        }
        
        $maxId = 0;
        foreach ($this->data[$collection] as $item) {
            if (isset($item['id']) && $item['id'] > $maxId) {
                $maxId = $item['id'];
            }
        }
        
        return $maxId + 1;
    }
    
    /**
     * Najde položku podle ID
     */
    protected function findById($collection, $id) {
        if (!isset($this->data[$collection])) {
            return null;
        }
        
        foreach ($this->data[$collection] as $index => $item) {
            if (isset($item['id']) && $item['id'] == $id) {
                return ['index' => $index, 'item' => $item];
            }
        }
        
        return null;
    }
    
    /**
     * Validuje povinná pole
     */
    protected function validateRequired($data, $requiredFields) {
        $errors = [];
        
        foreach ($requiredFields as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                $errors[] = "Pole '{$field}' je povinné";
            }
        }
        
        return $errors;
    }
    
    /**
     * Sanitizuje vstupní data
     */
    protected function sanitizeData($data) {
        if (is_array($data)) {
            return array_map([$this, 'sanitizeData'], $data);
        }
        
        if (is_string($data)) {
            return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
        }
        
        return $data;
    }
    
    /**
     * Přidá časové značky
     */
    protected function addTimestamps($data, $isUpdate = false) {
        $now = date('c'); // ISO 8601 formát
        
        if (!$isUpdate) {
            $data['created_at'] = $now;
        }
        
        $data['updated_at'] = $now;
        
        return $data;
    }
}
