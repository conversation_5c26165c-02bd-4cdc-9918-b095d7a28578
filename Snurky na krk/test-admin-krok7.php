<?php
/**
 * Test admin rozhraní - Krok 7
 * Testuje dokončené admin šablony a funkcionalitu
 * 
 * <AUTHOR> Team
 * @version 1.0
 */

// Inicializace
define('SNURKY_INIT', true);
require_once 'config/config.php';

// Načtení autoloaderu
require_once 'config/autoload.php';

echo "<h1>Test admin rozhraní - Krok 7</h1>\n";

$success = [];
$errors = [];

// Test 1: Kontrola admin šablon
echo "<h2>1. Test admin šablon</h2>\n";

$adminTemplates = [
    'templates/admin/products/edit.php',
    'templates/admin/dealers/index.php',
    'templates/admin/dealers/create.php',
    'templates/admin/dealers/edit.php',
    'templates/admin/settings/index.php'
];

foreach ($adminTemplates as $template) {
    if (file_exists($template)) {
        $success[] = "✅ Šablona $template existuje";
        
        // Kontrola základní struktury
        $content = file_get_contents($template);
        if (strpos($content, 'SNURKY_INIT') !== false) {
            $success[] = "✅ Šablona $template má bezpečnostní kontrolu";
        } else {
            $errors[] = "❌ Šablona $template nemá bezpečnostní kontrolu";
        }
        
        if (strpos($content, 'csrf_token') !== false) {
            $success[] = "✅ Šablona $template má CSRF ochranu";
        } else {
            $errors[] = "❌ Šablona $template nemá CSRF ochranu";
        }
    } else {
        $errors[] = "❌ Šablona $template neexistuje";
    }
}

// Test 2: Kontrola FileManager třídy
echo "<h2>2. Test FileManager třídy</h2>\n";

try {
    if (class_exists('FileManager')) {
        $success[] = "✅ FileManager třída existuje";
        
        $fileManager = new FileManager();
        $success[] = "✅ FileManager lze vytvořit";
        
        // Test metod
        $methods = ['uploadFile', 'createThumbnail', 'deleteFile', 'getFileInfo'];
        foreach ($methods as $method) {
            if (method_exists($fileManager, $method)) {
                $success[] = "✅ FileManager má metodu $method";
            } else {
                $errors[] = "❌ FileManager nemá metodu $method";
            }
        }
    } else {
        $errors[] = "❌ FileManager třída neexistuje";
    }
} catch (Exception $e) {
    $errors[] = "❌ Chyba při testování FileManager: " . $e->getMessage();
}

// Test 3: Kontrola upload adresářů
echo "<h2>3. Test upload adresářů</h2>\n";

$uploadDirs = [
    'assets/uploads',
    'assets/uploads/products',
    'assets/uploads/thumbnails',
    'assets/uploads/documents'
];

foreach ($uploadDirs as $dir) {
    if (is_dir($dir)) {
        $success[] = "✅ Adresář $dir existuje";
        
        if (is_writable($dir)) {
            $success[] = "✅ Adresář $dir je zapisovatelný";
        } else {
            $errors[] = "❌ Adresář $dir není zapisovatelný";
        }
    } else {
        // Pokusíme se vytvořit adresář
        if (mkdir($dir, 0755, true)) {
            $success[] = "✅ Adresář $dir byl vytvořen";
        } else {
            $errors[] = "❌ Nepodařilo se vytvořit adresář $dir";
        }
    }
}

// Test 4: Kontrola admin JavaScript
echo "<h2>4. Test admin JavaScript</h2>\n";

if (file_exists('assets/js/admin.js')) {
    $success[] = "✅ Admin JavaScript existuje";
    
    $jsContent = file_get_contents('assets/js/admin.js');
    
    $jsFunctions = [
        'initAutoSave',
        'autoSaveForm',
        'showAutoSaveIndicator',
        'initFormValidation',
        'initImageUpload'
    ];
    
    foreach ($jsFunctions as $func) {
        if (strpos($jsContent, $func) !== false) {
            $success[] = "✅ JavaScript má funkci $func";
        } else {
            $errors[] = "❌ JavaScript nemá funkci $func";
        }
    }
} else {
    $errors[] = "❌ Admin JavaScript neexistuje";
}

// Test 5: Kontrola admin CSS
echo "<h2>5. Test admin CSS</h2>\n";

if (file_exists('assets/css/admin.css')) {
    $success[] = "✅ Admin CSS existuje";
    
    $cssContent = file_get_contents('assets/css/admin.css');
    
    $cssClasses = [
        '.admin-body',
        '.admin-navbar',
        '.admin-container',
        '.sidebar'
    ];
    
    foreach ($cssClasses as $class) {
        if (strpos($cssContent, $class) !== false) {
            $success[] = "✅ CSS má třídu $class";
        } else {
            $errors[] = "❌ CSS nemá třídu $class";
        }
    }
} else {
    $errors[] = "❌ Admin CSS neexistuje";
}

// Test 6: Kontrola admin kontrolerů
echo "<h2>6. Test admin kontrolerů</h2>\n";

$adminControllers = [
    'AdminProductController',
    'AdminDealerController', 
    'AdminSettingsController',
    'AdminDashboardController',
    'AdminAuthController'
];

foreach ($adminControllers as $controller) {
    try {
        if (class_exists($controller)) {
            $success[] = "✅ Kontroler $controller existuje";
            
            // Test základních metod
            $reflection = new ReflectionClass($controller);
            $methods = $reflection->getMethods(ReflectionMethod::IS_PUBLIC);
            
            $requiredMethods = ['index'];
            if ($controller === 'AdminProductController' || $controller === 'AdminDealerController') {
                $requiredMethods = array_merge($requiredMethods, ['create', 'store', 'edit', 'update']);
            }
            
            foreach ($requiredMethods as $method) {
                if ($reflection->hasMethod($method)) {
                    $success[] = "✅ Kontroler $controller má metodu $method";
                } else {
                    $errors[] = "❌ Kontroler $controller nemá metodu $method";
                }
            }
        } else {
            $errors[] = "❌ Kontroler $controller neexistuje";
        }
    } catch (Exception $e) {
        $errors[] = "❌ Chyba při testování kontroleru $controller: " . $e->getMessage();
    }
}

// Test 7: Kontrola autoloaderu
echo "<h2>7. Test autoloaderu</h2>\n";

$autoloadContent = file_get_contents('config/autoload.php');
if (strpos($autoloadContent, 'FileManager') !== false) {
    $success[] = "✅ FileManager je v autoloaderu";
} else {
    $errors[] = "❌ FileManager není v autoloaderu";
}

// Test 8: Simulace upload funkcionality
echo "<h2>8. Test upload funkcionality</h2>\n";

try {
    if (class_exists('FileManager')) {
        $fileManager = new FileManager();
        
        // Test validace souboru (simulace)
        $testFile = [
            'name' => 'test.jpg',
            'size' => 1024 * 1024, // 1MB
            'error' => UPLOAD_ERR_NO_FILE
        ];
        
        // Test by měl selhat kvůli UPLOAD_ERR_NO_FILE
        try {
            $reflection = new ReflectionClass($fileManager);
            $method = $reflection->getMethod('validateFile');
            $method->setAccessible(true);
            $method->invoke($fileManager, $testFile);
            $errors[] = "❌ Validace souboru by měla selhat";
        } catch (Exception $e) {
            $success[] = "✅ Validace souboru správně detekuje chyby";
        }
        
        // Test formátování velikosti souboru
        $info = $fileManager->getFileInfo('assets/images/logo.svg');
        if ($info === null) {
            $success[] = "✅ getFileInfo správně vrací null pro neexistující soubor";
        }
    }
} catch (Exception $e) {
    $errors[] = "❌ Chyba při testování upload funkcionality: " . $e->getMessage();
}

// Test 9: Kontrola bezpečnostních funkcí
echo "<h2>9. Test bezpečnostních funkcí</h2>\n";

// Kontrola CSRF tokenů v šablonách
$templatesWithCSRF = 0;
foreach ($adminTemplates as $template) {
    if (file_exists($template)) {
        $content = file_get_contents($template);
        if (strpos($content, 'csrf_token') !== false) {
            $templatesWithCSRF++;
        }
    }
}

if ($templatesWithCSRF === count($adminTemplates)) {
    $success[] = "✅ Všechny admin šablony mají CSRF ochranu";
} else {
    $errors[] = "❌ Ne všechny admin šablony mají CSRF ochranu";
}

// Výsledky testů
echo "<h2>Výsledky testů</h2>\n";

echo "<h3 style='color: green;'>Úspěšné testy (" . count($success) . "):</h3>\n";
foreach ($success as $item) {
    echo "<p>$item</p>\n";
}

if (!empty($errors)) {
    echo "<h3 style='color: red;'>Chyby (" . count($errors) . "):</h3>\n";
    foreach ($errors as $item) {
        echo "<p>$item</p>\n";
    }
}

$totalTests = count($success) + count($errors);
$successRate = round((count($success) / $totalTests) * 100, 1);

echo "<h3>Celkové hodnocení:</h3>\n";
echo "<p><strong>Úspěšnost: $successRate% ($totalTests testů)</strong></p>\n";

if ($successRate >= 90) {
    echo "<p style='color: green; font-weight: bold;'>✅ Krok 7 je úspěšně dokončen!</p>\n";
} elseif ($successRate >= 75) {
    echo "<p style='color: orange; font-weight: bold;'>⚠️ Krok 7 je z větší části dokončen, ale vyžaduje drobné úpravy.</p>\n";
} else {
    echo "<p style='color: red; font-weight: bold;'>❌ Krok 7 vyžaduje další práci.</p>\n";
}

echo "<hr>\n";
echo "<p><em>Test dokončen: " . date('d.m.Y H:i:s') . "</em></p>\n";
?>
