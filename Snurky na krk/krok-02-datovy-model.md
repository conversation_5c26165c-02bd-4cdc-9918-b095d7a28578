# Krok 2: <PERSON><PERSON><PERSON>r<PERSON> datového modelu

## <PERSON><PERSON><PERSON> kroku
Definovat strukturu JSON souborů pro ukládání všech dat webu a vytvořit API pro práci s těmito daty.

## Úkoly

### 2.1 Struktura JSON souborů

#### site-config.json - Základní nastavení webu
```json
{
  "title": "SnurkyNaKrk.cz",
  "description": "Popis webu pro SEO",
  "favicon": "favicon.ico",
  "logo": "logo.png",
  "contact": {
    "email": "<EMAIL>",
    "phone": "+420 123 456 789",
    "address": "Adresa firmy"
  },
  "social": {
    "facebook": "",
    "instagram": ""
  }
}
```

#### content.json - <PERSON><PERSON><PERSON> obsah
```json
{
  "homepage": {
    "intro_title": "Vítejte na SnurkyNaKrk.cz",
    "intro_text": "Ú<PERSON>dní text o značce...",
    "about_title": "O nás",
    "about_text": "Text o firmě..."
  },
  "seo": {
    "meta_description": "Meta popis pro SEO",
    "keywords": "kl<PERSON><PERSON>, slova"
  }
}
```

#### products.json - TOP produkty
```json
{
  "products": [
    {
      "id": 1,
      "name": "Název produktu",
      "description": "Popis produktu",
      "image": "product1.jpg",
      "order": 1,
      "active": true
    }
  ]
}
```

#### dealers.json - Obchodníci
```json
{
  "dealers": [
    {
      "id": 1,
      "name": "Název obchodu",
      "city": "Praha",
      "contact": "<EMAIL>",
      "phone": "+420 123 456 789",
      "website": "https://obchod.cz",
      "logo": "dealer1.jpg",
      "order": 1,
      "active": true
    }
  ]
}
```

### 2.2 PHP třídy pro práci s daty
- DataManager.php - hlavní třída pro práci s JSON
- ConfigManager.php - správa konfigurace
- ProductManager.php - správa produktů
- DealerManager.php - správa obchodníků

### 2.3 Validační pravidla
- Definice povinných polí
- Validace formátů (email, telefon, URL)
- Sanitizace vstupních dat

### 2.4 Výchozí data
- Vytvoření základních JSON souborů s ukázkovými daty
- Nastavení výchozích hodnot

## Výstupy kroku
- ✅ Definované JSON struktury
- ✅ PHP třídy pro práci s daty
- ✅ Validační systém
- ✅ Výchozí datové soubory

## Testování
- Test čtení a zápisu JSON souborů
- Validace datových struktur
- Test základních CRUD operací

## Následující krok
Krok 3: Základní PHP framework - routing a bezpečnostní funkce
