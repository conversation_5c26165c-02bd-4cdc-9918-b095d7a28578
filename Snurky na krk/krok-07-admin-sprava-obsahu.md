# Krok 7: Administrace - správa obsahu

## <PERSON><PERSON><PERSON> kroku
Vytvořit admin dashboard a formul<PERSON><PERSON><PERSON> pro správu základního obsahu webu (texty, kontakty, nastavení).

## Úkoly

### 7.1 Admin Dashboard
- dashboard.php - hlavní admin stránka
- Přehled statistik webu
- Rychlé akce
- Poslední změny

#### Dashboard komponenty:
```html
<div class="dashboard-grid">
  <div class="stats-card">
    <h3>Produkty</h3>
    <span class="count">{{products_count}}</span>
  </div>
  <div class="stats-card">
    <h3>O<PERSON><PERSON><PERSON><PERSON><PERSON></h3>
    <span class="count">{{dealers_count}}</span>
  </div>
  <div class="recent-activity">
    <h3>Poslední změny</h3>
    <ul>{{recent_changes}}</ul>
  </div>
</div>
```

### 7.2 Správa základních nastavení
- settings.php - formul<PERSON>ř pro nastavení webu
- Editace title, description, kontaktů
- Upload loga a faviconu
- Social media odkazy

#### Formulář nastavení:
```html
<form class="settings-form" method="POST" enctype="multipart/form-data">
  <div class="form-section">
    <h3>Základní informace</h3>
    <input type="text" name="title" value="{{current_title}}">
    <textarea name="description">{{current_description}}</textarea>
  </div>
  
  <div class="form-section">
    <h3>Logo a favicon</h3>
    <input type="file" name="logo" accept="image/*">
    <input type="file" name="favicon" accept="image/x-icon">
  </div>
  
  <div class="form-section">
    <h3>Kontaktní údaje</h3>
    <input type="email" name="email" value="{{current_email}}">
    <input type="tel" name="phone" value="{{current_phone}}">
    <textarea name="address">{{current_address}}</textarea>
  </div>
</form>
```

### 7.3 Správa textového obsahu
- content.php - editace textů stránky
- WYSIWYG editor (TinyMCE nebo CKEditor)
- Náhled změn
- Verzování obsahu

#### Editovatelné texty:
- Úvodní text hlavní stránky
- Text sekce "O nás"
- Texty jednotlivých stránek
- Footer texty

### 7.4 Upload a správa souborů
- FileManager.php - správa nahrávaných souborů
- Validace typů souborů
- Optimalizace obrázků
- Thumbnail generování

#### Podporované formáty:
```php
$allowedImages = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
$allowedDocs = ['pdf', 'doc', 'docx'];
$maxFileSize = 5 * 1024 * 1024; // 5MB
```

### 7.5 Náhled změn
- preview.php - náhled webu s neuloženými změnami
- Live preview v iframe
- Porovnání před/po změnách
- Rollback funkcionalita

### 7.6 Admin navigace
- Sidebar navigace
- Breadcrumbs
- Quick actions menu
- User menu (profil, logout)

#### Admin menu struktura:
```html
<nav class="admin-sidebar">
  <ul>
    <li><a href="/admin/dashboard">Dashboard</a></li>
    <li><a href="/admin/settings">Nastavení</a></li>
    <li><a href="/admin/content">Obsah</a></li>
    <li><a href="/admin/products">Produkty</a></li>
    <li><a href="/admin/dealers">Obchodníci</a></li>
    <li><a href="/admin/files">Soubory</a></li>
  </ul>
</nav>
```

### 7.7 Formulářová validace
- Client-side validace (JavaScript)
- Server-side validace (PHP)
- Error handling a zobrazení chyb
- Success zprávy

#### Validační pravidla:
```javascript
const validationRules = {
  title: { required: true, maxLength: 100 },
  email: { required: true, email: true },
  phone: { pattern: /^\+?[0-9\s\-\(\)]+$/ },
  description: { maxLength: 500 }
};
```

### 7.8 Auto-save funkcionalita
- Automatické ukládání rozpracovaných změn
- Draft režim
- Upozornění na neuložené změny
- Recovery po výpadku

## Výstupy kroku
- ✅ Admin dashboard s přehledem
- ✅ Formuláře pro základní nastavení
- ✅ Správa textového obsahu
- ✅ Upload a správa souborů
- ✅ Náhled změn
- ✅ Admin navigace
- ✅ Formulářová validace
- ✅ Auto-save funkcionalita

## Testování
- Test všech formulářů v admin sekci
- Validace upload funkcionalita
- Test náhledu změn
- Kontrola auto-save
- Test na různých zařízeních
- Validace bezpečnosti formulářů

## Následující krok
Krok 8: Administrace - správa produktů a obchodníků (CRUD operace)
