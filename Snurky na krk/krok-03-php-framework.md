# Krok 3: Základní PHP framework

## Cíl kroku
Vytvořit jednoduchý PHP framework pro routing, správu požadavků a základní bezpečnostní funkce.

## Úkoly

### 3.1 Router systém
- Router.php - třída pro zpracování URL
- Mapování URL na kontrolery
- Podpora pro parametry v URL
- 404 error handling

#### Příklad routingu:
```
/ -> HomeController::index()
/produkty -> ProductController::index()
/obchodnici -> DealerController::index()
/kontakt -> ContactController::index()
/admin -> AdminController::login()
/admin/dashboard -> AdminController::dashboard()
```

### 3.2 Základní kontrolery
- BaseController.php - rodičovská třída
- HomeController.php - h<PERSON><PERSON><PERSON> str<PERSON>
- ProductController.php - produkty
- DealerController.php - obchodníci
- ContactController.php - kontakt
- AdminController.php - administrace

### 3.3 Template systém
- TemplateEngine.php - jednoduchý template engine
- Načítání a renderování šablon
- Předávání dat do šablon
- Layout systém (header, footer, content)

### 3.4 Bezpečnostní funkce
- Security.php - bezpečnostní funkce
- CSRF ochrana
- XSS prevence
- Input sanitizace
- File upload validace

#### Bezpečnostní funkce:
```php
- sanitizeInput($data)
- validateEmail($email)
- validatePhone($phone)
- generateCSRFToken()
- validateCSRFToken($token)
- isValidImageFile($file)
```

### 3.5 Session management
- SessionManager.php - správa sessions
- Bezpečné ukládání session dat
- Timeout handling
- Admin autentizace

### 3.6 Error handling
- ErrorHandler.php - zpracování chyb
- Logování chyb
- Uživatelsky přívětivé chybové stránky
- Debug režim pro vývoj

## Výstupy kroku
- ✅ Funkční routing systém
- ✅ Základní kontrolery
- ✅ Template engine
- ✅ Bezpečnostní funkce
- ✅ Session management
- ✅ Error handling

## Testování
- Test routingu všech URL
- Ověření bezpečnostních funkcí
- Test template systému
- Kontrola error handlingu

## Následující krok
Krok 4: Frontend - základní layout a responzivní design
