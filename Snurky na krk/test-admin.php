<?php
/**
 * Test administračního rozhraní pro SnurkyNaKrk.cz
 * 
 * <AUTHOR> Team
 * @version 1.0
 */

// Definice konstanty pro zabránění přímému přístupu k souborům
define('SNURKY_INIT', true);

// Načtení základní konfigurace
require_once 'config/config.php';
require_once 'config/constants.php';
require_once 'config/autoload.php';

echo "<h1>Test administračního rozhraní SnurkyNaKrk.cz</h1>\n";
echo "<hr>\n";

$errors = [];
$warnings = [];
$success = [];

// Test 1: Kontrola admin kontrolerů
echo "<h2>1. Test admin kontrolerů</h2>\n";

$adminControllers = [
    'AdminAuthController',
    'AdminDashboardController', 
    'AdminProductController',
    'AdminDealerController',
    'AdminSettingsController'
];

foreach ($adminControllers as $controller) {
    try {
        if (class_exists($controller)) {
            $success[] = "✅ Kontroler $controller existuje";
            
            // Test vytvoření instance
            $instance = new $controller();
            $success[] = "✅ Kontroler $controller lze vytvořit";
        } else {
            $errors[] = "❌ Kontroler $controller neexistuje";
        }
    } catch (Exception $e) {
        $errors[] = "❌ Chyba při vytváření kontroleru $controller: " . $e->getMessage();
    }
}

// Test 2: Kontrola admin šablon
echo "<h2>2. Test admin šablon</h2>\n";

$adminTemplates = [
    'templates/layouts/admin.php',
    'templates/admin/login.php',
    'templates/admin/dashboard.php',
    'templates/admin/products/index.php',
    'templates/admin/products/create.php',
    'templates/admin/error.php'
];

foreach ($adminTemplates as $template) {
    if (file_exists($template)) {
        $success[] = "✅ Šablona $template existuje";
    } else {
        $errors[] = "❌ Šablona $template neexistuje";
    }
}

// Test 3: Kontrola admin CSS a JS
echo "<h2>3. Test admin assets</h2>\n";

$adminAssets = [
    'assets/css/admin.css',
    'assets/js/admin.js'
];

foreach ($adminAssets as $asset) {
    if (file_exists($asset)) {
        $success[] = "✅ Asset $asset existuje";
    } else {
        $errors[] = "❌ Asset $asset neexistuje";
    }
}

// Test 4: Kontrola upload adresáře
echo "<h2>4. Test upload adresáře</h2>\n";

if (is_dir('assets/uploads')) {
    $success[] = "✅ Upload adresář existuje";
    
    if (is_writable('assets/uploads')) {
        $success[] = "✅ Upload adresář je zapisovatelný";
    } else {
        $warnings[] = "⚠️ Upload adresář není zapisovatelný";
    }
} else {
    $errors[] = "❌ Upload adresář neexistuje";
}

// Test 5: Kontrola admin uživatele v nastavení
echo "<h2>5. Test admin uživatele</h2>\n";

try {
    $settingsModel = new SettingsModel();
    $settings = $settingsModel->getSettings();
    
    if (isset($settings['admin']['users']) && !empty($settings['admin']['users'])) {
        $success[] = "✅ Admin uživatelé jsou nakonfigurováni";
        
        $adminUser = $settings['admin']['users'][0];
        if (isset($adminUser['username']) && isset($adminUser['password'])) {
            $success[] = "✅ Admin uživatel má username a heslo";
            echo "<p><strong>Admin přihlašovací údaje:</strong></p>\n";
            echo "<ul>\n";
            echo "<li>Username: " . htmlspecialchars($adminUser['username']) . "</li>\n";
            echo "<li>Heslo: admin (výchozí heslo)</li>\n";
            echo "</ul>\n";
        } else {
            $errors[] = "❌ Admin uživatel nemá kompletní údaje";
        }
    } else {
        $errors[] = "❌ Admin uživatelé nejsou nakonfigurováni";
    }
} catch (Exception $e) {
    $errors[] = "❌ Chyba při načítání nastavení: " . $e->getMessage();
}

// Test 6: Test SessionManager admin funkcí
echo "<h2>6. Test SessionManager admin funkcí</h2>\n";

try {
    $sessionManager = new SessionManager();
    
    if (method_exists($sessionManager, 'isAdminLoggedIn')) {
        $success[] = "✅ Metoda isAdminLoggedIn() existuje";
    } else {
        $errors[] = "❌ Metoda isAdminLoggedIn() neexistuje";
    }
    
    if (method_exists($sessionManager, 'loginAdmin')) {
        $success[] = "✅ Metoda loginAdmin() existuje";
    } else {
        $errors[] = "❌ Metoda loginAdmin() neexistuje";
    }
    
    if (method_exists($sessionManager, 'logoutAdmin')) {
        $success[] = "✅ Metoda logoutAdmin() existuje";
    } else {
        $errors[] = "❌ Metoda logoutAdmin() neexistuje";
    }
} catch (Exception $e) {
    $errors[] = "❌ Chyba při testování SessionManager: " . $e->getMessage();
}

// Test 7: Test TemplateEngine admin funkcí
echo "<h2>7. Test TemplateEngine admin funkcí</h2>\n";

try {
    $templateEngine = new TemplateEngine();
    
    if (method_exists($templateEngine, 'isCurrentPage')) {
        $success[] = "✅ Metoda isCurrentPage() existuje";
    } else {
        $errors[] = "❌ Metoda isCurrentPage() neexistuje";
    }
    
    if (method_exists($templateEngine, 'getFlashMessage')) {
        $success[] = "✅ Metoda getFlashMessage() existuje";
    } else {
        $errors[] = "❌ Metoda getFlashMessage() neexistuje";
    }
    
    if (method_exists($templateEngine, 'formatStatus')) {
        $success[] = "✅ Metoda formatStatus() existuje";
    } else {
        $errors[] = "❌ Metoda formatStatus() neexistuje";
    }
} catch (Exception $e) {
    $errors[] = "❌ Chyba při testování TemplateEngine: " . $e->getMessage();
}

// Výsledky testů
echo "<hr>\n";
echo "<h2>Výsledky testů</h2>\n";

if (!empty($success)) {
    echo "<h3 style='color: green;'>✅ Úspěšné testy (" . count($success) . ")</h3>\n";
    echo "<ul>\n";
    foreach ($success as $msg) {
        echo "<li>$msg</li>\n";
    }
    echo "</ul>\n";
}

if (!empty($warnings)) {
    echo "<h3 style='color: orange;'>⚠️ Varování (" . count($warnings) . ")</h3>\n";
    echo "<ul>\n";
    foreach ($warnings as $msg) {
        echo "<li>$msg</li>\n";
    }
    echo "</ul>\n";
}

if (!empty($errors)) {
    echo "<h3 style='color: red;'>❌ Chyby (" . count($errors) . ")</h3>\n";
    echo "<ul>\n";
    foreach ($errors as $msg) {
        echo "<li>$msg</li>\n";
    }
    echo "</ul>\n";
} else {
    echo "<h3 style='color: green;'>🎉 Všechny testy prošly úspěšně!</h3>\n";
    echo "<p><strong>Administrační rozhraní je připraveno k použití.</strong></p>\n";
    echo "<p><a href='/admin' target='_blank'>Otevřít administraci</a></p>\n";
}

echo "<hr>\n";
echo "<p><em>Test dokončen: " . date('d.m.Y H:i:s') . "</em></p>\n";
?>
