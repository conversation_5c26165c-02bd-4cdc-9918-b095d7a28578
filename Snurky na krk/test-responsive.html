<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test responzivního designu - Snurky na krk</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-section {
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        
        .test-section h2 {
            color: #2c5aa0;
            margin-top: 0;
        }
        
        .iframe-container {
            position: relative;
            width: 100%;
            margin: 20px 0;
            border: 2px solid #ddd;
            border-radius: 5px;
            overflow: hidden;
        }
        
        .iframe-label {
            background: #2c5aa0;
            color: white;
            padding: 5px 10px;
            font-size: 14px;
            font-weight: bold;
        }
        
        iframe {
            width: 100%;
            border: none;
            display: block;
        }
        
        .mobile { height: 600px; }
        .tablet { height: 800px; }
        .desktop { height: 600px; }
        
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        
        .btn {
            background: #2c5aa0;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn:hover {
            background: #1e3d6f;
        }
        
        .info {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        
        .checklist {
            list-style: none;
            padding: 0;
        }
        
        .checklist li {
            padding: 5px 0;
            position: relative;
            padding-left: 25px;
        }
        
        .checklist li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #28a745;
            font-weight: bold;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .iframe-container {
                margin: 10px 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Test responzivního designu</h1>
        
        <div class="info">
            <strong>Návod k testování:</strong>
            <ul>
                <li>Otevřete různé stránky webu v rámcích níže</li>
                <li>Zkontrolujte, zda se obsah správně přizpůsobuje různým velikostem</li>
                <li>Testujte navigaci, formuláře a interaktivní prvky</li>
                <li>Ověřte čitelnost textu a použitelnost na dotykovych zařízeních</li>
            </ul>
        </div>
        
        <div class="controls">
            <a href="http://localhost:8000/" class="btn" target="mobile-frame">Hlavní stránka</a>
            <a href="http://localhost:8000/produkty" class="btn" target="mobile-frame">Produkty</a>
            <a href="http://localhost:8000/obchodnici" class="btn" target="mobile-frame">Obchodníci</a>
            <a href="http://localhost:8000/kontakt" class="btn" target="mobile-frame">Kontakt</a>
            <a href="http://localhost:8000/admin" class="btn" target="mobile-frame">Admin</a>
        </div>
        
        <div class="test-section">
            <h2>📱 Mobilní zobrazení (375px)</h2>
            <div class="iframe-container">
                <div class="iframe-label">iPhone SE / Galaxy S8 (375px × 667px)</div>
                <iframe name="mobile-frame" src="http://localhost:8000/" class="mobile" style="width: 375px; max-width: 100%;"></iframe>
            </div>
        </div>
        
        <div class="test-section">
            <h2>📱 Tablet zobrazení (768px)</h2>
            <div class="iframe-container">
                <div class="iframe-label">iPad (768px × 1024px)</div>
                <iframe name="tablet-frame" src="http://localhost:8000/" class="tablet" style="width: 768px; max-width: 100%;"></iframe>
            </div>
        </div>
        
        <div class="test-section">
            <h2>💻 Desktop zobrazení (1200px)</h2>
            <div class="iframe-container">
                <div class="iframe-label">Desktop (1200px × 800px)</div>
                <iframe name="desktop-frame" src="http://localhost:8000/" class="desktop" style="width: 1200px; max-width: 100%;"></iframe>
            </div>
        </div>
        
        <div class="test-section">
            <h2>✅ Kontrolní seznam</h2>
            <ul class="checklist">
                <li>Navigace se správně skládá na mobilních zařízeních</li>
                <li>Hamburger menu funguje na malých obrazovkách</li>
                <li>Text je čitelný na všech velikostech</li>
                <li>Tlačítka jsou dostatečně velká pro dotyk</li>
                <li>Formuláře jsou použitelné na mobilech</li>
                <li>Obrázky se správně škálují</li>
                <li>Tabulky jsou responzivní nebo scrollovatelné</li>
                <li>Footer se správně zobrazuje</li>
                <li>Admin rozhraní je použitelné na tabletech</li>
                <li>Žádné horizontální scrollování na malých obrazovkách</li>
            </ul>
        </div>
        
        <div class="test-section">
            <h2>🔧 Nástroje pro testování</h2>
            <p>Pro pokročilé testování doporučujeme:</p>
            <ul>
                <li><strong>Chrome DevTools:</strong> F12 → Device Toolbar</li>
                <li><strong>Firefox Responsive Design Mode:</strong> F12 → Responsive Design Mode</li>
                <li><strong>Online nástroje:</strong> 
                    <a href="https://responsivedesignchecker.com/" target="_blank">ResponsiveDesignChecker.com</a>
                </li>
                <li><strong>Fyzická zařízení:</strong> Testování na skutečných mobilech a tabletech</li>
            </ul>
        </div>
        
        <div class="info">
            <strong>Poznámka:</strong> Tento test zobrazuje stránky v rámcích (iframe) pro rychlé porovnání. 
            Pro nejpřesnější testování doporučujeme otevřít stránky přímo v prohlížeči a použít 
            vývojářské nástroje pro simulaci různých zařízení.
        </div>
    </div>
    
    <script>
        // Synchronizace všech iframe při kliknutí na tlačítka
        document.querySelectorAll('.btn').forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                const url = this.href;
                
                // Načti URL do všech iframe
                document.querySelectorAll('iframe').forEach(iframe => {
                    iframe.src = url;
                });
            });
        });
        
        // Přidání touch-friendly hover efektů pro mobilní zařízení
        if ('ontouchstart' in window) {
            document.querySelectorAll('.btn').forEach(btn => {
                btn.addEventListener('touchstart', function() {
                    this.style.background = '#1e3d6f';
                });
                
                btn.addEventListener('touchend', function() {
                    setTimeout(() => {
                        this.style.background = '#2c5aa0';
                    }, 150);
                });
            });
        }
    </script>
</body>
</html>
