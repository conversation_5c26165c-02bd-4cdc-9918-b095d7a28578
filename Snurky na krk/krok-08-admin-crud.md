# Krok 8: Administrace - správa produktů a obchodníků

## C<PERSON>l kroku
Implementovat kompletní CRUD (Create, Read, Update, Delete) operace pro produkty a obchodníky s pokročilými funkcemi.

## Úkoly

### 8.1 Správa produktů
- products-admin.php - seznam všech produktů
- product-form.php - formulář pro přidání/editaci
- Drag & drop řazení produktů
- Bulk operace (aktivace/deaktivace)

#### Seznam produktů:
```html
<div class="products-admin">
  <div class="admin-header">
    <h2>Správa produktů</h2>
    <a href="/admin/products/new" class="btn-primary">Přidat produkt</a>
  </div>
  
  <table class="admin-table sortable">
    <thead>
      <tr>
        <th>Pořad<PERSON></th>
        <th>O<PERSON><PERSON><PERSON><PERSON><PERSON></th>
        <th>N<PERSON><PERSON>v</th>
        <th>Popis</th>
        <th>Stav</th>
        <th>Ak<PERSON></th>
      </tr>
    </thead>
    <tbody id="products-list">
      <!-- Dynamicky načtené produkty -->
    </tbody>
  </table>
</div>
```

### 8.2 Formulář pro produkty
- Název produktu (povinné)
- Popis produktu (WYSIWYG editor)
- Upload obrázku s náhledem
- Aktivní/neaktivní stav
- Pořadí zobrazení

#### Formulář produktu:
```html
<form class="product-form" method="POST" enctype="multipart/form-data">
  <div class="form-group">
    <label for="name">Název produktu *</label>
    <input type="text" name="name" required maxlength="100">
  </div>
  
  <div class="form-group">
    <label for="description">Popis produktu</label>
    <textarea name="description" class="wysiwyg"></textarea>
  </div>
  
  <div class="form-group">
    <label for="image">Obrázek produktu</label>
    <input type="file" name="image" accept="image/*">
    <div class="image-preview"></div>
  </div>
  
  <div class="form-group">
    <label>
      <input type="checkbox" name="active" value="1">
      Aktivní produkt
    </label>
  </div>
  
  <div class="form-actions">
    <button type="submit" class="btn-primary">Uložit</button>
    <a href="/admin/products" class="btn-secondary">Zrušit</a>
  </div>
</form>
```

### 8.3 Správa obchodníků
- dealers-admin.php - seznam obchodníků
- dealer-form.php - formulář pro obchodníka
- Filtrování podle města
- Export do CSV

#### Seznam obchodníků:
```html
<div class="dealers-admin">
  <div class="admin-header">
    <h2>Správa obchodníků</h2>
    <div class="admin-actions">
      <select id="city-filter">
        <option value="">Všechna města</option>
        <!-- Dynamicky načtená města -->
      </select>
      <a href="/admin/dealers/export" class="btn-secondary">Export CSV</a>
      <a href="/admin/dealers/new" class="btn-primary">Přidat obchodníka</a>
    </div>
  </div>
  
  <div class="dealers-grid">
    <!-- Karty obchodníků -->
  </div>
</div>
```

### 8.4 Formulář pro obchodníky
- Název obchodu (povinné)
- Město (povinné)
- Kontaktní údaje
- Website URL
- Upload loga

#### Formulář obchodníka:
```html
<form class="dealer-form" method="POST" enctype="multipart/form-data">
  <div class="form-row">
    <div class="form-group">
      <label for="name">Název obchodu *</label>
      <input type="text" name="name" required maxlength="100">
    </div>
    <div class="form-group">
      <label for="city">Město *</label>
      <input type="text" name="city" required maxlength="50">
    </div>
  </div>
  
  <div class="form-row">
    <div class="form-group">
      <label for="email">E-mail</label>
      <input type="email" name="email">
    </div>
    <div class="form-group">
      <label for="phone">Telefon</label>
      <input type="tel" name="phone">
    </div>
  </div>
  
  <div class="form-group">
    <label for="website">Website</label>
    <input type="url" name="website" placeholder="https://">
  </div>
  
  <div class="form-group">
    <label for="logo">Logo obchodu</label>
    <input type="file" name="logo" accept="image/*">
    <div class="image-preview"></div>
  </div>
</form>
```

### 8.5 AJAX operace
- Inline editace (název, popis)
- Drag & drop řazení
- Rychlé aktivace/deaktivace
- Delete s potvrzením

#### JavaScript CRUD funkce:
```javascript
const CrudManager = {
  // Inline editace
  enableInlineEdit: function(element) {},
  
  // Drag & drop řazení
  initSortable: function(container) {},
  
  // Rychlé akce
  toggleActive: function(id, type) {},
  deleteItem: function(id, type) {},
  
  // Bulk operace
  bulkAction: function(action, ids) {}
};
```

### 8.6 Validace a sanitizace
- Server-side validace všech polí
- Sanitizace HTML obsahu
- Validace obrázků (typ, velikost)
- Duplicate detection

### 8.7 Image processing
- Automatické resize obrázků
- Generování thumbnailů
- WebP konverze pro lepší výkon
- Watermark (volitelně)

#### Image processing funkce:
```php
class ImageProcessor {
  public function resize($image, $maxWidth, $maxHeight) {}
  public function generateThumbnail($image, $size) {}
  public function convertToWebP($image) {}
  public function addWatermark($image, $watermark) {}
}
```

### 8.8 Export/Import funkcionalita
- Export produktů/obchodníků do CSV
- Import z CSV souboru
- Backup/restore dat
- Data migration tools

## Výstupy kroku
- ✅ Kompletní CRUD pro produkty
- ✅ Kompletní CRUD pro obchodníky
- ✅ AJAX operace a inline editace
- ✅ Drag & drop řazení
- ✅ Image processing
- ✅ Validace a sanitizace
- ✅ Export/Import funkcionalita
- ✅ Bulk operace

## Testování
- Test všech CRUD operací
- Validace formulářů
- Test upload a zpracování obrázků
- Test AJAX funkcionalita
- Test drag & drop řazení
- Test export/import
- Performance testing s velkým množstvím dat

## Následující krok
Krok 9: SEO optimalizace a finalizace frontendu
