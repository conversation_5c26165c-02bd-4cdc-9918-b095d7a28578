<!-- Detail produktu -->
<div class="container">
    <div class="row">
        <div class="col-lg-6 mb-4">
            <!-- Galerie obrázků -->
            <div class="product-gallery">
                <?php if (!empty($product['images']) && is_array($product['images'])): ?>
                <!-- <PERSON><PERSON><PERSON><PERSON> obr<PERSON>k -->
                <div class="main-image mb-3">
                    <img id="mainProductImage" 
                         src="<?= $this->url('/assets/uploads/' . $product['images'][0]) ?>" 
                         alt="<?= $this->escape($product['name']) ?>"
                         class="img-fluid rounded shadow">
                </div>
                
                <!-- Miniaturky -->
                <?php if (count($product['images']) > 1): ?>
                <div class="thumbnails">
                    <div class="row g-2">
                        <?php foreach ($product['images'] as $index => $image): ?>
                        <div class="col-3">
                            <img src="<?= $this->url('/assets/uploads/' . $image) ?>" 
                                 alt="<?= $this->escape($product['name']) ?> - obrázek <?= $index + 1 ?>"
                                 class="img-fluid rounded thumbnail-image <?= $index === 0 ? 'active' : '' ?>"
                                 data-main-src="<?= $this->url('/assets/uploads/' . $image) ?>"
                                 style="cursor: pointer; opacity: <?= $index === 0 ? '1' : '0.7' ?>;">
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>
                
                <?php elseif (!empty($product['image'])): ?>
                <!-- Jeden obrázek -->
                <div class="main-image">
                    <img src="<?= $this->url('/assets/uploads/' . $product['image']) ?>" 
                         alt="<?= $this->escape($product['name']) ?>"
                         class="img-fluid rounded shadow">
                </div>
                
                <?php else: ?>
                <!-- Placeholder -->
                <div class="main-image">
                    <img src="<?= $this->url('/assets/images/product-placeholder.svg') ?>" 
                         alt="<?= $this->escape($product['name']) ?>"
                         class="img-fluid rounded">
                </div>
                <?php endif; ?>
            </div>
        </div>
        
        <div class="col-lg-6">
            <!-- Informace o produktu -->
            <div class="product-info">
                <!-- Breadcrumb -->
                <nav aria-label="breadcrumb" class="mb-3">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item">
                            <a href="<?= $this->url('/') ?>">Domů</a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="<?= $this->url('/produkty') ?>">Produkty</a>
                        </li>
                        <?php if (!empty($product['category'])): ?>
                        <li class="breadcrumb-item">
                            <a href="<?= $this->url('/produkty?category=' . urlencode($product['category'])) ?>">
                                <?= $this->escape($product['category']) ?>
                            </a>
                        </li>
                        <?php endif; ?>
                        <li class="breadcrumb-item active" aria-current="page">
                            <?= $this->escape($product['name']) ?>
                        </li>
                    </ol>
                </nav>
                
                <!-- Název a kategorie -->
                <h1 class="mb-2"><?= $this->escape($product['name']) ?></h1>
                <p class="text-muted mb-3">
                    Kategorie: <span class="badge bg-light text-dark"><?= $this->escape($product['category']) ?></span>
                </p>
                
                <!-- Cena -->
                <div class="price-section mb-4">
                    <?php if (isset($product['sale_price']) && $product['sale_price'] < $product['price']): ?>
                    <div class="d-flex align-items-center gap-3 mb-2">
                        <span class="h3 text-danger mb-0"><?= $this->formatPrice($product['sale_price']) ?></span>
                        <span class="h5 text-muted text-decoration-line-through mb-0"><?= $this->formatPrice($product['price']) ?></span>
                        <span class="badge bg-danger">
                            -<?= round((($product['price'] - $product['sale_price']) / $product['price']) * 100) ?>%
                        </span>
                    </div>
                    <?php else: ?>
                    <span class="h3 text-primary"><?= $this->formatPrice($product['price']) ?></span>
                    <?php endif; ?>
                </div>
                
                <!-- Popis -->
                <div class="description mb-4">
                    <h5>Popis produktu</h5>
                    <p><?= nl2br($this->escape($product['description'])) ?></p>
                </div>
                
                <!-- Specifikace -->
                <?php if (!empty($product['specifications'])): ?>
                <div class="specifications mb-4">
                    <h5>Specifikace</h5>
                    <ul class="list-unstyled">
                        <?php foreach ($product['specifications'] as $key => $value): ?>
                        <li class="mb-1">
                            <strong><?= $this->escape($key) ?>:</strong> <?= $this->escape($value) ?>
                        </li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                <?php endif; ?>
                
                <!-- Dostupnost -->
                <div class="availability mb-4">
                    <h5>Dostupnost</h5>
                    <?php if (isset($product['in_stock']) && $product['in_stock']): ?>
                    <span class="badge bg-success">
                        <i class="fas fa-check me-1"></i>Skladem
                    </span>
                    <?php else: ?>
                    <span class="badge bg-warning">
                        <i class="fas fa-clock me-1"></i>Na objednávku
                    </span>
                    <?php endif; ?>
                </div>
                
                <!-- Akce -->
                <div class="product-actions">
                    <div class="d-grid gap-2 d-md-block">
                        <a href="<?= $this->url('/obchodnici') ?>" class="btn btn-primary btn-lg">
                            <i class="fas fa-store me-2"></i>
                            Najít obchodníka
                        </a>
                        <a href="<?= $this->url('/kontakt') ?>" class="btn btn-outline-primary btn-lg">
                            <i class="fas fa-envelope me-2"></i>
                            Kontaktovat nás
                        </a>
                    </div>
                    
                    <!-- Sdílení -->
                    <div class="social-share mt-3">
                        <p class="small text-muted mb-2">Sdílet:</p>
                        <div class="d-flex gap-2">
                            <a href="https://www.facebook.com/sharer/sharer.php?u=<?= urlencode($this->getCurrentUrl()) ?>" 
                               target="_blank" 
                               class="btn btn-outline-primary btn-sm">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <a href="https://twitter.com/intent/tweet?url=<?= urlencode($this->getCurrentUrl()) ?>&text=<?= urlencode($product['name']) ?>" 
                               target="_blank" 
                               class="btn btn-outline-info btn-sm">
                                <i class="fab fa-twitter"></i>
                            </a>
                            <a href="mailto:?subject=<?= urlencode($product['name']) ?>&body=<?= urlencode('Podívej se na tento produkt: ' . $this->getCurrentUrl()) ?>" 
                               class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-envelope"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Podobné produkty -->
    <?php if (!empty($relatedProducts)): ?>
    <section class="related-products mt-5 pt-5 border-top">
        <h3 class="mb-4">Podobné produkty</h3>
        <div class="row">
            <?php foreach ($relatedProducts as $relatedProduct): ?>
            <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                <div class="card product-card h-100">
                    <?php if (!empty($relatedProduct['image'])): ?>
                    <img src="<?= $this->url('/assets/uploads/' . $relatedProduct['image']) ?>" 
                         class="card-img-top" 
                         alt="<?= $this->escape($relatedProduct['name']) ?>"
                         loading="lazy">
                    <?php else: ?>
                    <img src="<?= $this->url('/assets/images/product-placeholder.svg') ?>" 
                         class="card-img-top" 
                         alt="<?= $this->escape($relatedProduct['name']) ?>"
                         loading="lazy">
                    <?php endif; ?>
                    
                    <div class="card-body d-flex flex-column">
                        <h6 class="card-title"><?= $this->escape($relatedProduct['name']) ?></h6>
                        <p class="card-text text-muted small flex-grow-1">
                            <?= $this->truncate($this->escape($relatedProduct['description']), 60) ?>
                        </p>
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="fw-bold text-primary">
                                <?= $this->formatPrice($relatedProduct['price']) ?>
                            </span>
                            <a href="<?= $this->url('/produkt/' . $relatedProduct['id']) ?>" 
                               class="btn btn-outline-primary btn-sm">
                                Detail
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </section>
    <?php endif; ?>
</div>

<!-- JavaScript pro galerii -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Přepínání obrázků v galerii
    const thumbnails = document.querySelectorAll('.thumbnail-image');
    const mainImage = document.getElementById('mainProductImage');
    
    thumbnails.forEach(thumbnail => {
        thumbnail.addEventListener('click', function() {
            // Aktualizuj hlavní obrázek
            mainImage.src = this.dataset.mainSrc;
            
            // Aktualizuj aktivní thumbnail
            thumbnails.forEach(t => {
                t.classList.remove('active');
                t.style.opacity = '0.7';
            });
            
            this.classList.add('active');
            this.style.opacity = '1';
        });
    });
});
</script>
