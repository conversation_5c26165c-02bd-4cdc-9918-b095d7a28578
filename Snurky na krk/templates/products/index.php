<!-- Produkty stránka -->
<div class="container">
    <!-- Filtry a vyhledávání -->
    <section class="filters-section py-4">
        <div class="row">
            <div class="col-lg-8">
                <h1>Produkty</h1>
                <p class="text-muted">Objevte naši širokou nabídku kvalitních šňůrek na krk</p>
            </div>
            <div class="col-lg-4">
                <!-- Vyhledávání -->
                <form method="GET" class="mb-3">
                    <div class="input-group">
                        <input type="text" 
                               class="form-control" 
                               name="search" 
                               placeholder="Hledat produkty..." 
                               value="<?= $this->escape($currentSearch) ?>">
                        <button class="btn btn-outline-primary" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                    <?php if (!empty($currentCategory)): ?>
                    <input type="hidden" name="category" value="<?= $this->escape($currentCategory) ?>">
                    <?php endif; ?>
                </form>
            </div>
        </div>
        
        <div class="row">
            <div class="col-12">
                <!-- Filtry -->
                <div class="d-flex flex-wrap gap-2 mb-4">
                    <!-- Kategorie filter -->
                    <div class="dropdown">
                        <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <?= !empty($currentCategory) ? $this->escape($currentCategory) : 'Všechny kategorie' ?>
                        </button>
                        <ul class="dropdown-menu">
                            <li>
                                <a class="dropdown-item <?= empty($currentCategory) ? 'active' : '' ?>" 
                                   href="<?= $this->url('/produkty' . (!empty($currentSearch) ? '?search=' . urlencode($currentSearch) : '')) ?>">
                                    Všechny kategorie
                                </a>
                            </li>
                            <?php foreach ($categories as $category): ?>
                            <li>
                                <a class="dropdown-item <?= $currentCategory === $category ? 'active' : '' ?>" 
                                   href="<?= $this->url('/produkty?category=' . urlencode($category) . (!empty($currentSearch) ? '&search=' . urlencode($currentSearch) : '')) ?>">
                                    <?= $this->escape($category) ?>
                                </a>
                            </li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                    
                    <!-- Řazení -->
                    <div class="dropdown">
                        <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <?php
                            $sortLabels = [
                                'name_asc' => 'Název A-Z',
                                'name_desc' => 'Název Z-A',
                                'price_asc' => 'Cena vzestupně',
                                'price_desc' => 'Cena sestupně',
                                'newest' => 'Nejnovější'
                            ];
                            echo $sortLabels[$currentSort] ?? 'Řazení';
                            ?>
                        </button>
                        <ul class="dropdown-menu">
                            <?php foreach ($sortLabels as $sortKey => $sortLabel): ?>
                            <li>
                                <a class="dropdown-item <?= $currentSort === $sortKey ? 'active' : '' ?>" 
                                   href="<?= $this->url('/produkty?' . http_build_query(array_filter(['category' => $currentCategory, 'search' => $currentSearch, 'sort' => $sortKey]))) ?>">
                                    <?= $sortLabel ?>
                                </a>
                            </li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                    
                    <!-- Vymazat filtry -->
                    <?php if (!empty($currentCategory) || !empty($currentSearch)): ?>
                    <a href="<?= $this->url('/produkty') ?>" class="btn btn-outline-danger">
                        <i class="fas fa-times me-1"></i>
                        Vymazat filtry
                    </a>
                    <?php endif; ?>
                </div>
                
                <!-- Počet výsledků -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <p class="text-muted mb-0">
                        Zobrazeno <?= count($products) ?> z <?= $pagination['total_items'] ?> produktů
                        <?php if (!empty($currentSearch)): ?>
                        pro "<?= $this->escape($currentSearch) ?>"
                        <?php endif; ?>
                        <?php if (!empty($currentCategory)): ?>
                        v kategorii "<?= $this->escape($currentCategory) ?>"
                        <?php endif; ?>
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Produkty grid -->
    <section class="products-grid">
        <?php if (!empty($products)): ?>
        <div class="row">
            <?php foreach ($products as $product): ?>
            <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                <div class="card product-card h-100">
                    <!-- Obrázek produktu -->
                    <div class="position-relative">
                        <?php if (!empty($product['image'])): ?>
                        <img src="<?= $this->url('/assets/uploads/' . $product['image']) ?>" 
                             class="card-img-top" 
                             alt="<?= $this->escape($product['name']) ?>"
                             loading="lazy">
                        <?php else: ?>
                        <img src="<?= $this->url('/assets/images/product-placeholder.svg') ?>" 
                             class="card-img-top" 
                             alt="<?= $this->escape($product['name']) ?>"
                             loading="lazy">
                        <?php endif; ?>
                        
                        <!-- Badge pro featured produkty -->
                        <?php if (isset($product['featured']) && $product['featured']): ?>
                        <span class="badge bg-primary position-absolute top-0 start-0 m-2">
                            <i class="fas fa-star me-1"></i>TOP
                        </span>
                        <?php endif; ?>
                        
                        <!-- Badge pro slevu -->
                        <?php if (isset($product['sale_price']) && $product['sale_price'] < $product['price']): ?>
                        <span class="badge bg-danger position-absolute top-0 end-0 m-2">
                            -<?= round((($product['price'] - $product['sale_price']) / $product['price']) * 100) ?>%
                        </span>
                        <?php endif; ?>
                    </div>
                    
                    <div class="card-body d-flex flex-column">
                        <h5 class="card-title"><?= $this->escape($product['name']) ?></h5>
                        <p class="card-text text-muted small flex-grow-1">
                            <?= $this->truncate($this->escape($product['description']), 80) ?>
                        </p>
                        
                        <!-- Kategorie -->
                        <div class="mb-2">
                            <span class="badge bg-light text-dark"><?= $this->escape($product['category']) ?></span>
                        </div>
                        
                        <!-- Cena -->
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <?php if (isset($product['sale_price']) && $product['sale_price'] < $product['price']): ?>
                                <span class="product-price"><?= $this->formatPrice($product['sale_price']) ?></span>
                                <span class="product-price-old"><?= $this->formatPrice($product['price']) ?></span>
                                <?php else: ?>
                                <span class="product-price"><?= $this->formatPrice($product['price']) ?></span>
                                <?php endif; ?>
                            </div>
                            <a href="<?= $this->url('/produkt/' . $product['id']) ?>" 
                               class="btn btn-primary btn-sm">
                                Detail
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        
        <!-- Stránkování -->
        <?php if ($pagination['total'] > 1): ?>
        <nav aria-label="Stránkování produktů" class="mt-5">
            <ul class="pagination justify-content-center">
                <!-- Předchozí stránka -->
                <?php if ($pagination['current'] > 1): ?>
                <li class="page-item">
                    <a class="page-link" 
                       href="<?= $this->url('/produkty?' . http_build_query(array_filter(['category' => $currentCategory, 'search' => $currentSearch, 'sort' => $currentSort, 'page' => $pagination['current'] - 1]))) ?>">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                </li>
                <?php endif; ?>
                
                <!-- Čísla stránek -->
                <?php
                $start = max(1, $pagination['current'] - 2);
                $end = min($pagination['total'], $pagination['current'] + 2);
                ?>
                
                <?php if ($start > 1): ?>
                <li class="page-item">
                    <a class="page-link" 
                       href="<?= $this->url('/produkty?' . http_build_query(array_filter(['category' => $currentCategory, 'search' => $currentSearch, 'sort' => $currentSort, 'page' => 1]))) ?>">
                        1
                    </a>
                </li>
                <?php if ($start > 2): ?>
                <li class="page-item disabled">
                    <span class="page-link">...</span>
                </li>
                <?php endif; ?>
                <?php endif; ?>
                
                <?php for ($i = $start; $i <= $end; $i++): ?>
                <li class="page-item <?= $i === $pagination['current'] ? 'active' : '' ?>">
                    <a class="page-link" 
                       href="<?= $this->url('/produkty?' . http_build_query(array_filter(['category' => $currentCategory, 'search' => $currentSearch, 'sort' => $currentSort, 'page' => $i]))) ?>">
                        <?= $i ?>
                    </a>
                </li>
                <?php endfor; ?>
                
                <?php if ($end < $pagination['total']): ?>
                <?php if ($end < $pagination['total'] - 1): ?>
                <li class="page-item disabled">
                    <span class="page-link">...</span>
                </li>
                <?php endif; ?>
                <li class="page-item">
                    <a class="page-link" 
                       href="<?= $this->url('/produkty?' . http_build_query(array_filter(['category' => $currentCategory, 'search' => $currentSearch, 'sort' => $currentSort, 'page' => $pagination['total']]))) ?>">
                        <?= $pagination['total'] ?>
                    </a>
                </li>
                <?php endif; ?>
                
                <!-- Další stránka -->
                <?php if ($pagination['current'] < $pagination['total']): ?>
                <li class="page-item">
                    <a class="page-link" 
                       href="<?= $this->url('/produkty?' . http_build_query(array_filter(['category' => $currentCategory, 'search' => $currentSearch, 'sort' => $currentSort, 'page' => $pagination['current'] + 1]))) ?>">
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
                <?php endif; ?>
            </ul>
        </nav>
        <?php endif; ?>
        
        <?php else: ?>
        <!-- Žádné produkty -->
        <div class="text-center py-5">
            <i class="fas fa-search fa-3x text-muted mb-3"></i>
            <h3>Žádné produkty nenalezeny</h3>
            <p class="text-muted">
                <?php if (!empty($currentSearch) || !empty($currentCategory)): ?>
                Zkuste změnit kritéria vyhledávání nebo <a href="<?= $this->url('/produkty') ?>">zobrazit všechny produkty</a>.
                <?php else: ?>
                V současné době nemáme žádné produkty k dispozici.
                <?php endif; ?>
            </p>
        </div>
        <?php endif; ?>
    </section>
</div>
