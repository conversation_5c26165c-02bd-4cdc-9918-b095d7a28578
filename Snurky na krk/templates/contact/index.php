<!-- Konta<PERSON><PERSON><PERSON> stránka -->
<div class="container">
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <!-- Header -->
            <div class="text-center mb-5">
                <h1>Kontakt</h1>
                <p class="lead text-muted">
                    M<PERSON>te dotazy? Rádi vám pomůžeme!
                </p>
            </div>
        </div>
    </div>
    
    <div class="row">
        <!-- Kontaktní informace -->
        <div class="col-lg-4 mb-5">
            <div class="card h-100">
                <div class="card-body">
                    <h3 class="card-title">
                        <i class="fas fa-address-card me-2 text-primary"></i>
                        Kontaktní údaje
                    </h3>
                    
                    <?php if (!empty($contactInfo['company_name'])): ?>
                    <div class="mb-3">
                        <h5><?= $this->escape($contactInfo['company_name']) ?></h5>
                    </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($contactInfo['address'])): ?>
                    <div class="mb-3">
                        <h6><i class="fas fa-map-marker-alt me-2"></i>Adresa</h6>
                        <p class="text-muted mb-0">
                            <?= nl2br($this->escape($contactInfo['address'])) ?>
                        </p>
                    </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($contactInfo['email'])): ?>
                    <div class="mb-3">
                        <h6><i class="fas fa-envelope me-2"></i>Email</h6>
                        <p class="mb-0">
                            <a href="mailto:<?= $this->escape($contactInfo['email']) ?>" class="text-decoration-none">
                                <?= $this->escape($contactInfo['email']) ?>
                            </a>
                        </p>
                    </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($contactInfo['phone'])): ?>
                    <div class="mb-3">
                        <h6><i class="fas fa-phone me-2"></i>Telefon</h6>
                        <p class="mb-0">
                            <a href="tel:<?= $this->escape($contactInfo['phone']) ?>" class="text-decoration-none">
                                <?= $this->escape($contactInfo['phone']) ?>
                            </a>
                        </p>
                    </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($contactInfo['business_hours'])): ?>
                    <div class="mb-3">
                        <h6><i class="fas fa-clock me-2"></i>Otevírací doba</h6>
                        <p class="text-muted mb-0">
                            <?= nl2br($this->escape($contactInfo['business_hours'])) ?>
                        </p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Rychlé odkazy -->
            <div class="card mt-4">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="fas fa-link me-2 text-primary"></i>
                        Rychlé odkazy
                    </h5>
                    <div class="d-grid gap-2">
                        <a href="<?= $this->url('/produkty') ?>" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-shopping-bag me-2"></i>
                            Naše produkty
                        </a>
                        <a href="<?= $this->url('/obchodnici') ?>" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-store me-2"></i>
                            Najít obchodníka
                        </a>
                        <a href="<?= $this->url('/obchodnici/registrace') ?>" class="btn btn-outline-success btn-sm">
                            <i class="fas fa-handshake me-2"></i>
                            Stát se obchodníkem
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Kontaktní formulář -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-envelope me-2"></i>
                        Napište nám
                    </h3>
                </div>
                <div class="card-body">
                    <form method="POST" data-validate>
                        <?= $this->csrf() ?>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">Jméno a příjmení *</label>
                                    <input type="text" 
                                           class="form-control <?= isset($errors['name']) ? 'is-invalid' : '' ?>" 
                                           id="name" 
                                           name="name" 
                                           value="<?= $this->escape($formData['name'] ?? '') ?>"
                                           required>
                                    <?php if (isset($errors['name'])): ?>
                                    <div class="invalid-feedback">
                                        <?= $this->escape($errors['name']) ?>
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="email" class="form-label">Email *</label>
                                    <input type="email" 
                                           class="form-control <?= isset($errors['email']) ? 'is-invalid' : '' ?>" 
                                           id="email" 
                                           name="email" 
                                           value="<?= $this->escape($formData['email'] ?? '') ?>"
                                           required>
                                    <?php if (isset($errors['email'])): ?>
                                    <div class="invalid-feedback">
                                        <?= $this->escape($errors['email']) ?>
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="phone" class="form-label">Telefon</label>
                                    <input type="tel" 
                                           class="form-control <?= isset($errors['phone']) ? 'is-invalid' : '' ?>" 
                                           id="phone" 
                                           name="phone" 
                                           value="<?= $this->escape($formData['phone'] ?? '') ?>">
                                    <?php if (isset($errors['phone'])): ?>
                                    <div class="invalid-feedback">
                                        <?= $this->escape($errors['phone']) ?>
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="subject" class="form-label">Předmět *</label>
                                    <select class="form-select <?= isset($errors['subject']) ? 'is-invalid' : '' ?>" 
                                            id="subject" 
                                            name="subject" 
                                            required>
                                        <option value="">Vyberte předmět</option>
                                        <option value="general" <?= ($formData['subject'] ?? '') === 'general' ? 'selected' : '' ?>>
                                            Obecný dotaz
                                        </option>
                                        <option value="products" <?= ($formData['subject'] ?? '') === 'products' ? 'selected' : '' ?>>
                                            Dotaz k produktům
                                        </option>
                                        <option value="dealer" <?= ($formData['subject'] ?? '') === 'dealer' ? 'selected' : '' ?>>
                                            Obchodní spolupráce
                                        </option>
                                        <option value="complaint" <?= ($formData['subject'] ?? '') === 'complaint' ? 'selected' : '' ?>>
                                            Reklamace
                                        </option>
                                        <option value="technical" <?= ($formData['subject'] ?? '') === 'technical' ? 'selected' : '' ?>>
                                            Technická podpora
                                        </option>
                                        <option value="other" <?= ($formData['subject'] ?? '') === 'other' ? 'selected' : '' ?>>
                                            Ostatní
                                        </option>
                                    </select>
                                    <?php if (isset($errors['subject'])): ?>
                                    <div class="invalid-feedback">
                                        <?= $this->escape($errors['subject']) ?>
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="message" class="form-label">Zpráva *</label>
                            <textarea class="form-control <?= isset($errors['message']) ? 'is-invalid' : '' ?>" 
                                      id="message" 
                                      name="message" 
                                      rows="6" 
                                      placeholder="Napište nám váš dotaz nebo zprávu..."
                                      required><?= $this->escape($formData['message'] ?? '') ?></textarea>
                            <?php if (isset($errors['message'])): ?>
                            <div class="invalid-feedback">
                                <?= $this->escape($errors['message']) ?>
                            </div>
                            <?php endif; ?>
                            <div class="form-text">Minimálně 10 znaků</div>
                        </div>
                        
                        <!-- GDPR souhlas -->
                        <div class="mb-4">
                            <div class="form-check">
                                <input class="form-check-input <?= isset($errors['privacy_consent']) ? 'is-invalid' : '' ?>" 
                                       type="checkbox" 
                                       id="privacy_consent" 
                                       name="privacy_consent" 
                                       value="1"
                                       <?= isset($formData['privacy_consent']) ? 'checked' : '' ?>
                                       required>
                                <label class="form-check-label" for="privacy_consent">
                                    Souhlasím se zpracováním osobních údajů podle 
                                    <a href="<?= $this->url('/ochrana-udaju') ?>" target="_blank">zásad ochrany osobních údajů</a> *
                                </label>
                                <?php if (isset($errors['privacy_consent'])): ?>
                                <div class="invalid-feedback">
                                    <?= $this->escape($errors['privacy_consent']) ?>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <!-- Newsletter -->
                        <div class="mb-4">
                            <div class="form-check">
                                <input class="form-check-input" 
                                       type="checkbox" 
                                       id="newsletter" 
                                       name="newsletter" 
                                       value="1"
                                       <?= isset($formData['newsletter']) ? 'checked' : '' ?>>
                                <label class="form-check-label" for="newsletter">
                                    Chci dostávat novinky a informace o produktech emailem
                                </label>
                            </div>
                        </div>
                        
                        <!-- Tlačítka -->
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <button type="reset" class="btn btn-outline-secondary">
                                <i class="fas fa-undo me-2"></i>
                                Vymazat
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane me-2"></i>
                                Odeslat zprávu
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- FAQ sekce -->
            <div class="card mt-4">
                <div class="card-header">
                    <h4 class="card-title mb-0">
                        <i class="fas fa-question-circle me-2"></i>
                        Často kladené dotazy
                    </h4>
                </div>
                <div class="card-body">
                    <div class="accordion" id="faqAccordion">
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
                                    Jak dlouho trvá dodání produktů?
                                </button>
                            </h2>
                            <div id="faq1" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    Dodací lhůta závisí na dostupnosti produktu a způsobu dodání. 
                                    Standardně dodáváme do 3-5 pracovních dnů po objednání.
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq2">
                                    Mohu si objednat produkty přímo od vás?
                                </button>
                            </h2>
                            <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    Naše produkty prodáváme výhradně prostřednictvím sítě autorizovaných obchodníků. 
                                    Najděte si nejbližšího obchodníka v sekci "Obchodníci".
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq3">
                                    Jak se mohu stát obchodníkem?
                                </button>
                            </h2>
                            <div id="faq3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    Vyplňte registrační formulář v sekci "Registrace obchodníka". 
                                    Vaše žádost posoudíme do 3 pracovních dnů a budeme vás kontaktovat.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
