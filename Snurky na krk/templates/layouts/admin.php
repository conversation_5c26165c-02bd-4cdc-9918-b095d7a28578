<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($title ?? 'Administrace') ?> - SnurkyNaKrk.cz</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    
    <!-- Admin CSS -->
    <link href="<?= BASE_URL ?>/assets/css/admin.css" rel="stylesheet">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="<?= BASE_URL ?>/assets/images/favicon.svg">
</head>
<body class="admin-body">
    
    <!-- Admin Header -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark admin-navbar">
        <div class="container-fluid">
            <a class="navbar-brand" href="/admin/dashboard">
                <i class="fas fa-cogs me-2"></i>
                Administrace
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#adminNavbar">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="adminNavbar">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link <?= (strpos($_SERVER['REQUEST_URI'], '/admin/dashboard') !== false) ? 'active' : '' ?>"
                           href="/admin/dashboard">
                            <i class="fas fa-tachometer-alt me-1"></i>
                            Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?= (strpos($_SERVER['REQUEST_URI'], '/admin/produkty') !== false) ? 'active' : '' ?>"
                           href="/admin/produkty">
                            <i class="fas fa-box me-1"></i>
                            Produkty
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?= (strpos($_SERVER['REQUEST_URI'], '/admin/obchodnici') !== false) ? 'active' : '' ?>"
                           href="/admin/obchodnici">
                            <i class="fas fa-users me-1"></i>
                            Obchodníci
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?= (strpos($_SERVER['REQUEST_URI'], '/admin/nastaveni') !== false) ? 'active' : '' ?>"
                           href="/admin/nastaveni">
                            <i class="fas fa-cog me-1"></i>
                            Nastavení
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>
                            <?= htmlspecialchars($_SESSION['admin_username'] ?? 'Admin') ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li>
                                <a class="dropdown-item" href="<?= BASE_URL ?>" target="_blank">
                                    <i class="fas fa-external-link-alt me-2"></i>
                                    Zobrazit web
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item text-danger" href="/admin/logout">
                                    <i class="fas fa-sign-out-alt me-2"></i>
                                    Odhlásit se
                                </a>
                            </li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    
    <!-- Main Content -->
    <div class="container-fluid admin-container">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link <?= (strpos($_SERVER['REQUEST_URI'], '/admin/dashboard') !== false) ? 'active' : '' ?>"
                               href="/admin/dashboard">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?= (strpos($_SERVER['REQUEST_URI'], '/admin/produkty') !== false) ? 'active' : '' ?>"
                               href="/admin/produkty">
                                <i class="fas fa-box me-2"></i>
                                Produkty
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?= (strpos($_SERVER['REQUEST_URI'], '/admin/obchodnici') !== false) ? 'active' : '' ?>"
                               href="/admin/obchodnici">
                                <i class="fas fa-users me-2"></i>
                                Obchodníci
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?= (strpos($_SERVER['REQUEST_URI'], '/admin/nastaveni') !== false) ? 'active' : '' ?>"
                               href="/admin/nastaveni">
                                <i class="fas fa-cog me-2"></i>
                                Nastavení
                            </a>
                        </li>
                    </ul>
                    
                    <hr>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="<?= BASE_URL ?>" target="_blank">
                                <i class="fas fa-external-link-alt me-2"></i>
                                Zobrazit web
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-danger" href="/admin/logout">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                Odhlásit se
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
            
            <!-- Page Content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 admin-main">
                
                <!-- Flash Messages -->
                <?php if (isset($_SESSION['flash_messages']) && !empty($_SESSION['flash_messages'])): ?>
                    <?php foreach ($_SESSION['flash_messages'] as $flashMessage): ?>
                        <div class="alert alert-<?= $flashMessage['type'] ?> alert-dismissible fade show mt-3" role="alert">
                            <?= htmlspecialchars($flashMessage['message']) ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endforeach; ?>
                    <?php unset($_SESSION['flash_messages']); ?>
                <?php endif; ?>
                
                <!-- Error Message -->
                <?php if (isset($error)): ?>
                    <div class="alert alert-danger alert-dismissible fade show mt-3" role="alert">
                        <?= htmlspecialchars($error) ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <!-- Page Header -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2"><?= htmlspecialchars($title ?? 'Administrace') ?></h1>
                    
                    <?php if (isset($headerActions)): ?>
                        <div class="btn-toolbar mb-2 mb-md-0">
                            <?= $headerActions ?>
                        </div>
                    <?php endif; ?>
                </div>
                
                <!-- Page Content -->
                <?= $content ?>
                
            </main>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Admin JS -->
    <script src="<?= BASE_URL ?>/assets/js/admin.js"></script>
    
    <!-- Page specific scripts -->
    <?php if (isset($scripts)): ?>
        <?= $scripts ?>
    <?php endif; ?>
    
</body>
</html>
