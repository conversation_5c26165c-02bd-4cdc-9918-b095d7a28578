<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $this->escape($title ?? 'Šňůrky na krk') ?></title>
    
    <?php if (isset($description) && !empty($description)): ?>
    <meta name="description" content="<?= $this->escape($description) ?>">
    <?php endif; ?>
    
    <meta name="keywords" content="šňůrky, krk, módní do<PERSON>, obchodníci">
    <meta name="author" content="Šňůrky na krk">

    <!-- SEO meta tagy -->
    <meta property="og:title" content="<?= $this->escape($title ?? 'Šňůrky na krk') ?>">
    <meta property="og:description" content="<?= $this->escape($description ?? 'Kvalitní šňůrky na krk pro každou příležitost') ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?= $this->getCurrentUrl() ?>">
    <meta property="og:site_name" content="Šňůrky na krk">
    <meta property="og:locale" content="cs_CZ">

    <!-- Twitter Card -->
    <meta name="twitter:card" content="summary">
    <meta name="twitter:title" content="<?= $this->escape($title ?? 'Šňůrky na krk') ?>">
    <meta name="twitter:description" content="<?= $this->escape($description ?? 'Kvalitní šňůrky na krk pro každou příležitost') ?>">

    <!-- Canonical URL -->
    <link rel="canonical" href="<?= $this->getCurrentUrl() ?>">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.6/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-4bw+/aepP/YC94hEpVNVgiZdgIC5+VKNBQNGCHeKRQN+PtmoHDEXuppvnDJzQIu9" crossorigin="anonymous">
    <link rel="stylesheet" href="<?= $this->url('/assets/css/style.css') ?>">

    <!-- Font Awesome pro ikony -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous" referrerpolicy="no-referrer">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="<?= $this->url('/assets/images/favicon.svg') ?>">
    <link rel="alternate icon" href="<?= $this->url('/assets/images/favicon.svg') ?>">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="navbar navbar-expand-lg navbar-light bg-light">
            <div class="container">
                <a class="navbar-brand" href="<?= $this->url('/') ?>">
                    <img src="<?= $this->url('/assets/images/logo.svg') ?>" alt="Šňůrky na krk" height="40">
                </a>
                
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>
                
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link" href="<?= $this->url('/') ?>">Domů</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?= $this->url('/produkty') ?>">Produkty</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="https://czechimage.cz" target="_blank">
                                Nakupovat <i class="fas fa-external-link-alt fa-sm"></i>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?= $this->url('/kontakt') ?>">Kontakt</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?= $this->url('/o-nas') ?>">O nás</a>
                        </li>
                    </ul>
                    
                    <!-- Vyhledávání -->
                    <form class="d-flex me-3" action="<?= $this->url('/produkty') ?>" method="GET">
                        <input class="form-control me-2" type="search" name="search" placeholder="Hledat produkty..." 
                               value="<?= $this->escape($_GET['search'] ?? '') ?>">
                        <button class="btn btn-outline-primary" type="submit">Hledat</button>
                    </form>
                    
                    <!-- Admin link -->
                    <a href="<?= $this->url('/admin') ?>" class="btn btn-sm btn-outline-secondary">Admin</a>
                </div>
            </div>
        </nav>
    </header>

    <!-- Flash zprávy -->
    <?php $flashMessages = $this->getGlobalData('flash_messages') ?? []; ?>
    <?php if (!empty($flashMessages)): ?>
    <div class="container mt-3">
        <?php foreach ($flashMessages as $message): ?>
        <div class="alert alert-<?= $message['type'] === 'error' ? 'danger' : $message['type'] ?> alert-dismissible fade show">
            <?= $this->escape($message['message']) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endforeach; ?>
    </div>
    <?php endif; ?>

    <!-- Breadcrumb -->
    <?php if (isset($breadcrumb) && !empty($breadcrumb)): ?>
    <div class="container mt-3">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <?php foreach ($breadcrumb as $item): ?>
                    <?php if (is_array($item)): ?>
                        <li class="breadcrumb-item">
                            <a href="<?= $this->escape($item['url']) ?>"><?= $this->escape($item['title']) ?></a>
                        </li>
                    <?php else: ?>
                        <li class="breadcrumb-item active" aria-current="page"><?= $this->escape($item) ?></li>
                    <?php endif; ?>
                <?php endforeach; ?>
            </ol>
        </nav>
    </div>
    <?php endif; ?>

    <!-- Main Content -->
    <main class="main-content">
        <?= $content ?>
    </main>

    <!-- Footer -->
    <footer class="footer bg-dark text-light mt-5">
        <div class="container py-4">
            <div class="row">
                <div class="col-md-4">
                    <h5>Šňůrky na krk</h5>
                    <p>Podpůrný web pro czechimage.cz. Objevte naši kolekci kvalitních šňůrek na krk a dalších módních doplňků.</p>
                    <a href="https://czechimage.cz" class="btn btn-outline-light btn-sm" target="_blank">
                        Hlavní obchod czechimage.cz
                    </a>
                </div>
                
                <div class="col-md-4">
                    <h5>Rychlé odkazy</h5>
                    <ul class="list-unstyled">
                        <li><a href="<?= $this->url('/produkty') ?>" class="text-light">Kolekce produktů</a></li>
                        <li><a href="https://czechimage.cz" class="text-light" target="_blank">Nakupovat online</a></li>
                        <li><a href="<?= $this->url('/kontakt') ?>" class="text-light">Kontakt</a></li>
                        <li><a href="<?= $this->url('/o-nas') ?>" class="text-light">O značce</a></li>
                    </ul>
                </div>
                
                <div class="col-md-4">
                    <h5>Kontakt</h5>
                    <?php $contactInfo = $this->getGlobalData('contact_info') ?? []; ?>
                    <?php if (!empty($contactInfo['email'])): ?>
                    <p><i class="fas fa-envelope"></i> <?= $this->escape($contactInfo['email']) ?></p>
                    <?php endif; ?>
                    <?php if (!empty($contactInfo['phone'])): ?>
                    <p><i class="fas fa-phone"></i> <?= $this->escape($contactInfo['phone']) ?></p>
                    <?php endif; ?>
                </div>
            </div>
            
            <hr class="my-4">
            
            <div class="row">
                <div class="col-md-6">
                    <p>&copy; <?= date('Y') ?> Šňůrky na krk. Všechna práva vyhrazena.</p>
                </div>
                <div class="col-md-6 text-end">
                    <a href="<?= $this->url('/obchodni-podminky') ?>" class="text-light me-3">Obchodní podmínky</a>
                    <a href="<?= $this->url('/ochrana-udaju') ?>" class="text-light">Ochrana údajů</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.6/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>
    <script src="<?= $this->url('/assets/js/app.js') ?>"></script>
    
    <?php if (isset($additionalJs)): ?>
        <?= $additionalJs ?>
    <?php endif; ?>

    <!-- Strukturovaná data JSON-LD -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "Šňůrky na krk",
        "url": "<?= $this->url('/') ?>",
        "logo": "<?= $this->url('/assets/images/logo.svg') ?>",
        "description": "Kvalitní šňůrky na krk pro každou příležitost. Najděte si svého obchodníka v celé České republice.",
        "contactPoint": {
            "@type": "ContactPoint",
            "telephone": "<?= $this->escape($this->getGlobalData('contact_info')['phone'] ?? '') ?>",
            "email": "<?= $this->escape($this->getGlobalData('contact_info')['email'] ?? '') ?>",
            "contactType": "customer service",
            "availableLanguage": "Czech"
        },
        "address": {
            "@type": "PostalAddress",
            "addressCountry": "CZ",
            "addressLocality": "Praha"
        },
        "sameAs": [
            "https://www.facebook.com/snurkynakrk",
            "https://www.instagram.com/snurkynakrk"
        ]
    }
    </script>

    <?php if (isset($structuredData)): ?>
    <!-- Dodatečná strukturovaná data -->
    <script type="application/ld+json">
    <?= json_encode($structuredData, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES) ?>
    </script>
    <?php endif; ?>
</body>
</html>
