<?php
/**
 * Admin šablona pro nastavení aplikace
 *
 * <AUTHOR> Team
 * @version 1.0
 */

if (!defined('SNURKY_INIT')) {
    die('Přímý přístup není povolen');
}
?>

<div class="container-fluid">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="/admin/dashboard">Dashboard</a></li>
            <li class="breadcrumb-item active" aria-current="page">Nastavení</li>
        </ol>
    </nav>

    <!-- Hlavička -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-cog me-2"></i>
            Nastavení aplikace
        </h1>
    </div>

    <!-- Flash zprávy -->
    <?php if ($flash = $this->getFlashMessage()): ?>
        <div class="alert alert-<?= $flash['type'] === 'error' ? 'danger' : $flash['type'] ?> alert-dismissible fade show" role="alert">
            <?= htmlspecialchars($flash['message']) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <!-- Navigace v nastavení -->
        <div class="col-lg-3">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-list me-2"></i>
                        Kategorie nastavení
                    </h6>
                </div>
                <div class="list-group list-group-flush">
                    <a href="#web-settings" class="list-group-item list-group-item-action active" data-bs-toggle="pill">
                        <i class="fas fa-globe me-2"></i>
                        Webové nastavení
                    </a>
                    <a href="#business-settings" class="list-group-item list-group-item-action" data-bs-toggle="pill">
                        <i class="fas fa-store me-2"></i>
                        Obchodní nastavení
                    </a>
                    <a href="#email-settings" class="list-group-item list-group-item-action" data-bs-toggle="pill">
                        <i class="fas fa-envelope me-2"></i>
                        Email nastavení
                    </a>
                    <a href="#admin-settings" class="list-group-item list-group-item-action" data-bs-toggle="pill">
                        <i class="fas fa-user-shield me-2"></i>
                        Admin nastavení
                    </a>
                    <a href="#backup-settings" class="list-group-item list-group-item-action" data-bs-toggle="pill">
                        <i class="fas fa-database me-2"></i>
                        Zálohy a údržba
                    </a>
                </div>
            </div>
        </div>

        <!-- Obsah nastavení -->
        <div class="col-lg-9">
            <form method="POST" enctype="multipart/form-data" id="settingsForm">
                <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($csrf_token) ?>">

                <div class="tab-content">
                    <!-- Webové nastavení -->
                    <div class="tab-pane fade show active" id="web-settings">
                        <div class="card shadow">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">
                                    <i class="fas fa-globe me-2"></i>
                                    Webové nastavení
                                </h6>
                            </div>
                            <div class="card-body">
                                <!-- Základní informace -->
                                <div class="mb-4">
                                    <h6 class="text-primary mb-3">Základní informace</h6>

                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="site_title" class="form-label">Název webu</label>
                                            <input type="text" class="form-control" id="site_title" name="web[site_title]"
                                                   value="<?= htmlspecialchars($settings['web']['site_title'] ?? '') ?>">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="site_tagline" class="form-label">Slogan</label>
                                            <input type="text" class="form-control" id="site_tagline" name="web[site_tagline]"
                                                   value="<?= htmlspecialchars($settings['web']['site_tagline'] ?? '') ?>">
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="site_description" class="form-label">Popis webu</label>
                                        <textarea class="form-control" id="site_description" name="web[site_description]" rows="3"><?= htmlspecialchars($settings['web']['site_description'] ?? '') ?></textarea>
                                        <div class="form-text">Používá se pro SEO meta description</div>
                                    </div>

                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="site_keywords" class="form-label">Klíčová slova</label>
                                            <input type="text" class="form-control" id="site_keywords" name="web[site_keywords]"
                                                   value="<?= htmlspecialchars($settings['web']['site_keywords'] ?? '') ?>">
                                            <div class="form-text">Oddělte čárkami</div>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="site_language" class="form-label">Jazyk webu</label>
                                            <select class="form-select" id="site_language" name="web[site_language]">
                                                <option value="cs" <?= ($settings['web']['site_language'] ?? 'cs') === 'cs' ? 'selected' : '' ?>>Čeština</option>
                                                <option value="sk" <?= ($settings['web']['site_language'] ?? '') === 'sk' ? 'selected' : '' ?>>Slovenština</option>
                                                <option value="en" <?= ($settings['web']['site_language'] ?? '') === 'en' ? 'selected' : '' ?>>Angličtina</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <!-- Logo a favicon -->
                                <div class="mb-4">
                                    <h6 class="text-primary mb-3">Logo a favicon</h6>

                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="logo" class="form-label">Logo</label>
                                            <?php if (!empty($settings['web']['logo'])): ?>
                                                <div class="mb-2">
                                                    <img src="<?= htmlspecialchars($settings['web']['logo']) ?>"
                                                         alt="Aktuální logo" class="img-thumbnail" style="max-height: 100px;">
                                                </div>
                                            <?php endif; ?>
                                            <input type="file" class="form-control" id="logo" name="logo" accept="image/*">
                                            <div class="form-text">Doporučená velikost: 200x60px, formát PNG nebo SVG</div>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="favicon" class="form-label">Favicon</label>
                                            <?php if (!empty($settings['web']['favicon'])): ?>
                                                <div class="mb-2">
                                                    <img src="<?= htmlspecialchars($settings['web']['favicon']) ?>"
                                                         alt="Aktuální favicon" class="img-thumbnail" style="max-height: 32px;">
                                                </div>
                                            <?php endif; ?>
                                            <input type="file" class="form-control" id="favicon" name="favicon" accept="image/*">
                                            <div class="form-text">Velikost: 32x32px nebo 16x16px, formát ICO nebo PNG</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Kontaktní údaje -->
                                <div class="mb-4">
                                    <h6 class="text-primary mb-3">Kontaktní údaje</h6>

                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="contact_email" class="form-label">Kontaktní email</label>
                                            <input type="email" class="form-control" id="contact_email" name="web[contact_email]"
                                                   value="<?= htmlspecialchars($settings['web']['contact_email'] ?? '') ?>">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="contact_phone" class="form-label">Kontaktní telefon</label>
                                            <input type="tel" class="form-control" id="contact_phone" name="web[contact_phone]"
                                                   value="<?= htmlspecialchars($settings['web']['contact_phone'] ?? '') ?>">
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="contact_address" class="form-label">Adresa</label>
                                        <textarea class="form-control" id="contact_address" name="web[contact_address]" rows="2"><?= htmlspecialchars($settings['web']['contact_address'] ?? '') ?></textarea>
                                    </div>
                                </div>

                                <!-- Sociální sítě -->
                                <div class="mb-4">
                                    <h6 class="text-primary mb-3">Sociální sítě</h6>

                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="facebook_url" class="form-label">Facebook URL</label>
                                            <input type="url" class="form-control" id="facebook_url" name="web[facebook_url]"
                                                   value="<?= htmlspecialchars($settings['web']['facebook_url'] ?? '') ?>">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="instagram_url" class="form-label">Instagram URL</label>
                                            <input type="url" class="form-control" id="instagram_url" name="web[instagram_url]"
                                                   value="<?= htmlspecialchars($settings['web']['instagram_url'] ?? '') ?>">
                                        </div>
                                    </div>

                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="youtube_url" class="form-label">YouTube URL</label>
                                            <input type="url" class="form-control" id="youtube_url" name="web[youtube_url]"
                                                   value="<?= htmlspecialchars($settings['web']['youtube_url'] ?? '') ?>">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="linkedin_url" class="form-label">LinkedIn URL</label>
                                            <input type="url" class="form-control" id="linkedin_url" name="web[linkedin_url]"
                                                   value="<?= htmlspecialchars($settings['web']['linkedin_url'] ?? '') ?>">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Obchodní nastavení -->
                    <div class="tab-pane fade" id="business-settings">
                        <div class="card shadow">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">
                                    <i class="fas fa-store me-2"></i>
                                    Obchodní nastavení
                                </h6>
                            </div>
                            <div class="card-body">
                                <!-- Základní obchodní údaje -->
                                <div class="mb-4">
                                    <h6 class="text-primary mb-3">Základní obchodní údaje</h6>

                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="company_name" class="form-label">Název firmy</label>
                                            <input type="text" class="form-control" id="company_name" name="business[company_name]"
                                                   value="<?= htmlspecialchars($settings['business']['company_name'] ?? '') ?>">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="company_ico" class="form-label">IČO</label>
                                            <input type="text" class="form-control" id="company_ico" name="business[company_ico]"
                                                   value="<?= htmlspecialchars($settings['business']['company_ico'] ?? '') ?>">
                                        </div>
                                    </div>

                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="company_dic" class="form-label">DIČ</label>
                                            <input type="text" class="form-control" id="company_dic" name="business[company_dic]"
                                                   value="<?= htmlspecialchars($settings['business']['company_dic'] ?? '') ?>">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="vat_registered" class="form-label">Plátce DPH</label>
                                            <select class="form-select" id="vat_registered" name="business[vat_registered]">
                                                <option value="0" <?= ($settings['business']['vat_registered'] ?? '0') === '0' ? 'selected' : '' ?>>Ne</option>
                                                <option value="1" <?= ($settings['business']['vat_registered'] ?? '0') === '1' ? 'selected' : '' ?>>Ano</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <!-- Cenové nastavení -->
                                <div class="mb-4">
                                    <h6 class="text-primary mb-3">Cenové nastavení</h6>

                                    <div class="row mb-3">
                                        <div class="col-md-4">
                                            <label for="currency" class="form-label">Měna</label>
                                            <select class="form-select" id="currency" name="business[currency]">
                                                <option value="CZK" <?= ($settings['business']['currency'] ?? 'CZK') === 'CZK' ? 'selected' : '' ?>>CZK (Koruna česká)</option>
                                                <option value="EUR" <?= ($settings['business']['currency'] ?? '') === 'EUR' ? 'selected' : '' ?>>EUR (Euro)</option>
                                                <option value="USD" <?= ($settings['business']['currency'] ?? '') === 'USD' ? 'selected' : '' ?>>USD (Americký dolar)</option>
                                            </select>
                                        </div>
                                        <div class="col-md-4">
                                            <label for="vat_rate" class="form-label">Sazba DPH (%)</label>
                                            <input type="number" class="form-control" id="vat_rate" name="business[vat_rate]"
                                                   value="<?= htmlspecialchars($settings['business']['vat_rate'] ?? '21') ?>"
                                                   min="0" max="100" step="0.1">
                                        </div>
                                        <div class="col-md-4">
                                            <label for="default_commission" class="form-label">Výchozí provize (%)</label>
                                            <input type="number" class="form-control" id="default_commission" name="business[default_commission]"
                                                   value="<?= htmlspecialchars($settings['business']['default_commission'] ?? '10') ?>"
                                                   min="0" max="100" step="0.1">
                                        </div>
                                    </div>
                                </div>

                                <!-- Objednávky -->
                                <div class="mb-4">
                                    <h6 class="text-primary mb-3">Nastavení objednávek</h6>

                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="min_order_amount" class="form-label">Minimální částka objednávky</label>
                                            <input type="number" class="form-control" id="min_order_amount" name="business[min_order_amount]"
                                                   value="<?= htmlspecialchars($settings['business']['min_order_amount'] ?? '0') ?>"
                                                   min="0" step="0.01">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="free_shipping_limit" class="form-label">Doprava zdarma od</label>
                                            <input type="number" class="form-control" id="free_shipping_limit" name="business[free_shipping_limit]"
                                                   value="<?= htmlspecialchars($settings['business']['free_shipping_limit'] ?? '1000') ?>"
                                                   min="0" step="0.01">
                                        </div>
                                    </div>

                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="shipping_cost" class="form-label">Cena dopravy</label>
                                            <input type="number" class="form-control" id="shipping_cost" name="business[shipping_cost]"
                                                   value="<?= htmlspecialchars($settings['business']['shipping_cost'] ?? '99') ?>"
                                                   min="0" step="0.01">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="order_prefix" class="form-label">Prefix čísla objednávky</label>
                                            <input type="text" class="form-control" id="order_prefix" name="business[order_prefix]"
                                                   value="<?= htmlspecialchars($settings['business']['order_prefix'] ?? 'SN') ?>"
                                                   maxlength="5">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Email nastavení -->
                    <div class="tab-pane fade" id="email-settings">
                        <div class="card shadow">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">
                                    <i class="fas fa-envelope me-2"></i>
                                    Email nastavení
                                </h6>
                            </div>
                            <div class="card-body">
                                <!-- SMTP nastavení -->
                                <div class="mb-4">
                                    <h6 class="text-primary mb-3">SMTP nastavení</h6>

                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="smtp_host" class="form-label">SMTP server</label>
                                            <input type="text" class="form-control" id="smtp_host" name="email[smtp_host]"
                                                   value="<?= htmlspecialchars($settings['email']['smtp_host'] ?? '') ?>"
                                                   placeholder="smtp.gmail.com">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="smtp_port" class="form-label">SMTP port</label>
                                            <input type="number" class="form-control" id="smtp_port" name="email[smtp_port]"
                                                   value="<?= htmlspecialchars($settings['email']['smtp_port'] ?? '587') ?>"
                                                   min="1" max="65535">
                                        </div>
                                    </div>

                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="smtp_username" class="form-label">SMTP uživatelské jméno</label>
                                            <input type="text" class="form-control" id="smtp_username" name="email[smtp_username]"
                                                   value="<?= htmlspecialchars($settings['email']['smtp_username'] ?? '') ?>">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="smtp_password" class="form-label">SMTP heslo</label>
                                            <input type="password" class="form-control" id="smtp_password" name="email[smtp_password]"
                                                   value="<?= htmlspecialchars($settings['email']['smtp_password'] ?? '') ?>">
                                        </div>
                                    </div>

                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="smtp_encryption" class="form-label">Šifrování</label>
                                            <select class="form-select" id="smtp_encryption" name="email[smtp_encryption]">
                                                <option value="tls" <?= ($settings['email']['smtp_encryption'] ?? 'tls') === 'tls' ? 'selected' : '' ?>>TLS</option>
                                                <option value="ssl" <?= ($settings['email']['smtp_encryption'] ?? '') === 'ssl' ? 'selected' : '' ?>>SSL</option>
                                                <option value="none" <?= ($settings['email']['smtp_encryption'] ?? '') === 'none' ? 'selected' : '' ?>>Žádné</option>
                                            </select>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-check mt-4">
                                                <input class="form-check-input" type="checkbox" id="smtp_auth" name="email[smtp_auth]"
                                                       value="1" <?= ($settings['email']['smtp_auth'] ?? '1') === '1' ? 'checked' : '' ?>>
                                                <label class="form-check-label" for="smtp_auth">
                                                    Vyžadovat autentizaci
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Email adresy -->
                                <div class="mb-4">
                                    <h6 class="text-primary mb-3">Email adresy</h6>

                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="from_email" class="form-label">Odesílatel (email)</label>
                                            <input type="email" class="form-control" id="from_email" name="email[from_email]"
                                                   value="<?= htmlspecialchars($settings['email']['from_email'] ?? '') ?>">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="from_name" class="form-label">Odesílatel (jméno)</label>
                                            <input type="text" class="form-control" id="from_name" name="email[from_name]"
                                                   value="<?= htmlspecialchars($settings['email']['from_name'] ?? '') ?>">
                                        </div>
                                    </div>

                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="admin_email" class="form-label">Admin email</label>
                                            <input type="email" class="form-control" id="admin_email" name="email[admin_email]"
                                                   value="<?= htmlspecialchars($settings['email']['admin_email'] ?? '') ?>">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="support_email" class="form-label">Podpora email</label>
                                            <input type="email" class="form-control" id="support_email" name="email[support_email]"
                                                   value="<?= htmlspecialchars($settings['email']['support_email'] ?? '') ?>">
                                        </div>
                                    </div>
                                </div>

                                <!-- Test email -->
                                <div class="mb-4">
                                    <h6 class="text-primary mb-3">Test emailu</h6>
                                    <div class="row">
                                        <div class="col-md-8">
                                            <input type="email" class="form-control" id="test_email" placeholder="Zadejte email pro test">
                                        </div>
                                        <div class="col-md-4">
                                            <button type="button" class="btn btn-outline-primary" onclick="sendTestEmail()">
                                                <i class="fas fa-paper-plane me-2"></i>
                                                Poslat test
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Admin nastavení -->
                    <div class="tab-pane fade" id="admin-settings">
                        <div class="card shadow">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">
                                    <i class="fas fa-user-shield me-2"></i>
                                    Admin nastavení
                                </h6>
                            </div>
                            <div class="card-body">
                                <!-- Změna hesla -->
                                <div class="mb-4">
                                    <h6 class="text-primary mb-3">Změna admin hesla</h6>

                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="current_password" class="form-label">Současné heslo</label>
                                            <input type="password" class="form-control" id="current_password" name="admin[current_password]">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="new_password" class="form-label">Nové heslo</label>
                                            <input type="password" class="form-control" id="new_password" name="admin[new_password]">
                                        </div>
                                    </div>

                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="confirm_password" class="form-label">Potvrzení nového hesla</label>
                                            <input type="password" class="form-control" id="confirm_password" name="admin[confirm_password]">
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mt-4">
                                                <button type="button" class="btn btn-warning" onclick="changePassword()">
                                                    <i class="fas fa-key me-2"></i>
                                                    Změnit heslo
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Bezpečnostní nastavení -->
                                <div class="mb-4">
                                    <h6 class="text-primary mb-3">Bezpečnostní nastavení</h6>

                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="session_timeout" class="form-label">Timeout session (minuty)</label>
                                            <input type="number" class="form-control" id="session_timeout" name="security[session_timeout]"
                                                   value="<?= htmlspecialchars($settings['security']['session_timeout'] ?? '30') ?>"
                                                   min="5" max="480">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="max_login_attempts" class="form-label">Max. pokusů o přihlášení</label>
                                            <input type="number" class="form-control" id="max_login_attempts" name="security[max_login_attempts]"
                                                   value="<?= htmlspecialchars($settings['security']['max_login_attempts'] ?? '5') ?>"
                                                   min="1" max="20">
                                        </div>
                                    </div>

                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="enable_2fa" name="security[enable_2fa]"
                                                       value="1" <?= ($settings['security']['enable_2fa'] ?? '0') === '1' ? 'checked' : '' ?>>
                                                <label class="form-check-label" for="enable_2fa">
                                                    Povolit 2FA autentizaci
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="log_admin_actions" name="security[log_admin_actions]"
                                                       value="1" <?= ($settings['security']['log_admin_actions'] ?? '1') === '1' ? 'checked' : '' ?>>
                                                <label class="form-check-label" for="log_admin_actions">
                                                    Logovat admin akce
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Údržba -->
                                <div class="mb-4">
                                    <h6 class="text-primary mb-3">Údržba</h6>

                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="maintenance_mode" name="system[maintenance_mode]"
                                                       value="1" <?= ($settings['system']['maintenance_mode'] ?? '0') === '1' ? 'checked' : '' ?>>
                                                <label class="form-check-label" for="maintenance_mode">
                                                    Režim údržby
                                                </label>
                                            </div>
                                            <div class="form-text">Web bude nedostupný pro návštěvníky</div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="debug_mode" name="system[debug_mode]"
                                                       value="1" <?= ($settings['system']['debug_mode'] ?? '0') === '1' ? 'checked' : '' ?>>
                                                <label class="form-check-label" for="debug_mode">
                                                    Debug režim
                                                </label>
                                            </div>
                                            <div class="form-text">Zobrazovat detailní chyby</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Zálohy a údržba -->
                    <div class="tab-pane fade" id="backup-settings">
                        <div class="card shadow">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">
                                    <i class="fas fa-database me-2"></i>
                                    Zálohy a údržba
                                </h6>
                            </div>
                            <div class="card-body">
                                <!-- Zálohy -->
                                <div class="mb-4">
                                    <h6 class="text-primary mb-3">Správa záloh</h6>

                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <div class="d-grid">
                                                <button type="button" class="btn btn-success" onclick="createBackup()">
                                                    <i class="fas fa-download me-2"></i>
                                                    Vytvořit zálohu
                                                </button>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="d-grid">
                                                <button type="button" class="btn btn-warning" onclick="showRestoreModal()">
                                                    <i class="fas fa-upload me-2"></i>
                                                    Obnovit ze zálohy
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">Automatické zálohy</label>
                                        <select class="form-select" name="backup[auto_backup_frequency]">
                                            <option value="disabled" <?= ($settings['backup']['auto_backup_frequency'] ?? 'disabled') === 'disabled' ? 'selected' : '' ?>>Vypnuto</option>
                                            <option value="daily" <?= ($settings['backup']['auto_backup_frequency'] ?? '') === 'daily' ? 'selected' : '' ?>>Denně</option>
                                            <option value="weekly" <?= ($settings['backup']['auto_backup_frequency'] ?? '') === 'weekly' ? 'selected' : '' ?>>Týdně</option>
                                            <option value="monthly" <?= ($settings['backup']['auto_backup_frequency'] ?? '') === 'monthly' ? 'selected' : '' ?>>Měsíčně</option>
                                        </select>
                                    </div>
                                </div>

                                <!-- Čištění dat -->
                                <div class="mb-4">
                                    <h6 class="text-primary mb-3">Čištění dat</h6>

                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <div class="d-grid">
                                                <button type="button" class="btn btn-outline-warning" onclick="clearLogs()">
                                                    <i class="fas fa-broom me-2"></i>
                                                    Vymazat logy
                                                </button>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="d-grid">
                                                <button type="button" class="btn btn-outline-danger" onclick="clearCache()">
                                                    <i class="fas fa-trash me-2"></i>
                                                    Vymazat cache
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Systémové informace -->
                                <div class="mb-4">
                                    <h6 class="text-primary mb-3">Systémové informace</h6>

                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <tr>
                                                <td><strong>PHP verze:</strong></td>
                                                <td><?= PHP_VERSION ?></td>
                                            </tr>
                                            <tr>
                                                <td><strong>Velikost dat:</strong></td>
                                                <td id="dataSize">Načítá se...</td>
                                            </tr>
                                            <tr>
                                                <td><strong>Počet produktů:</strong></td>
                                                <td><?= count($stats['products'] ?? []) ?></td>
                                            </tr>
                                            <tr>
                                                <td><strong>Počet obchodníků:</strong></td>
                                                <td><?= count($stats['merchants'] ?? []) ?></td>
                                            </tr>
                                            <tr>
                                                <td><strong>Poslední záloha:</strong></td>
                                                <td id="lastBackup">Načítá se...</td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tlačítka pro uložení -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card shadow">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-2"></i>
                                            Uložit nastavení
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary" onclick="resetForm()">
                                            <i class="fas fa-undo me-2"></i>
                                            Obnovit
                                        </button>
                                    </div>
                                    <div>
                                        <small class="text-muted">
                                            <i class="fas fa-info-circle me-1"></i>
                                            Změny se projeví okamžitě po uložení
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Auto-save indikátor -->
<div id="autosaveIndicator" class="position-fixed bottom-0 end-0 m-3" style="display: none;">
    <div class="alert alert-info mb-0">
        <i class="fas fa-save me-2"></i>
        Automaticky uloženo
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-save funkcionalita
    let autoSaveTimeout;
    const form = document.getElementById('settingsForm');
    const formInputs = form.querySelectorAll('input, textarea, select');

    formInputs.forEach(input => {
        input.addEventListener('input', function() {
            clearTimeout(autoSaveTimeout);
            autoSaveTimeout = setTimeout(autoSave, 5000); // Auto-save po 5 sekundách
        });
    });

    function autoSave() {
        const formData = new FormData(form);
        formData.append('auto_save', '1');

        fetch(window.location.href, {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAutoSaveIndicator();
            }
        })
        .catch(error => {
            console.error('Auto-save error:', error);
        });
    }

    function showAutoSaveIndicator() {
        const indicator = document.getElementById('autosaveIndicator');
        indicator.style.display = 'block';
        setTimeout(() => {
            indicator.style.display = 'none';
        }, 2000);
    }

    // Načtení systémových informací
    loadSystemInfo();
});

function sendTestEmail() {
    const email = document.getElementById('test_email').value;
    if (!email) {
        alert('Zadejte email adresu pro test.');
        return;
    }

    fetch('/admin/nastaveni/test-email', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            email: email,
            csrf_token: '<?= htmlspecialchars($csrf_token) ?>'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Test email byl úspěšně odeslán.');
        } else {
            alert('Chyba při odesílání: ' + data.message);
        }
    })
    .catch(error => {
        alert('Chyba při odesílání test emailu.');
    });
}

function changePassword() {
    const currentPassword = document.getElementById('current_password').value;
    const newPassword = document.getElementById('new_password').value;
    const confirmPassword = document.getElementById('confirm_password').value;

    if (!currentPassword || !newPassword || !confirmPassword) {
        alert('Vyplňte všechna pole pro změnu hesla.');
        return;
    }

    if (newPassword !== confirmPassword) {
        alert('Nová hesla se neshodují.');
        return;
    }

    if (newPassword.length < 6) {
        alert('Nové heslo musí mít alespoň 6 znaků.');
        return;
    }

    if (confirm('Opravdu chcete změnit admin heslo?')) {
        fetch('/admin/nastaveni/zmenit-heslo', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                current_password: currentPassword,
                new_password: newPassword,
                csrf_token: '<?= htmlspecialchars($csrf_token) ?>'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Heslo bylo úspěšně změněno.');
                document.getElementById('current_password').value = '';
                document.getElementById('new_password').value = '';
                document.getElementById('confirm_password').value = '';
            } else {
                alert('Chyba při změně hesla: ' + data.message);
            }
        })
        .catch(error => {
            alert('Chyba při změně hesla.');
        });
    }
}

function createBackup() {
    if (confirm('Opravdu chcete vytvořit zálohu dat?')) {
        window.location.href = '/admin/nastaveni/vytvorit-zalohu';
    }
}

function showRestoreModal() {
    // Implementace modalu pro obnovení ze zálohy
    alert('Funkce obnovení ze zálohy bude implementována v další verzi.');
}

function clearLogs() {
    if (confirm('Opravdu chcete vymazat všechny logy? Tato akce je nevratná.')) {
        fetch('/admin/nastaveni/vymazat-logy', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                csrf_token: '<?= htmlspecialchars($csrf_token) ?>'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Logy byly úspěšně vymazány.');
                loadSystemInfo();
            } else {
                alert('Chyba při mazání logů: ' + data.message);
            }
        });
    }
}

function clearCache() {
    if (confirm('Opravdu chcete vymazat cache?')) {
        fetch('/admin/nastaveni/vymazat-cache', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                csrf_token: '<?= htmlspecialchars($csrf_token) ?>'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Cache byla úspěšně vymazána.');
            } else {
                alert('Chyba při mazání cache: ' + data.message);
            }
        });
    }
}

function loadSystemInfo() {
    fetch('/admin/nastaveni/system-info')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById('dataSize').textContent = data.dataSize || 'N/A';
            document.getElementById('lastBackup').textContent = data.lastBackup || 'Nikdy';
        }
    })
    .catch(error => {
        console.error('Error loading system info:', error);
    });
}

function resetForm() {
    if (confirm('Opravdu chcete obnovit formulář? Neuložené změny budou ztraceny.')) {
        location.reload();
    }
}
</script>