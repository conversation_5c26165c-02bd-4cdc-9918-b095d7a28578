<?php $this->layout('layouts/admin') ?>

<div class="row justify-content-center">
    <div class="col-lg-6">
        <div class="card border-danger">
            <div class="card-header bg-danger text-white">
                <h5 class="mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Chyba aplikace
                </h5>
            </div>
            <div class="card-body text-center">
                <i class="fas fa-exclamation-circle fa-4x text-danger mb-4"></i>
                
                <h4 class="text-danger mb-3">
                    <?= htmlspecialchars($message ?? '<PERSON><PERSON><PERSON> k neočekávané chybě') ?>
                </h4>
                
                <p class="text-muted mb-4">
                    Omlouváme se za způsobené potíže. Zkuste to prosím znovu nebo kontaktujte administrátora.
                </p>
                
                <?php if (isset($error) && $error): ?>
                    <div class="alert alert-warning text-start">
                        <h6>Technické detaily:</h6>
                        <code><?= htmlspecialchars($error) ?></code>
                    </div>
                <?php endif; ?>
                
                <div class="d-flex justify-content-center gap-3">
                    <button onclick="history.back()" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>
                        Zpět
                    </button>
                    <a href="/admin/dashboard" class="btn btn-primary">
                        <i class="fas fa-home me-2"></i>
                        Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
