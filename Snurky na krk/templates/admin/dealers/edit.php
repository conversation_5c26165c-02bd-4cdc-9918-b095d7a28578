<?php
/**
 * Admin šablona pro editaci obchodníka
 * 
 * <AUTHOR> Team
 * @version 1.0
 */

if (!defined('SNURKY_INIT')) {
    die('Přímý přístup není povolen');
}
?>

<div class="container-fluid">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="/admin/dashboard">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="/admin/obchodnici">Obchodníci</a></li>
            <li class="breadcrumb-item active" aria-current="page">Editace obchodníka</li>
        </ol>
    </nav>

    <!-- Hlavička -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-edit me-2"></i>
            Editace obchodníka: <?= htmlspecialchars($dealer['company_name']) ?>
        </h1>
        <div>
            <a href="/admin/obchodnici" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>
                Zpět na seznam
            </a>
        </div>
    </div>

    <!-- Flash zprávy -->
    <?php if ($flash = $this->getFlashMessage()): ?>
        <div class="alert alert-<?= $flash['type'] === 'error' ? 'danger' : $flash['type'] ?> alert-dismissible fade show" role="alert">
            <?= htmlspecialchars($flash['message']) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <!-- Formulář -->
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-store me-2"></i>
                        Informace o obchodníkovi
                    </h6>
                </div>
                <div class="card-body">
                    <form method="POST" id="dealerForm" class="needs-validation" novalidate>
                        <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($csrf_token) ?>">
                        
                        <!-- Základní informace o firmě -->
                        <div class="mb-4">
                            <h5 class="text-primary mb-3">
                                <i class="fas fa-building me-2"></i>
                                Informace o firmě
                            </h5>
                            
                            <div class="row mb-3">
                                <div class="col-md-8">
                                    <label for="company_name" class="form-label">Název firmy *</label>
                                    <input type="text" class="form-control" id="company_name" name="company_name" 
                                           value="<?= htmlspecialchars($dealer['company_name']) ?>" required>
                                    <div class="invalid-feedback">
                                        Zadejte název firmy.
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <label for="ico" class="form-label">IČO</label>
                                    <input type="text" class="form-control" id="ico" name="ico" 
                                           value="<?= htmlspecialchars($dealer['ico'] ?? '') ?>" 
                                           pattern="[0-9]{8}" maxlength="8">
                                    <div class="form-text">8 číslic</div>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="dic" class="form-label">DIČ</label>
                                    <input type="text" class="form-control" id="dic" name="dic" 
                                           value="<?= htmlspecialchars($dealer['dic'] ?? '') ?>">
                                </div>
                                <div class="col-md-6">
                                    <label for="business_type" class="form-label">Typ podnikání</label>
                                    <select class="form-select" id="business_type" name="business_type">
                                        <option value="">Vyberte typ</option>
                                        <option value="retail" <?= ($dealer['business_type'] ?? '') === 'retail' ? 'selected' : '' ?>>Maloobchod</option>
                                        <option value="wholesale" <?= ($dealer['business_type'] ?? '') === 'wholesale' ? 'selected' : '' ?>>Velkoobchod</option>
                                        <option value="online" <?= ($dealer['business_type'] ?? '') === 'online' ? 'selected' : '' ?>>E-shop</option>
                                        <option value="other" <?= ($dealer['business_type'] ?? '') === 'other' ? 'selected' : '' ?>>Jiné</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Kontaktní osoba -->
                        <div class="mb-4">
                            <h5 class="text-primary mb-3">
                                <i class="fas fa-user me-2"></i>
                                Kontaktní osoba
                            </h5>
                            
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="contact_person" class="form-label">Jméno a příjmení *</label>
                                    <input type="text" class="form-control" id="contact_person" name="contact_person" 
                                           value="<?= htmlspecialchars($dealer['contact_person']) ?>" required>
                                    <div class="invalid-feedback">
                                        Zadejte jméno kontaktní osoby.
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label for="position" class="form-label">Pozice</label>
                                    <input type="text" class="form-control" id="position" name="position" 
                                           value="<?= htmlspecialchars($dealer['position'] ?? '') ?>" 
                                           placeholder="např. jednatel, nákupčí">
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="email" class="form-label">Email *</label>
                                    <input type="email" class="form-control" id="email" name="email" 
                                           value="<?= htmlspecialchars($dealer['email']) ?>" required>
                                    <div class="invalid-feedback">
                                        Zadejte platný email.
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label for="phone" class="form-label">Telefon</label>
                                    <input type="tel" class="form-control" id="phone" name="phone" 
                                           value="<?= htmlspecialchars($dealer['phone'] ?? '') ?>" 
                                           placeholder="+420 123 456 789">
                                </div>
                            </div>
                        </div>

                        <!-- Adresa -->
                        <div class="mb-4">
                            <h5 class="text-primary mb-3">
                                <i class="fas fa-map-marker-alt me-2"></i>
                                Adresa
                            </h5>
                            
                            <div class="mb-3">
                                <label for="address" class="form-label">Ulice a číslo popisné *</label>
                                <input type="text" class="form-control" id="address" name="address" 
                                       value="<?= htmlspecialchars($dealer['address']) ?>" required>
                                <div class="invalid-feedback">
                                    Zadejte adresu.
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <label for="city" class="form-label">Město *</label>
                                    <input type="text" class="form-control" id="city" name="city" 
                                           value="<?= htmlspecialchars($dealer['city']) ?>" required>
                                    <div class="invalid-feedback">
                                        Zadejte město.
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <label for="postal_code" class="form-label">PSČ *</label>
                                    <input type="text" class="form-control" id="postal_code" name="postal_code" 
                                           value="<?= htmlspecialchars($dealer['postal_code']) ?>" 
                                           pattern="[0-9]{3}\s?[0-9]{2}" required>
                                    <div class="invalid-feedback">
                                        Zadejte platné PSČ.
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <label for="region" class="form-label">Kraj</label>
                                    <select class="form-select" id="region" name="region">
                                        <option value="">Vyberte kraj</option>
                                        <option value="Praha" <?= ($dealer['region'] ?? '') === 'Praha' ? 'selected' : '' ?>>Praha</option>
                                        <option value="Středočeský" <?= ($dealer['region'] ?? '') === 'Středočeský' ? 'selected' : '' ?>>Středočeský</option>
                                        <option value="Jihočeský" <?= ($dealer['region'] ?? '') === 'Jihočeský' ? 'selected' : '' ?>>Jihočeský</option>
                                        <option value="Plzeňský" <?= ($dealer['region'] ?? '') === 'Plzeňský' ? 'selected' : '' ?>>Plzeňský</option>
                                        <option value="Karlovarský" <?= ($dealer['region'] ?? '') === 'Karlovarský' ? 'selected' : '' ?>>Karlovarský</option>
                                        <option value="Ústecký" <?= ($dealer['region'] ?? '') === 'Ústecký' ? 'selected' : '' ?>>Ústecký</option>
                                        <option value="Liberecký" <?= ($dealer['region'] ?? '') === 'Liberecký' ? 'selected' : '' ?>>Liberecký</option>
                                        <option value="Královéhradecký" <?= ($dealer['region'] ?? '') === 'Královéhradecký' ? 'selected' : '' ?>>Královéhradecký</option>
                                        <option value="Pardubický" <?= ($dealer['region'] ?? '') === 'Pardubický' ? 'selected' : '' ?>>Pardubický</option>
                                        <option value="Vysočina" <?= ($dealer['region'] ?? '') === 'Vysočina' ? 'selected' : '' ?>>Vysočina</option>
                                        <option value="Jihomoravský" <?= ($dealer['region'] ?? '') === 'Jihomoravský' ? 'selected' : '' ?>>Jihomoravský</option>
                                        <option value="Olomoucký" <?= ($dealer['region'] ?? '') === 'Olomoucký' ? 'selected' : '' ?>>Olomoucký</option>
                                        <option value="Zlínský" <?= ($dealer['region'] ?? '') === 'Zlínský' ? 'selected' : '' ?>>Zlínský</option>
                                        <option value="Moravskoslezský" <?= ($dealer['region'] ?? '') === 'Moravskoslezský' ? 'selected' : '' ?>>Moravskoslezský</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Obchodní informace -->
                        <div class="mb-4">
                            <h5 class="text-primary mb-3">
                                <i class="fas fa-handshake me-2"></i>
                                Obchodní informace
                            </h5>
                            
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="commission_rate" class="form-label">Provize (%)</label>
                                    <input type="number" class="form-control" id="commission_rate" name="commission_rate" 
                                           value="<?= htmlspecialchars($dealer['commission_rate'] ?? '10') ?>" 
                                           min="0" max="100" step="0.1">
                                    <div class="form-text">Výchozí provize je 10%</div>
                                </div>
                                <div class="col-md-6">
                                    <label for="status" class="form-label">Status</label>
                                    <select class="form-select" id="status" name="status">
                                        <option value="pending" <?= $dealer['status'] === 'pending' ? 'selected' : '' ?>>Čeká na schválení</option>
                                        <option value="active" <?= $dealer['status'] === 'active' ? 'selected' : '' ?>>Aktivní</option>
                                        <option value="inactive" <?= $dealer['status'] === 'inactive' ? 'selected' : '' ?>>Neaktivní</option>
                                        <option value="rejected" <?= $dealer['status'] === 'rejected' ? 'selected' : '' ?>>Zamítnutý</option>
                                    </select>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="notes" class="form-label">Poznámky</label>
                                <textarea class="form-control" id="notes" name="notes" rows="3"><?= htmlspecialchars($dealer['notes'] ?? '') ?></textarea>
                                <div class="form-text">Interní poznámky pro administrátory</div>
                            </div>
                        </div>

                        <!-- Tlačítka -->
                        <div class="d-flex justify-content-between">
                            <div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>
                                    Uložit změny
                                </button>
                                <?php if ($dealer['status'] === 'pending'): ?>
                                    <button type="button" class="btn btn-success" onclick="approveDealer()">
                                        <i class="fas fa-check me-2"></i>
                                        Schválit
                                    </button>
                                    <button type="button" class="btn btn-warning" onclick="rejectDealer()">
                                        <i class="fas fa-times me-2"></i>
                                        Zamítnout
                                    </button>
                                <?php endif; ?>
                            </div>
                            <div>
                                <a href="/admin/obchodnici" class="btn btn-secondary">
                                    <i class="fas fa-times me-2"></i>
                                    Zrušit
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Sidebar s informacemi -->
        <div class="col-lg-4">
            <!-- Informace o obchodníkovi -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-info-circle me-2"></i>
                        Informace
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <strong>ID obchodníka:</strong> <?= $dealer['id'] ?>
                    </div>
                    <div class="mb-2">
                        <strong>Registrace:</strong> <?= date('d.m.Y H:i', strtotime($dealer['created_at'])) ?>
                    </div>
                    <?php if (!empty($dealer['updated_at'])): ?>
                        <div class="mb-2">
                            <strong>Upraveno:</strong> <?= date('d.m.Y H:i', strtotime($dealer['updated_at'])) ?>
                        </div>
                    <?php endif; ?>
                    <div class="mb-2">
                        <strong>Status:</strong> 
                        <?php
                        $statusClass = [
                            'pending' => 'warning',
                            'active' => 'success',
                            'inactive' => 'secondary',
                            'rejected' => 'danger'
                        ][$dealer['status']] ?? 'secondary';
                        
                        $statusText = [
                            'pending' => 'Čeká na schválení',
                            'active' => 'Aktivní',
                            'inactive' => 'Neaktivní',
                            'rejected' => 'Zamítnutý'
                        ][$dealer['status']] ?? 'Neznámý';
                        ?>
                        <span class="badge bg-<?= $statusClass ?>">
                            <?= $statusText ?>
                        </span>
                    </div>
                </div>
            </div>

            <!-- Rychlé akce -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-bolt me-2"></i>
                        Rychlé akce
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="mailto:<?= htmlspecialchars($dealer['email']) ?>" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-envelope me-2"></i>
                            Poslat email
                        </a>
                        <?php if (!empty($dealer['phone'])): ?>
                            <a href="tel:<?= htmlspecialchars($dealer['phone']) ?>" class="btn btn-outline-info btn-sm">
                                <i class="fas fa-phone me-2"></i>
                                Zavolat
                            </a>
                        <?php endif; ?>
                        <button type="button" class="btn btn-outline-warning btn-sm" onclick="duplicateDealer()">
                            <i class="fas fa-copy me-2"></i>
                            Duplikovat
                        </button>
                        <button type="button" class="btn btn-outline-danger btn-sm" onclick="deleteDealer()">
                            <i class="fas fa-trash me-2"></i>
                            Smazat obchodníka
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Auto-save indikátor -->
<div id="autosaveIndicator" class="position-fixed bottom-0 end-0 m-3" style="display: none;">
    <div class="alert alert-info mb-0">
        <i class="fas fa-save me-2"></i>
        Automaticky uloženo
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Validace formuláře
    const form = document.getElementById('dealerForm');
    
    // Formátování PSČ
    document.getElementById('postal_code').addEventListener('input', function() {
        let value = this.value.replace(/\s/g, '');
        if (value.length > 3) {
            value = value.substring(0, 3) + ' ' + value.substring(3, 5);
        }
        this.value = value;
    });
    
    // Validace IČO
    document.getElementById('ico').addEventListener('input', function() {
        this.value = this.value.replace(/\D/g, '').substring(0, 8);
    });

    // Auto-save funkcionalita
    let autoSaveTimeout;
    const formInputs = form.querySelectorAll('input, textarea, select');
    
    formInputs.forEach(input => {
        input.addEventListener('input', function() {
            clearTimeout(autoSaveTimeout);
            autoSaveTimeout = setTimeout(autoSave, 3000); // Auto-save po 3 sekundách
        });
    });

    function autoSave() {
        const formData = new FormData(form);
        formData.append('auto_save', '1');
        
        fetch(window.location.href, {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAutoSaveIndicator();
            }
        })
        .catch(error => {
            console.error('Auto-save error:', error);
        });
    }

    function showAutoSaveIndicator() {
        const indicator = document.getElementById('autosaveIndicator');
        indicator.style.display = 'block';
        setTimeout(() => {
            indicator.style.display = 'none';
        }, 2000);
    }
});

function approveDealer() {
    if (confirm('Opravdu chcete schválit tohoto obchodníka?')) {
        document.getElementById('status').value = 'active';
        document.getElementById('dealerForm').submit();
    }
}

function rejectDealer() {
    if (confirm('Opravdu chcete zamítnout tohoto obchodníka?')) {
        document.getElementById('status').value = 'rejected';
        document.getElementById('dealerForm').submit();
    }
}

function duplicateDealer() {
    if (confirm('Opravdu chcete duplikovat tohoto obchodníka?')) {
        window.location.href = '/admin/obchodnici/duplikovat/<?= $dealer['id'] ?>';
    }
}

function deleteDealer() {
    if (confirm('Opravdu chcete smazat tohoto obchodníka? Tato akce je nevratná.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '/admin/obchodnici/<?= $dealer['id'] ?>/smazat';
        
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrf_token';
        csrfInput.value = '<?= htmlspecialchars($csrf_token) ?>';
        
        form.appendChild(csrfInput);
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
