<?php
/**
 * Admin šablona pro seznam obchodníků
 * 
 * <AUTHOR> Team
 * @version 1.0
 */

if (!defined('SNURKY_INIT')) {
    die('<PERSON>římý přístup není povolen');
}
?>

<div class="container-fluid">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="/admin/dashboard">Dashboard</a></li>
            <li class="breadcrumb-item active" aria-current="page">Ob<PERSON>dníci</li>
        </ol>
    </nav>

    <!-- Hlavička -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-users me-2"></i>
            Spr<PERSON>va obchodníků
        </h1>
        <div>
            <a href="/admin/obchodnici/novy" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                Nový obchodník
            </a>
        </div>
    </div>

    <!-- Flash zprávy -->
    <?php if ($flash = $this->getFlashMessage()): ?>
        <div class="alert alert-<?= $flash['type'] === 'error' ? 'danger' : $flash['type'] ?> alert-dismissible fade show" role="alert">
            <?= htmlspecialchars($flash['message']) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Filtry a vyhledávání -->
    <div class="card shadow mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-4">
                    <label for="search" class="form-label">Vyhledávání</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="<?= htmlspecialchars($search ?? '') ?>" 
                           placeholder="Název firmy, jméno, email...">
                </div>
                <div class="col-md-3">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">Všechny statusy</option>
                        <option value="pending" <?= ($selected_status ?? '') === 'pending' ? 'selected' : '' ?>>Čeká na schválení</option>
                        <option value="active" <?= ($selected_status ?? '') === 'active' ? 'selected' : '' ?>>Aktivní</option>
                        <option value="inactive" <?= ($selected_status ?? '') === 'inactive' ? 'selected' : '' ?>>Neaktivní</option>
                        <option value="rejected" <?= ($selected_status ?? '') === 'rejected' ? 'selected' : '' ?>>Zamítnutý</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="region" class="form-label">Region</label>
                    <select class="form-select" id="region" name="region">
                        <option value="">Všechny regiony</option>
                        <?php foreach ($regions as $region): ?>
                            <option value="<?= htmlspecialchars($region) ?>" 
                                    <?= ($selected_region ?? '') === $region ? 'selected' : '' ?>>
                                <?= htmlspecialchars($region) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-search"></i>
                    </button>
                    <a href="/admin/obchodnici" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i>
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Seznam obchodníků -->
    <div class="card shadow">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-list me-2"></i>
                Seznam obchodníků (<?= count($dealers) ?>)
            </h6>
            <div class="dropdown">
                <button class="btn btn-outline-primary btn-sm dropdown-toggle" type="button" 
                        data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fas fa-cog me-2"></i>
                    Hromadné akce
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="#" onclick="bulkAction('activate')">
                        <i class="fas fa-check me-2"></i>Aktivovat vybrané
                    </a></li>
                    <li><a class="dropdown-item" href="#" onclick="bulkAction('deactivate')">
                        <i class="fas fa-ban me-2"></i>Deaktivovat vybrané
                    </a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item text-danger" href="#" onclick="bulkAction('delete')">
                        <i class="fas fa-trash me-2"></i>Smazat vybrané
                    </a></li>
                </ul>
            </div>
        </div>
        <div class="card-body p-0">
            <?php if (empty($dealers)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">Žádní obchodníci</h5>
                    <p class="text-muted">Zatím nebyli zaregistrováni žádní obchodníci.</p>
                    <a href="/admin/obchodnici/novy" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        Přidat prvního obchodníka
                    </a>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th width="40">
                                    <input type="checkbox" id="selectAll" class="form-check-input">
                                </th>
                                <th>Obchodník</th>
                                <th>Kontakt</th>
                                <th>Region</th>
                                <th>Status</th>
                                <th>Registrace</th>
                                <th width="120">Akce</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($dealers as $dealer): ?>
                                <tr>
                                    <td>
                                        <input type="checkbox" class="form-check-input dealer-checkbox" 
                                               value="<?= $dealer['id'] ?>">
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-3">
                                                <i class="fas fa-store text-white"></i>
                                            </div>
                                            <div>
                                                <div class="fw-bold"><?= htmlspecialchars($dealer['company_name']) ?></div>
                                                <div class="text-muted small">
                                                    <?= htmlspecialchars($dealer['contact_person']) ?>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <i class="fas fa-envelope me-1"></i>
                                            <a href="mailto:<?= htmlspecialchars($dealer['email']) ?>">
                                                <?= htmlspecialchars($dealer['email']) ?>
                                            </a>
                                        </div>
                                        <?php if (!empty($dealer['phone'])): ?>
                                            <div class="text-muted small">
                                                <i class="fas fa-phone me-1"></i>
                                                <?= htmlspecialchars($dealer['phone']) ?>
                                            </div>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">
                                            <?= htmlspecialchars($dealer['region'] ?? 'Neurčeno') ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php
                                        $statusClass = [
                                            'pending' => 'warning',
                                            'active' => 'success',
                                            'inactive' => 'secondary',
                                            'rejected' => 'danger'
                                        ][$dealer['status']] ?? 'secondary';
                                        
                                        $statusText = [
                                            'pending' => 'Čeká na schválení',
                                            'active' => 'Aktivní',
                                            'inactive' => 'Neaktivní',
                                            'rejected' => 'Zamítnutý'
                                        ][$dealer['status']] ?? 'Neznámý';
                                        ?>
                                        <span class="badge bg-<?= $statusClass ?>">
                                            <?= $statusText ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="text-muted small">
                                            <?= date('d.m.Y', strtotime($dealer['created_at'])) ?>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="/admin/obchodnici/<?= $dealer['id'] ?>" 
                                               class="btn btn-outline-primary btn-sm" title="Editovat">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <?php if ($dealer['status'] === 'pending'): ?>
                                                <button type="button" class="btn btn-outline-success btn-sm" 
                                                        onclick="approveDealer(<?= $dealer['id'] ?>)" title="Schválit">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                                <button type="button" class="btn btn-outline-danger btn-sm" 
                                                        onclick="rejectDealer(<?= $dealer['id'] ?>)" title="Zamítnout">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            <?php else: ?>
                                                <button type="button" class="btn btn-outline-danger btn-sm" 
                                                        onclick="deleteDealer(<?= $dealer['id'] ?>)" title="Smazat">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Select all checkbox
    const selectAllCheckbox = document.getElementById('selectAll');
    const dealerCheckboxes = document.querySelectorAll('.dealer-checkbox');
    
    selectAllCheckbox?.addEventListener('change', function() {
        dealerCheckboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
    });
    
    // Update select all when individual checkboxes change
    dealerCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const checkedCount = document.querySelectorAll('.dealer-checkbox:checked').length;
            selectAllCheckbox.checked = checkedCount === dealerCheckboxes.length;
            selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < dealerCheckboxes.length;
        });
    });
});

function approveDealer(dealerId) {
    if (confirm('Opravdu chcete schválit tohoto obchodníka?')) {
        updateDealerStatus(dealerId, 'active');
    }
}

function rejectDealer(dealerId) {
    if (confirm('Opravdu chcete zamítnout tohoto obchodníka?')) {
        updateDealerStatus(dealerId, 'rejected');
    }
}

function deleteDealer(dealerId) {
    if (confirm('Opravdu chcete smazat tohoto obchodníka? Tato akce je nevratná.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/obchodnici/${dealerId}/smazat`;
        
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrf_token';
        csrfInput.value = '<?= htmlspecialchars($csrf_token ?? '') ?>';
        
        form.appendChild(csrfInput);
        document.body.appendChild(form);
        form.submit();
    }
}

function updateDealerStatus(dealerId, status) {
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = `/admin/obchodnici/${dealerId}/status`;
    
    const csrfInput = document.createElement('input');
    csrfInput.type = 'hidden';
    csrfInput.name = 'csrf_token';
    csrfInput.value = '<?= htmlspecialchars($csrf_token ?? '') ?>';
    
    const statusInput = document.createElement('input');
    statusInput.type = 'hidden';
    statusInput.name = 'status';
    statusInput.value = status;
    
    form.appendChild(csrfInput);
    form.appendChild(statusInput);
    document.body.appendChild(form);
    form.submit();
}

function bulkAction(action) {
    const checkedBoxes = document.querySelectorAll('.dealer-checkbox:checked');
    
    if (checkedBoxes.length === 0) {
        alert('Vyberte alespoň jednoho obchodníka.');
        return;
    }
    
    const dealerIds = Array.from(checkedBoxes).map(cb => cb.value);
    
    let confirmMessage = '';
    switch (action) {
        case 'activate':
            confirmMessage = `Opravdu chcete aktivovat ${dealerIds.length} obchodníků?`;
            break;
        case 'deactivate':
            confirmMessage = `Opravdu chcete deaktivovat ${dealerIds.length} obchodníků?`;
            break;
        case 'delete':
            confirmMessage = `Opravdu chcete smazat ${dealerIds.length} obchodníků? Tato akce je nevratná.`;
            break;
    }
    
    if (confirm(confirmMessage)) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '/admin/obchodnici/bulk-action';
        
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrf_token';
        csrfInput.value = '<?= htmlspecialchars($csrf_token ?? '') ?>';
        
        const actionInput = document.createElement('input');
        actionInput.type = 'hidden';
        actionInput.name = 'action';
        actionInput.value = action;
        
        const idsInput = document.createElement('input');
        idsInput.type = 'hidden';
        idsInput.name = 'dealer_ids';
        idsInput.value = JSON.stringify(dealerIds);
        
        form.appendChild(csrfInput);
        form.appendChild(actionInput);
        form.appendChild(idsInput);
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
