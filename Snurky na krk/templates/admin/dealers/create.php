<?php
/**
 * Admin šablona pro vytvoření nového obchodníka
 * 
 * <AUTHOR> Team
 * @version 1.0
 */

if (!defined('SNURKY_INIT')) {
    die('<PERSON>římý přístup není povolen');
}
?>

<div class="container-fluid">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="/admin/dashboard">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="/admin/obchodnici">Ob<PERSON>dn<PERSON><PERSON></a></li>
            <li class="breadcrumb-item active" aria-current="page">Nový obchodník</li>
        </ol>
    </nav>

    <!-- Hlavička -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-user-plus me-2"></i>
            Nový obchodník
        </h1>
        <div>
            <a href="/admin/obchodnici" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>
                Zpět na seznam
            </a>
        </div>
    </div>

    <!-- Flash zprávy -->
    <?php if ($flash = $this->getFlashMessage()): ?>
        <div class="alert alert-<?= $flash['type'] === 'error' ? 'danger' : $flash['type'] ?> alert-dismissible fade show" role="alert">
            <?= htmlspecialchars($flash['message']) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <!-- Formulář -->
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-store me-2"></i>
                        Informace o obchodníkovi
                    </h6>
                </div>
                <div class="card-body">
                    <form method="POST" id="dealerForm" class="needs-validation" novalidate>
                        <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($csrf_token) ?>">
                        
                        <!-- Základní informace o firmě -->
                        <div class="mb-4">
                            <h5 class="text-primary mb-3">
                                <i class="fas fa-building me-2"></i>
                                Informace o firmě
                            </h5>
                            
                            <div class="row mb-3">
                                <div class="col-md-8">
                                    <label for="company_name" class="form-label">Název firmy *</label>
                                    <input type="text" class="form-control" id="company_name" name="company_name" 
                                           value="<?= htmlspecialchars($old['company_name'] ?? '') ?>" required>
                                    <div class="invalid-feedback">
                                        Zadejte název firmy.
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <label for="ico" class="form-label">IČO</label>
                                    <input type="text" class="form-control" id="ico" name="ico" 
                                           value="<?= htmlspecialchars($old['ico'] ?? '') ?>" 
                                           pattern="[0-9]{8}" maxlength="8">
                                    <div class="form-text">8 číslic</div>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="dic" class="form-label">DIČ</label>
                                    <input type="text" class="form-control" id="dic" name="dic" 
                                           value="<?= htmlspecialchars($old['dic'] ?? '') ?>">
                                </div>
                                <div class="col-md-6">
                                    <label for="business_type" class="form-label">Typ podnikání</label>
                                    <select class="form-select" id="business_type" name="business_type">
                                        <option value="">Vyberte typ</option>
                                        <option value="retail" <?= ($old['business_type'] ?? '') === 'retail' ? 'selected' : '' ?>>Maloobchod</option>
                                        <option value="wholesale" <?= ($old['business_type'] ?? '') === 'wholesale' ? 'selected' : '' ?>>Velkoobchod</option>
                                        <option value="online" <?= ($old['business_type'] ?? '') === 'online' ? 'selected' : '' ?>>E-shop</option>
                                        <option value="other" <?= ($old['business_type'] ?? '') === 'other' ? 'selected' : '' ?>>Jiné</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Kontaktní osoba -->
                        <div class="mb-4">
                            <h5 class="text-primary mb-3">
                                <i class="fas fa-user me-2"></i>
                                Kontaktní osoba
                            </h5>
                            
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="contact_person" class="form-label">Jméno a příjmení *</label>
                                    <input type="text" class="form-control" id="contact_person" name="contact_person" 
                                           value="<?= htmlspecialchars($old['contact_person'] ?? '') ?>" required>
                                    <div class="invalid-feedback">
                                        Zadejte jméno kontaktní osoby.
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label for="position" class="form-label">Pozice</label>
                                    <input type="text" class="form-control" id="position" name="position" 
                                           value="<?= htmlspecialchars($old['position'] ?? '') ?>" 
                                           placeholder="např. jednatel, nákupčí">
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="email" class="form-label">Email *</label>
                                    <input type="email" class="form-control" id="email" name="email" 
                                           value="<?= htmlspecialchars($old['email'] ?? '') ?>" required>
                                    <div class="invalid-feedback">
                                        Zadejte platný email.
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label for="phone" class="form-label">Telefon</label>
                                    <input type="tel" class="form-control" id="phone" name="phone" 
                                           value="<?= htmlspecialchars($old['phone'] ?? '') ?>" 
                                           placeholder="+*********** 789">
                                </div>
                            </div>
                        </div>

                        <!-- Adresa -->
                        <div class="mb-4">
                            <h5 class="text-primary mb-3">
                                <i class="fas fa-map-marker-alt me-2"></i>
                                Adresa
                            </h5>
                            
                            <div class="mb-3">
                                <label for="address" class="form-label">Ulice a číslo popisné *</label>
                                <input type="text" class="form-control" id="address" name="address" 
                                       value="<?= htmlspecialchars($old['address'] ?? '') ?>" required>
                                <div class="invalid-feedback">
                                    Zadejte adresu.
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <label for="city" class="form-label">Město *</label>
                                    <input type="text" class="form-control" id="city" name="city" 
                                           value="<?= htmlspecialchars($old['city'] ?? '') ?>" required>
                                    <div class="invalid-feedback">
                                        Zadejte město.
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <label for="postal_code" class="form-label">PSČ *</label>
                                    <input type="text" class="form-control" id="postal_code" name="postal_code" 
                                           value="<?= htmlspecialchars($old['postal_code'] ?? '') ?>" 
                                           pattern="[0-9]{3}\s?[0-9]{2}" required>
                                    <div class="invalid-feedback">
                                        Zadejte platné PSČ.
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <label for="region" class="form-label">Kraj</label>
                                    <select class="form-select" id="region" name="region">
                                        <option value="">Vyberte kraj</option>
                                        <option value="Praha" <?= ($old['region'] ?? '') === 'Praha' ? 'selected' : '' ?>>Praha</option>
                                        <option value="Středočeský" <?= ($old['region'] ?? '') === 'Středočeský' ? 'selected' : '' ?>>Středočeský</option>
                                        <option value="Jihočeský" <?= ($old['region'] ?? '') === 'Jihočeský' ? 'selected' : '' ?>>Jihočeský</option>
                                        <option value="Plzeňský" <?= ($old['region'] ?? '') === 'Plzeňský' ? 'selected' : '' ?>>Plzeňský</option>
                                        <option value="Karlovarský" <?= ($old['region'] ?? '') === 'Karlovarský' ? 'selected' : '' ?>>Karlovarský</option>
                                        <option value="Ústecký" <?= ($old['region'] ?? '') === 'Ústecký' ? 'selected' : '' ?>>Ústecký</option>
                                        <option value="Liberecký" <?= ($old['region'] ?? '') === 'Liberecký' ? 'selected' : '' ?>>Liberecký</option>
                                        <option value="Královéhradecký" <?= ($old['region'] ?? '') === 'Královéhradecký' ? 'selected' : '' ?>>Královéhradecký</option>
                                        <option value="Pardubický" <?= ($old['region'] ?? '') === 'Pardubický' ? 'selected' : '' ?>>Pardubický</option>
                                        <option value="Vysočina" <?= ($old['region'] ?? '') === 'Vysočina' ? 'selected' : '' ?>>Vysočina</option>
                                        <option value="Jihomoravský" <?= ($old['region'] ?? '') === 'Jihomoravský' ? 'selected' : '' ?>>Jihomoravský</option>
                                        <option value="Olomoucký" <?= ($old['region'] ?? '') === 'Olomoucký' ? 'selected' : '' ?>>Olomoucký</option>
                                        <option value="Zlínský" <?= ($old['region'] ?? '') === 'Zlínský' ? 'selected' : '' ?>>Zlínský</option>
                                        <option value="Moravskoslezský" <?= ($old['region'] ?? '') === 'Moravskoslezský' ? 'selected' : '' ?>>Moravskoslezský</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Obchodní informace -->
                        <div class="mb-4">
                            <h5 class="text-primary mb-3">
                                <i class="fas fa-handshake me-2"></i>
                                Obchodní informace
                            </h5>
                            
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="commission_rate" class="form-label">Provize (%)</label>
                                    <input type="number" class="form-control" id="commission_rate" name="commission_rate" 
                                           value="<?= htmlspecialchars($old['commission_rate'] ?? '10') ?>" 
                                           min="0" max="100" step="0.1">
                                    <div class="form-text">Výchozí provize je 10%</div>
                                </div>
                                <div class="col-md-6">
                                    <label for="status" class="form-label">Status</label>
                                    <select class="form-select" id="status" name="status">
                                        <option value="pending" <?= ($old['status'] ?? 'pending') === 'pending' ? 'selected' : '' ?>>Čeká na schválení</option>
                                        <option value="active" <?= ($old['status'] ?? '') === 'active' ? 'selected' : '' ?>>Aktivní</option>
                                        <option value="inactive" <?= ($old['status'] ?? '') === 'inactive' ? 'selected' : '' ?>>Neaktivní</option>
                                    </select>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="notes" class="form-label">Poznámky</label>
                                <textarea class="form-control" id="notes" name="notes" rows="3"><?= htmlspecialchars($old['notes'] ?? '') ?></textarea>
                                <div class="form-text">Interní poznámky pro administrátory</div>
                            </div>
                        </div>

                        <!-- Tlačítka -->
                        <div class="d-flex justify-content-between">
                            <div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>
                                    Vytvořit obchodníka
                                </button>
                                <button type="button" class="btn btn-outline-secondary" id="saveAsDraft">
                                    <i class="fas fa-file-alt me-2"></i>
                                    Uložit jako koncept
                                </button>
                            </div>
                            <div>
                                <a href="/admin/obchodnici" class="btn btn-secondary">
                                    <i class="fas fa-times me-2"></i>
                                    Zrušit
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Sidebar s nápovědou -->
        <div class="col-lg-4">
            <!-- Nápověda -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-info-circle me-2"></i>
                        Nápověda
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6 class="text-primary">Povinné údaje</h6>
                        <ul class="small text-muted mb-0">
                            <li>Název firmy</li>
                            <li>Jméno kontaktní osoby</li>
                            <li>Email</li>
                            <li>Adresa</li>
                        </ul>
                    </div>
                    
                    <div class="mb-3">
                        <h6 class="text-primary">Provize</h6>
                        <p class="small text-muted mb-0">
                            Výchozí provize je 10%. Můžete ji upravit podle typu obchodníka a objemu objednávek.
                        </p>
                    </div>
                    
                    <div>
                        <h6 class="text-primary">Status</h6>
                        <ul class="small text-muted mb-0">
                            <li><strong>Čeká na schválení:</strong> Nový obchodník</li>
                            <li><strong>Aktivní:</strong> Může objednávat</li>
                            <li><strong>Neaktivní:</strong> Pozastavený přístup</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Rychlé akce -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-bolt me-2"></i>
                        Rychlé akce
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-outline-info btn-sm" onclick="fillTestData()">
                            <i class="fas fa-magic me-2"></i>
                            Vyplnit testovací data
                        </button>
                        <a href="/admin/obchodnici" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-list me-2"></i>
                            Seznam obchodníků
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Validace formuláře
    const form = document.getElementById('dealerForm');
    
    // Uložit jako koncept
    document.getElementById('saveAsDraft').addEventListener('click', function() {
        document.getElementById('status').value = 'pending';
        form.submit();
    });
    
    // Formátování PSČ
    document.getElementById('postal_code').addEventListener('input', function() {
        let value = this.value.replace(/\s/g, '');
        if (value.length > 3) {
            value = value.substring(0, 3) + ' ' + value.substring(3, 5);
        }
        this.value = value;
    });
    
    // Validace IČO
    document.getElementById('ico').addEventListener('input', function() {
        this.value = this.value.replace(/\D/g, '').substring(0, 8);
    });
});

function fillTestData() {
    if (confirm('Opravdu chcete vyplnit testovací data? Současné údaje budou přepsány.')) {
        document.getElementById('company_name').value = 'Testovací obchod s.r.o.';
        document.getElementById('ico').value = '12345678';
        document.getElementById('dic').value = 'CZ12345678';
        document.getElementById('business_type').value = 'retail';
        document.getElementById('contact_person').value = 'Jan Novák';
        document.getElementById('position').value = 'jednatel';
        document.getElementById('email').value = '<EMAIL>';
        document.getElementById('phone').value = '+*********** 789';
        document.getElementById('address').value = 'Testovací 123';
        document.getElementById('city').value = 'Praha';
        document.getElementById('postal_code').value = '110 00';
        document.getElementById('region').value = 'Praha';
        document.getElementById('commission_rate').value = '10';
        document.getElementById('notes').value = 'Testovací obchodník vytvořený pro účely testování.';
    }
}
</script>
