<?php $this->layout('layouts/admin') ?>

<?php 
$headerActions = '
    <div class="btn-group" role="group">
        <a href="/admin/produkty" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i>
            Zpět na seznam
        </a>
    </div>
';
?>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-plus me-2"></i>
                    Nový produkt
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data" class="needs-validation" novalidate>
                    <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($csrf_token) ?>">
                    
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="name" class="form-label">Název produktu *</label>
                                <input type="text" 
                                       class="form-control" 
                                       id="name" 
                                       name="name" 
                                       value="<?= htmlspecialchars($old_data['name'] ?? '') ?>"
                                       required>
                                <div class="invalid-feedback">
                                    Zadejte název produktu.
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="category" class="form-label">Kategorie *</label>
                                <select class="form-select" id="category" name="category" required>
                                    <option value="">Vyberte kategorii</option>
                                    <?php foreach ($categories as $category): ?>
                                        <option value="<?= htmlspecialchars($category) ?>"
                                                <?= ($old_data['category'] ?? '') === $category ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($category) ?>
                                        </option>
                                    <?php endforeach; ?>
                                    <option value="new">+ Nová kategorie</option>
                                </select>
                                <div class="invalid-feedback">
                                    Vyberte kategorii produktu.
                                </div>
                            </div>
                            
                            <!-- Pole pro novou kategorii -->
                            <div class="mb-3" id="new-category-field" style="display: none;">
                                <label for="new_category" class="form-label">Nová kategorie</label>
                                <input type="text" 
                                       class="form-control" 
                                       id="new_category" 
                                       name="new_category" 
                                       placeholder="Název nové kategorie">
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">Popis produktu *</label>
                        <textarea class="form-control" 
                                  id="description" 
                                  name="description" 
                                  rows="4" 
                                  required><?= htmlspecialchars($old_data['description'] ?? '') ?></textarea>
                        <div class="invalid-feedback">
                            Zadejte popis produktu.
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="price" class="form-label">Cena (Kč) *</label>
                                <input type="number" 
                                       class="form-control" 
                                       id="price" 
                                       name="price" 
                                       min="0" 
                                       step="1"
                                       value="<?= htmlspecialchars($old_data['price'] ?? '') ?>"
                                       required>
                                <div class="invalid-feedback">
                                    Zadejte platnou cenu.
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="stock" class="form-label">Skladem (ks) *</label>
                                <input type="number" 
                                       class="form-control" 
                                       id="stock" 
                                       name="stock" 
                                       min="0" 
                                       value="<?= htmlspecialchars($old_data['stock'] ?? '0') ?>"
                                       required>
                                <div class="invalid-feedback">
                                    Zadejte počet kusů na skladě.
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="image" class="form-label">Obrázek produktu</label>
                                <input type="file" 
                                       class="form-control" 
                                       id="image" 
                                       name="image" 
                                       accept="image/*">
                                <div class="form-text">
                                    Podporované formáty: JPG, PNG, GIF, WebP. Max. 5MB.
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-check mb-3">
                                <input class="form-check-input" 
                                       type="checkbox" 
                                       id="active" 
                                       name="active" 
                                       value="1"
                                       <?= ($old_data['active'] ?? true) ? 'checked' : '' ?>>
                                <label class="form-check-label" for="active">
                                    Aktivní produkt
                                </label>
                                <div class="form-text">
                                    Aktivní produkty se zobrazují na webu.
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check mb-3">
                                <input class="form-check-input" 
                                       type="checkbox" 
                                       id="featured" 
                                       name="featured" 
                                       value="1"
                                       <?= ($old_data['featured'] ?? false) ? 'checked' : '' ?>>
                                <label class="form-check-label" for="featured">
                                    Doporučený produkt
                                </label>
                                <div class="form-text">
                                    Doporučené produkty se zobrazují na hlavní stránce.
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="/admin/produkty" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>
                            Zrušit
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            Uložit produkt
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    Nápověda
                </h6>
            </div>
            <div class="card-body">
                <h6>Název produktu</h6>
                <p class="small text-muted">Zadejte výstižný název produktu, který bude zobrazen zákazníkům.</p>
                
                <h6>Kategorie</h6>
                <p class="small text-muted">Vyberte existující kategorii nebo vytvořte novou pro lepší organizaci produktů.</p>
                
                <h6>Popis</h6>
                <p class="small text-muted">Detailní popis produktu včetně materiálu, rozměrů a dalších vlastností.</p>
                
                <h6>Obrázek</h6>
                <p class="small text-muted">Nahrajte kvalitní obrázek produktu. Doporučené rozměry: 800x800px.</p>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Zobrazení pole pro novou kategorii
    const categorySelect = document.getElementById('category');
    const newCategoryField = document.getElementById('new-category-field');
    const newCategoryInput = document.getElementById('new_category');
    
    categorySelect.addEventListener('change', function() {
        if (this.value === 'new') {
            newCategoryField.style.display = 'block';
            newCategoryInput.required = true;
        } else {
            newCategoryField.style.display = 'none';
            newCategoryInput.required = false;
            newCategoryInput.value = '';
        }
    });
    
    // Náhled obrázku
    const imageInput = document.getElementById('image');
    imageInput.addEventListener('change', function() {
        if (this.files && this.files[0]) {
            const reader = new FileReader();
            reader.onload = function(e) {
                let preview = document.getElementById('image-preview');
                if (!preview) {
                    preview = document.createElement('img');
                    preview.id = 'image-preview';
                    preview.className = 'img-thumbnail mt-2';
                    preview.style.maxWidth = '200px';
                    imageInput.parentNode.appendChild(preview);
                }
                preview.src = e.target.result;
            };
            reader.readAsDataURL(this.files[0]);
        }
    });
    
    // Bootstrap form validation
    const form = document.querySelector('.needs-validation');
    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        form.classList.add('was-validated');
    });
});
</script>
