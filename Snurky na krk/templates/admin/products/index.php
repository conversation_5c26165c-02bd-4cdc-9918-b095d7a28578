<?php $this->layout('layouts/admin') ?>

<?php 
$headerActions = '
    <div class="btn-group" role="group">
        <a href="/admin/produkty/novy" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>
            Nový produkt
        </a>
    </div>
';
?>

<!-- Filtry -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <label for="category" class="form-label">Kategorie</label>
                <select name="category" id="category" class="form-select">
                    <option value="">Všechny kategorie</option>
                    <?php foreach ($categories as $category): ?>
                        <option value="<?= htmlspecialchars($category) ?>" 
                                <?= $selected_category === $category ? 'selected' : '' ?>>
                            <?= htmlspecialchars($category) ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-6">
                <label for="search" class="form-label">Vyhledávání</label>
                <input type="text" name="search" id="search" class="form-control" 
                       placeholder="Název nebo popis produktu..." 
                       value="<?= htmlspecialchars($search) ?>">
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="fas fa-search me-2"></i>
                        Filtrovat
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Seznam produktů -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-box me-2"></i>
            Seznam produktů (<?= count($products) ?>)
        </h5>
    </div>
    <div class="card-body">
        <?php if (empty($products)): ?>
            <div class="text-center py-5">
                <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">Žádné produkty nenalezeny</h5>
                <p class="text-muted">Zkuste změnit filtry nebo přidejte nový produkt.</p>
                <a href="/admin/produkty/novy" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    Přidat první produkt
                </a>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Obrázek</th>
                            <th>Název</th>
                            <th>Kategorie</th>
                            <th>Cena</th>
                            <th>Sklad</th>
                            <th>Status</th>
                            <th>Akce</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($products as $product): ?>
                            <tr>
                                <td>
                                    <?php if ($product['image']): ?>
                                        <img src="<?= BASE_URL ?>/assets/uploads/<?= htmlspecialchars($product['image']) ?>" 
                                             alt="<?= htmlspecialchars($product['name']) ?>"
                                             class="img-thumbnail" style="width: 50px; height: 50px; object-fit: cover;">
                                    <?php else: ?>
                                        <div class="bg-light d-flex align-items-center justify-content-center" 
                                             style="width: 50px; height: 50px; border-radius: 0.375rem;">
                                            <i class="fas fa-image text-muted"></i>
                                        </div>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div>
                                        <strong><?= htmlspecialchars($product['name']) ?></strong>
                                        <?php if ($product['featured']): ?>
                                            <span class="badge bg-warning ms-2">Doporučený</span>
                                        <?php endif; ?>
                                    </div>
                                    <small class="text-muted">
                                        <?= $this->truncate($product['description'], 60) ?>
                                    </small>
                                </td>
                                <td>
                                    <span class="badge bg-secondary">
                                        <?= htmlspecialchars($product['category']) ?>
                                    </span>
                                </td>
                                <td>
                                    <strong><?= $this->formatPrice($product['price']) ?></strong>
                                </td>
                                <td>
                                    <?php if ($product['stock'] > 0): ?>
                                        <span class="text-success">
                                            <i class="fas fa-check-circle me-1"></i>
                                            <?= $product['stock'] ?> ks
                                        </span>
                                    <?php else: ?>
                                        <span class="text-danger">
                                            <i class="fas fa-times-circle me-1"></i>
                                            Vyprodáno
                                        </span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php $status = $this->formatStatus($product['active'] ? 'active' : 'inactive'); ?>
                                    <?= $status['badge'] ?>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="/admin/produkty/<?= $product['id'] ?>" 
                                           class="btn btn-outline-primary" 
                                           title="Editovat">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form method="POST" action="/admin/produkty/<?= $product['id'] ?>/smazat" 
                                              class="d-inline" onsubmit="return confirm('Opravdu chcete smazat tento produkt?')">
                                            <input type="hidden" name="csrf_token" value="<?= $this->csrfToken() ?>">
                                            <button type="submit" class="btn btn-outline-danger" title="Smazat">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-submit formuláře při změně filtru
    const categorySelect = document.getElementById('category');
    if (categorySelect) {
        categorySelect.addEventListener('change', function() {
            this.form.submit();
        });
    }
    
    // Potvrzení smazání
    const deleteButtons = document.querySelectorAll('form[action*="/smazat"]');
    deleteButtons.forEach(form => {
        form.addEventListener('submit', function(e) {
            if (!confirm('Opravdu chcete smazat tento produkt? Tato akce je nevratná.')) {
                e.preventDefault();
            }
        });
    });
});
</script>
