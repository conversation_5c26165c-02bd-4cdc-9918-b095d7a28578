<?php
/**
 * Admin šablona pro editaci produktu
 * 
 * <AUTHOR> Team
 * @version 1.0
 */

if (!defined('SNURKY_INIT')) {
    die('<PERSON>římý přístup není povolen');
}
?>

<div class="container-fluid">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="/admin/dashboard">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="/admin/produkty">Produkty</a></li>
            <li class="breadcrumb-item active" aria-current="page">Editace produktu</li>
        </ol>
    </nav>

    <!-- Hlavička -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-edit me-2"></i>
            Editace produktu: <?= htmlspecialchars($product['name']) ?>
        </h1>
        <div>
            <a href="/admin/produkty" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>
                Zpět na seznam
            </a>
        </div>
    </div>

    <!-- Flash zprávy -->
    <?php if ($flash = $this->getFlashMessage()): ?>
        <div class="alert alert-<?= $flash['type'] === 'error' ? 'danger' : $flash['type'] ?> alert-dismissible fade show" role="alert">
            <?= htmlspecialchars($flash['message']) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <!-- Formulář -->
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-box me-2"></i>
                        Informace o produktu
                    </h6>
                </div>
                <div class="card-body">
                    <form method="POST" enctype="multipart/form-data" id="productForm" class="needs-validation" novalidate>
                        <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($csrf_token) ?>">
                        
                        <!-- Základní informace -->
                        <div class="row mb-3">
                            <div class="col-md-8">
                                <label for="name" class="form-label">Název produktu *</label>
                                <input type="text" class="form-control" id="name" name="name" 
                                       value="<?= htmlspecialchars($product['name']) ?>" required>
                                <div class="invalid-feedback">
                                    Zadejte název produktu.
                                </div>
                            </div>
                            <div class="col-md-4">
                                <label for="sku" class="form-label">SKU</label>
                                <input type="text" class="form-control" id="sku" name="sku" 
                                       value="<?= htmlspecialchars($product['sku'] ?? '') ?>">
                            </div>
                        </div>

                        <!-- Kategorie a status -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="category" class="form-label">Kategorie *</label>
                                <select class="form-select" id="category" name="category" required>
                                    <option value="">Vyberte kategorii</option>
                                    <?php foreach ($categories as $category): ?>
                                        <option value="<?= htmlspecialchars($category) ?>" 
                                                <?= $product['category'] === $category ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($category) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="invalid-feedback">
                                    Vyberte kategorii produktu.
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label for="status" class="form-label">Status</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="active" <?= $product['status'] === 'active' ? 'selected' : '' ?>>Aktivní</option>
                                    <option value="inactive" <?= $product['status'] === 'inactive' ? 'selected' : '' ?>>Neaktivní</option>
                                    <option value="draft" <?= $product['status'] === 'draft' ? 'selected' : '' ?>>Koncept</option>
                                </select>
                            </div>
                        </div>

                        <!-- Popis -->
                        <div class="mb-3">
                            <label for="description" class="form-label">Popis produktu</label>
                            <textarea class="form-control" id="description" name="description" rows="4"><?= htmlspecialchars($product['description'] ?? '') ?></textarea>
                        </div>

                        <!-- Cena a sklad -->
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label for="price" class="form-label">Cena (Kč) *</label>
                                <input type="number" class="form-control" id="price" name="price" 
                                       value="<?= $product['price'] ?>" min="0" step="0.01" required>
                                <div class="invalid-feedback">
                                    Zadejte platnou cenu.
                                </div>
                            </div>
                            <div class="col-md-4">
                                <label for="stock" class="form-label">Skladem (ks)</label>
                                <input type="number" class="form-control" id="stock" name="stock" 
                                       value="<?= $product['stock'] ?? 0 ?>" min="0">
                            </div>
                            <div class="col-md-4">
                                <label for="weight" class="form-label">Hmotnost (g)</label>
                                <input type="number" class="form-control" id="weight" name="weight" 
                                       value="<?= $product['weight'] ?? '' ?>" min="0">
                            </div>
                        </div>

                        <!-- Obrázek -->
                        <div class="mb-3">
                            <label for="image" class="form-label">Obrázek produktu</label>
                            <?php if (!empty($product['image'])): ?>
                                <div class="mb-2">
                                    <img src="<?= htmlspecialchars($product['image']) ?>" 
                                         alt="Aktuální obrázek" class="img-thumbnail" style="max-width: 200px;">
                                    <div class="form-text">Aktuální obrázek</div>
                                </div>
                            <?php endif; ?>
                            <input type="file" class="form-control" id="image" name="image" accept="image/*">
                            <div class="form-text">Podporované formáty: JPG, PNG, GIF, WebP. Maximální velikost: 5MB.</div>
                        </div>

                        <!-- Specifikace -->
                        <div class="mb-3">
                            <label class="form-label">Specifikace</label>
                            <div id="specifications">
                                <?php if (!empty($product['specifications'])): ?>
                                    <?php foreach ($product['specifications'] as $key => $value): ?>
                                        <div class="row mb-2 specification-row">
                                            <div class="col-md-5">
                                                <input type="text" class="form-control" name="spec_keys[]" 
                                                       value="<?= htmlspecialchars($key) ?>" placeholder="Název vlastnosti">
                                            </div>
                                            <div class="col-md-5">
                                                <input type="text" class="form-control" name="spec_values[]" 
                                                       value="<?= htmlspecialchars($value) ?>" placeholder="Hodnota">
                                            </div>
                                            <div class="col-md-2">
                                                <button type="button" class="btn btn-outline-danger btn-sm remove-spec">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </div>
                            <button type="button" class="btn btn-outline-primary btn-sm" id="addSpecification">
                                <i class="fas fa-plus me-2"></i>
                                Přidat specifikaci
                            </button>
                        </div>

                        <!-- Tlačítka -->
                        <div class="d-flex justify-content-between">
                            <div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>
                                    Uložit změny
                                </button>
                                <button type="button" class="btn btn-outline-secondary" id="saveAsDraft">
                                    <i class="fas fa-file-alt me-2"></i>
                                    Uložit jako koncept
                                </button>
                            </div>
                            <div>
                                <a href="/admin/produkty" class="btn btn-secondary">
                                    <i class="fas fa-times me-2"></i>
                                    Zrušit
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Sidebar s informacemi -->
        <div class="col-lg-4">
            <!-- Informace o produktu -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-info-circle me-2"></i>
                        Informace
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <strong>ID produktu:</strong> <?= $product['id'] ?>
                    </div>
                    <div class="mb-2">
                        <strong>Vytvořeno:</strong> <?= date('d.m.Y H:i', strtotime($product['created_at'])) ?>
                    </div>
                    <?php if (!empty($product['updated_at'])): ?>
                        <div class="mb-2">
                            <strong>Upraveno:</strong> <?= date('d.m.Y H:i', strtotime($product['updated_at'])) ?>
                        </div>
                    <?php endif; ?>
                    <div class="mb-2">
                        <strong>Status:</strong> 
                        <span class="badge bg-<?= $product['status'] === 'active' ? 'success' : ($product['status'] === 'draft' ? 'warning' : 'secondary') ?>">
                            <?= ucfirst($product['status']) ?>
                        </span>
                    </div>
                </div>
            </div>

            <!-- Rychlé akce -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-bolt me-2"></i>
                        Rychlé akce
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="/produkt/<?= $product['id'] ?>" class="btn btn-outline-primary btn-sm" target="_blank">
                            <i class="fas fa-eye me-2"></i>
                            Zobrazit na webu
                        </a>
                        <button type="button" class="btn btn-outline-info btn-sm" onclick="duplicateProduct()">
                            <i class="fas fa-copy me-2"></i>
                            Duplikovat produkt
                        </button>
                        <button type="button" class="btn btn-outline-danger btn-sm" onclick="deleteProduct()">
                            <i class="fas fa-trash me-2"></i>
                            Smazat produkt
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Auto-save indikátor -->
<div id="autosaveIndicator" class="position-fixed bottom-0 end-0 m-3" style="display: none;">
    <div class="alert alert-info mb-0">
        <i class="fas fa-save me-2"></i>
        Automaticky uloženo
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Validace formuláře
    const form = document.getElementById('productForm');
    
    // Přidání specifikace
    document.getElementById('addSpecification').addEventListener('click', function() {
        const container = document.getElementById('specifications');
        const row = document.createElement('div');
        row.className = 'row mb-2 specification-row';
        row.innerHTML = `
            <div class="col-md-5">
                <input type="text" class="form-control" name="spec_keys[]" placeholder="Název vlastnosti">
            </div>
            <div class="col-md-5">
                <input type="text" class="form-control" name="spec_values[]" placeholder="Hodnota">
            </div>
            <div class="col-md-2">
                <button type="button" class="btn btn-outline-danger btn-sm remove-spec">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `;
        container.appendChild(row);
    });

    // Odstranění specifikace
    document.addEventListener('click', function(e) {
        if (e.target.closest('.remove-spec')) {
            e.target.closest('.specification-row').remove();
        }
    });

    // Auto-save funkcionalita
    let autoSaveTimeout;
    const formInputs = form.querySelectorAll('input, textarea, select');
    
    formInputs.forEach(input => {
        input.addEventListener('input', function() {
            clearTimeout(autoSaveTimeout);
            autoSaveTimeout = setTimeout(autoSave, 3000); // Auto-save po 3 sekundách
        });
    });

    function autoSave() {
        const formData = new FormData(form);
        formData.append('auto_save', '1');
        
        fetch(window.location.href, {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAutoSaveIndicator();
            }
        })
        .catch(error => {
            console.error('Auto-save error:', error);
        });
    }

    function showAutoSaveIndicator() {
        const indicator = document.getElementById('autosaveIndicator');
        indicator.style.display = 'block';
        setTimeout(() => {
            indicator.style.display = 'none';
        }, 2000);
    }

    // Uložit jako koncept
    document.getElementById('saveAsDraft').addEventListener('click', function() {
        document.getElementById('status').value = 'draft';
        form.submit();
    });
});

function duplicateProduct() {
    if (confirm('Opravdu chcete duplikovat tento produkt?')) {
        window.location.href = '/admin/produkty/duplikovat/<?= $product['id'] ?>';
    }
}

function deleteProduct() {
    if (confirm('Opravdu chcete smazat tento produkt? Tato akce je nevratná.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '/admin/produkty/<?= $product['id'] ?>/smazat';
        
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrf_token';
        csrfInput.value = '<?= htmlspecialchars($csrf_token) ?>';
        
        form.appendChild(csrfInput);
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
