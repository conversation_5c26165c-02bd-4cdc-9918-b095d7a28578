<!-- <PERSON>trace obchodníka -->
<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Header -->
            <div class="text-center mb-5">
                <h1>Registrace obchodníka</h1>
                <p class="lead text-muted">
                    Staňte se součástí naší sítě úspěšných obchodníků
                </p>
            </div>
            
            <!-- Výhody -->
            <div class="row mb-5">
                <div class="col-md-4 text-center mb-3">
                    <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" 
                         style="width: 60px; height: 60px;">
                        <i class="fas fa-percentage"></i>
                    </div>
                    <h5>Atraktivní provize</h5>
                    <p class="small text-muted">Výhodné provizn<PERSON> pod<PERSON>ky pro všechny produkty</p>
                </div>
                <div class="col-md-4 text-center mb-3">
                    <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" 
                         style="width: 60px; height: 60px;">
                        <i class="fas fa-headset"></i>
                    </div>
                    <h5>Podpora</h5>
                    <p class="small text-muted">Kompletní podpora při prodeji a marketingu</p>
                </div>
                <div class="col-md-4 text-center mb-3">
                    <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" 
                         style="width: 60px; height: 60px;">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h5>Růst</h5>
                    <p class="small text-muted">Možnost rozšíření obchodní činnosti</p>
                </div>
            </div>
            
            <!-- Formulář -->
            <div class="card shadow">
                <div class="card-header">
                    <h3 class="card-title mb-0">Registrační formulář</h3>
                </div>
                <div class="card-body">
                    <form method="POST" data-validate>
                        <?= $this->csrf() ?>
                        
                        <!-- Obchodní informace -->
                        <fieldset class="mb-4">
                            <legend class="h5 border-bottom pb-2 mb-3">Obchodní informace</legend>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="business_name" class="form-label">Název firmy *</label>
                                        <input type="text" 
                                               class="form-control <?= isset($errors['business_name']) ? 'is-invalid' : '' ?>" 
                                               id="business_name" 
                                               name="business_name" 
                                               value="<?= $this->escape($formData['business_name'] ?? '') ?>"
                                               required>
                                        <?php if (isset($errors['business_name'])): ?>
                                        <div class="invalid-feedback">
                                            <?= $this->escape($errors['business_name']) ?>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="contact_person" class="form-label">Kontaktní osoba *</label>
                                        <input type="text" 
                                               class="form-control <?= isset($errors['contact_person']) ? 'is-invalid' : '' ?>" 
                                               id="contact_person" 
                                               name="contact_person" 
                                               value="<?= $this->escape($formData['contact_person'] ?? '') ?>"
                                               required>
                                        <?php if (isset($errors['contact_person'])): ?>
                                        <div class="invalid-feedback">
                                            <?= $this->escape($errors['contact_person']) ?>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </fieldset>
                        
                        <!-- Kontaktní údaje -->
                        <fieldset class="mb-4">
                            <legend class="h5 border-bottom pb-2 mb-3">Kontaktní údaje</legend>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="email" class="form-label">Email *</label>
                                        <input type="email" 
                                               class="form-control <?= isset($errors['email']) ? 'is-invalid' : '' ?>" 
                                               id="email" 
                                               name="email" 
                                               value="<?= $this->escape($formData['email'] ?? '') ?>"
                                               required>
                                        <?php if (isset($errors['email'])): ?>
                                        <div class="invalid-feedback">
                                            <?= $this->escape($errors['email']) ?>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="phone" class="form-label">Telefon *</label>
                                        <input type="tel" 
                                               class="form-control <?= isset($errors['phone']) ? 'is-invalid' : '' ?>" 
                                               id="phone" 
                                               name="phone" 
                                               value="<?= $this->escape($formData['phone'] ?? '') ?>"
                                               required>
                                        <?php if (isset($errors['phone'])): ?>
                                        <div class="invalid-feedback">
                                            <?= $this->escape($errors['phone']) ?>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="website" class="form-label">Webové stránky</label>
                                <input type="url" 
                                       class="form-control <?= isset($errors['website']) ? 'is-invalid' : '' ?>" 
                                       id="website" 
                                       name="website" 
                                       value="<?= $this->escape($formData['website'] ?? '') ?>"
                                       placeholder="https://">
                                <?php if (isset($errors['website'])): ?>
                                <div class="invalid-feedback">
                                    <?= $this->escape($errors['website']) ?>
                                </div>
                                <?php endif; ?>
                            </div>
                        </fieldset>
                        
                        <!-- Adresa -->
                        <fieldset class="mb-4">
                            <legend class="h5 border-bottom pb-2 mb-3">Adresa</legend>
                            
                            <div class="mb-3">
                                <label for="street" class="form-label">Ulice a číslo popisné *</label>
                                <input type="text" 
                                       class="form-control <?= isset($errors['street']) ? 'is-invalid' : '' ?>" 
                                       id="street" 
                                       name="street" 
                                       value="<?= $this->escape($formData['street'] ?? '') ?>"
                                       required>
                                <?php if (isset($errors['street'])): ?>
                                <div class="invalid-feedback">
                                    <?= $this->escape($errors['street']) ?>
                                </div>
                                <?php endif; ?>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="city" class="form-label">Město *</label>
                                        <input type="text" 
                                               class="form-control <?= isset($errors['city']) ? 'is-invalid' : '' ?>" 
                                               id="city" 
                                               name="city" 
                                               value="<?= $this->escape($formData['city'] ?? '') ?>"
                                               required>
                                        <?php if (isset($errors['city'])): ?>
                                        <div class="invalid-feedback">
                                            <?= $this->escape($errors['city']) ?>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label for="zip" class="form-label">PSČ *</label>
                                        <input type="text" 
                                               class="form-control <?= isset($errors['zip']) ? 'is-invalid' : '' ?>" 
                                               id="zip" 
                                               name="zip" 
                                               value="<?= $this->escape($formData['zip'] ?? '') ?>"
                                               pattern="[0-9]{3}\s?[0-9]{2}"
                                               placeholder="123 45"
                                               required>
                                        <?php if (isset($errors['zip'])): ?>
                                        <div class="invalid-feedback">
                                            <?= $this->escape($errors['zip']) ?>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label for="region" class="form-label">Kraj *</label>
                                        <select class="form-select <?= isset($errors['region']) ? 'is-invalid' : '' ?>" 
                                                id="region" 
                                                name="region" 
                                                required>
                                            <option value="">Vyberte kraj</option>
                                            <?php
                                            $regions = [
                                                'Hlavní město Praha', 'Středočeský kraj', 'Jihočeský kraj',
                                                'Plzeňský kraj', 'Karlovarský kraj', 'Ústecký kraj',
                                                'Liberecký kraj', 'Královéhradecký kraj', 'Pardubický kraj',
                                                'Kraj Vysočina', 'Jihomoravský kraj', 'Olomoucký kraj',
                                                'Zlínský kraj', 'Moravskoslezský kraj'
                                            ];
                                            foreach ($regions as $region):
                                            ?>
                                            <option value="<?= $region ?>" <?= ($formData['region'] ?? '') === $region ? 'selected' : '' ?>>
                                                <?= $region ?>
                                            </option>
                                            <?php endforeach; ?>
                                        </select>
                                        <?php if (isset($errors['region'])): ?>
                                        <div class="invalid-feedback">
                                            <?= $this->escape($errors['region']) ?>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </fieldset>
                        
                        <!-- Popis -->
                        <fieldset class="mb-4">
                            <legend class="h5 border-bottom pb-2 mb-3">Dodatečné informace</legend>
                            
                            <div class="mb-3">
                                <label for="description" class="form-label">Popis vaší firmy</label>
                                <textarea class="form-control <?= isset($errors['description']) ? 'is-invalid' : '' ?>" 
                                          id="description" 
                                          name="description" 
                                          rows="4"
                                          placeholder="Stručně popište vaši firmu, zaměření, zkušenosti..."><?= $this->escape($formData['description'] ?? '') ?></textarea>
                                <?php if (isset($errors['description'])): ?>
                                <div class="invalid-feedback">
                                    <?= $this->escape($errors['description']) ?>
                                </div>
                                <?php endif; ?>
                                <div class="form-text">Tento popis bude zobrazen na stránce obchodníků</div>
                            </div>
                        </fieldset>
                        
                        <!-- Souhlas -->
                        <div class="mb-4">
                            <div class="form-check">
                                <input class="form-check-input <?= isset($errors['agree_terms']) ? 'is-invalid' : '' ?>" 
                                       type="checkbox" 
                                       id="agree_terms" 
                                       name="agree_terms" 
                                       value="1"
                                       <?= isset($formData['agree_terms']) ? 'checked' : '' ?>
                                       required>
                                <label class="form-check-label" for="agree_terms">
                                    Souhlasím s <a href="<?= $this->url('/obchodni-podminky') ?>" target="_blank">obchodními podmínkami</a> 
                                    a <a href="<?= $this->url('/ochrana-udaju') ?>" target="_blank">zásadami ochrany osobních údajů</a> *
                                </label>
                                <?php if (isset($errors['agree_terms'])): ?>
                                <div class="invalid-feedback">
                                    <?= $this->escape($errors['agree_terms']) ?>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <!-- Tlačítka -->
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="<?= $this->url('/obchodnici') ?>" class="btn btn-outline-secondary">
                                Zpět na seznam
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane me-2"></i>
                                Odeslat registraci
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Informace o procesu -->
            <div class="alert alert-info mt-4">
                <h5 class="alert-heading">
                    <i class="fas fa-info-circle me-2"></i>
                    Co bude následovat?
                </h5>
                <p class="mb-0">
                    Po odeslání registrace bude vaše žádost posouzena naším týmem. 
                    O výsledku vás budeme informovat emailem do 3 pracovních dnů.
                </p>
            </div>
        </div>
    </div>
</div>
