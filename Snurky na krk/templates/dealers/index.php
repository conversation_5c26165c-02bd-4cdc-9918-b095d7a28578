<!-- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> str<PERSON>ka -->
<div class="container">
    <!-- Header sekce -->
    <section class="dealers-header py-4">
        <div class="row">
            <div class="col-lg-8">
                <h1><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON></h1>
                <p class="text-muted">Naj<PERSON><PERSON><PERSON> si obchodníka s našimi produkty ve vašem okolí</p>
            </div>
            <div class="col-lg-4">
                <!-- Vyhledávání -->
                <form method="GET" class="mb-3">
                    <div class="input-group">
                        <input type="text" 
                               class="form-control" 
                               name="search" 
                               placeholder="Hledat obchodníky..." 
                               value="<?= $this->escape($currentSearch) ?>">
                        <button class="btn btn-outline-primary" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                    <?php if (!empty($currentRegion)): ?>
                    <input type="hidden" name="region" value="<?= $this->escape($currentRegion) ?>">
                    <?php endif; ?>
                </form>
            </div>
        </div>
        
        <div class="row">
            <div class="col-12">
                <!-- Filtry -->
                <div class="d-flex flex-wrap gap-2 mb-4">
                    <!-- Region filter -->
                    <div class="dropdown">
                        <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <?= !empty($currentRegion) ? $this->escape($currentRegion) : 'Všechny regiony' ?>
                        </button>
                        <ul class="dropdown-menu">
                            <li>
                                <a class="dropdown-item <?= empty($currentRegion) ? 'active' : '' ?>" 
                                   href="<?= $this->url('/obchodnici' . (!empty($currentSearch) ? '?search=' . urlencode($currentSearch) : '')) ?>">
                                    Všechny regiony
                                </a>
                            </li>
                            <?php foreach ($regions as $region): ?>
                            <li>
                                <a class="dropdown-item <?= $currentRegion === $region ? 'active' : '' ?>" 
                                   href="<?= $this->url('/obchodnici?region=' . urlencode($region) . (!empty($currentSearch) ? '&search=' . urlencode($currentSearch) : '')) ?>">
                                    <?= $this->escape($region) ?>
                                </a>
                            </li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                    
                    <!-- Vymazat filtry -->
                    <?php if (!empty($currentRegion) || !empty($currentSearch)): ?>
                    <a href="<?= $this->url('/obchodnici') ?>" class="btn btn-outline-danger">
                        <i class="fas fa-times me-1"></i>
                        Vymazat filtry
                    </a>
                    <?php endif; ?>
                </div>
                
                <!-- Počet výsledků -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <p class="text-muted mb-0">
                        Zobrazeno <?= count($merchants) ?> z <?= $pagination['total_items'] ?> obchodníků
                        <?php if (!empty($currentSearch)): ?>
                        pro "<?= $this->escape($currentSearch) ?>"
                        <?php endif; ?>
                        <?php if (!empty($currentRegion)): ?>
                        v regionu "<?= $this->escape($currentRegion) ?>"
                        <?php endif; ?>
                    </p>
                    
                    <a href="<?= $this->url('/obchodnici/registrace') ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        Stát se obchodníkem
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Obchodníci grid -->
    <section class="dealers-grid">
        <?php if (!empty($merchants)): ?>
        <div class="row">
            <?php foreach ($merchants as $merchant): ?>
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card h-100 shadow-sm">
                    <div class="card-body">
                        <!-- Logo obchodníka -->
                        <div class="text-center mb-3">
                            <?php if (!empty($merchant['logo'])): ?>
                            <img src="<?= $this->url('/assets/uploads/' . $merchant['logo']) ?>" 
                                 alt="<?= $this->escape($merchant['business_name']) ?>"
                                 class="img-fluid rounded"
                                 style="max-height: 80px; max-width: 120px;">
                            <?php else: ?>
                            <div class="bg-light rounded d-inline-flex align-items-center justify-content-center"
                                 style="width: 80px; height: 80px;">
                                <i class="fas fa-store fa-2x text-muted"></i>
                            </div>
                            <?php endif; ?>
                        </div>
                        
                        <!-- Název obchodníka -->
                        <h5 class="card-title text-center mb-3">
                            <?= $this->escape($merchant['business_name']) ?>
                        </h5>
                        
                        <!-- Kontaktní osoba -->
                        <?php if (!empty($merchant['contact_person'])): ?>
                        <p class="text-muted small mb-2">
                            <i class="fas fa-user me-2"></i>
                            <?= $this->escape($merchant['contact_person']) ?>
                        </p>
                        <?php endif; ?>
                        
                        <!-- Adresa -->
                        <div class="mb-3">
                            <p class="text-muted small mb-1">
                                <i class="fas fa-map-marker-alt me-2"></i>
                                <?php if (!empty($merchant['address'])): ?>
                                <?= $this->escape($merchant['address']['street'] ?? '') ?><br>
                                <?= $this->escape($merchant['address']['city'] ?? '') ?> 
                                <?= $this->escape($merchant['address']['zip'] ?? '') ?>
                                <?php if (!empty($merchant['address']['region'])): ?>
                                <br><small class="text-muted"><?= $this->escape($merchant['address']['region']) ?></small>
                                <?php endif; ?>
                                <?php endif; ?>
                            </p>
                        </div>
                        
                        <!-- Kontaktní informace -->
                        <div class="contact-info mb-3">
                            <?php if (!empty($merchant['email'])): ?>
                            <p class="small mb-1">
                                <i class="fas fa-envelope me-2"></i>
                                <a href="mailto:<?= $this->escape($merchant['email']) ?>" class="text-decoration-none">
                                    <?= $this->escape($merchant['email']) ?>
                                </a>
                            </p>
                            <?php endif; ?>
                            
                            <?php if (!empty($merchant['phone'])): ?>
                            <p class="small mb-1">
                                <i class="fas fa-phone me-2"></i>
                                <a href="tel:<?= $this->escape($merchant['phone']) ?>" class="text-decoration-none">
                                    <?= $this->escape($merchant['phone']) ?>
                                </a>
                            </p>
                            <?php endif; ?>
                            
                            <?php if (!empty($merchant['website'])): ?>
                            <p class="small mb-1">
                                <i class="fas fa-globe me-2"></i>
                                <a href="<?= $this->escape($merchant['website']) ?>" 
                                   target="_blank" 
                                   rel="noopener noreferrer" 
                                   class="text-decoration-none">
                                    Web
                                </a>
                            </p>
                            <?php endif; ?>
                        </div>
                        
                        <!-- Popis -->
                        <?php if (!empty($merchant['description'])): ?>
                        <p class="card-text small text-muted">
                            <?= $this->truncate($this->escape($merchant['description']), 100) ?>
                        </p>
                        <?php endif; ?>
                    </div>
                    
                    <div class="card-footer bg-transparent">
                        <div class="d-grid">
                            <a href="<?= $this->url('/obchodnik/' . $merchant['id']) ?>" 
                               class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-info-circle me-2"></i>
                                Více informací
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        
        <!-- Stránkování -->
        <?php if ($pagination['total'] > 1): ?>
        <nav aria-label="Stránkování obchodníků" class="mt-5">
            <ul class="pagination justify-content-center">
                <!-- Předchozí stránka -->
                <?php if ($pagination['current'] > 1): ?>
                <li class="page-item">
                    <a class="page-link" 
                       href="<?= $this->url('/obchodnici?' . http_build_query(array_filter(['region' => $currentRegion, 'search' => $currentSearch, 'page' => $pagination['current'] - 1]))) ?>">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                </li>
                <?php endif; ?>
                
                <!-- Čísla stránek -->
                <?php
                $start = max(1, $pagination['current'] - 2);
                $end = min($pagination['total'], $pagination['current'] + 2);
                ?>
                
                <?php for ($i = $start; $i <= $end; $i++): ?>
                <li class="page-item <?= $i === $pagination['current'] ? 'active' : '' ?>">
                    <a class="page-link" 
                       href="<?= $this->url('/obchodnici?' . http_build_query(array_filter(['region' => $currentRegion, 'search' => $currentSearch, 'page' => $i]))) ?>">
                        <?= $i ?>
                    </a>
                </li>
                <?php endfor; ?>
                
                <!-- Další stránka -->
                <?php if ($pagination['current'] < $pagination['total']): ?>
                <li class="page-item">
                    <a class="page-link" 
                       href="<?= $this->url('/obchodnici?' . http_build_query(array_filter(['region' => $currentRegion, 'search' => $currentSearch, 'page' => $pagination['current'] + 1]))) ?>">
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
                <?php endif; ?>
            </ul>
        </nav>
        <?php endif; ?>
        
        <?php else: ?>
        <!-- Žádní obchodníci -->
        <div class="text-center py-5">
            <i class="fas fa-store fa-3x text-muted mb-3"></i>
            <h3>Žádní obchodníci nenalezeni</h3>
            <p class="text-muted">
                <?php if (!empty($currentSearch) || !empty($currentRegion)): ?>
                Zkuste změnit kritéria vyhledávání nebo <a href="<?= $this->url('/obchodnici') ?>">zobrazit všechny obchodníky</a>.
                <?php else: ?>
                V současné době nemáme žádné registrované obchodníky.
                <?php endif; ?>
            </p>
            <a href="<?= $this->url('/obchodnici/registrace') ?>" class="btn btn-primary mt-3">
                <i class="fas fa-plus me-2"></i>
                Staňte se prvním obchodníkem
            </a>
        </div>
        <?php endif; ?>
    </section>
    
    <!-- Call to Action -->
    <section class="cta-section py-5 mt-5 bg-light rounded">
        <div class="text-center">
            <h3>Chcete se stát naším obchodníkem?</h3>
            <p class="text-muted mb-4">
                Připojte se k naší síti úspěšných obchodníků a začněte prodávat kvalitní šňůrky na krk.
            </p>
            <a href="<?= $this->url('/obchodnici/registrace') ?>" class="btn btn-primary btn-lg">
                <i class="fas fa-handshake me-2"></i>
                Registrovat se jako obchodník
            </a>
        </div>
    </section>
</div>
