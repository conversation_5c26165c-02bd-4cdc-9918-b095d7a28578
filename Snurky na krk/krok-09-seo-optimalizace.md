# Krok 9: SEO optimalizace a finalizace

## C<PERSON>l kroku
Implementovat pokročilé SEO funkce, optimalizovat výkon webu a dokončit všechny frontend komponenty.

## Úkoly

### 9.1 SEO meta tagy a strukturovaná data
- SeoManager.php - správa SEO elementů
- Dynamické meta tagy pro každou stránku
- Open Graph a Twitter Cards
- JSON-LD strukturovaná data

#### Meta tagy template:
```html
<title>{{page_title}} - {{site_title}}</title>
<meta name="description" content="{{page_description}}">
<meta name="keywords" content="{{page_keywords}}">
<meta name="robots" content="index, follow">
<link rel="canonical" href="{{canonical_url}}">

<!-- Open Graph -->
<meta property="og:title" content="{{og_title}}">
<meta property="og:description" content="{{og_description}}">
<meta property="og:image" content="{{og_image}}">
<meta property="og:url" content="{{og_url}}">
<meta property="og:type" content="website">

<!-- Twitter Cards -->
<meta name="twitter:card" content="summary_large_image">
<meta name="twitter:title" content="{{twitter_title}}">
<meta name="twitter:description" content="{{twitter_description}}">
<meta name="twitter:image" content="{{twitter_image}}">
```

### 9.2 Strukturovaná data (Schema.org)
- Organization schema pro firmu
- Product schema pro produkty
- LocalBusiness schema pro obchodníky
- BreadcrumbList schema

#### Organization schema:
```json
{
  "@context": "https://schema.org",
  "@type": "Organization",
  "name": "SnurkyNaKrk.cz",
  "url": "https://snurkynakrekt.cz",
  "logo": "https://snurkynakrekt.cz/assets/images/logo.png",
  "contactPoint": {
    "@type": "ContactPoint",
    "telephone": "{{phone}}",
    "email": "{{email}}",
    "contactType": "customer service"
  },
  "address": {
    "@type": "PostalAddress",
    "addressCountry": "CZ",
    "addressLocality": "{{city}}"
  }
}
```

### 9.3 Sitemap.xml generování
- SitemapGenerator.php - automatické generování sitemap
- Dynamické URL z databáze
- Priorita a frekvence změn
- Image sitemap pro produkty

#### Sitemap struktura:
```xml
<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <url>
    <loc>https://snurkynakrekt.cz/</loc>
    <lastmod>{{last_modified}}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>1.0</priority>
  </url>
  <!-- Dynamické URL pro produkty a obchodníky -->
</urlset>
```

### 9.4 Performance optimalizace
- CSS/JS minifikace a komprese
- Image lazy loading
- Critical CSS inline
- Gzip komprese

#### Performance funkce:
```php
class PerformanceOptimizer {
  public function minifyCSS($css) {}
  public function minifyJS($js) {}
  public function generateCriticalCSS($url) {}
  public function optimizeImages($directory) {}
  public function enableGzipCompression() {}
}
```

### 9.5 Robots.txt a .htaccess optimalizace
- robots.txt pro správné indexování
- .htaccess pro cache headers
- Redirect pravidla
- Security headers

#### .htaccess optimalizace:
```apache
# Cache headers
<IfModule mod_expires.c>
  ExpiresActive on
  ExpiresByType text/css "access plus 1 year"
  ExpiresByType application/javascript "access plus 1 year"
  ExpiresByType image/png "access plus 1 year"
</IfModule>

# Gzip compression
<IfModule mod_deflate.c>
  AddOutputFilterByType DEFLATE text/plain
  AddOutputFilterByType DEFLATE text/html
  AddOutputFilterByType DEFLATE text/css
</IfModule>
```

### 9.6 Accessibility (A11y) vylepšení
- ARIA labels a roles
- Keyboard navigation
- Screen reader optimalizace
- Color contrast kontrola

#### Accessibility checklist:
- Alt texty pro všechny obrázky
- Proper heading hierarchy (H1-H6)
- Focus indicators
- Skip links
- Form labels association

### 9.7 Progressive Web App (PWA) základy
- manifest.json soubor
- Service worker pro offline cache
- App icons různých velikostí
- Installable web app

#### manifest.json:
```json
{
  "name": "SnurkyNaKrk.cz",
  "short_name": "SnurkyNaKrk",
  "description": "Oficiální web značky SnurkyNaKrk",
  "start_url": "/",
  "display": "standalone",
  "background_color": "#ffffff",
  "theme_color": "#000000",
  "icons": [
    {
      "src": "/assets/icons/icon-192.png",
      "sizes": "192x192",
      "type": "image/png"
    }
  ]
}
```

### 9.8 Analytics a monitoring
- Google Analytics 4 integrace
- Google Search Console setup
- Error tracking (volitelně)
- Performance monitoring

#### Analytics implementace:
```html
<!-- Google Analytics 4 -->
<script async src="https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'GA_MEASUREMENT_ID');
</script>
```

### 9.9 Cross-browser testing
- Testování ve všech hlavních prohlížečích
- Polyfills pro starší prohlížeče
- Graceful degradation
- Feature detection

### 9.10 Mobile optimalizace
- Touch-friendly interface
- Viewport optimalizace
- Mobile-specific features
- App-like experience

## Výstupy kroku
- ✅ Kompletní SEO optimalizace
- ✅ Strukturovaná data
- ✅ Automatické sitemap generování
- ✅ Performance optimalizace
- ✅ Accessibility vylepšení
- ✅ PWA základy
- ✅ Analytics integrace
- ✅ Cross-browser kompatibilita
- ✅ Mobile optimalizace

## Testování
- SEO audit (Google PageSpeed, Lighthouse)
- Accessibility testing (WAVE, axe)
- Cross-browser testing
- Mobile device testing
- Performance benchmarking
- Search Console validation

## Následující krok
Krok 10: Dokumentace a příprava pro nasazení
