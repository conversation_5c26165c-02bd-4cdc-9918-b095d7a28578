<?php
/**
 * Vstupní bod pro administrační rozhra<PERSON>NaKrk.cz
 * 
 * <AUTHOR> Team
 * @version 1.0
 */

// Definice konstanty pro zabránění přímému přístupu k souborům
define('SNURKY_INIT', true);
define('ADMIN_AREA', true);

// Načtení základní konfigurace
require_once dirname(__DIR__) . '/config/config.php';
require_once dirname(__DIR__) . '/config/constants.php';
require_once dirname(__DIR__) . '/config/autoload.php';

// Nastavení error handleru pro admin
if (!DEBUG_MODE) {
    set_error_handler(function($severity, $message, $file, $line) {
        error_log("Admin Error: $message in $file on line $line");
        return true;
    });
    
    set_exception_handler(function($exception) {
        error_log("Admin Uncaught exception: " . $exception->getMessage());
        http_response_code(500);
        include ADMIN_PATH . '/templates/error.php';
        exit;
    });
}

try {
    // Inicializace admin routeru
    $router = new Router();
    
    // Definice routes pro admin
    $router->addRoute('GET', '/admin', 'AdminAuthController@loginForm');
    $router->addRoute('GET', '/admin/', 'AdminAuthController@loginForm');
    $router->addRoute('GET', '/admin/login', 'AdminAuthController@loginForm');
    $router->addRoute('POST', '/admin/login', 'AdminAuthController@login');
    $router->addRoute('GET', '/admin/logout', 'AdminAuthController@logout');
    
    // Chráněné admin routes (vyžadují přihlášení)
    $router->addRoute('GET', '/admin/dashboard', 'AdminDashboardController@index');
    $router->addRoute('GET', '/admin/produkty', 'AdminProductController@index');
    $router->addRoute('GET', '/admin/produkty/novy', 'AdminProductController@create');
    $router->addRoute('POST', '/admin/produkty/novy', 'AdminProductController@store');
    $router->addRoute('GET', '/admin/produkty/{id}', 'AdminProductController@edit');
    $router->addRoute('POST', '/admin/produkty/{id}', 'AdminProductController@update');
    $router->addRoute('POST', '/admin/produkty/{id}/smazat', 'AdminProductController@delete');
    
    $router->addRoute('GET', '/admin/obchodnici', 'AdminDealerController@index');
    $router->addRoute('GET', '/admin/obchodnici/novy', 'AdminDealerController@create');
    $router->addRoute('POST', '/admin/obchodnici/novy', 'AdminDealerController@store');
    $router->addRoute('GET', '/admin/obchodnici/{id}', 'AdminDealerController@edit');
    $router->addRoute('POST', '/admin/obchodnici/{id}', 'AdminDealerController@update');
    $router->addRoute('POST', '/admin/obchodnici/{id}/smazat', 'AdminDealerController@delete');
    
    $router->addRoute('GET', '/admin/nastaveni', 'AdminSettingsController@index');
    $router->addRoute('POST', '/admin/nastaveni', 'AdminSettingsController@update');
    
    // Získání aktuální URL
    $requestUri = $_SERVER['REQUEST_URI'];
    $requestMethod = $_SERVER['REQUEST_METHOD'];
    
    // Odstranění query parametrů z URL
    $requestUri = strtok($requestUri, '?');
    
    // Spuštění routeru
    $router->dispatch($requestMethod, $requestUri);
    
} catch (Exception $e) {
    // Logování chyby
    error_log("Admin application error: " . $e->getMessage());
    
    if (DEBUG_MODE) {
        // V debug režimu zobrazíme detailní chybu
        echo "<h1>Chyba admin aplikace</h1>";
        echo "<p><strong>Zpráva:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
        echo "<p><strong>Soubor:</strong> " . htmlspecialchars($e->getFile()) . "</p>";
        echo "<p><strong>Řádek:</strong> " . $e->getLine() . "</p>";
        echo "<h3>Stack trace:</h3>";
        echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
    } else {
        // V produkci zobrazíme obecnou chybovou stránku
        http_response_code(500);
        if (file_exists(ADMIN_PATH . '/templates/error.php')) {
            include ADMIN_PATH . '/templates/error.php';
        } else {
            echo "<h1>Omlouváme se, došlo k chybě v administraci</h1>";
            echo "<p>Zkuste to prosím později.</p>";
        }
    }
}
