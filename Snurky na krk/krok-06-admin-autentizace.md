# Krok 6: Administrace - autentizace

## C<PERSON><PERSON> kroku
Implementovat bezpečný přihlašovací systém pro administraci s ochranou proti neoprávněnému přístupu.

## Úkoly

### 6.1 Autentizační systém
- AuthManager.php - spr<PERSON>va autentizace
- <PERSON><PERSON><PERSON><PERSON> (password_hash/password_verify)
- Session management pro admin
- Remember me funkcionalita

#### Struktura admin uživatele:
```json
{
  "admin": {
    "username": "admin",
    "password_hash": "$2y$10$...",
    "email": "<EMAIL>",
    "last_login": "2024-01-01 12:00:00",
    "login_attempts": 0,
    "locked_until": null
  }
}
```

### 6.2 Přihlašovací stránka
- login.php - přihlašovací formulář
- Responzivn<PERSON> design
- CSRF ochrana
- Rate limiting proti brute force

#### Přihlašovací formulář:
```html
<form class="login-form" method="POST">
  <input type="hidden" name="csrf_token" value="{{csrf_token}}">
  <div class="form-group">
    <label for="username">Uživatelské jméno</label>
    <input type="text" name="username" required>
  </div>
  <div class="form-group">
    <label for="password">Heslo</label>
    <input type="password" name="password" required>
  </div>
  <div class="form-group">
    <label>
      <input type="checkbox" name="remember_me">
      Zapamatovat si mě
    </label>
  </div>
  <button type="submit">Přihlásit se</button>
</form>
```

### 6.3 Bezpečnostní opatření
- Brute force ochrana (max 5 pokusů za 15 minut)
- IP blocking při opakovaných pokusech
- Session timeout (30 minut nečinnosti)
- Secure cookies (httpOnly, secure, sameSite)

#### Bezpečnostní funkce:
```php
- checkLoginAttempts($username, $ip)
- lockAccount($username, $duration)
- isAccountLocked($username)
- logSecurityEvent($event, $details)
- validateSession()
- regenerateSessionId()
```

### 6.4 Middleware pro ochranu admin stránek
- AdminMiddleware.php - kontrola přihlášení
- Automatické přesměrování na login
- Kontrola oprávnění
- Session validace

### 6.5 Logout funkcionalita
- Bezpečné odhlášení
- Zničení session
- Smazání remember me cookies
- Redirect na hlavní stránku

### 6.6 Změna hesla
- Formulář pro změnu hesla
- Validace současného hesla
- Silné heslo requirements
- Potvrzení změny emailem

#### Validace hesla:
```php
- Minimálně 8 znaků
- Alespoň 1 velké písmeno
- Alespoň 1 malé písmeno
- Alespoň 1 číslo
- Alespoň 1 speciální znak
```

### 6.7 Admin CSS a JavaScript
- admin.css - styly pro admin rozhraní
- admin.js - JavaScript pro admin funkcionalita
- Tmavý/světlý režim
- Responzivní admin layout

### 6.8 Logování a monitoring
- SecurityLogger.php - logování bezpečnostních událostí
- Login/logout logy
- Failed login attempts
- Suspicious activity detection

## Výstupy kroku
- ✅ Bezpečný autentizační systém
- ✅ Přihlašovací stránka
- ✅ Brute force ochrana
- ✅ Session management
- ✅ Admin middleware
- ✅ Logout funkcionalita
- ✅ Změna hesla
- ✅ Bezpečnostní logování

## Testování
- Test přihlášení s správnými údaji
- Test nesprávných přihlašovacích údajů
- Test brute force ochrany
- Test session timeout
- Test logout funkcionalita
- Test změny hesla
- Penetrační testování admin sekce

## Následující krok
Krok 7: Administrace - správa základního obsahu webu
