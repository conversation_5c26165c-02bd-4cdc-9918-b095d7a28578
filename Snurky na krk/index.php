<?php
/**
 * <PERSON>lavn<PERSON> vstupní bod aplikace SnurkyNaKrk.cz
 * 
 * <AUTHOR> Team
 * @version 1.0
 */

// Definice konstanty pro zabránění přímému přístupu k souborům
define('SNURKY_INIT', true);

// Načtení základní konfigurace
require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/config/constants.php';
require_once __DIR__ . '/config/autoload.php';

// Nastavení error handleru pro produkci
if (!DEBUG_MODE) {
    set_error_handler(function($severity, $message, $file, $line) {
        error_log("Error: $message in $file on line $line");
        return true;
    });
    
    set_exception_handler(function($exception) {
        error_log("Uncaught exception: " . $exception->getMessage());
        http_response_code(500);
        include TEMPLATES_PATH . '/error.php';
        exit;
    });
}

try {
    // Inicializace aplikace
    $router = new Router();
    
    // Definice routes pro veřejné stránky
    $router->addRoute('GET', '/', 'HomeController@index');
    $router->addRoute('GET', '/home', 'HomeController@index');
    $router->addRoute('GET', '/produkty', 'ProductController@index');
    $router->addRoute('GET', '/produkt/{id}', 'ProductController@detail');
    $router->addRoute('GET', '/obchodnici', 'DealerController@index');
    $router->addRoute('GET', '/obchodnik/{id}', 'DealerController@detail');
    $router->addRoute('GET', '/kontakt', 'ContactController@index');
    $router->addRoute('POST', '/kontakt', 'ContactController@send');
    $router->addRoute('GET', '/o-nas', 'HomeController@about');
    
    // Získání aktuální URL
    $requestUri = $_SERVER['REQUEST_URI'];
    $requestMethod = $_SERVER['REQUEST_METHOD'];
    
    // Odstranění query parametrů z URL
    $requestUri = strtok($requestUri, '?');
    
    // Spuštění routeru
    $router->dispatch($requestMethod, $requestUri);
    
} catch (Exception $e) {
    // Logování chyby
    error_log("Application error: " . $e->getMessage());
    
    if (DEBUG_MODE) {
        // V debug režimu zobrazíme detailní chybu
        echo "<h1>Chyba aplikace</h1>";
        echo "<p><strong>Zpráva:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
        echo "<p><strong>Soubor:</strong> " . htmlspecialchars($e->getFile()) . "</p>";
        echo "<p><strong>Řádek:</strong> " . $e->getLine() . "</p>";
        echo "<h3>Stack trace:</h3>";
        echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
    } else {
        // V produkci zobrazíme obecnou chybovou stránku
        http_response_code(500);
        if (file_exists(TEMPLATES_PATH . '/error.php')) {
            include TEMPLATES_PATH . '/error.php';
        } else {
            echo "<h1>Omlouváme se, došlo k chybě</h1>";
            echo "<p>Zkuste to prosím později.</p>";
        }
    }
}
