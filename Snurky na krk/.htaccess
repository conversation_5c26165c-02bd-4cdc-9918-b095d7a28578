# .htaccess pro SnurkyNaKrk.cz
# Konfigurace Apache serveru

# Zapnutí URL rewriting
RewriteEngine On

# Bezpečnost<PERSON><PERSON>
<IfModule mod_headers.c>
    # Ochrana proti XSS
    Header always set X-XSS-Protection "1; mode=block"
    
    # Ochrana proti MIME type sniffing
    Header always set X-Content-Type-Options "nosniff"
    
    # Ochrana proti clickjacking
    Header always set X-Frame-Options "SAMEORIGIN"
    
    # Referrer policy
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    
    # Content Security Policy (základní)
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; font-src 'self'"
</IfModule>

# Ochrana citlivých souborů a adresářů
<FilesMatch "\.(json|log|md|txt|bak|backup|old)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# O<PERSON>rana konfiguračn<PERSON><PERSON> souborů
<Files "config.php">
    Order Allow,Deny
    Deny from all
</Files>

<Files "constants.php">
    Order Allow,Deny
    Deny from all
</Files>

<Files "autoload.php">
    Order Allow,Deny
    Deny from all
</Files>

# Ochrana data adresáře
<Directory "data">
    Order Allow,Deny
    Deny from all
</Directory>

# Ochrana includes adresáře
<Directory "includes">
    Order Allow,Deny
    Deny from all
</Directory>

# Ochrana templates adresáře
<Directory "templates">
    Order Allow,Deny
    Deny from all
</Directory>

# Ochrana config adresáře
<Directory "config">
    Order Allow,Deny
    Deny from all
</Directory>

# Povolení přístupu k assets
<Directory "assets">
    Order Allow,Deny
    Allow from all
</Directory>

# Povolení přístupu k cache assetům
<Directory "data/cache/assets">
    Order Allow,Deny
    Allow from all

    # Cache headers pro optimalizované assety
    <IfModule mod_expires.c>
        ExpiresActive on
        ExpiresByType text/css "access plus 1 month"
        ExpiresByType application/javascript "access plus 1 month"
    </IfModule>

    # Gzip komprese pro optimalizované assety
    <IfModule mod_deflate.c>
        AddOutputFilterByType DEFLATE text/css application/javascript
    </IfModule>
</Directory>

# Komprese souborů
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>

# Cache hlavičky pro statické soubory
<IfModule mod_expires.c>
    ExpiresActive On
    
    # Obrázky
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/webp "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
    
    # CSS a JavaScript
    ExpiresByType text/css "access plus 1 week"
    ExpiresByType application/javascript "access plus 1 week"
    ExpiresByType application/x-javascript "access plus 1 week"
    
    # Fonty
    ExpiresByType font/woff "access plus 1 month"
    ExpiresByType font/woff2 "access plus 1 month"
    ExpiresByType application/font-woff "access plus 1 month"
    ExpiresByType application/font-woff2 "access plus 1 month"
    
    # HTML
    ExpiresByType text/html "access plus 1 hour"
</IfModule>

# URL Rewriting pravidla
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d

# Přesměrování admin požadavků
RewriteRule ^admin/?(.*)$ admin/index.php [QSA,L]

# Přesměrování všech ostatních požadavků na index.php
RewriteRule ^(.*)$ index.php [QSA,L]

# Přesměrování na HTTPS (odkomentovat v produkci)
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Přesměrování www na non-www (nebo naopak podle preference)
# RewriteCond %{HTTP_HOST} ^www\.(.*)$ [NC]
# RewriteRule ^(.*)$ https://%1/$1 [R=301,L]

# Ochrana proti hotlinking obrázků
RewriteCond %{HTTP_REFERER} !^$
RewriteCond %{HTTP_REFERER} !^https?://(www\.)?snurkynakrekt\.cz [NC]
RewriteCond %{REQUEST_URI} \.(jpe?g|png|gif|webp)$ [NC]
RewriteRule \.(jpe?g|png|gif|webp)$ - [F]

# Nastavení výchozího charset
AddDefaultCharset UTF-8

# Nastavení MIME typů
<IfModule mod_mime.c>
    AddType application/javascript .js
    AddType text/css .css
    AddType image/webp .webp
    AddType font/woff .woff
    AddType font/woff2 .woff2
</IfModule>

# Ochrana proti directory browsing
Options -Indexes

# Nastavení error stránek
ErrorDocument 404 /404.php
ErrorDocument 403 /403.php
ErrorDocument 500 /500.php
