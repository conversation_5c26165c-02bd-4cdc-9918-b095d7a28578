<?php
/**
 * Kompletní test všech funkcionalit aplikace
 * Testuje frontend, backend, admin rozhraní a optimalizace
 */

define('SNURKY_INIT', true);

require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/config/constants.php';
require_once __DIR__ . '/config/autoload.php';

echo "🚀 Kompletní test aplikace Snurky na krk\n";
echo "=" . str_repeat("=", 60) . "\n\n";

$errors = [];
$warnings = [];
$passed = 0;
$total = 0;

function runTest($name, $callback) {
    global $errors, $warnings, $passed, $total;
    $total++;
    
    echo "🧪 Test: $name\n";
    echo "-" . str_repeat("-", 40) . "\n";
    
    try {
        $result = $callback();
        if ($result === true) {
            echo "✅ PROŠEL\n\n";
            $passed++;
        } else {
            echo "❌ SELHAL: $result\n\n";
            $errors[] = "$name: $result";
        }
    } catch (Exception $e) {
        echo "❌ CHYBA: " . $e->getMessage() . "\n\n";
        $errors[] = "$name: " . $e->getMessage();
    }
}

// Test 1: Základní třídy a autoloader
runTest("Základní třídy a autoloader", function() {
    $classes = [
        'Router', 'TemplateEngine', 'Security', 'SessionManager', 
        'ErrorHandler', 'CacheManager', 'AssetOptimizer', 'SEOManager',
        'BaseModel', 'ProductModel', 'MerchantModel', 'OrderModel', 'SettingsModel'
    ];
    
    foreach ($classes as $class) {
        if (!class_exists($class)) {
            return "Třída $class neexistuje";
        }
    }
    
    return true;
});

// Test 2: Datové modely
runTest("Datové modely a CRUD operace", function() {
    $productModel = new ProductModel();
    $products = $productModel->getAll();
    
    if (!is_array($products)) {
        return "ProductModel::getAll() nevrací pole";
    }
    
    $categories = $productModel->getCategories();
    if (!is_array($categories)) {
        return "ProductModel::getCategories() nevrací pole";
    }
    
    return true;
});

// Test 3: Cache systém
runTest("Cache systém", function() {
    $cache = new CacheManager();
    
    // Test základních operací
    $testData = ['test' => 'cache_data', 'time' => time()];
    $cache->set('test_cache', $testData, 60);
    
    $retrieved = $cache->get('test_cache');
    if ($retrieved !== $testData) {
        return "Cache set/get nefunguje";
    }
    
    if (!$cache->has('test_cache')) {
        return "Cache has() nefunguje";
    }
    
    $cache->delete('test_cache');
    if ($cache->has('test_cache')) {
        return "Cache delete() nefunguje";
    }
    
    return true;
});

// Test 4: Asset optimalizace
runTest("Asset optimalizace", function() {
    $optimizer = new AssetOptimizer();
    
    // Test CSS optimalizace
    $cssFiles = ['css/style.css'];
    $optimizedCSS = $optimizer->optimizeCSS($cssFiles, 'test_complete.min.css');
    
    if (!strpos($optimizedCSS, '.min.css')) {
        return "CSS optimalizace nefunguje";
    }
    
    // Test JS optimalizace
    $jsFiles = ['js/app.js'];
    $optimizedJS = $optimizer->optimizeJS($jsFiles, 'test_complete.min.js');
    
    if (!strpos($optimizedJS, '.min.js')) {
        return "JS optimalizace nefunguje";
    }
    
    return true;
});

// Test 5: Template Engine
runTest("Template Engine a rendering", function() {
    $template = new TemplateEngine();
    
    // Test základního renderování
    $template->setGlobalData('test_var', 'test_value');
    $globalData = $template->getGlobalData('test_var');
    
    if ($globalData !== 'test_value') {
        return "Template Engine global data nefungují";
    }
    
    // Test helper metod
    $escaped = $template->escape('<script>alert("test")</script>');
    if (strpos($escaped, '<script>') !== false) {
        return "Template Engine escape nefunguje";
    }
    
    $price = $template->formatPrice(1234);
    if (!strpos($price, '1 234')) {
        return "Template Engine formatPrice nefunguje";
    }
    
    return true;
});

// Test 6: SEO Manager
runTest("SEO Manager a meta tagy", function() {
    $seo = new SEOManager();
    
    // Test meta tagů
    $metaTags = $seo->generateMetaTags('home');
    if (!strpos($metaTags, '<title>')) {
        return "SEO meta tagy se negenerují";
    }
    
    // Test strukturovaných dat
    $structuredData = $seo->generateStructuredData('organization');
    if (!strpos($structuredData, 'application/ld+json')) {
        return "SEO strukturovaná data se negenerují";
    }
    
    return true;
});

// Test 7: Bezpečnostní funkce
runTest("Bezpečnostní funkce", function() {
    $security = new Security();
    
    // Test CSRF tokenu
    $token = $security->generateCSRFToken();
    if (empty($token)) {
        return "CSRF token se negeneruje";
    }
    
    // Test sanitizace
    $dirty = '<script>alert("xss")</script>';
    $clean = $security->sanitizeInput($dirty);
    if (strpos($clean, '<script>') !== false) {
        return "Input sanitizace nefunguje";
    }
    
    // Test validace emailu
    if (!$security->validateEmail('<EMAIL>')) {
        return "Email validace nefunguje";
    }
    
    if ($security->validateEmail('invalid-email')) {
        return "Email validace je příliš benevolentní";
    }
    
    return true;
});

// Test 8: Session Manager
runTest("Session Manager", function() {
    $session = new SessionManager();
    
    // Test flash zpráv
    $session->setFlashMessage('Test zpráva', 'success');
    $flash = $session->getFlashMessage();
    
    if (!$flash || $flash['message'] !== 'Test zpráva') {
        return "Flash zprávy nefungují";
    }
    
    return true;
});

// Test 9: Router
runTest("Router a URL routing", function() {
    $router = new Router();
    
    // Přidání test route
    $router->addRoute('GET', '/test', 'TestController@index');
    
    // Router by měl existovat a mít metody
    if (!method_exists($router, 'addRoute') || !method_exists($router, 'dispatch')) {
        return "Router nemá požadované metody";
    }
    
    return true;
});

// Test 10: Adresářová struktura
runTest("Adresářová struktura a oprávnění", function() {
    $requiredDirs = [
        'assets', 'assets/css', 'assets/js', 'assets/images', 'assets/uploads',
        'config', 'data', 'data/cache', 'data/cache/assets',
        'includes', 'includes/models', 'includes/controllers', 'includes/helpers',
        'templates', 'templates/layouts', 'admin'
    ];
    
    foreach ($requiredDirs as $dir) {
        $fullPath = ROOT_PATH . '/' . $dir;
        if (!is_dir($fullPath)) {
            return "Adresář $dir neexistuje";
        }
    }
    
    // Test zapisovatelných adresářů
    $writableDirs = ['data', 'data/cache', 'data/cache/assets', 'assets/uploads'];
    foreach ($writableDirs as $dir) {
        $fullPath = ROOT_PATH . '/' . $dir;
        if (!is_writable($fullPath)) {
            return "Adresář $dir není zapisovatelný";
        }
    }
    
    return true;
});

// Test 11: Konfigurační soubory
runTest("Konfigurační soubory", function() {
    $configFiles = [
        'config/config.php',
        'config/constants.php', 
        'config/autoload.php',
        '.htaccess',
        'robots.txt'
    ];
    
    foreach ($configFiles as $file) {
        $fullPath = ROOT_PATH . '/' . $file;
        if (!file_exists($fullPath)) {
            return "Konfigurační soubor $file neexistuje";
        }
    }
    
    return true;
});

// Test 12: JSON datové soubory
runTest("JSON datové soubory", function() {
    $dataFiles = [
        'data/products.json',
        'data/merchants.json', 
        'data/orders.json',
        'data/settings.json'
    ];
    
    foreach ($dataFiles as $file) {
        $fullPath = ROOT_PATH . '/' . $file;
        if (!file_exists($fullPath)) {
            return "Datový soubor $file neexistuje";
        }
        
        $content = file_get_contents($fullPath);
        $json = json_decode($content, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            return "Datový soubor $file obsahuje neplatný JSON";
        }
    }
    
    return true;
});

// Výsledky testů
echo "📊 Výsledky kompletního testování\n";
echo "=" . str_repeat("=", 60) . "\n";

echo "✅ Prošlo: $passed/$total testů\n";

if (empty($errors)) {
    echo "🎉 Všechny testy prošly úspěšně!\n";
    echo "🚀 Aplikace je připravena k nasazení!\n";
} else {
    echo "❌ Nalezeny chyby:\n";
    foreach ($errors as $error) {
        echo "   • $error\n";
    }
}

if (!empty($warnings)) {
    echo "\n⚠️  Varování:\n";
    foreach ($warnings as $warning) {
        echo "   • $warning\n";
    }
}

$successRate = round(($passed / $total) * 100, 1);
echo "\n📈 Úspěšnost: $successRate%\n";

if ($successRate >= 90) {
    echo "🌟 Výborné! Aplikace je ve skvělém stavu.\n";
} elseif ($successRate >= 75) {
    echo "👍 Dobré! Několik drobných problémů k vyřešení.\n";
} else {
    echo "⚠️  Pozor! Aplikace potřebuje více práce před nasazením.\n";
}

echo "\n🔧 Doporučení pro produkci:\n";
echo "• Zapněte HTTPS přesměrování v .htaccess\n";
echo "• Nastavte produkční error reporting\n";
echo "• Pravidelně zálohujte JSON datové soubory\n";
echo "• Monitorujte velikost cache adresářů\n";
echo "• Testujte na různých zařízeních a prohlížečích\n";
