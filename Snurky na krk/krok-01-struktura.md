# Krok 1: Příprava projektové struktury

## C<PERSON>l kroku
Vytvořit základní adresářovou strukturu projektu a nastavit Apache konfiguraci pro správné fungování webu.

## Úkoly

### 1.1 Vytvoření adresářové struktury
```
/
├── admin/              # Administrační rozhraní
│   ├── css/           # Styly pro admin
│   ├── js/            # JavaScript pro admin
│   └── includes/      # Společné PHP soubory pro admin
├── assets/            # Veřejné statické soubory
│   ├── css/          # Styly pro frontend
│   ├── js/           # JavaScript pro frontend
│   ├── images/       # <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (logo, produkty, atd.)
│   └── uploads/      # Nahrané soubory
├── data/             # JSON datové soubory
├── includes/         # Společné PHP třídy a funkce
├── templates/        # HTML šablony
└── config/          # Konfigurační soubory
```

### 1.2 Vytvoření .htaccess
- Nastavení URL rewriting
- Ochrana citlivých adresářů
- Optimalizace pro Apache

### 1.3 Základní konfigurační soubory
- config.php - hlavní konfigurace
- constants.php - konstanty aplikace
- autoload.php - automatické načítání tříd

### 1.4 Bezpečnostní nastavení
- Ochrana proti direct access k PHP souborům
- Nastavení správných oprávnění souborů
- Základní validace prostředí

## Výstupy kroku
- ✅ Kompletní adresářová struktura
- ✅ Funkční .htaccess konfigurace
- ✅ Základní PHP konfigurační soubory
- ✅ Bezpečnostní opatření

## Testování
- Ověření, že se web načte bez chyb
- Test ochrany admin adresáře
- Kontrola správného routingu URL

## Následující krok
Krok 2: Návrh datového modelu - definice JSON struktury pro ukládání dat
