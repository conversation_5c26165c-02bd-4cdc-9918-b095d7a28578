# Plán realizace projektu SnurkyNaKrk.cz

## Přehled projektu
Podpůrný web pro značku SnurkyNaKrk.cz s jednoduchou administrací, postavený na PHP 8.3 bez databáze (JSON storage).

## Rozdělení do 10 kroků

### Krok 1: Příprava projektové struktury
- Vytvoření základní adresářové struktury
- Nastavení .htaccess pro Apache
- Příprava základních konfiguračních souborů

### Krok 2: Návrh datového modelu
- Definice JSON struktury pro ukládání dat
- Vytvoření výchozích datových souborů
- Návrh API pro práci s daty

### Krok 3: Základní PHP framework
- Vytvoření jednoduchého routingu
- Základní třídy pro práci s daty
- Bezpečnostní funkce

### Krok 4: Frontend - základní layout
- HTML struktura stránky
- Responzivní CSS design
- Základní JavaScript funkcionalita

### Krok 5: Frontend - veřejné stránky
- Hlavní stránka s úvodním textem
- Sekce TOP produkty
- Sekce Obchodníci
- Kontaktní stránka

### Krok 6: Administrace - autentizace
- Přihlašovací systém
- Ochrana admin sekce
- Session management

### Krok 7: Administrace - správa obsahu
- Dashboard pro správu
- Formuláře pro editaci základních údajů
- Upload a správa obrázků

### Krok 8: Administrace - správa produktů a obchodníků
- CRUD operace pro produkty
- CRUD operace pro obchodníky
- Validace a sanitizace dat

### Krok 9: SEO optimalizace a finalizace
- Meta tagy a strukturovaná data
- Optimalizace rychlosti načítání
- Testování na různých zařízeních

### Krok 10: Dokumentace a nasazení
- Uživatelská dokumentace
- Technická dokumentace
- Příprava pro nasazení na hosting

## Časový odhad
Celkový čas realizace: 15-20 hodin
Každý krok: 1.5-2 hodiny
