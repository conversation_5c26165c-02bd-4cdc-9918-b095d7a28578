<?php

// Test datového modelu
require_once 'config/config.php';

echo "<h1>Test datového modelu - Snurky na krk</h1>\n";

try {
    echo "<h2>1. Test ProductModel</h2>\n";
    $productModel = new ProductModel();
    
    // Test načtení produktů
    $products = $productModel->getAllProducts();
    echo "✅ Načteno " . count($products) . " produktů<br>\n";
    
    // Test získání produktu podle ID
    $product = $productModel->getProductById(1);
    if ($product) {
        echo "✅ Produkt ID 1: " . $product['name'] . "<br>\n";
    }
    
    // Test kategorií
    $categories = $productModel->getAllCategories();
    echo "✅ Načteno " . count($categories) . " kategorií<br>\n";
    
    // Test doporučených produktů
    $featured = $productModel->getFeaturedProducts();
    echo "✅ Načteno " . count($featured) . " doporučených produktů<br>\n";
    
    echo "<h2>2. Test MerchantModel</h2>\n";
    $merchantModel = new MerchantModel();
    
    // Test načtení obchodníků
    $merchants = $merchantModel->getAllMerchants();
    echo "✅ Načteno " . count($merchants) . " obchodníků<br>\n";
    
    // Test získání obchodníka podle ID
    $merchant = $merchantModel->getMerchantById(1);
    if ($merchant) {
        echo "✅ Obchodník ID 1: " . $merchant['name'] . "<br>\n";
    }
    
    // Test provizních sazeb
    $commissionRates = $merchantModel->getCommissionRates();
    echo "✅ Načteny provizní sazby: " . implode(', ', array_keys($commissionRates)) . "<br>\n";
    
    echo "<h2>3. Test OrderModel</h2>\n";
    $orderModel = new OrderModel();
    
    // Test načtení objednávek
    $orders = $orderModel->getAllOrders();
    echo "✅ Načteno " . count($orders) . " objednávek<br>\n";
    
    // Test statusů
    $orderStatuses = $orderModel->getOrderStatuses();
    echo "✅ Načteno " . count($orderStatuses) . " statusů objednávek<br>\n";
    
    $paymentStatuses = $orderModel->getPaymentStatuses();
    echo "✅ Načteno " . count($paymentStatuses) . " statusů plateb<br>\n";
    
    // Test statistik
    $stats = $orderModel->getOrderStatistics();
    echo "✅ Statistiky: " . $stats['total_orders'] . " objednávek, celkem " . $stats['total_amount'] . " CZK<br>\n";
    
    echo "<h2>4. Test SettingsModel</h2>\n";
    $settingsModel = new SettingsModel();
    
    // Test načtení nastavení
    $siteSettings = $settingsModel->getSiteSettings();
    echo "✅ Načtena nastavení webu: " . $siteSettings['site_name'] . "<br>\n";
    
    $businessSettings = $settingsModel->getBusinessSettings();
    echo "✅ Načtena obchodní nastavení: měna " . $businessSettings['currency'] . "<br>\n";
    
    // Test režimu údržby
    $maintenanceMode = $settingsModel->isMaintenanceMode();
    echo "✅ Režim údržby: " . ($maintenanceMode ? 'ZAPNUT' : 'VYPNUT') . "<br>\n";
    
    echo "<h2>5. Test vytvoření nového produktu</h2>\n";
    
    $newProductData = [
        'name' => 'Testovací šňůrka',
        'description' => 'Toto je testovací produkt',
        'price' => 200,
        'category' => 'bavlnene',
        'material' => '100% bavlna',
        'colors' => ['černá', 'bílá'],
        'sizes' => ['M', 'L'],
        'stock' => [
            'M' => ['černá' => 5, 'bílá' => 3],
            'L' => ['černá' => 4, 'bílá' => 2]
        ]
    ];
    
    $newProductId = $productModel->addProduct($newProductData);
    echo "✅ Vytvořen nový produkt s ID: " . $newProductId . "<br>\n";
    
    // Ověření, že se produkt uložil
    $createdProduct = $productModel->getProductById($newProductId);
    if ($createdProduct) {
        echo "✅ Produkt byl úspěšně uložen: " . $createdProduct['name'] . "<br>\n";
    }
    
    echo "<h2>6. Test vytvoření nového obchodníka</h2>\n";
    
    $newMerchantData = [
        'name' => 'Testovací obchod s.r.o.',
        'contact_person' => 'Test Testovič',
        'email' => '<EMAIL>',
        'phone' => '+420 123 456 789',
        'address' => [
            'street' => 'Testovací 123',
            'city' => 'Praha',
            'postal_code' => '120 00',
            'country' => 'Česká republika'
        ]
    ];
    
    $newMerchantId = $merchantModel->addMerchant($newMerchantData);
    echo "✅ Vytvořen nový obchodník s ID: " . $newMerchantId . "<br>\n";
    
    echo "<h2>7. Test vytvoření nové objednávky</h2>\n";
    
    $newOrderData = [
        'merchant_id' => 1,
        'items' => [
            [
                'product_id' => 1,
                'size' => 'M',
                'color' => 'černá',
                'quantity' => 2,
                'unit_price' => 150
            ]
        ],
        'commission_rate' => 15
    ];
    
    $newOrderId = $orderModel->createOrder($newOrderData);
    echo "✅ Vytvořena nová objednávka s ID: " . $newOrderId . "<br>\n";
    
    // Získání čísla objednávky
    $createdOrder = $orderModel->getOrderById($newOrderId);
    if ($createdOrder) {
        echo "✅ Číslo objednávky: " . $createdOrder['order_number'] . "<br>\n";
        echo "✅ Celková částka: " . $createdOrder['total_amount'] . " CZK<br>\n";
    }
    
    echo "<h2>✅ Všechny testy datového modelu proběhly úspěšně!</h2>\n";
    
} catch (Exception $e) {
    echo "<h2>❌ Chyba při testování:</h2>\n";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>\n";
    echo "<pre>" . $e->getTraceAsString() . "</pre>\n";
}

echo "<hr>\n";
echo "<p><a href='index.php'>← Zpět na hlavní stránku</a></p>\n";
?>
