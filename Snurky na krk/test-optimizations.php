<?php
/**
 * Test optimalizací výkonu
 * Testuje cache systém, asset optimalizaci a další vylepšení
 */

define('SNURKY_INIT', true);

require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/config/constants.php';
require_once __DIR__ . '/config/autoload.php';

echo "🚀 Test optimalizací výkonu aplikace Snurky na krk\n";
echo "=" . str_repeat("=", 60) . "\n\n";

$errors = [];
$warnings = [];

// Test 1: Cache Manager
echo "📦 Test 1: Cache Manager\n";
echo "-" . str_repeat("-", 30) . "\n";

try {
    $cache = new CacheManager();
    
    // Test základních operací
    $testData = ['test' => 'data', 'timestamp' => time()];
    $cache->set('test_key', $testData, 60);
    
    $retrievedData = $cache->get('test_key');
    if ($retrievedData === $testData) {
        echo "✅ Cache set/get funguje správně\n";
    } else {
        $errors[] = "Cache set/get nefunguje správně";
    }
    
    // Test has metody
    if ($cache->has('test_key')) {
        echo "✅ Cache has() metoda funguje\n";
    } else {
        $errors[] = "Cache has() metoda nefunguje";
    }
    
    // Test remember metody
    $callbackCalled = false;
    $result = $cache->remember('callback_test', function() use (&$callbackCalled) {
        $callbackCalled = true;
        return 'callback_result';
    }, 60);
    
    if ($result === 'callback_result' && $callbackCalled) {
        echo "✅ Cache remember() metoda funguje\n";
    } else {
        $errors[] = "Cache remember() metoda nefunguje";
    }
    
    // Test druhého volání (mělo by být z cache)
    $callbackCalled = false;
    $result2 = $cache->remember('callback_test', function() use (&$callbackCalled) {
        $callbackCalled = true;
        return 'callback_result_2';
    }, 60);
    
    if ($result2 === 'callback_result' && !$callbackCalled) {
        echo "✅ Cache remember() používá cache při druhém volání\n";
    } else {
        $warnings[] = "Cache remember() možná nepoužívá cache správně";
    }
    
    // Test statistik
    $stats = $cache->getStats();
    if (isset($stats['total_files']) && $stats['total_files'] > 0) {
        echo "✅ Cache statistiky: {$stats['total_files']} souborů, {$stats['total_size_formatted']}\n";
    } else {
        $warnings[] = "Cache statistiky nejsou dostupné";
    }
    
    // Vyčištění test cache
    $cache->delete('test_key');
    $cache->delete('callback_test');
    
} catch (Exception $e) {
    $errors[] = "Cache Manager test selhal: " . $e->getMessage();
}

echo "\n";

// Test 2: Asset Optimizer
echo "🎨 Test 2: Asset Optimizer\n";
echo "-" . str_repeat("-", 30) . "\n";

try {
    $optimizer = new AssetOptimizer();
    
    // Test CSS optimalizace
    $cssFiles = ['css/style.css'];
    $optimizedCSS = $optimizer->optimizeCSS($cssFiles, 'test.min.css');
    
    if (strpos($optimizedCSS, 'test.min.css') !== false) {
        echo "✅ CSS optimalizace funguje\n";
    } else {
        $errors[] = "CSS optimalizace nefunguje";
    }
    
    // Test JS optimalizace
    $jsFiles = ['js/app.js'];
    $optimizedJS = $optimizer->optimizeJS($jsFiles, 'test.min.js');
    
    if (strpos($optimizedJS, 'test.min.js') !== false) {
        echo "✅ JavaScript optimalizace funguje\n";
    } else {
        $errors[] = "JavaScript optimalizace nefunguje";
    }
    
    // Test cache statistik
    $stats = $optimizer->getCacheStats();
    if (isset($stats['files_count'])) {
        echo "✅ Asset cache: {$stats['files_count']} souborů, {$stats['total_size_formatted']}\n";
    } else {
        $warnings[] = "Asset cache statistiky nejsou dostupné";
    }
    
} catch (Exception $e) {
    $errors[] = "Asset Optimizer test selhal: " . $e->getMessage();
}

echo "\n";

// Test 3: Template Engine s optimalizacemi
echo "🎭 Test 3: Template Engine optimalizace\n";
echo "-" . str_repeat("-", 30) . "\n";

try {
    $template = new TemplateEngine();
    
    // Test optimalizovaných CSS tagů
    $cssTag = $template->optimizedCSS(['css/style.css']);
    if (strpos($cssTag, '<link rel="stylesheet"') !== false) {
        echo "✅ Optimalizované CSS tagy fungují\n";
    } else {
        $errors[] = "Optimalizované CSS tagy nefungují";
    }
    
    // Test optimalizovaných JS tagů
    $jsTag = $template->optimizedJS(['js/app.js']);
    if (strpos($jsTag, '<script src=') !== false) {
        echo "✅ Optimalizované JS tagy fungují\n";
    } else {
        $errors[] = "Optimalizované JS tagy nefungují";
    }
    
    // Test asset URL s cache busting
    $assetUrl = $template->asset('css/style.css');
    if (strpos($assetUrl, '?v=') !== false) {
        echo "✅ Asset URL s cache busting funguje\n";
    } else {
        $warnings[] = "Asset URL cache busting možná nefunguje";
    }
    
} catch (Exception $e) {
    $errors[] = "Template Engine optimalizace test selhal: " . $e->getMessage();
}

echo "\n";

// Test 4: BaseModel s cache
echo "💾 Test 4: BaseModel s cache podporou\n";
echo "-" . str_repeat("-", 30) . "\n";

try {
    // Test s ProductModel
    $productModel = new ProductModel();
    
    // Měření času načítání bez cache
    $start = microtime(true);
    $products1 = $productModel->getAll();
    $timeWithoutCache = microtime(true) - $start;
    
    // Druhé načítání (mělo by být z cache)
    $start = microtime(true);
    $products2 = $productModel->getAll();
    $timeWithCache = microtime(true) - $start;
    
    if ($timeWithCache < $timeWithoutCache) {
        echo "✅ Cache zrychluje načítání dat\n";
        echo "   Bez cache: " . round($timeWithoutCache * 1000, 2) . "ms\n";
        echo "   S cache: " . round($timeWithCache * 1000, 2) . "ms\n";
    } else {
        $warnings[] = "Cache možná nezrychluje načítání dat";
    }
    
    // Test cache invalidace
    $productModel->clearCache();
    echo "✅ Cache invalidace funguje\n";
    
} catch (Exception $e) {
    $errors[] = "BaseModel cache test selhal: " . $e->getMessage();
}

echo "\n";

// Test 5: Kontrola adresářů a oprávnění
echo "📁 Test 5: Adresáře a oprávnění\n";
echo "-" . str_repeat("-", 30) . "\n";

$requiredDirs = [
    'data/cache' => 'Cache adresář',
    'data/cache/assets' => 'Asset cache adresář',
    'assets/uploads' => 'Upload adresář'
];

foreach ($requiredDirs as $dir => $description) {
    $fullPath = ROOT_PATH . '/' . $dir;
    
    if (is_dir($fullPath)) {
        if (is_writable($fullPath)) {
            echo "✅ $description existuje a je zapisovatelný\n";
        } else {
            $warnings[] = "$description není zapisovatelný";
        }
    } else {
        $errors[] = "$description neexistuje";
    }
}

echo "\n";

// Test 6: Bezpečnostní soubory
echo "🔒 Test 6: Bezpečnostní soubory\n";
echo "-" . str_repeat("-", 30) . "\n";

$securityFiles = [
    'data/cache/.htaccess' => 'Cache .htaccess',
    'data/cache/assets/.htaccess' => 'Asset cache .htaccess'
];

foreach ($securityFiles as $file => $description) {
    $fullPath = ROOT_PATH . '/' . $file;
    
    if (file_exists($fullPath)) {
        echo "✅ $description existuje\n";
    } else {
        $warnings[] = "$description neexistuje";
    }
}

echo "\n";

// Výsledky testů
echo "📊 Výsledky testů\n";
echo "=" . str_repeat("=", 60) . "\n";

if (empty($errors)) {
    echo "✅ Všechny testy prošly úspěšně!\n";
} else {
    echo "❌ Nalezeny chyby:\n";
    foreach ($errors as $error) {
        echo "   • $error\n";
    }
}

if (!empty($warnings)) {
    echo "\n⚠️  Varování:\n";
    foreach ($warnings as $warning) {
        echo "   • $warning\n";
    }
}

echo "\n🎯 Optimalizace jsou připraveny k použití!\n";
echo "\nDoporučení pro produkci:\n";
echo "• Zapněte HTTPS přesměrování v .htaccess\n";
echo "• Nastavte delší cache TTL pro produkční prostředí\n";
echo "• Pravidelně čistěte expirované cache soubory\n";
echo "• Monitorujte velikost cache adresářů\n";
