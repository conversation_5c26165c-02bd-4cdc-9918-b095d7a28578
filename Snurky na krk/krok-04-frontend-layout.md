# Krok 4: Frontend - základní layout

## Cíl kroku
Vytvořit responzivní HTML/CSS layout s moderním designem a základní JavaScript funkcionalitou.

## Úkoly

### 4.1 HTML struktura
- layout.php - hlavní layout šablona
- Sémantické HTML5 elementy
- Správná struktura pro SEO
- Accessibility (ARIA labels, alt texty)

#### Základní struktura:
```html
<!DOCTYPE html>
<html lang="cs">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>{{title}} - SnurkyNaKrk.cz</title>
  <meta name="description" content="{{description}}">
</head>
<body>
  <header>
    <nav><!-- Navigace --></nav>
  </header>
  <main>
    {{content}}
  </main>
  <footer><!-- <PERSON><PERSON><PERSON><PERSON> --></footer>
</body>
</html>
```

### 4.2 CSS framework
- style.css - hlavní stylesheet
- Responzivní grid systém
- CSS custom properties (proměnné)
- Mobile-first přístup

#### CSS struktura:
```css
/* Reset a základní styly */
/* CSS proměnné */
/* Typography */
/* Layout komponenty */
/* Navigace */
/* Formuláře */
/* Tlačítka */
/* Media queries */
```

### 4.3 Responzivní design
- Breakpointy: 320px, 768px, 1024px, 1200px
- Flexbox/Grid layout
- Responzivní obrázky
- Touch-friendly navigace pro mobily

### 4.4 Navigační menu
- Hlavní navigace
- Hamburger menu pro mobily
- Breadcrumbs
- Active states

### 4.5 Komponenty UI
- Tlačítka (primární, sekundární)
- Formulářové prvky
- Karty pro produkty/obchodníky
- Modal okna
- Loading states

### 4.6 JavaScript funkcionalita
- app.js - hlavní JavaScript soubor
- Mobile menu toggle
- Smooth scrolling
- Form validace
- Image lazy loading

#### JavaScript funkce:
```javascript
- initMobileMenu()
- initSmoothScroll()
- initFormValidation()
- initLazyLoading()
- showModal(content)
- hideModal()
```

### 4.7 Optimalizace výkonu
- CSS minifikace
- Image optimization
- Critical CSS
- Lazy loading obrázků

## ✅ Výstupy kroku - DOKONČENO
- ✅ Responzivní HTML layout (`templates/layouts/main.php`)
- ✅ Kompletní CSS framework (`assets/css/style.css` - 1119 řádků)
- ✅ JavaScript funkcionalita (`assets/js/app.js` - plně funkční)
- ✅ UI komponenty (tlačítka, formuláře, karty, modaly)
- ✅ Admin CSS (`assets/css/admin.css`)
- ✅ SVG grafika (logo, favicon, placeholders)
- ✅ Bootstrap 5.3.6 + Font Awesome 6.5.1 integrace
- ✅ Optimalizace výkonu a accessibility

## ✅ Testování - DOKONČENO
- ✅ Test na různých zařízeních (desktop, tablet, mobil)
- ✅ Kontrola v různých prohlížečích
- ✅ W3C CSS validace (bez chyb)
- ✅ Test accessibility (focus, ARIA, semantic HTML)
- ✅ Performance audit (lazy loading, optimalizace)
- ✅ `test-frontend.php` - kompletní test všech komponent

## Technické detaily
- **CSS Custom Properties:** 50+ proměnných pro konzistentní design
- **Responzivní breakpointy:** 576px, 768px, 992px, 1200px, 1400px
- **JavaScript moduly:** Mobile menu, validace, lazy loading, modaly
- **Accessibility:** WCAG 2.1 kompatibilní, keyboard navigace
- **Performance:** Optimalizované animace, lazy loading, CDN

## Následující krok
Krok 5: Veřejné stránky - produkty, obchodníci, kontakt
