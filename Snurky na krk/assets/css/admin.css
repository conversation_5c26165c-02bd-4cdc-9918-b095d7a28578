/**
 * Admin CSS pro SnurkyNaKrk.cz
 * Specializované styly pro administrační rozhraní
 */

/* Admin Layout */
.admin-body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fc;
    min-height: 100vh;
}

.admin-navbar {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    border: none;
    background-color: #fff !important;
    min-height: 56px;
}

.admin-container {
    padding: 0;
    min-height: calc(100vh - 56px);
}

.sidebar {
    position: fixed;
    top: 56px;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 0;
    box-shadow: inset -1px 0 0 rgba(0, 0, 0, 0.1);
    background-color: #f8f9fc !important;
    overflow-y: auto;
    width: 16.666667%;
}

.sidebar .nav-link {
    color: #858796;
    padding: 0.75rem 1rem;
    border-radius: 0.35rem;
    margin: 0.125rem 0.5rem;
    transition: all 0.15s ease-in-out;
}

.sidebar .nav-link:hover {
    color: #5a5c69;
    background-color: #eaecf4;
}

.sidebar .nav-link.active {
    color: #6e707e;
    background-color: #e3e6f0;
    font-weight: 700;
}

.sidebar .nav-link i {
    font-size: 0.85rem;
    margin-right: 0.25rem;
}

.admin-main {
    margin-left: 0;
    padding-top: 1rem;
}

@media (min-width: 768px) {
    .admin-main {
        margin-left: 16.666667%;
    }

    .sidebar {
        width: 16.666667%;
    }
}

@media (min-width: 992px) {
    .admin-main {
        margin-left: 16.666667%;
    }

    .sidebar {
        width: 16.666667%;
    }
}

/* Cards */
.card {
    border: 1px solid #e3e6f0;
    border-radius: 0.35rem;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

.card-header {
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
}

/* Tables */
.table {
    color: #858796;
}

.table th {
    border-top: none;
    border-bottom: 1px solid #e3e6f0;
    font-weight: 800;
    font-size: 0.65rem;
    color: #6e707e;
    text-transform: uppercase;
    letter-spacing: 0.1rem;
}

.table td {
    border-top: 1px solid #e3e6f0;
    font-size: 0.8rem;
}

/* Buttons */
.btn {
    border-radius: 0.35rem;
    font-size: 0.8rem;
    font-weight: 800;
    padding: 0.375rem 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.05rem;
}

.btn-sm {
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
}

.btn-primary {
    background-color: #4e73df;
    border-color: #4e73df;
}

.btn-primary:hover {
    background-color: #2e59d9;
    border-color: #2653d4;
}

.btn-success {
    background-color: #1cc88a;
    border-color: #1cc88a;
}

.btn-success:hover {
    background-color: #17a673;
    border-color: #169b6b;
}

.btn-info {
    background-color: #36b9cc;
    border-color: #36b9cc;
}

.btn-info:hover {
    background-color: #2c9faf;
    border-color: #2a96a5;
}

.btn-warning {
    background-color: #f6c23e;
    border-color: #f6c23e;
    color: #fff;
}

.btn-warning:hover {
    background-color: #f4b619;
    border-color: #f4b30d;
    color: #fff;
}

.btn-danger {
    background-color: #e74a3b;
    border-color: #e74a3b;
}

.btn-danger:hover {
    background-color: #e02d1b;
    border-color: #d52a1a;
}

/* Forms */
.form-control {
    border: 1px solid #d1d3e2;
    border-radius: 0.35rem;
    color: #6e707e;
    font-size: 0.8rem;
}

.form-control:focus {
    border-color: #bac8f3;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

.form-select {
    border: 1px solid #d1d3e2;
    border-radius: 0.35rem;
    color: #6e707e;
    font-size: 0.8rem;
}

.form-select:focus {
    border-color: #bac8f3;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

/* Alerts */
.alert {
    border: none;
    border-radius: 0.35rem;
    font-size: 0.85rem;
}

.alert-primary {
    background-color: #dae2f8;
    color: #2653d4;
}

.alert-success {
    background-color: #d1f2eb;
    color: #0f6848;
}

.alert-info {
    background-color: #d6f3f7;
    color: #0c5460;
}

.alert-warning {
    background-color: #fef5e7;
    color: #856404;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}

/* Badges */
.badge {
    font-size: 0.65rem;
    font-weight: 800;
    padding: 0.35rem 0.65rem;
    border-radius: 10rem;
    text-transform: uppercase;
    letter-spacing: 0.05rem;
}

/* Pagination */
.pagination .page-link {
    border: 1px solid #dddfeb;
    color: #858796;
    padding: 0.5rem 0.75rem;
    margin-left: -1px;
    font-size: 0.8rem;
}

.pagination .page-link:hover {
    background-color: #eaecf4;
    border-color: #dddfeb;
    color: #5a5c69;
}

.pagination .page-item.active .page-link {
    background-color: #4e73df;
    border-color: #4e73df;
    color: #fff;
}

/* Dropdowns */
.dropdown-menu {
    border: 1px solid #e3e6f0;
    border-radius: 0.35rem;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

.dropdown-item {
    color: #858796;
    font-size: 0.85rem;
    padding: 0.5rem 1rem;
}

.dropdown-item:hover {
    background-color: #f8f9fc;
    color: #5a5c69;
}

/* Progress bars */
.progress {
    height: 0.5rem;
    border-radius: 10rem;
    background-color: #eaecf4;
}

.progress-bar {
    border-radius: 10rem;
}

/* Custom utilities */
.text-xs {
    font-size: 0.7rem;
}

.font-weight-bold {
    font-weight: 700 !important;
}

.text-uppercase {
    text-transform: uppercase !important;
}

.shadow {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
}

.shadow-sm {
    box-shadow: 0 0.125rem 0.25rem 0 rgba(58, 59, 69, 0.2) !important;
}

.shadow-lg {
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;
}

/* Responsive adjustments */
@media (max-width: 767.98px) {
    .sidebar {
        position: static;
        height: auto;
        width: 100%;
        top: auto;
        bottom: auto;
        left: auto;
        box-shadow: none;
        border-bottom: 1px solid #e3e6f0;
    }

    .admin-main {
        margin-left: 0;
        padding-top: 0.5rem;
    }

    .card {
        margin-bottom: 1rem;
    }

    .admin-navbar .navbar-collapse {
        background-color: #fff;
        border-top: 1px solid #e3e6f0;
        margin-top: 0.5rem;
        padding-top: 0.5rem;
    }
}

/* Loading states */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* File upload */
.custom-file-input:focus ~ .custom-file-label {
    border-color: #bac8f3;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

/* Image preview */
.image-preview {
    max-width: 200px;
    max-height: 200px;
    border: 1px solid #d1d3e2;
    border-radius: 0.35rem;
    padding: 0.25rem;
}

/* Status indicators */
.status-active {
    color: #1cc88a;
}

.status-inactive {
    color: #858796;
}

.status-pending {
    color: #f6c23e;
}

.status-rejected {
    color: #e74a3b;
}

/* Dashboard specific styles */
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}

.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}

.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}

.icon-circle {
    height: 2.5rem;
    width: 2.5rem;
    border-radius: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.text-gray-800 {
    color: #5a5c69 !important;
}

.text-gray-300 {
    color: #dddfeb !important;
}

/* Admin specific utilities */
.no-gutters {
    margin-right: 0;
    margin-left: 0;
}

.no-gutters > .col,
.no-gutters > [class*="col-"] {
    padding-right: 0;
    padding-left: 0;
}

/* Better spacing for admin content */
.admin-main .card {
    margin-bottom: 1.5rem;
}

.admin-main .row {
    margin-bottom: 1rem;
}

/* Improved form styling */
.form-group {
    margin-bottom: 1rem;
}

.form-label {
    font-weight: 600;
    color: #5a5c69;
    margin-bottom: 0.5rem;
}

/* Better table styling */
.table-responsive {
    border-radius: 0.35rem;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

/* Improved button spacing */
.btn + .btn {
    margin-left: 0.5rem;
}

.btn-group .btn {
    margin-left: 0;
}
