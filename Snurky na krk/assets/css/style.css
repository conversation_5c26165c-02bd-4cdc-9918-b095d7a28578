/* ===================================
   ŠŇŮRKY NA KRK - HLAVNÍ STYLESHEET
   =================================== */

/* CSS Custom Properties (proměnné) */
:root {
  /* Barvy */
  --primary-color: #2c3e50;
  --secondary-color: #3498db;
  --accent-color: #e74c3c;
  --success-color: #27ae60;
  --warning-color: #f39c12;
  --danger-color: #e74c3c;
  --light-color: #ecf0f1;
  --dark-color: #2c3e50;
  --muted-color: #7f8c8d;
  
  /* Pozadí */
  --bg-primary: #ffffff;
  --bg-secondary: #f8f9fa;
  --bg-dark: #2c3e50;
  --bg-light: #ecf0f1;
  
  /* Texty */
  --text-primary: #2c3e50;
  --text-secondary: #7f8c8d;
  --text-light: #ffffff;
  --text-muted: #95a5a6;
  
  /* Stíny */
  --shadow-sm: 0 2px 4px rgba(0,0,0,0.1);
  --shadow-md: 0 4px 8px rgba(0,0,0,0.15);
  --shadow-lg: 0 8px 16px rgba(0,0,0,0.2);
  
  /* Rozměry */
  --border-radius: 8px;
  --border-radius-sm: 4px;
  --border-radius-lg: 12px;
  
  /* Breakpointy */
  --breakpoint-sm: 576px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 992px;
  --breakpoint-xl: 1200px;
  --breakpoint-xxl: 1400px;
  
  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 3rem;
  
  /* Typography */
  --font-family-base: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  --font-family-heading: 'Georgia', 'Times New Roman', serif;
  --font-size-base: 1rem;
  --font-size-sm: 0.875rem;
  --font-size-lg: 1.125rem;
  --line-height-base: 1.6;
}

/* ===================================
   RESET A ZÁKLADNÍ STYLY
   =================================== */

/* Box sizing reset */
*,
*::before,
*::after {
  box-sizing: border-box;
}

/* Základní reset */
body {
  margin: 0;
  padding: 0;
  font-family: var(--font-family-base);
  font-size: var(--font-size-base);
  line-height: var(--line-height-base);
  color: var(--text-primary);
  background-color: var(--bg-primary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Odstranění defaultních marginů */
h1, h2, h3, h4, h5, h6,
p, ul, ol, dl, blockquote, pre {
  margin-top: 0;
}

/* Obrázky responzivní */
img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* Odkazy */
a {
  color: var(--secondary-color);
  text-decoration: none;
  transition: color 0.3s ease;
}

a:hover,
a:focus {
  color: var(--primary-color);
  text-decoration: underline;
}

/* Focus styly pro accessibility */
*:focus {
  outline: 2px solid var(--secondary-color);
  outline-offset: 2px;
}

/* ===================================
   TYPOGRAPHY
   =================================== */

h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-family-heading);
  font-weight: 600;
  line-height: 1.3;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.75rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1rem; }

p {
  margin-bottom: var(--spacing-md);
}

.lead {
  font-size: var(--font-size-lg);
  font-weight: 300;
  color: var(--text-secondary);
}

.text-muted {
  color: var(--text-muted) !important;
}

.text-primary {
  color: var(--primary-color) !important;
}

.text-secondary {
  color: var(--secondary-color) !important;
}

/* ===================================
   LAYOUT KOMPONENTY
   =================================== */

/* Container rozšíření */
.container-fluid {
  padding-left: var(--spacing-md);
  padding-right: var(--spacing-md);
}

/* Hlavní layout */
.main-content {
  min-height: calc(100vh - 200px);
  padding: var(--spacing-lg) 0;
}

/* Sekce */
.section {
  padding: var(--spacing-xl) 0;
}

.section-sm {
  padding: var(--spacing-lg) 0;
}

.section-lg {
  padding: calc(var(--spacing-xl) * 1.5) 0;
}

/* Hero sekce */
.hero {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  color: var(--text-light);
  padding: calc(var(--spacing-xl) * 2) 0;
  text-align: center;
}

.hero h1 {
  color: var(--text-light);
  font-size: 3rem;
  margin-bottom: var(--spacing-lg);
}

.hero .lead {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1.25rem;
  margin-bottom: var(--spacing-xl);
}

/* ===================================
   NAVIGACE
   =================================== */

.navbar {
  box-shadow: var(--shadow-sm);
  background-color: var(--bg-primary) !important;
}

.navbar-brand img {
  transition: transform 0.3s ease;
}

.navbar-brand:hover img {
  transform: scale(1.05);
}

.navbar-nav .nav-link {
  font-weight: 500;
  padding: var(--spacing-sm) var(--spacing-md) !important;
  transition: all 0.3s ease;
  position: relative;
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
  color: var(--secondary-color) !important;
}

.navbar-nav .nav-link::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background-color: var(--secondary-color);
  transition: all 0.3s ease;
  transform: translateX(-50%);
}

.navbar-nav .nav-link:hover::after,
.navbar-nav .nav-link.active::after {
  width: 80%;
}

/* Mobile menu */
.navbar-toggler {
  border: none;
  padding: var(--spacing-xs);
}

.navbar-toggler:focus {
  box-shadow: none;
}

/* Vyhledávací formulář */
.navbar .form-control {
  border-radius: var(--border-radius);
  border: 1px solid #dee2e6;
  transition: all 0.3s ease;
}

.navbar .form-control:focus {
  border-color: var(--secondary-color);
  box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

/* ===================================
   TLAČÍTKA
   =================================== */

.btn {
  border-radius: var(--border-radius);
  font-weight: 500;
  padding: var(--spacing-sm) var(--spacing-lg);
  transition: all 0.3s ease;
  border: 2px solid transparent;
  text-decoration: none;
  display: inline-block;
  cursor: pointer;
  font-size: var(--font-size-base);
  line-height: 1.5;
}

.btn:hover {
  text-decoration: none;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn:active {
  transform: translateY(0);
}

.btn:focus {
  outline: none;
  box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

/* Primární tlačítko */
.btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: var(--text-light);
}

.btn-primary:hover {
  background-color: #1a252f;
  border-color: #1a252f;
  color: var(--text-light);
}

/* Sekundární tlačítko */
.btn-secondary {
  background-color: var(--secondary-color);
  border-color: var(--secondary-color);
  color: var(--text-light);
}

.btn-secondary:hover {
  background-color: #2980b9;
  border-color: #2980b9;
  color: var(--text-light);
}

/* Outline tlačítka */
.btn-outline-primary {
  color: var(--primary-color);
  border-color: var(--primary-color);
  background-color: transparent;
}

.btn-outline-primary:hover {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: var(--text-light);
}

.btn-outline-secondary {
  color: var(--secondary-color);
  border-color: var(--secondary-color);
  background-color: transparent;
}

.btn-outline-secondary:hover {
  background-color: var(--secondary-color);
  border-color: var(--secondary-color);
  color: var(--text-light);
}

/* Velikosti tlačítek */
.btn-sm {
  padding: var(--spacing-xs) var(--spacing-md);
  font-size: var(--font-size-sm);
}

.btn-lg {
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: var(--font-size-lg);
}

/* ===================================
   FORMULÁŘE
   =================================== */

.form-control {
  border-radius: var(--border-radius);
  border: 1px solid #dee2e6;
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: var(--font-size-base);
  transition: all 0.3s ease;
  background-color: var(--bg-primary);
  color: var(--text-primary);
}

.form-control:focus {
  border-color: var(--secondary-color);
  box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
  outline: none;
}

.form-control::placeholder {
  color: var(--text-muted);
  opacity: 1;
}

.form-label {
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.form-text {
  font-size: var(--font-size-sm);
  color: var(--text-muted);
  margin-top: var(--spacing-xs);
}

/* Validační styly */
.form-control.is-valid {
  border-color: var(--success-color);
}

.form-control.is-invalid {
  border-color: var(--danger-color);
}

.valid-feedback {
  color: var(--success-color);
  font-size: var(--font-size-sm);
  margin-top: var(--spacing-xs);
}

.invalid-feedback {
  color: var(--danger-color);
  font-size: var(--font-size-sm);
  margin-top: var(--spacing-xs);
}

/* Checkbox a radio */
.form-check-input {
  margin-top: 0.25em;
}

.form-check-label {
  margin-left: var(--spacing-xs);
}

/* Select */
.form-select {
  border-radius: var(--border-radius);
  border: 1px solid #dee2e6;
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: var(--bg-primary);
  transition: all 0.3s ease;
}

.form-select:focus {
  border-color: var(--secondary-color);
  box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

/* ===================================
   KARTY A KOMPONENTY
   =================================== */

.card {
  border-radius: var(--border-radius);
  border: 1px solid #dee2e6;
  box-shadow: var(--shadow-sm);
  transition: all 0.3s ease;
  background-color: var(--bg-primary);
}

.card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.card-header {
  background-color: var(--bg-secondary);
  border-bottom: 1px solid #dee2e6;
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.card-body {
  padding: var(--spacing-lg);
}

.card-footer {
  background-color: var(--bg-secondary);
  border-top: 1px solid #dee2e6;
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: 0 0 var(--border-radius) var(--border-radius);
}

.card-title {
  margin-bottom: var(--spacing-md);
  color: var(--text-primary);
}

.card-text {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-md);
}

/* Produktové karty */
.product-card {
  height: 100%;
  transition: all 0.3s ease;
}

.product-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

.product-card .card-img-top {
  height: 200px;
  object-fit: cover;
  border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.product-price {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--accent-color);
}

.product-price-old {
  text-decoration: line-through;
  color: var(--text-muted);
  font-size: var(--font-size-sm);
  margin-right: var(--spacing-sm);
}

/* ===================================
   BREADCRUMBS
   =================================== */

.breadcrumb {
  background-color: transparent;
  padding: var(--spacing-sm) 0;
  margin-bottom: var(--spacing-lg);
}

.breadcrumb-item {
  font-size: var(--font-size-sm);
}

.breadcrumb-item + .breadcrumb-item::before {
  content: "›";
  color: var(--text-muted);
  font-weight: bold;
}

.breadcrumb-item.active {
  color: var(--text-muted);
}

.breadcrumb-item a {
  color: var(--secondary-color);
  text-decoration: none;
}

.breadcrumb-item a:hover {
  color: var(--primary-color);
  text-decoration: underline;
}

/* ===================================
   ALERTS A ZPRÁVY
   =================================== */

.alert {
  border-radius: var(--border-radius);
  padding: var(--spacing-md) var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  border: 1px solid transparent;
  position: relative;
}

.alert-success {
  background-color: rgba(39, 174, 96, 0.1);
  border-color: var(--success-color);
  color: #155724;
}

.alert-danger {
  background-color: rgba(231, 76, 60, 0.1);
  border-color: var(--danger-color);
  color: #721c24;
}

.alert-warning {
  background-color: rgba(243, 156, 18, 0.1);
  border-color: var(--warning-color);
  color: #856404;
}

.alert-info {
  background-color: rgba(52, 152, 219, 0.1);
  border-color: var(--secondary-color);
  color: #0c5460;
}

.alert-dismissible .btn-close {
  position: absolute;
  top: 0;
  right: 0;
  padding: var(--spacing-md) var(--spacing-lg);
  background: none;
  border: none;
  font-size: 1.25rem;
  cursor: pointer;
}

/* ===================================
   MODAL OKNA
   =================================== */

.modal {
  display: none;
  position: fixed;
  z-index: 1050;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(2px);
}

.modal.show {
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-dialog {
  max-width: 500px;
  width: 90%;
  margin: var(--spacing-lg);
}

.modal-content {
  background-color: var(--bg-primary);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  padding: var(--spacing-lg);
  border-bottom: 1px solid #dee2e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-title {
  margin: 0;
  color: var(--text-primary);
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--text-muted);
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.modal-close:hover {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
}

.modal-body {
  padding: var(--spacing-lg);
}

.modal-footer {
  padding: var(--spacing-lg);
  border-top: 1px solid #dee2e6;
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-md);
}

/* ===================================
   LOADING STATES
   =================================== */

.loading {
  position: relative;
  pointer-events: none;
  opacity: 0.6;
}

.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid var(--secondary-color);
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid var(--secondary-color);
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.spinner-lg {
  width: 40px;
  height: 40px;
  border-width: 4px;
}

/* ===================================
   FOOTER
   =================================== */

.footer {
  background-color: var(--bg-dark) !important;
  color: var(--text-light);
  margin-top: auto;
}

.footer h5 {
  color: var(--text-light);
  margin-bottom: var(--spacing-md);
  font-size: 1.1rem;
}

.footer p {
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: var(--spacing-sm);
}

.footer a {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer a:hover {
  color: var(--text-light);
  text-decoration: underline;
}

.footer .list-unstyled li {
  margin-bottom: var(--spacing-xs);
}

.footer hr {
  border-color: rgba(255, 255, 255, 0.2);
}

/* ===================================
   UTILITY TŘÍDY
   =================================== */

/* Spacing utilities */
.mt-auto { margin-top: auto !important; }
.mb-auto { margin-bottom: auto !important; }
.ms-auto { margin-left: auto !important; }
.me-auto { margin-right: auto !important; }

/* Text utilities */
.text-center { text-align: center !important; }
.text-start { text-align: left !important; }
.text-end { text-align: right !important; }

/* Display utilities */
.d-none { display: none !important; }
.d-block { display: block !important; }
.d-inline { display: inline !important; }
.d-inline-block { display: inline-block !important; }
.d-flex { display: flex !important; }

/* Flex utilities */
.justify-content-center { justify-content: center !important; }
.justify-content-between { justify-content: space-between !important; }
.justify-content-end { justify-content: flex-end !important; }
.align-items-center { align-items: center !important; }
.align-items-start { align-items: flex-start !important; }
.align-items-end { align-items: flex-end !important; }

/* ===================================
   RESPONZIVNÍ DESIGN - MEDIA QUERIES
   =================================== */

/* Extra small devices (phones, less than 576px) */
@media (max-width: 575.98px) {
  .hero h1 {
    font-size: 2rem;
  }

  .hero .lead {
    font-size: 1rem;
  }

  .container {
    padding-left: var(--spacing-sm);
    padding-right: var(--spacing-sm);
  }

  .btn {
    width: 100%;
    margin-bottom: var(--spacing-sm);
  }

  .btn:last-child {
    margin-bottom: 0;
  }

  .card-body {
    padding: var(--spacing-md);
  }

  .modal-dialog {
    margin: var(--spacing-sm);
  }

  .navbar-nav {
    text-align: center;
  }

  .navbar-nav .nav-link {
    padding: var(--spacing-md) !important;
  }
}

/* Small devices (landscape phones, 576px and up) */
@media (min-width: 576px) and (max-width: 767.98px) {
  .hero h1 {
    font-size: 2.5rem;
  }

  .btn-group-responsive .btn {
    margin-bottom: var(--spacing-sm);
  }
}

/* Medium devices (tablets, 768px and up) */
@media (min-width: 768px) {
  .navbar-nav .nav-link::after {
    display: block;
  }

  .hero h1 {
    font-size: 3rem;
  }

  .hero .lead {
    font-size: 1.25rem;
  }

  .btn-group-responsive {
    display: flex;
    gap: var(--spacing-md);
  }

  .btn-group-responsive .btn {
    margin-bottom: 0;
    width: auto;
  }
}

/* Large devices (desktops, 992px and up) */
@media (min-width: 992px) {
  .hero h1 {
    font-size: 3.5rem;
  }

  .section {
    padding: calc(var(--spacing-xl) * 1.5) 0;
  }

  .product-card:hover {
    transform: translateY(-8px);
  }
}

/* Extra large devices (large desktops, 1200px and up) */
@media (min-width: 1200px) {
  .hero {
    padding: calc(var(--spacing-xl) * 2.5) 0;
  }

  .hero h1 {
    font-size: 4rem;
  }

  .container {
    max-width: 1140px;
  }
}

/* ===================================
   PRINT STYLY
   =================================== */

@media print {
  .navbar,
  .footer,
  .btn,
  .modal {
    display: none !important;
  }

  .main-content {
    padding: 0;
    min-height: auto;
  }

  .card {
    box-shadow: none;
    border: 1px solid #000;
  }

  a {
    color: #000 !important;
    text-decoration: underline !important;
  }

  .hero {
    background: none !important;
    color: #000 !important;
  }
}

/* ===================================
   ACCESSIBILITY VYLEPŠENÍ
   =================================== */

/* Vysoký kontrast pro uživatele s poruchami zraku */
@media (prefers-contrast: high) {
  :root {
    --primary-color: #000000;
    --secondary-color: #0066cc;
    --text-primary: #000000;
    --text-secondary: #333333;
  }
}

/* Redukované animace pro uživatele s vestibulárními poruchami */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .product-card:hover,
  .card:hover {
    transform: none !important;
  }
}

/* Focus visible pro lepší keyboard navigaci */
@supports selector(:focus-visible) {
  *:focus {
    outline: none;
  }

  *:focus-visible {
    outline: 2px solid var(--secondary-color);
    outline-offset: 2px;
  }
}

/* ===================================
   LAZY LOADING OBRÁZKŮ
   =================================== */

img.lazy {
  opacity: 0;
  transition: opacity 0.3s ease;
  background-color: var(--bg-secondary);
}

img.lazy-loaded {
  opacity: 1;
}

/* Placeholder pro lazy loading */
img[data-src] {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100'%3E%3Crect width='100' height='100' fill='%23ecf0f1'/%3E%3Ctext x='50' y='50' font-family='Arial' font-size='12' fill='%237f8c8d' text-anchor='middle' dy='0.3em'%3ENačítání...%3C/text%3E%3C/svg%3E");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

/* ===================================
   HAMBURGER MENU ANIMACE
   =================================== */

.navbar-toggler {
  position: relative;
  width: 30px;
  height: 30px;
  border: none;
  background: transparent;
  cursor: pointer;
}

.navbar-toggler-icon {
  display: block;
  position: relative;
  width: 20px;
  height: 2px;
  background-color: var(--text-primary);
  transition: all 0.3s ease;
  margin: 0 auto;
}

.navbar-toggler-icon::before,
.navbar-toggler-icon::after {
  content: '';
  position: absolute;
  width: 20px;
  height: 2px;
  background-color: var(--text-primary);
  transition: all 0.3s ease;
}

.navbar-toggler-icon::before {
  top: -6px;
}

.navbar-toggler-icon::after {
  bottom: -6px;
}

/* Animace při otevření */
.navbar-toggler.active .navbar-toggler-icon {
  background-color: transparent;
}

.navbar-toggler.active .navbar-toggler-icon::before {
  top: 0;
  transform: rotate(45deg);
}

.navbar-toggler.active .navbar-toggler-icon::after {
  bottom: 0;
  transform: rotate(-45deg);
}

/* ===================================
   SMOOTH SCROLL BEHAVIOR
   =================================== */

html {
  scroll-behavior: smooth;
}

/* Offset pro anchor linky kvůli fixed header */
:target {
  scroll-margin-top: 80px;
}

/* ===================================
   CUSTOM SCROLLBAR
   =================================== */

/* Webkit browsers */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--muted-color);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-secondary);
}

/* Firefox */
html {
  scrollbar-width: thin;
  scrollbar-color: var(--muted-color) var(--bg-secondary);
}
