/**
 * Admin JavaScript pro SnurkyNaKrk.cz
 * Funkcionality pro administrační rozhraní
 */

document.addEventListener('DOMContentLoaded', function() {

    // Inicializace admin funkcionalit
    initAdminFeatures();
    initFormValidation();
    initDataTables();
    initImageUpload();
    initConfirmDialogs();
    initAutoSave();

});

/**
 * Inicializace základních admin funkcionalit
 */
function initAdminFeatures() {
    
    // Auto-dismiss alerts po 5 sekundách
    const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
    alerts.forEach(alert => {
        setTimeout(() => {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        }, 5000);
    });
    
    // Tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Popovers
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
    
}

/**
 * Inicializace validace formulářů
 */
function initFormValidation() {
    
    // Bootstrap form validation
    const forms = document.querySelectorAll('.needs-validation');
    
    Array.prototype.slice.call(forms).forEach(function(form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            
            form.classList.add('was-validated');
        }, false);
    });
    
    // Real-time validace pro specifická pole
    initRealTimeValidation();
    
}

/**
 * Real-time validace formulářových polí
 */
function initRealTimeValidation() {
    
    // Email validace
    const emailFields = document.querySelectorAll('input[type="email"]');
    emailFields.forEach(field => {
        field.addEventListener('blur', function() {
            validateEmail(this);
        });
    });
    
    // Telefonní čísla
    const phoneFields = document.querySelectorAll('input[name*="phone"], input[name*="telefon"]');
    phoneFields.forEach(field => {
        field.addEventListener('blur', function() {
            validatePhone(this);
        });
    });
    
    // Ceny
    const priceFields = document.querySelectorAll('input[name*="price"], input[name*="cena"]');
    priceFields.forEach(field => {
        field.addEventListener('input', function() {
            formatPrice(this);
        });
    });
    
}

/**
 * Validace emailové adresy
 */
function validateEmail(field) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const isValid = emailRegex.test(field.value);
    
    if (field.value && !isValid) {
        field.classList.add('is-invalid');
        showFieldError(field, 'Neplatný formát emailové adresy');
    } else {
        field.classList.remove('is-invalid');
        hideFieldError(field);
    }
}

/**
 * Validace telefonního čísla
 */
function validatePhone(field) {
    const phoneRegex = /^(\+420\s?)?[0-9]{3}\s?[0-9]{3}\s?[0-9]{3}$/;
    const isValid = phoneRegex.test(field.value.replace(/\s/g, ''));
    
    if (field.value && !isValid) {
        field.classList.add('is-invalid');
        showFieldError(field, 'Neplatný formát telefonního čísla');
    } else {
        field.classList.remove('is-invalid');
        hideFieldError(field);
    }
}

/**
 * Formátování ceny
 */
function formatPrice(field) {
    let value = field.value.replace(/[^\d]/g, '');
    if (value) {
        value = parseInt(value).toLocaleString('cs-CZ');
        field.value = value;
    }
}

/**
 * Zobrazení chyby u pole
 */
function showFieldError(field, message) {
    hideFieldError(field);
    
    const errorDiv = document.createElement('div');
    errorDiv.className = 'invalid-feedback';
    errorDiv.textContent = message;
    
    field.parentNode.appendChild(errorDiv);
}

/**
 * Skrytí chyby u pole
 */
function hideFieldError(field) {
    const errorDiv = field.parentNode.querySelector('.invalid-feedback');
    if (errorDiv) {
        errorDiv.remove();
    }
}

/**
 * Inicializace DataTables pro tabulky
 */
function initDataTables() {
    
    const tables = document.querySelectorAll('.data-table');
    
    tables.forEach(table => {
        // Základní konfigurace DataTables
        if (typeof $.fn.DataTable !== 'undefined') {
            $(table).DataTable({
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/cs.json'
                },
                responsive: true,
                pageLength: 25,
                order: [[0, 'desc']],
                columnDefs: [
                    { orderable: false, targets: 'no-sort' }
                ]
            });
        }
    });
    
}

/**
 * Inicializace upload obrázků
 */
function initImageUpload() {
    
    const imageInputs = document.querySelectorAll('input[type="file"][accept*="image"]');
    
    imageInputs.forEach(input => {
        input.addEventListener('change', function() {
            previewImage(this);
        });
    });
    
}

/**
 * Náhled obrázku před uploadem
 */
function previewImage(input) {
    
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        
        reader.onload = function(e) {
            let preview = input.parentNode.querySelector('.image-preview');
            
            if (!preview) {
                preview = document.createElement('img');
                preview.className = 'image-preview mt-2';
                input.parentNode.appendChild(preview);
            }
            
            preview.src = e.target.result;
            preview.style.display = 'block';
        };
        
        reader.readAsDataURL(input.files[0]);
    }
    
}

/**
 * Inicializace potvrzovacích dialogů
 */
function initConfirmDialogs() {
    
    const deleteButtons = document.querySelectorAll('.btn-delete, .delete-btn');
    
    deleteButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            
            const message = this.dataset.message || 'Opravdu chcete tento záznam smazat?';
            
            if (confirm(message)) {
                // Pokud je to formulář, odešli ho
                const form = this.closest('form');
                if (form) {
                    form.submit();
                } else {
                    // Jinak přesměruj na URL
                    window.location.href = this.href;
                }
            }
        });
    });
    
}

/**
 * Utility funkce pro AJAX požadavky
 */
function adminAjax(url, data, method = 'POST') {
    
    return fetch(url, {
        method: method,
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: method !== 'GET' ? JSON.stringify(data) : null
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        return response.json();
    });
    
}

/**
 * Zobrazení loading stavu
 */
function showLoading(element) {
    element.classList.add('loading');
    element.disabled = true;
    
    const originalText = element.textContent;
    element.dataset.originalText = originalText;
    element.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Načítání...';
}

/**
 * Skrytí loading stavu
 */
function hideLoading(element) {
    element.classList.remove('loading');
    element.disabled = false;
    
    const originalText = element.dataset.originalText;
    if (originalText) {
        element.textContent = originalText;
    }
}

/**
 * Zobrazení toast notifikace
 */
function showToast(message, type = 'info') {
    
    const toastContainer = document.querySelector('.toast-container') || createToastContainer();
    
    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${type} border-0`;
    toast.setAttribute('role', 'alert');
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;
    
    toastContainer.appendChild(toast);
    
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
    
    // Automatické odstranění po skrytí
    toast.addEventListener('hidden.bs.toast', function() {
        toast.remove();
    });
    
}

/**
 * Vytvoření kontejneru pro toast notifikace
 */
function createToastContainer() {
    const container = document.createElement('div');
    container.className = 'toast-container position-fixed bottom-0 end-0 p-3';
    document.body.appendChild(container);
    return container;
}

/**
 * Kopírování textu do schránky
 */
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        showToast('Text byl zkopírován do schránky', 'success');
    }).catch(function() {
        showToast('Chyba při kopírování do schránky', 'danger');
    });
}

/**
 * Auto-save funkcionalita pro formuláře
 */
function initAutoSave() {
    const autoSaveForms = document.querySelectorAll('[data-auto-save]');

    autoSaveForms.forEach(form => {
        let autoSaveTimeout;
        const inputs = form.querySelectorAll('input, textarea, select');

        inputs.forEach(input => {
            input.addEventListener('input', function() {
                clearTimeout(autoSaveTimeout);
                autoSaveTimeout = setTimeout(() => {
                    autoSaveForm(form);
                }, 3000); // Auto-save po 3 sekundách
            });
        });
    });
}

/**
 * Provedení auto-save formuláře
 */
function autoSaveForm(form) {
    const formData = new FormData(form);
    formData.append('auto_save', '1');

    // Zobrazení indikátoru ukládání
    showAutoSaveIndicator('Ukládá se...', 'info');

    fetch(form.action || window.location.href, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAutoSaveIndicator('Automaticky uloženo', 'success');
        } else {
            showAutoSaveIndicator('Chyba při ukládání', 'error');
        }
    })
    .catch(error => {
        console.error('Auto-save error:', error);
        showAutoSaveIndicator('Chyba při ukládání', 'error');
    });
}

/**
 * Zobrazení auto-save indikátoru
 */
function showAutoSaveIndicator(message, type = 'info') {
    // Odstranění existujícího indikátoru
    const existingIndicator = document.getElementById('autoSaveIndicator');
    if (existingIndicator) {
        existingIndicator.remove();
    }

    // Vytvoření nového indikátoru
    const indicator = document.createElement('div');
    indicator.id = 'autoSaveIndicator';
    indicator.className = `alert alert-${type === 'error' ? 'danger' : type} position-fixed`;
    indicator.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 200px; opacity: 0.9;';
    indicator.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'exclamation-triangle' : 'spinner fa-spin'} me-2"></i>
        ${message}
    `;

    document.body.appendChild(indicator);

    // Automatické odstranění po 3 sekundách
    setTimeout(() => {
        if (indicator.parentNode) {
            indicator.style.opacity = '0';
            setTimeout(() => {
                if (indicator.parentNode) {
                    indicator.remove();
                }
            }, 300);
        }
    }, 3000);
}
