/**
 * ŠŇŮRKY NA KRK - HLAVNÍ JAVASCRIPT SOUBOR
 * ==========================================
 */

// Globální objekt aplikace
window.SnurkyApp = {
    // Konfigurace
    config: {
        animationDuration: 300,
        scrollOffset: 80,
        lazyLoadOffset: 100,
        modalZIndex: 1050
    },
    
    // Inicializace aplikace
    init: function() {
        console.log('🚀 Inicializace Šňůrky na krk aplikace...');
        
        // Čekáme na načtení DOM
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', this.onDOMReady.bind(this));
        } else {
            this.onDOMReady();
        }
    },
    
    // Spuštění po načtení DOM
    onDOMReady: function() {
        this.initMobileMenu();
        this.initSmoothScroll();
        this.initFormValidation();
        this.initLazyLoading();
        this.initModals();
        this.initTooltips();
        this.initAlerts();
        this.initSearchForm();
        
        console.log('✅ Aplikace úspěšně inicializována');
    },
    
    // Mobilní menu
    initMobileMenu: function() {
        const navbarToggler = document.querySelector('.navbar-toggler');
        const navbarCollapse = document.querySelector('.navbar-collapse');
        
        if (!navbarToggler || !navbarCollapse) return;
        
        navbarToggler.addEventListener('click', function() {
            const isExpanded = navbarToggler.getAttribute('aria-expanded') === 'true';
            
            navbarToggler.setAttribute('aria-expanded', !isExpanded);
            navbarCollapse.classList.toggle('show');
            
            // Animace hamburger ikony
            navbarToggler.classList.toggle('active');
        });
        
        // Zavření menu při kliknutí na odkaz
        const navLinks = navbarCollapse.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', () => {
                if (window.innerWidth < 992) {
                    navbarCollapse.classList.remove('show');
                    navbarToggler.setAttribute('aria-expanded', 'false');
                    navbarToggler.classList.remove('active');
                }
            });
        });
        
        // Zavření menu při kliknutí mimo
        document.addEventListener('click', (e) => {
            if (!navbarToggler.contains(e.target) && !navbarCollapse.contains(e.target)) {
                navbarCollapse.classList.remove('show');
                navbarToggler.setAttribute('aria-expanded', 'false');
                navbarToggler.classList.remove('active');
            }
        });
    },
    
    // Plynulé scrollování
    initSmoothScroll: function() {
        const links = document.querySelectorAll('a[href^="#"]');
        
        links.forEach(link => {
            link.addEventListener('click', (e) => {
                const href = link.getAttribute('href');
                if (href === '#') return;
                
                const target = document.querySelector(href);
                if (!target) return;
                
                e.preventDefault();
                
                const offsetTop = target.offsetTop - this.config.scrollOffset;
                
                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
            });
        });
    },
    
    // Validace formulářů
    initFormValidation: function() {
        const forms = document.querySelectorAll('form[data-validate]');
        
        forms.forEach(form => {
            form.addEventListener('submit', (e) => {
                if (!this.validateForm(form)) {
                    e.preventDefault();
                    e.stopPropagation();
                }
                form.classList.add('was-validated');
            });
            
            // Real-time validace
            const inputs = form.querySelectorAll('input, textarea, select');
            inputs.forEach(input => {
                input.addEventListener('blur', () => {
                    this.validateField(input);
                });
                
                input.addEventListener('input', () => {
                    if (input.classList.contains('is-invalid')) {
                        this.validateField(input);
                    }
                });
            });
        });
    },
    
    // Validace jednotlivého pole
    validateField: function(field) {
        const value = field.value.trim();
        const type = field.type;
        const required = field.hasAttribute('required');
        let isValid = true;
        let message = '';
        
        // Kontrola povinných polí
        if (required && !value) {
            isValid = false;
            message = 'Toto pole je povinné';
        }
        
        // Specifické validace podle typu
        if (value && type === 'email') {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(value)) {
                isValid = false;
                message = 'Zadejte platnou emailovou adresu';
            }
        }
        
        if (value && type === 'tel') {
            const phoneRegex = /^(\+420\s?)?[0-9\s]{9,}$/;
            if (!phoneRegex.test(value)) {
                isValid = false;
                message = 'Zadejte platné telefonní číslo';
            }
        }
        
        // Minimální délka
        const minLength = field.getAttribute('minlength');
        if (value && minLength && value.length < parseInt(minLength)) {
            isValid = false;
            message = `Minimální délka je ${minLength} znaků`;
        }
        
        // Aktualizace UI
        this.updateFieldValidation(field, isValid, message);
        
        return isValid;
    },
    
    // Aktualizace validačního stavu pole
    updateFieldValidation: function(field, isValid, message) {
        field.classList.remove('is-valid', 'is-invalid');
        field.classList.add(isValid ? 'is-valid' : 'is-invalid');
        
        // Aktualizace zprávy
        let feedback = field.parentNode.querySelector('.invalid-feedback');
        if (!feedback) {
            feedback = document.createElement('div');
            feedback.className = 'invalid-feedback';
            field.parentNode.appendChild(feedback);
        }
        
        feedback.textContent = message;
        feedback.style.display = isValid ? 'none' : 'block';
    },
    
    // Validace celého formuláře
    validateForm: function(form) {
        const inputs = form.querySelectorAll('input, textarea, select');
        let isValid = true;
        
        inputs.forEach(input => {
            if (!this.validateField(input)) {
                isValid = false;
            }
        });
        
        return isValid;
    },
    
    // Lazy loading obrázků
    initLazyLoading: function() {
        const images = document.querySelectorAll('img[data-src]');
        
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        img.classList.add('lazy-loaded');
                        observer.unobserve(img);
                    }
                });
            }, {
                rootMargin: `${this.config.lazyLoadOffset}px`
            });
            
            images.forEach(img => {
                img.classList.add('lazy');
                imageObserver.observe(img);
            });
        } else {
            // Fallback pro starší prohlížeče
            images.forEach(img => {
                img.src = img.dataset.src;
            });
        }
    },
    
    // Modal okna
    initModals: function() {
        const modalTriggers = document.querySelectorAll('[data-modal]');
        
        modalTriggers.forEach(trigger => {
            trigger.addEventListener('click', (e) => {
                e.preventDefault();
                const modalId = trigger.getAttribute('data-modal');
                this.showModal(modalId);
            });
        });
        
        // Zavření modalu při kliknutí na pozadí nebo křížek
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal') || e.target.classList.contains('modal-close')) {
                this.hideModal();
            }
        });
        
        // Zavření modalu klávesou Escape
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.hideModal();
            }
        });
    },
    
    // Zobrazení modalu
    showModal: function(modalId) {
        const modal = document.getElementById(modalId);
        if (!modal) return;
        
        modal.style.display = 'flex';
        modal.classList.add('show');
        document.body.style.overflow = 'hidden';
        
        // Focus na první focusovatelný element
        const focusableElements = modal.querySelectorAll('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');
        if (focusableElements.length > 0) {
            focusableElements[0].focus();
        }
    },
    
    // Skrytí modalu
    hideModal: function() {
        const modals = document.querySelectorAll('.modal.show');
        modals.forEach(modal => {
            modal.classList.remove('show');
            setTimeout(() => {
                modal.style.display = 'none';
            }, this.config.animationDuration);
        });
        document.body.style.overflow = '';
    },
    
    // Tooltips
    initTooltips: function() {
        const tooltipElements = document.querySelectorAll('[data-tooltip]');
        
        tooltipElements.forEach(element => {
            element.addEventListener('mouseenter', this.showTooltip.bind(this));
            element.addEventListener('mouseleave', this.hideTooltip.bind(this));
        });
    },
    
    // Zobrazení tooltipu
    showTooltip: function(e) {
        const element = e.target;
        const text = element.getAttribute('data-tooltip');
        
        const tooltip = document.createElement('div');
        tooltip.className = 'tooltip-custom';
        tooltip.textContent = text;
        tooltip.style.cssText = `
            position: absolute;
            background: var(--bg-dark);
            color: var(--text-light);
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 14px;
            z-index: 1060;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.3s ease;
        `;
        
        document.body.appendChild(tooltip);
        
        const rect = element.getBoundingClientRect();
        tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
        tooltip.style.top = rect.top - tooltip.offsetHeight - 8 + 'px';
        
        setTimeout(() => {
            tooltip.style.opacity = '1';
        }, 10);
        
        element._tooltip = tooltip;
    },
    
    // Skrytí tooltipu
    hideTooltip: function(e) {
        const element = e.target;
        if (element._tooltip) {
            element._tooltip.style.opacity = '0';
            setTimeout(() => {
                if (element._tooltip && element._tooltip.parentNode) {
                    element._tooltip.parentNode.removeChild(element._tooltip);
                }
                delete element._tooltip;
            }, this.config.animationDuration);
        }
    },
    
    // Automatické zavírání alertů
    initAlerts: function() {
        const alerts = document.querySelectorAll('.alert[data-auto-dismiss]');
        
        alerts.forEach(alert => {
            const delay = parseInt(alert.getAttribute('data-auto-dismiss')) || 5000;
            
            setTimeout(() => {
                alert.style.opacity = '0';
                alert.style.transform = 'translateY(-20px)';
                
                setTimeout(() => {
                    if (alert.parentNode) {
                        alert.parentNode.removeChild(alert);
                    }
                }, this.config.animationDuration);
            }, delay);
        });
        
        // Manuální zavírání alertů
        const closeButtons = document.querySelectorAll('.alert .btn-close');
        closeButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                const alert = e.target.closest('.alert');
                if (alert) {
                    alert.style.opacity = '0';
                    alert.style.transform = 'translateY(-20px)';
                    
                    setTimeout(() => {
                        if (alert.parentNode) {
                            alert.parentNode.removeChild(alert);
                        }
                    }, this.config.animationDuration);
                }
            });
        });
    },
    
    // Vyhledávací formulář
    initSearchForm: function() {
        const searchForm = document.querySelector('form[action*="produkty"]');
        const searchInput = searchForm?.querySelector('input[name="search"]');
        
        if (!searchForm || !searchInput) return;
        
        // Automatické doplňování (jednoduchá implementace)
        let searchTimeout;
        searchInput.addEventListener('input', (e) => {
            clearTimeout(searchTimeout);
            const query = e.target.value.trim();
            
            if (query.length >= 2) {
                searchTimeout = setTimeout(() => {
                    // Zde by byla implementace AJAX vyhledávání
                    console.log('Vyhledávání:', query);
                }, 300);
            }
        });
    },
    
    // Utility funkce
    utils: {
        // Debounce funkce
        debounce: function(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        },
        
        // Throttle funkce
        throttle: function(func, limit) {
            let inThrottle;
            return function() {
                const args = arguments;
                const context = this;
                if (!inThrottle) {
                    func.apply(context, args);
                    inThrottle = true;
                    setTimeout(() => inThrottle = false, limit);
                }
            };
        },
        
        // Formátování ceny
        formatPrice: function(price) {
            return new Intl.NumberFormat('cs-CZ', {
                style: 'currency',
                currency: 'CZK'
            }).format(price);
        },
        
        // Formátování data
        formatDate: function(date) {
            return new Intl.DateTimeFormat('cs-CZ').format(new Date(date));
        }
    }
};

// Inicializace aplikace
SnurkyApp.init();
