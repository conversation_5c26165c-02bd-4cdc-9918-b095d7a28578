<?php
/**
 * Generátor sitemap.xml pro SEO
 */

require_once 'config/autoload.php';

// Nastavení hlavi<PERSON> pro XML
header('Content-Type: application/xml; charset=utf-8');

try {
    // Inicializace modelů
    $productModel = new ProductModel();
    $merchantModel = new MerchantModel();
    
    // Základní URL webu
    $baseUrl = 'https://' . ($_SERVER['HTTP_HOST'] ?? 'snurkynakrk.cz');
    
    // Začátek XML
    echo '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
    echo '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";
    
    // Statické stránky
    $staticPages = [
        ['url' => '/', 'priority' => '1.0', 'changefreq' => 'daily'],
        ['url' => '/produkty', 'priority' => '0.9', 'changefreq' => 'daily'],
        ['url' => '/obchodnici', 'priority' => '0.8', 'changefreq' => 'weekly'],
        ['url' => '/kontakt', 'priority' => '0.7', 'changefreq' => 'monthly'],
        ['url' => '/o-nas', 'priority' => '0.6', 'changefreq' => 'monthly'],
        ['url' => '/obchodnici/registrace', 'priority' => '0.5', 'changefreq' => 'monthly']
    ];
    
    foreach ($staticPages as $page) {
        echo "  <url>\n";
        echo "    <loc>" . htmlspecialchars($baseUrl . $page['url']) . "</loc>\n";
        echo "    <lastmod>" . date('Y-m-d') . "</lastmod>\n";
        echo "    <changefreq>" . $page['changefreq'] . "</changefreq>\n";
        echo "    <priority>" . $page['priority'] . "</priority>\n";
        echo "  </url>\n";
    }
    
    // Produkty
    $products = $productModel->getAll();
    foreach ($products as $product) {
        if (isset($product['active']) && $product['active']) {
            echo "  <url>\n";
            echo "    <loc>" . htmlspecialchars($baseUrl . '/produkt/' . $product['id']) . "</loc>\n";
            echo "    <lastmod>" . date('Y-m-d', strtotime($product['updated_at'] ?? $product['created_at'] ?? 'now')) . "</lastmod>\n";
            echo "    <changefreq>weekly</changefreq>\n";
            echo "    <priority>0.8</priority>\n";
            echo "  </url>\n";
        }
    }
    
    // Kategorie produktů
    $categories = $productModel->getCategories();
    foreach ($categories as $category) {
        echo "  <url>\n";
        echo "    <loc>" . htmlspecialchars($baseUrl . '/produkty?category=' . urlencode($category)) . "</loc>\n";
        echo "    <lastmod>" . date('Y-m-d') . "</lastmod>\n";
        echo "    <changefreq>weekly</changefreq>\n";
        echo "    <priority>0.7</priority>\n";
        echo "  </url>\n";
    }
    
    // Obchodníci
    $merchants = $merchantModel->getAll();
    foreach ($merchants as $merchant) {
        if (isset($merchant['status']) && $merchant['status'] === 'active') {
            echo "  <url>\n";
            echo "    <loc>" . htmlspecialchars($baseUrl . '/obchodnik/' . $merchant['id']) . "</loc>\n";
            echo "    <lastmod>" . date('Y-m-d', strtotime($merchant['updated_at'] ?? $merchant['created_at'] ?? 'now')) . "</lastmod>\n";
            echo "    <changefreq>monthly</changefreq>\n";
            echo "    <priority>0.6</priority>\n";
            echo "  </url>\n";
        }
    }
    
    // Konec XML
    echo '</urlset>' . "\n";
    
} catch (Exception $e) {
    // V případě chyby vrátíme základní sitemap
    echo '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
    echo '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";
    echo "  <url>\n";
    echo "    <loc>" . htmlspecialchars($baseUrl . '/') . "</loc>\n";
    echo "    <lastmod>" . date('Y-m-d') . "</lastmod>\n";
    echo "    <changefreq>daily</changefreq>\n";
    echo "    <priority>1.0</priority>\n";
    echo "  </url>\n";
    echo '</urlset>' . "\n";
}
?>
