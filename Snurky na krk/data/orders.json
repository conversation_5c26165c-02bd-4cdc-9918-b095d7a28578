{"orders": [{"id": 1, "order_number": "ORD-2024-001", "merchant_id": 1, "status": "completed", "order_date": "2024-01-15T10:30:00Z", "delivery_date": "2024-01-20T00:00:00Z", "items": [{"product_id": 1, "size": "M", "color": "černá", "quantity": 10, "unit_price": 150, "total_price": 1500}, {"product_id": 1, "size": "L", "color": "modr<PERSON>", "quantity": 5, "unit_price": 150, "total_price": 750}], "subtotal": 2250, "commission_rate": 15, "commission_amount": 337.5, "total_amount": 2250, "payment_status": "paid", "payment_date": "2024-01-25T14:20:00Z", "notes": "<PERSON><PERSON><PERSON> dodání p<PERSON>", "created_at": "2024-01-15T10:30:00Z", "updated_at": "2024-01-25T14:20:00Z"}], "order_statuses": [{"id": "pending", "name": "Čekající", "description": "Objednávka byla přijata a čeká na zpracování"}, {"id": "processing", "name": "Zpracovává se", "description": "Objednávka se připravuje k odeslání"}, {"id": "shipped", "name": "Odesláno", "description": "Objednávka byla o<PERSON>lána"}, {"id": "completed", "name": "Dokončeno", "description": "Objednávka byla úspěšně dokončena"}, {"id": "cancelled", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Objednávka byla <PERSON>š<PERSON>"}], "payment_statuses": [{"id": "pending", "name": "Čeká na platbu"}, {"id": "paid", "name": "Zaplaceno"}, {"id": "overdue", "name": "Po s<PERSON>"}]}