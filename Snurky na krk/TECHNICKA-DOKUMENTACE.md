# Technická dokumentace - Snurky na krk

## 📋 Obsah
1. [Architektura systému](#architektura-systému)
2. [Technologie](#technologie)
3. [Struktura souborů](#struktura-souborů)
4. [<PERSON><PERSON><PERSON> model](#datový-model)
5. [API dokumentace](#api-dokumentace)
6. [Bezpečnost](#bezpečnost)
7. [Optimalizace](#optimalizace)
8. [<PERSON><PERSON><PERSON><PERSON>](#nasazení)

## 🏗️ Architektura systému

### Přehled
Aplikace je postavena na **MVC (Model-View-Controller)** architektuře s následujícími vrstvami:

```
┌─────────────────┐
│   Presentation  │ ← Templates, Views
├─────────────────┤
│   Controllers   │ ← Business Logic
├─────────────────┤
│     Models      │ ← Data Access Layer
├─────────────────┤
│   Data Storage  │ ← JSON Files
└─────────────────┘
```

### Klíčové komponenty

1. **Router** - URL routing a směrování požadavků
2. **Controllers** - Zpracování business logiky
3. **Models** - Pr<PERSON><PERSON> s daty (JSON storage)
4. **Templates** - Renderování HTML výstupu
5. **Security** - Bezpečnostní funkce
6. **Cache** - Optimalizace výkonu

## 💻 Technologie

### Backend
- **PHP 8.3+** - Hlavní programovací jazyk
- **JSON** - Datové úložiště (místo databáze)
- **Apache** - Webový server
- **Composer** - Správa závislostí (volitelné)

### Frontend
- **HTML5** - Sémantické značkování
- **CSS3** - Styling s custom properties
- **JavaScript ES6+** - Interaktivita
- **Bootstrap 5.3** - CSS framework
- **Font Awesome 6.5** - Ikony

### Vývojové nástroje
- **Git** - Verzování kódu
- **PHPUnit** - Testování (volitelné)
- **Sass** - CSS preprocessing (volitelné)

## 📁 Struktura souborů

```
snurky-na-krk/
├── admin/                  # Admin rozhraní
│   └── index.php          # Admin vstupní bod
├── assets/                # Statické soubory
│   ├── css/               # Styly
│   ├── js/                # JavaScript
│   ├── images/            # Obrázky
│   └── uploads/           # Nahrané soubory
├── config/                # Konfigurace
│   ├── config.php         # Hlavní konfigurace
│   ├── constants.php      # Konstanty
│   └── autoload.php       # Autoloader
├── data/                  # Datové soubory
│   ├── cache/             # Cache soubory
│   ├── products.json      # Produkty
│   ├── merchants.json     # Obchodníci
│   ├── orders.json        # Objednávky
│   └── settings.json      # Nastavení
├── includes/              # PHP třídy
│   ├── controllers/       # Kontrolery
│   ├── models/            # Datové modely
│   ├── helpers/           # Helper třídy
│   └── *.php             # Core třídy
├── templates/             # HTML šablony
│   ├── layouts/           # Layout šablony
│   ├── admin/             # Admin šablony
│   └── */                 # Stránkové šablony
├── index.php              # Hlavní vstupní bod
├── .htaccess             # Apache konfigurace
└── robots.txt            # SEO konfigurace
```

## 🗄️ Datový model

### JSON Schema

#### products.json
```json
{
  "products": [
    {
      "id": 1,
      "name": "string",
      "description": "string",
      "category": "string",
      "price": number,
      "in_stock": number,
      "images": ["string"],
      "specifications": {
        "material": "string",
        "length": "string",
        "width": "string"
      },
      "active": boolean,
      "created_at": "ISO 8601",
      "updated_at": "ISO 8601"
    }
  ]
}
```

#### merchants.json
```json
{
  "merchants": [
    {
      "id": 1,
      "company_name": "string",
      "contact_person": "string",
      "email": "string",
      "phone": "string",
      "address": {
        "street": "string",
        "city": "string",
        "postal_code": "string"
      },
      "commission_rate": number,
      "region": "string",
      "status": "active|inactive|pending",
      "created_at": "ISO 8601",
      "updated_at": "ISO 8601"
    }
  ]
}
```

### Datové modely (PHP třídy)

#### BaseModel
Základní třída pro všechny modely s metodami:
- `loadData()` - Načtení dat z JSON
- `saveData()` - Uložení dat do JSON
- `generateId()` - Generování unikátního ID
- `findById()` - Vyhledání podle ID
- `validateRequired()` - Validace povinných polí

#### ProductModel
- `getAll()` - Všechny produkty
- `getById($id)` - Produkt podle ID
- `getByCategory($category)` - Produkty podle kategorie
- `search($query)` - Vyhledávání produktů
- `create($data)` - Vytvoření produktu
- `update($id, $data)` - Aktualizace produktu
- `delete($id)` - Smazání produktu

## 🔌 API dokumentace

### Router API

```php
// Přidání route
$router->addRoute('GET', '/produkty', 'ProductController@index');
$router->addRoute('GET', '/produkt/{id}', 'ProductController@detail');

// Spuštění routeru
$router->dispatch($_SERVER['REQUEST_METHOD'], $_SERVER['REQUEST_URI']);
```

### Controller API

```php
class ProductController extends BaseController
{
    public function index()
    {
        $products = $this->productModel->getAll();
        $this->render('products/index', ['products' => $products]);
    }
    
    public function detail($id)
    {
        $product = $this->productModel->getById($id);
        $this->render('products/detail', ['product' => $product]);
    }
}
```

### Template API

```php
// Renderování s layoutem
$template->renderWithLayout('products/index', $data, 'main');

// Helper metody
$template->escape($string);
$template->formatPrice($price);
$template->formatDate($date);
$template->url($path, $params);
```

## 🔒 Bezpečnost

### Implementované ochranné mechanismy

1. **CSRF ochrana**
   ```php
   $security->generateCSRFToken();
   $security->validateCSRFToken($token);
   ```

2. **XSS prevence**
   ```php
   $security->sanitizeInput($input);
   $template->escape($output);
   ```

3. **Input validace**
   ```php
   $security->validateEmail($email);
   $security->validatePhone($phone);
   $security->validateUrl($url);
   ```

4. **File upload bezpečnost**
   ```php
   $fileManager->validateFile($file);
   $fileManager->sanitizeFilename($filename);
   ```

### Bezpečnostní hlavičky (.htaccess)
```apache
Header always set X-XSS-Protection "1; mode=block"
Header always set X-Content-Type-Options "nosniff"
Header always set X-Frame-Options "SAMEORIGIN"
Header always set Referrer-Policy "strict-origin-when-cross-origin"
```

## ⚡ Optimalizace

### Cache systém

```php
// Použití cache
$cache = new CacheManager();
$data = $cache->remember('products_all', function() {
    return $productModel->getAll();
}, 3600);

// Invalidace cache
$cache->delete('products_all');
$cache->clearExpired();
```

### Asset optimalizace

```php
// CSS optimalizace
$optimizer = new AssetOptimizer();
$cssUrl = $optimizer->optimizeCSS(['css/style.css'], 'combined.min.css');

// JavaScript optimalizace
$jsUrl = $optimizer->optimizeJS(['js/app.js'], 'combined.min.js');
```

### SEO optimalizace

```php
// Meta tagy
$seo = new SEOManager();
$metaTags = $seo->generateMetaTags('product', ['product' => $product]);

// Strukturovaná data
$structuredData = $seo->generateStructuredData('product', $product);
```

## 🚀 Nasazení

### Systémové požadavky

- **PHP 8.3+** s rozšířeními:
  - `json` - Práce s JSON
  - `gd` - Zpracování obrázků
  - `fileinfo` - MIME type detekce
  - `mbstring` - Multibyte string funkce
- **Apache 2.4+** s moduly:
  - `mod_rewrite` - URL rewriting
  - `mod_headers` - HTTP hlavičky
  - `mod_deflate` - Gzip komprese
  - `mod_expires` - Cache hlavičky

### Instalace

1. **Upload souborů**
   ```bash
   # Nahrání všech souborů na server
   rsync -av --exclude='.git' ./ user@server:/path/to/web/
   ```

2. **Nastavení oprávnění**
   ```bash
   chmod 755 /path/to/web/
   chmod 777 /path/to/web/data/
   chmod 777 /path/to/web/assets/uploads/
   ```

3. **Konfigurace**
   - Upravte `config/config.php` pro produkční prostředí
   - Nastavte správné URL v `BASE_URL`
   - Změňte výchozí admin heslo

### Produkční nastavení

```php
// config/config.php
define('DEBUG_MODE', false);
define('ENVIRONMENT', 'production');

// Error reporting
ini_set('display_errors', 0);
ini_set('log_errors', 1);
```

### Monitoring a údržba

1. **Log soubory**
   - `data/error.log` - PHP chyby
   - `data/access.log` - Přístupové logy

2. **Cache údržba**
   ```bash
   # Vyčištění cache (cron job)
   0 2 * * * php /path/to/web/scripts/clear-cache.php
   ```

3. **Zálohy**
   ```bash
   # Denní záloha dat (cron job)
   0 1 * * * tar -czf /backups/data-$(date +\%Y\%m\%d).tar.gz /path/to/web/data/
   ```

## 🧪 Testování

### Spuštění testů

```bash
# Kompletní test
php test-complete.php

# Test optimalizací
php test-optimizations.php

# Test admin rozhraní
php test-admin.php
```

### Testovací pokrytí

- ✅ Datové modely a CRUD operace
- ✅ Cache systém a optimalizace
- ✅ Bezpečnostní funkce
- ✅ Template engine a rendering
- ✅ Router a URL handling
- ✅ File upload a management

---

**Verze:** 1.0  
**Datum:** Červen 2025  
**Autor:** SnurkyNaKrk Team
