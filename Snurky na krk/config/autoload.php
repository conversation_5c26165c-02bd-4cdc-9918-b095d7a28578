<?php
/**
 * Autoloader pro SnurkyNaKrk.cz
 * Automatické na<PERSON>tání PHP tříd
 * 
 * <AUTHOR> Team
 * @version 1.0
 */

// Zabránění přímému přístupu
if (!defined('SNURKY_INIT')) {
    die('Přímý přístup není povolen');
}

/**
 * Autoloader funkce pro automatické načítání tříd
 * 
 * @param string $className Název třídy k načten<PERSON>
 * @return bool True pokud byla třída načten<PERSON>, jinak false
 */
function snurkyAutoloader($className) {
    // Mapa tříd a jejich umístění
    $classMap = [
        // Core třídy
        'Router' => INCLUDES_PATH . '/Router.php',
        'TemplateEngine' => INCLUDES_PATH . '/TemplateEngine.php',
        'Security' => INCLUDES_PATH . '/Security.php',
        'SessionManager' => INCLUDES_PATH . '/SessionManager.php',
        'ErrorHandler' => INCLUDES_PATH . '/ErrorHandler.php',
        'CacheManager' => INCLUDES_PATH . '/CacheManager.php',
        'AssetOptimizer' => INCLUDES_PATH . '/AssetOptimizer.php',
        'SEOManager' => INCLUDES_PATH . '/SEOManager.php',

        // Model třídy
        'BaseModel' => INCLUDES_PATH . '/models/BaseModel.php',
        'ProductModel' => INCLUDES_PATH . '/models/ProductModel.php',
        'MerchantModel' => INCLUDES_PATH . '/models/MerchantModel.php',
        'OrderModel' => INCLUDES_PATH . '/models/OrderModel.php',
        'SettingsModel' => INCLUDES_PATH . '/models/SettingsModel.php',

        // Controller třídy
        'BaseController' => INCLUDES_PATH . '/controllers/BaseController.php',
        'HomeController' => INCLUDES_PATH . '/controllers/HomeController.php',
        'ProductController' => INCLUDES_PATH . '/controllers/ProductController.php',
        'DealerController' => INCLUDES_PATH . '/controllers/DealerController.php',
        'ContactController' => INCLUDES_PATH . '/controllers/ContactController.php',
        'AdminController' => INCLUDES_PATH . '/controllers/AdminController.php',

        // Admin Controller třídy
        'AdminAuthController' => INCLUDES_PATH . '/controllers/AdminAuthController.php',
        'AdminDashboardController' => INCLUDES_PATH . '/controllers/AdminDashboardController.php',
        'AdminProductController' => INCLUDES_PATH . '/controllers/AdminProductController.php',
        'AdminDealerController' => INCLUDES_PATH . '/controllers/AdminDealerController.php',
        'AdminSettingsController' => INCLUDES_PATH . '/controllers/AdminSettingsController.php',

        // Helper třídy
        'ValidationHelper' => INCLUDES_PATH . '/helpers/ValidationHelper.php',
        'FormattingHelper' => INCLUDES_PATH . '/helpers/FormattingHelper.php',

        // Správa souborů
        'FileManager' => INCLUDES_PATH . '/FileManager.php',

        // Exception třídy
        'SnurkyException' => INCLUDES_PATH . '/exceptions/SnurkyException.php',
        'ValidationException' => INCLUDES_PATH . '/exceptions/ValidationException.php',
        'SecurityException' => INCLUDES_PATH . '/exceptions/SecurityException.php'
    ];
    
    // Pokud máme mapovanou třídu, načteme ji
    if (isset($classMap[$className])) {
        $filePath = $classMap[$className];
        if (file_exists($filePath)) {
            require_once $filePath;
            return true;
        }
    }
    
    // Pokusíme se najít třídu podle konvence názvů
    $possiblePaths = [
        INCLUDES_PATH . '/' . $className . '.php',
        INCLUDES_PATH . '/models/' . $className . '.php',
        INCLUDES_PATH . '/controllers/' . $className . '.php',
        INCLUDES_PATH . '/helpers/' . $className . '.php',
        INCLUDES_PATH . '/exceptions/' . $className . '.php',
        ADMIN_PATH . '/includes/' . $className . '.php'
    ];
    
    foreach ($possiblePaths as $path) {
        if (file_exists($path)) {
            require_once $path;
            return true;
        }
    }
    
    // Logování neúspěšného načtení třídy
    if (function_exists('error_log')) {
        error_log("Autoloader: Nepodařilo se načíst třídu '$className'");
    }
    
    return false;
}

// Registrace autoloaderu
spl_autoload_register('snurkyAutoloader');

/**
 * Funkce pro načtení všech souborů z adresáře
 * 
 * @param string $directory Cesta k adresáři
 * @param string $pattern Vzor pro názvy souborů (výchozí: *.php)
 * @return array Seznam načtených souborů
 */
function loadDirectory($directory, $pattern = '*.php') {
    $loadedFiles = [];
    
    if (!is_dir($directory)) {
        return $loadedFiles;
    }
    
    $files = glob($directory . '/' . $pattern);
    
    foreach ($files as $file) {
        if (is_file($file)) {
            require_once $file;
            $loadedFiles[] = basename($file);
        }
    }
    
    return $loadedFiles;
}

/**
 * Funkce pro načtení všech helper funkcí
 */
function loadHelpers() {
    $helpersPath = INCLUDES_PATH . '/helpers';
    if (is_dir($helpersPath)) {
        loadDirectory($helpersPath);
    }
}

/**
 * Funkce pro načtení všech exception tříd
 */
function loadExceptions() {
    $exceptionsPath = INCLUDES_PATH . '/exceptions';
    if (is_dir($exceptionsPath)) {
        loadDirectory($exceptionsPath);
    }
}

// Načtení základních helper funkcí při inicializaci
if (DEBUG_MODE) {
    // V debug režimu načteme všechny helpery a exceptions
    loadHelpers();
    loadExceptions();
}
