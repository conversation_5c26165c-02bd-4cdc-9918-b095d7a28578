<?php
/**
 * Hlavní konfigurační soubor pro SnurkyNaKrk.cz
 * 
 * <AUTHOR> Team
 * @version 1.0
 */

// Zabránění přímému přístupu
if (!defined('SNURKY_INIT')) {
    die('Přímý přístup není povolen');
}

// Základní nastavení PHP
error_reporting(E_ALL);
ini_set('display_errors', 0); // V produkci nastavit na 0
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../data/error.log');

// Nastavení časové zóny
date_default_timezone_set('Europe/Prague');

// Základní konstanty aplikace
define('SITE_NAME', 'Snurky na krk');
define('SITE_URL', 'https://snurkynakrekt.cz');
define('BASE_URL', 'http://localhost:8000'); // Pro development
define('SITE_EMAIL', '<EMAIL>');
define('SITE_PHONE', '+420 123 456 789');

// Cesty k adresářům
define('ROOT_PATH', dirname(__DIR__));
define('DATA_PATH', ROOT_PATH . '/data');
define('ASSETS_PATH', ROOT_PATH . '/assets');
define('TEMPLATES_PATH', ROOT_PATH . '/templates');
define('INCLUDES_PATH', ROOT_PATH . '/includes');
define('ADMIN_PATH', ROOT_PATH . '/admin');
define('UPLOADS_PATH', ASSETS_PATH . '/uploads');

// URL cesty
define('ASSETS_URL', '/assets');
define('UPLOADS_URL', '/assets/uploads');
define('ADMIN_URL', '/admin');

// Bezpečnostní nastavení
define('SESSION_NAME', 'snurky_session');
define('SESSION_LIFETIME', 3600); // 1 hodina
define('CSRF_TOKEN_NAME', 'snurky_csrf_token');
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOGIN_LOCKOUT_TIME', 900); // 15 minut

// Nastavení pro soubory
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'webp']);
define('ALLOWED_FILE_TYPES', ['pdf', 'doc', 'docx', 'txt']);

// Nastavení pro JSON soubory
define('JSON_OPTIONS', JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);

// Debug režim (v produkci nastavit na false)
define('DEBUG_MODE', true);

// Nastavení pro cache
define('CACHE_ENABLED', true);
define('CACHE_LIFETIME', 3600); // 1 hodina

// Nastavení pro SEO
define('DEFAULT_META_TITLE', 'Snurky na krk - Kvalitní šňůrky pro každou příležitost');
define('DEFAULT_META_DESCRIPTION', 'Objevte naši širokou nabídku kvalitních šňůrek na krk. Ručně vyráběné produkty od českých řemeslníků.');
define('DEFAULT_META_KEYWORDS', 'šňůrky na krk, náhrdelníky, šperky, ruční výroba, česká kvalita');

// Nastavení pro email
define('SMTP_HOST', '');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '');
define('SMTP_PASSWORD', '');
define('SMTP_ENCRYPTION', 'tls');

// Nastavení pro sociální sítě
define('FACEBOOK_URL', '');
define('INSTAGRAM_URL', '');
define('YOUTUBE_URL', '');

// Verze aplikace
define('APP_VERSION', '1.0.0');
define('APP_BUILD_DATE', '2024-01-01');

// Funkce pro načtení konfigurace z JSON souboru
function loadConfig($configFile = 'site_config.json') {
    $configPath = DATA_PATH . '/' . $configFile;
    if (file_exists($configPath)) {
        $config = json_decode(file_get_contents($configPath), true);
        if ($config !== null) {
            return $config;
        }
    }
    return [];
}

// Funkce pro uložení konfigurace do JSON souboru
function saveConfig($config, $configFile = 'site_config.json') {
    $configPath = DATA_PATH . '/' . $configFile;
    return file_put_contents($configPath, json_encode($config, JSON_OPTIONS)) !== false;
}

// Inicializace session
if (session_status() === PHP_SESSION_NONE) {
    session_name(SESSION_NAME);
    session_set_cookie_params([
        'lifetime' => SESSION_LIFETIME,
        'path' => '/',
        'domain' => '',
        'secure' => isset($_SERVER['HTTPS']),
        'httponly' => true,
        'samesite' => 'Strict'
    ]);
    session_start();
}

// Regenerace session ID pro bezpečnost
if (!isset($_SESSION['regenerated'])) {
    session_regenerate_id(true);
    $_SESSION['regenerated'] = true;
}
