<?php
/**
 * Konstanty aplikace pro SnurkyNaKrk.cz
 * 
 * <AUTHOR> Team
 * @version 1.0
 */

// Zabráněn<PERSON> přímému přístupu
if (!defined('SNURKY_INIT')) {
    die('<PERSON><PERSON><PERSON><PERSON><PERSON> přístup není povolen');
}

// Konstanty pro stránky
define('PAGE_HOME', 'home');
define('PAGE_PRODUCTS', 'products');
define('PAGE_DEALERS', 'dealers');
define('PAGE_CONTACT', 'contact');
define('PAGE_ABOUT', 'about');

// Konstanty pro admin stránky
define('ADMIN_LOGIN', 'login');
define('ADMIN_DASHBOARD', 'dashboard');
define('ADMIN_PRODUCTS', 'products');
define('ADMIN_DEALERS', 'dealers');
define('ADMIN_SETTINGS', 'settings');
define('ADMIN_LOGOUT', 'logout');

// Konstanty pro akce
define('ACTION_CREATE', 'create');
define('ACTION_READ', 'read');
define('ACTION_UPDATE', 'update');
define('ACTION_DELETE', 'delete');
define('ACTION_LIST', 'list');

// Konstanty pro HTTP metody
define('METHOD_GET', 'GET');
define('METHOD_POST', 'POST');
define('METHOD_PUT', 'PUT');
define('METHOD_DELETE', 'DELETE');

// Konstanty pro response typy
define('RESPONSE_JSON', 'json');
define('RESPONSE_HTML', 'html');
define('RESPONSE_REDIRECT', 'redirect');

// Konstanty pro status kódy
define('STATUS_SUCCESS', 200);
define('STATUS_CREATED', 201);
define('STATUS_BAD_REQUEST', 400);
define('STATUS_UNAUTHORIZED', 401);
define('STATUS_FORBIDDEN', 403);
define('STATUS_NOT_FOUND', 404);
define('STATUS_METHOD_NOT_ALLOWED', 405);
define('STATUS_INTERNAL_ERROR', 500);

// Konstanty pro zprávy
define('MSG_SUCCESS', 'success');
define('MSG_ERROR', 'error');
define('MSG_WARNING', 'warning');
define('MSG_INFO', 'info');

// Konstanty pro validaci
define('MIN_PASSWORD_LENGTH', 8);
define('MAX_TEXT_LENGTH', 1000);
define('MAX_TITLE_LENGTH', 100);
define('MAX_NAME_LENGTH', 50);
define('MAX_EMAIL_LENGTH', 100);
define('MAX_PHONE_LENGTH', 20);
define('MAX_URL_LENGTH', 255);

// Konstanty pro produkty
define('PRODUCT_STATUS_ACTIVE', 'active');
define('PRODUCT_STATUS_INACTIVE', 'inactive');
define('PRODUCT_STATUS_DRAFT', 'draft');

// Konstanty pro obchodníky
define('DEALER_STATUS_ACTIVE', 'active');
define('DEALER_STATUS_INACTIVE', 'inactive');
define('DEALER_STATUS_PENDING', 'pending');

// Konstanty pro kategorie produktů
define('CATEGORY_NECKLACES', 'necklaces');
define('CATEGORY_BRACELETS', 'bracelets');
define('CATEGORY_ACCESSORIES', 'accessories');
define('CATEGORY_CUSTOM', 'custom');

// Konstanty pro materiály
define('MATERIAL_LEATHER', 'leather');
define('MATERIAL_FABRIC', 'fabric');
define('MATERIAL_METAL', 'metal');
define('MATERIAL_WOOD', 'wood');
define('MATERIAL_STONE', 'stone');
define('MATERIAL_MIXED', 'mixed');

// Konstanty pro velikosti
define('SIZE_XS', 'xs');
define('SIZE_S', 's');
define('SIZE_M', 'm');
define('SIZE_L', 'l');
define('SIZE_XL', 'xl');
define('SIZE_UNIVERSAL', 'universal');

// Konstanty pro barvy
define('COLOR_BLACK', 'black');
define('COLOR_BROWN', 'brown');
define('COLOR_WHITE', 'white');
define('COLOR_RED', 'red');
define('COLOR_BLUE', 'blue');
define('COLOR_GREEN', 'green');
define('COLOR_YELLOW', 'yellow');
define('COLOR_MIXED', 'mixed');

// Konstanty pro log úrovně
define('LOG_DEBUG', 'debug');
define('LOG_INFO', 'info');
define('LOG_WARNING', 'warning');
define('LOG_ERROR', 'error');
define('LOG_CRITICAL', 'critical');

// Konstanty pro cache klíče
define('CACHE_PRODUCTS', 'products');
define('CACHE_DEALERS', 'dealers');
define('CACHE_SETTINGS', 'settings');
define('CACHE_MENU', 'menu');

// Konstanty pro soubory
define('FILE_PRODUCTS', 'products.json');
define('FILE_DEALERS', 'dealers.json');
define('FILE_SETTINGS', 'site_config.json');
define('FILE_USERS', 'users.json');
define('FILE_LOGS', 'app.log');

// Konstanty pro upload
define('UPLOAD_PATH', ROOT_PATH . '/assets/uploads');
define('UPLOAD_URL', BASE_URL . '/assets/uploads');
define('MAX_UPLOAD_SIZE', 5 * 1024 * 1024); // 5MB

// Pole s překlady stavů
define('STATUS_TRANSLATIONS', [
    PRODUCT_STATUS_ACTIVE => 'Aktivní',
    PRODUCT_STATUS_INACTIVE => 'Neaktivní',
    PRODUCT_STATUS_DRAFT => 'Koncept',
    DEALER_STATUS_ACTIVE => 'Aktivní',
    DEALER_STATUS_INACTIVE => 'Neaktivní',
    DEALER_STATUS_PENDING => 'Čeká na schválení'
]);

// Pole s překlady kategorií
define('CATEGORY_TRANSLATIONS', [
    CATEGORY_NECKLACES => 'Náhrdelníky',
    CATEGORY_BRACELETS => 'Náramky',
    CATEGORY_ACCESSORIES => 'Doplňky',
    CATEGORY_CUSTOM => 'Na zakázku'
]);

// Pole s překlady materiálů
define('MATERIAL_TRANSLATIONS', [
    MATERIAL_LEATHER => 'Kůže',
    MATERIAL_FABRIC => 'Látka',
    MATERIAL_METAL => 'Kov',
    MATERIAL_WOOD => 'Dřevo',
    MATERIAL_STONE => 'Kámen',
    MATERIAL_MIXED => 'Smíšené'
]);

// Pole s překlady velikostí
define('SIZE_TRANSLATIONS', [
    SIZE_XS => 'XS',
    SIZE_S => 'S',
    SIZE_M => 'M',
    SIZE_L => 'L',
    SIZE_XL => 'XL',
    SIZE_UNIVERSAL => 'Univerzální'
]);

// Pole s překlady barev
define('COLOR_TRANSLATIONS', [
    COLOR_BLACK => 'Černá',
    COLOR_BROWN => 'Hnědá',
    COLOR_WHITE => 'Bílá',
    COLOR_RED => 'Červená',
    COLOR_BLUE => 'Modrá',
    COLOR_GREEN => 'Zelená',
    COLOR_YELLOW => 'Žlutá',
    COLOR_MIXED => 'Smíšené'
]);
