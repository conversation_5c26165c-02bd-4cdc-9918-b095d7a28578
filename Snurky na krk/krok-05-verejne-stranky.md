# Krok 5: Frontend - <PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>ky

## <PERSON><PERSON><PERSON> kroku
Implementovat všechny veřejné stránky webu s dynamickým obsahem načítaným z JSON souborů.

## Úkoly

### 5.1 <PERSON><PERSON><PERSON><PERSON> strán<PERSON> (/)
- home.php - šablona hlavní str<PERSON>ky
- Hero sekce s úvodním textem
- Přehled TOP produktů
- Výběr obchodníků
- Call-to-action sekce

#### Struktura hlavní stránky:
```html
<section class="hero">
  <h1>{{intro_title}}</h1>
  <p>{{intro_text}}</p>
</section>

<section class="top-products">
  <h2>TOP produkty</h2>
  <div class="products-grid">
    <!-- Dynamicky načtené produkty -->
  </div>
</section>

<section class="featured-dealers">
  <h2>Na<PERSON><PERSON> ob<PERSON></h2>
  <div class="dealers-grid">
    <!-- <PERSON><PERSON><PERSON><PERSON><PERSON> -->
  </div>
</section>
```

### 5.2 Stránka produktů (/produkty)
- products.php - šablona pro produkty
- Grid layout pro produkty
- Filtrování a řazení
- Detail produktu v modalu

#### Funkce produktové stránky:
- Zobrazení všech aktivních produktů
- Responzivní grid (1-4 sloupce dle zařízení)
- Lazy loading obrázků
- SEO optimalizované URL

### 5.3 Stránka obchodníků (/obchodnici)
- dealers.php - šablona pro obchodníky
- Seznam obchodníků s kontakty
- Filtrování podle města
- Mapa s lokacemi (volitelně)

#### Struktura obchodníků:
```html
<div class="dealers-list">
  <div class="dealer-card">
    <img src="{{logo}}" alt="{{name}}">
    <h3>{{name}}</h3>
    <p>{{city}}</p>
    <div class="contact">
      <a href="mailto:{{email}}">{{email}}</a>
      <a href="tel:{{phone}}">{{phone}}</a>
    </div>
  </div>
</div>
```

### 5.4 Kontaktní stránka (/kontakt)
- contact.php - šablona kontaktů
- Kontaktní informace
- Kontaktní formulář
- Mapa (volitelně)

#### Kontaktní formulář:
```html
<form class="contact-form" method="POST">
  <input type="text" name="name" required>
  <input type="email" name="email" required>
  <input type="text" name="subject" required>
  <textarea name="message" required></textarea>
  <button type="submit">Odeslat</button>
</form>
```

### 5.5 Dynamické načítání obsahu
- Integrace s DataManager třídami
- Cachování dat pro výkon
- Fallback pro chybějící data
- Error handling

### 5.6 SEO optimalizace
- Strukturovaná data (JSON-LD)
- Open Graph meta tagy
- Canonical URLs
- Sitemap.xml generování

#### Strukturovaná data:
```json
{
  "@context": "https://schema.org",
  "@type": "Organization",
  "name": "SnurkyNaKrk.cz",
  "url": "https://snurkynakrekt.cz",
  "contactPoint": {
    "@type": "ContactPoint",
    "email": "<EMAIL>"
  }
}
```

### 5.7 Formuláře a interakce
- Kontaktní formulář s validací
- Newsletter přihlášení
- AJAX odeslání formulářů
- Success/error zprávy

## ✅ Výstupy kroku - DOKONČENO
- ✅ Kompletní hlavní stránka s hero sekcí a doporučenými produkty
- ✅ Stránka produktů s gridem, filtrováním a stránkováním
- ✅ Detail produktu s galerií a podobnými produkty
- ✅ Stránka obchodníků s filtrováním podle regionů
- ✅ Registrace obchodníka s kompletním formulářem
- ✅ Kontaktní stránka s formulářem a FAQ
- ✅ O nás stránka s historií a týmem
- ✅ SEO optimalizace (meta tagy, strukturovaná data, sitemap)
- ✅ Dynamické načítání obsahu z JSON souborů
- ✅ Responzivní design pro všechny stránky

## ✅ Testování - DOKONČENO
- ✅ Test všech stránek na různých zařízeních (desktop, tablet, mobil)
- ✅ Validace formulářů s error handling
- ✅ SEO audit (Open Graph, Twitter Card, JSON-LD)
- ✅ Test rychlosti načítání s lazy loading
- ✅ Kontrola funkčnosti všech odkazů a navigace
- ✅ Sitemap.xml generování a robots.txt

## Technické detaily
- **Šablony:** 7 nových template souborů
- **SEO:** Open Graph, Twitter Card, JSON-LD strukturovaná data
- **Formuláře:** CSRF ochrana, validace, GDPR souhlas
- **Performance:** Lazy loading obrázků, optimalizované dotazy
- **Accessibility:** Semantic HTML, ARIA labels, keyboard navigace

## Následující krok
Krok 6: Administrační rozhraní - autentizace a CRUD operace
