<?php
/**
 * <PERSON><PERSON><PERSON><PERSON><PERSON> test kroku 7
 */

define('SNURKY_INIT', true);
require_once 'config/config.php';
require_once 'config/autoload.php';

echo "<h1>Test Krok 7 - Dokončení admin rozhraní</h1>\n";

$success = 0;
$total = 0;

// Test šablon
$templates = [
    'templates/admin/products/edit.php',
    'templates/admin/dealers/index.php', 
    'templates/admin/dealers/create.php',
    'templates/admin/dealers/edit.php',
    'templates/admin/settings/index.php'
];

echo "<h2>1. Admin šablony</h2>\n";
foreach ($templates as $template) {
    $total++;
    if (file_exists($template)) {
        echo "✅ $template<br>\n";
        $success++;
    } else {
        echo "❌ $template<br>\n";
    }
}

// Test FileManager
echo "<h2>2. FileManager</h2>\n";
$total++;
if (class_exists('FileManager')) {
    echo "✅ FileManager třída existuje<br>\n";
    $success++;
} else {
    echo "❌ FileManager třída neexistuje<br>\n";
}

// Test upload adresářů
echo "<h2>3. Upload adresáře</h2>\n";
$dirs = ['assets/uploads', 'assets/uploads/products', 'assets/uploads/thumbnails'];
foreach ($dirs as $dir) {
    $total++;
    if (is_dir($dir)) {
        echo "✅ $dir<br>\n";
        $success++;
    } else {
        echo "❌ $dir<br>\n";
    }
}

// Test JavaScript
echo "<h2>4. JavaScript</h2>\n";
$total++;
if (file_exists('assets/js/admin.js')) {
    $js = file_get_contents('assets/js/admin.js');
    if (strpos($js, 'initAutoSave') !== false) {
        echo "✅ Admin JavaScript s auto-save<br>\n";
        $success++;
    } else {
        echo "❌ Admin JavaScript bez auto-save<br>\n";
    }
} else {
    echo "❌ Admin JavaScript neexistuje<br>\n";
}

// Test admin kontrolerů
echo "<h2>5. Admin kontrolery</h2>\n";
$controllers = [
    'AdminProductController',
    'AdminDealerController',
    'AdminSettingsController'
];

foreach ($controllers as $controller) {
    $total++;
    if (class_exists($controller)) {
        echo "✅ $controller<br>\n";
        $success++;
    } else {
        echo "❌ $controller<br>\n";
    }
}

// Výsledek
$percentage = round(($success / $total) * 100, 1);
echo "<h2>Výsledek</h2>\n";
echo "<p><strong>Úspěšnost: $percentage% ($success/$total)</strong></p>\n";

if ($percentage >= 90) {
    echo "<p style='color: green; font-size: 18px;'>🎉 <strong>Krok 7 úspěšně dokončen!</strong></p>\n";
} elseif ($percentage >= 75) {
    echo "<p style='color: orange; font-size: 18px;'>⚠️ <strong>Krok 7 z větší části dokončen</strong></p>\n";
} else {
    echo "<p style='color: red; font-size: 18px;'>❌ <strong>Krok 7 vyžaduje další práci</strong></p>\n";
}

echo "<hr><p><em>Test dokončen: " . date('d.m.Y H:i:s') . "</em></p>\n";
?>
