<?php
/**
 * Kompletní oprava modulu PriceInquiry a pozice sekce "Vlastní moduly"
 * 
 * Spustit v prohlížeči: /modules/priceinquiry/complete_fix.php
 */

// Načtení PrestaShop prostředí
$prestashop_path = dirname(dirname(dirname(__FILE__)));
require_once($prestashop_path . '/config/config.inc.php');
require_once($prestashop_path . '/init.php');

echo "<h1>Kompletní oprava modulu PriceInquiry</h1>";
echo "<hr>";

// 1. Smazání existujícího tabu AdminPriceInquiry (pokud existuje)
echo "<h2>1. Čištění existujících tabů</h2>";

$existing_tab_id = Tab::getIdFromClassName('AdminPriceInquiry');
if ($existing_tab_id) {
    $existing_tab = new Tab($existing_tab_id);
    if ($existing_tab->delete()) {
        echo "✅ Starý tab 'AdminPriceInquiry' sma<PERSON><PERSON><br>";
    } else {
        echo "❌ Chyba při mazání starého tabu<br>";
    }
} else {
    echo "ℹ️ Tab 'AdminPriceInquiry' neexistuje<br>";
}

// 2. Najdeme nebo vytvoříme sekci "Vlastní moduly"
echo "<br><h2>2. Kontrola sekce 'Vlastní moduly'</h2>";

$custom_modules_tab_id = Tab::getIdFromClassName('AdminCustomModules');
if (!$custom_modules_tab_id) {
    echo "❌ Sekce 'Vlastní moduly' neexistuje! Vytvářím...<br>";
    
    // Vytvoříme sekci
    $parent_tab = new Tab();
    $parent_tab->active = 1;
    $parent_tab->class_name = 'AdminCustomModules';
    $parent_tab->name = [];
    foreach (Language::getLanguages(true) as $lang) {
        $parent_tab->name[$lang['id_lang']] = 'Vlastní moduly';
    }
    $parent_tab->id_parent = 0;
    $parent_tab->icon = 'extension';
    $parent_tab->position = 999; // Dočasná pozice
    
    if ($parent_tab->add()) {
        $custom_modules_tab_id = $parent_tab->id;
        echo "✅ Sekce 'Vlastní moduly' vytvořena (ID: {$custom_modules_tab_id})<br>";
    } else {
        echo "❌ Chyba při vytváření sekce 'Vlastní moduly'<br>";
        exit;
    }
} else {
    echo "✅ Sekce 'Vlastní moduly' existuje (ID: {$custom_modules_tab_id})<br>";
}

// 3. Vytvoříme nový tab pro PriceInquiry
echo "<br><h2>3. Vytvoření tabu pro PriceInquiry</h2>";

$new_tab = new Tab();
$new_tab->active = 1;
$new_tab->class_name = 'AdminPriceInquiry';
$new_tab->name = [];
foreach (Language::getLanguages(true) as $lang) {
    $new_tab->name[$lang['id_lang']] = 'Cena na dotaz';
}
$new_tab->id_parent = $custom_modules_tab_id;
$new_tab->module = 'priceinquiry';

if ($new_tab->add()) {
    echo "✅ Tab 'AdminPriceInquiry' vytvořen v sekci 'Vlastní moduly'<br>";
} else {
    echo "❌ Chyba při vytváření tabu<br>";
}

// 4. Opravíme pozici sekce "Vlastní moduly" - umístíme za KONFIGURACE
echo "<br><h2>4. Oprava pozice sekce 'Vlastní moduly'</h2>";

$config_tab_id = Tab::getIdFromClassName('AdminParentPreferences'); // KONFIGURACE
if ($config_tab_id) {
    $config_tab = new Tab($config_tab_id);
    $target_position = $config_tab->position + 1;
    
    echo "🎯 Cílová pozice: {$target_position} (za KONFIGURACE na pozici {$config_tab->position})<br>";
    
    // Resetujeme pozice všech top-level tabů
    echo "🔄 Resetuji pozice všech top-level tabů...<br>";
    
    // Získáme všechny top-level taby seřazené podle současné pozice
    $sql = 'SELECT id_tab, class_name, position FROM `' . _DB_PREFIX_ . 'tab` WHERE id_parent = 0 ORDER BY position';
    $all_tabs = Db::getInstance()->executeS($sql);
    
    $new_position = 1;
    foreach ($all_tabs as $tab_data) {
        if ($tab_data['class_name'] == 'AdminCustomModules') {
            continue; // Přeskočíme, nastavíme později
        }
        
        // Nastavíme novou pozici
        $sql = 'UPDATE `' . _DB_PREFIX_ . 'tab` SET position = ' . (int)$new_position . ' WHERE id_tab = ' . (int)$tab_data['id_tab'];
        Db::getInstance()->execute($sql);
        
        // Pokud jsme právě nastavili KONFIGURACE, další pozice bude pro Vlastní moduly
        if ($tab_data['class_name'] == 'AdminParentPreferences') {
            $new_position++; // Pozice pro Vlastní moduly
            $sql = 'UPDATE `' . _DB_PREFIX_ . 'tab` SET position = ' . (int)$new_position . ' WHERE id_tab = ' . (int)$custom_modules_tab_id;
            if (Db::getInstance()->execute($sql)) {
                echo "✅ Sekce 'Vlastní moduly' umístěna na pozici {$new_position}<br>";
            }
        }
        
        $new_position++;
    }
} else {
    echo "❌ Sekce KONFIGURACE nenalezena<br>";
}

// 5. Vyčištění cache
echo "<br><h2>5. Vyčištění cache</h2>";

try {
    // Smarty cache
    if (method_exists(Context::getContext()->smarty, 'clearAllCache')) {
        Context::getContext()->smarty->clearAllCache();
        echo "✅ Smarty cache vyčištěna<br>";
    }
    
    // Tab cache
    if (class_exists('Tab')) {
        Tab::resetStaticCache();
        echo "✅ Tab cache resetována<br>";
    }
    
    // XML soubory
    $xml_files = array(
        _PS_ROOT_DIR_ . '/config/xml/tabs.xml',
        _PS_ROOT_DIR_ . '/config/xml/modules.xml',
    );
    
    foreach ($xml_files as $file) {
        if (file_exists($file)) {
            unlink($file);
            echo "✅ Smazán: " . basename($file) . "<br>";
        }
    }
    
} catch (Exception $e) {
    echo "⚠️ Chyba při čištění cache: " . $e->getMessage() . "<br>";
}

// 6. Finální kontrola
echo "<br><h2>6. Finální kontrola</h2>";

echo "<h3>Nové pořadí top-level sekcí:</h3>";
$sql = 'SELECT class_name, position FROM `' . _DB_PREFIX_ . 'tab` WHERE id_parent = 0 ORDER BY position';
$final_tabs = Db::getInstance()->executeS($sql);

foreach ($final_tabs as $tab) {
    $highlight = '';
    $highlight_end = '';
    if ($tab['class_name'] == 'AdminParentPreferences') {
        $highlight = '<strong style="color: blue;">';
        $highlight_end = '</strong> ← KONFIGURACE';
    } elseif ($tab['class_name'] == 'AdminCustomModules') {
        $highlight = '<strong style="color: green;">';
        $highlight_end = '</strong> ← VLASTNÍ MODULY';
    }
    echo "{$highlight}Pozice {$tab['position']}: {$tab['class_name']}{$highlight_end}<br>";
}

// Kontrola modulu PriceInquiry
$pi_tab_id = Tab::getIdFromClassName('AdminPriceInquiry');
if ($pi_tab_id) {
    $pi_tab = new Tab($pi_tab_id);
    if ($pi_tab->id_parent == $custom_modules_tab_id) {
        echo "<br>🎉 <strong style='color: green;'>ÚSPĚCH!</strong> Modul 'Cena na dotaz' je správně umístěn v sekci 'Vlastní moduly'<br>";
    } else {
        echo "<br>❌ Modul není správně umístěn<br>";
    }
} else {
    echo "<br>❌ Tab AdminPriceInquiry nebyl vytvořen<br>";
}

echo "<hr>";
echo "<h2>✅ Oprava dokončena!</h2>";
echo "<p><strong>Doporučené kroky:</strong></p>";
echo "<ol>";
echo "<li>Odhlaste se z administrace</li>";
echo "<li>Zavřete prohlížeč nebo vyčistěte cookies</li>";
echo "<li>Znovu se přihlaste do administrace</li>";
echo "<li>Zkontrolujte menu - sekce 'Vlastní moduly' by měla být za 'KONFIGURACE'</li>";
echo "<li>Klikněte na 'Cena na dotaz' - mělo by vás přesměrovat na konfiguraci modulu</li>";
echo "</ol>";

echo "<p><em>Poznámka: Pokud se změny neprojeví okamžitě, počkejte několik minut nebo restartujte webový server.</em></p>";
?>
