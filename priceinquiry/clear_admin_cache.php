<?php
/**
 * <PERSON><PERSON><PERSON>ištění cache administrace
 * Pomáhá vyřešit problémy s nekonzistentním zobrazováním menu
 * 
 * Spustit v prohlížeči: /modules/priceinquiry/clear_admin_cache.php
 */

// Načtení PrestaShop prostředí
$prestashop_path = dirname(dirname(dirname(__FILE__)));
require_once($prestashop_path . '/config/config.inc.php');
require_once($prestashop_path . '/init.php');

echo "<h1>Vyčištění cache administrace</h1>";
echo "<p>Řeší problémy s nekonzistentním zobrazováním menu</p>";
echo "<hr>";

$cleared = array();
$errors = array();

// 1. Vyčištění Smarty cache
echo "<h2>1. Vyčištění Smarty cache</h2>";
try {
    if (method_exists(Context::getContext()->smarty, 'clearAllCache')) {
        Context::getContext()->smarty->clearAllCache();
        $cleared[] = "Smarty cache";
        echo "✅ Smarty cache vyčištěna<br>";
    }
} catch (Exception $e) {
    $errors[] = "Smarty cache: " . $e->getMessage();
    echo "❌ Chyba při čištění Smarty cache: " . $e->getMessage() . "<br>";
}

// 2. Vyčištění cache složek
echo "<br><h2>2. Vyčištění cache složek</h2>";

$cache_dirs = array(
    _PS_CACHE_DIR_ . 'smarty/cache/',
    _PS_CACHE_DIR_ . 'smarty/compile/',
    _PS_CACHE_DIR_ . 'tcpdf/',
    _PS_CACHE_DIR_ . 'cachefs/',
);

foreach ($cache_dirs as $dir) {
    if (is_dir($dir)) {
        try {
            $files = glob($dir . '*');
            $count = 0;
            foreach ($files as $file) {
                if (is_file($file)) {
                    unlink($file);
                    $count++;
                } elseif (is_dir($file) && basename($file) != '.' && basename($file) != '..') {
                    // Rekurzivní mazání složek
                    $subfiles = glob($file . '/*');
                    foreach ($subfiles as $subfile) {
                        if (is_file($subfile)) {
                            unlink($subfile);
                            $count++;
                        }
                    }
                    if (count(glob($file . '/*')) == 0) {
                        rmdir($file);
                    }
                }
            }
            echo "✅ Vyčištěno: {$dir} ({$count} souborů)<br>";
            $cleared[] = basename($dir) . " ({$count} souborů)";
        } catch (Exception $e) {
            $errors[] = basename($dir) . ": " . $e->getMessage();
            echo "❌ Chyba při čištění {$dir}: " . $e->getMessage() . "<br>";
        }
    } else {
        echo "⚠️ Složka neexistuje: {$dir}<br>";
    }
}

// 3. Vyčištění XML cache
echo "<br><h2>3. Vyčištění XML cache</h2>";
$xml_files = array(
    _PS_ROOT_DIR_ . '/config/xml/tabs.xml',
    _PS_ROOT_DIR_ . '/config/xml/modules.xml',
);

foreach ($xml_files as $file) {
    if (file_exists($file)) {
        try {
            unlink($file);
            echo "✅ Smazán: " . basename($file) . "<br>";
            $cleared[] = basename($file);
        } catch (Exception $e) {
            $errors[] = basename($file) . ": " . $e->getMessage();
            echo "❌ Chyba při mazání {$file}: " . $e->getMessage() . "<br>";
        }
    } else {
        echo "ℹ️ Soubor neexistuje: " . basename($file) . "<br>";
    }
}

// 4. Regenerace tabů
echo "<br><h2>4. Regenerace tabů</h2>";
try {
    // Vynutíme regeneraci tabů
    if (class_exists('Tab')) {
        Tab::resetStaticCache();
        echo "✅ Cache tabů resetována<br>";
        $cleared[] = "Cache tabů";
    }
} catch (Exception $e) {
    $errors[] = "Regenerace tabů: " . $e->getMessage();
    echo "❌ Chyba při regeneraci tabů: " . $e->getMessage() . "<br>";
}

// 5. Vyčištění opcache (pokud je aktivní)
echo "<br><h2>5. Vyčištění OPcache</h2>";
if (function_exists('opcache_reset')) {
    try {
        opcache_reset();
        echo "✅ OPcache vyčištěna<br>";
        $cleared[] = "OPcache";
    } catch (Exception $e) {
        $errors[] = "OPcache: " . $e->getMessage();
        echo "❌ Chyba při čištění OPcache: " . $e->getMessage() . "<br>";
    }
} else {
    echo "ℹ️ OPcache není dostupná<br>";
}

echo "<hr>";

// Shrnutí
echo "<h2>📋 Shrnutí</h2>";

if (!empty($cleared)) {
    echo "<h3 style='color: green;'>✅ Úspěšně vyčištěno:</h3>";
    echo "<ul>";
    foreach ($cleared as $item) {
        echo "<li>{$item}</li>";
    }
    echo "</ul>";
}

if (!empty($errors)) {
    echo "<h3 style='color: red;'>❌ Chyby:</h3>";
    echo "<ul>";
    foreach ($errors as $error) {
        echo "<li>{$error}</li>";
    }
    echo "</ul>";
}

echo "<hr>";
echo "<h2>🎯 Doporučené kroky</h2>";
echo "<ol>";
echo "<li><strong>Odhlaste se</strong> z administrace</li>";
echo "<li><strong>Zavřete prohlížeč</strong> (nebo vyčistěte cookies)</li>";
echo "<li><strong>Znovu se přihlaste</strong> do administrace</li>";
echo "<li><strong>Zkontrolujte menu</strong> - sekce 'Vlastní moduly' by se měla zobrazovat konzistentně</li>";
echo "</ol>";

echo "<p><em>Poznámka: Vyčištění cache může trvat několik minut, než se projeví ve všech částech administrace.</em></p>";
?>
