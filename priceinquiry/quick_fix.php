<?php
/**
 * Rychlá oprava - přidání modulu PriceInquiry do sekce "Vlastní moduly"
 * 
 * Spustit v prohlížeči: /modules/priceinquiry/quick_fix.php
 */

// Načtení PrestaShop prostředí
$prestashop_path = dirname(dirname(dirname(__FILE__)));
require_once($prestashop_path . '/config/config.inc.php');
require_once($prestashop_path . '/init.php');

echo "<h1>Rychlá oprava - PriceInquiry do Vlastní moduly</h1>";
echo "<hr>";

// 1. Najdeme ID sekce "Vlastní moduly"
$custom_modules_tab_id = Tab::getIdFromClassName('AdminCustomModules');

if (!$custom_modules_tab_id) {
    echo "❌ Sekce 'Vlastní moduly' neexistuje!<br>";
    echo "Nejdříve musíte vytvořit sekci 'Vlastní moduly'.<br>";
    exit;
}

echo "✅ Sekce 'Vlastní moduly' nalezena (ID: {$custom_modules_tab_id})<br>";

// 2. Zkontrolujeme, zda tab AdminPriceInquiry existuje
$priceinquiry_tab_id = Tab::getIdFromClassName('AdminPriceInquiry');

if ($priceinquiry_tab_id) {
    echo "✅ Tab 'AdminPriceInquiry' existuje (ID: {$priceinquiry_tab_id})<br>";
    
    // Přesuneme ho do vlastní sekce
    $sql = 'UPDATE `' . _DB_PREFIX_ . 'tab` 
            SET id_parent = ' . (int)$custom_modules_tab_id . ' 
            WHERE id_tab = ' . (int)$priceinquiry_tab_id;
    
    if (Db::getInstance()->execute($sql)) {
        echo "✅ Tab přesunut do sekce 'Vlastní moduly'<br>";
    } else {
        echo "❌ Chyba při přesunu tabu<br>";
    }
} else {
    echo "⚠️ Tab 'AdminPriceInquiry' neexistuje, vytvářím...<br>";
    
    // Vytvoříme nový tab
    $tab = new Tab();
    $tab->active = 1;
    $tab->class_name = 'AdminPriceInquiry';
    $tab->name = [];
    foreach (Language::getLanguages(true) as $lang) {
        $tab->name[$lang['id_lang']] = 'Cena na dotaz';
    }
    $tab->id_parent = $custom_modules_tab_id;
    $tab->module = 'priceinquiry';
    
    if ($tab->add()) {
        echo "✅ Tab 'AdminPriceInquiry' vytvořen v sekci 'Vlastní moduly'<br>";
    } else {
        echo "❌ Chyba při vytváření tabu<br>";
    }
}

// 3. Opravíme pozici sekce "Vlastní moduly" (umístíme za KONFIGURACE)
echo "<br><h2>Oprava pozice sekce 'Vlastní moduly'</h2>";

$config_tab_id = Tab::getIdFromClassName('AdminParentPreferences');
if ($config_tab_id) {
    $config_tab = new Tab($config_tab_id);
    $target_position = $config_tab->position + 1;
    
    echo "🎯 Cílová pozice: {$target_position} (za KONFIGURACE)<br>";
    
    // Aktualizujeme pozici
    $sql = 'UPDATE `' . _DB_PREFIX_ . 'tab` 
            SET position = ' . (int)$target_position . ' 
            WHERE id_tab = ' . (int)$custom_modules_tab_id;
    
    if (Db::getInstance()->execute($sql)) {
        echo "✅ Pozice sekce 'Vlastní moduly' opravena<br>";
    } else {
        echo "❌ Chyba při opravě pozice<br>";
    }
} else {
    echo "❌ Sekce KONFIGURACE nenalezena<br>";
}

echo "<hr>";
echo "<h2>🎉 Hotovo!</h2>";
echo "<p>Modul 'Cena na dotaz' by se nyní měl zobrazovat v sekci 'Vlastní moduly'.</p>";
echo "<p><strong>Doporučení:</strong> Obnovte cache administrace nebo se odhlaste a znovu přihlaste.</p>";

// Zobrazíme finální stav
echo "<br><h3>Finální kontrola:</h3>";
$priceinquiry_tab_id = Tab::getIdFromClassName('AdminPriceInquiry');
if ($priceinquiry_tab_id) {
    $pi_tab = new Tab($priceinquiry_tab_id);
    if ($pi_tab->id_parent == $custom_modules_tab_id) {
        echo "✅ <strong style='color: green;'>ÚSPĚCH!</strong> Modul je v sekci 'Vlastní moduly'<br>";
    } else {
        echo "❌ Modul stále není v sekci 'Vlastní moduly'<br>";
    }
}
?>
