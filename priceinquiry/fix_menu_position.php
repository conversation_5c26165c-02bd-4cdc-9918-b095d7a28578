<?php
/**
 * Oprava pozice sekce "Vlastní moduly" - umístění za KONFIGURACE
 * Řeší problém se "zhroucením" sekce při navigaci
 * 
 * Spustit v prohlížeči: /modules/priceinquiry/fix_menu_position.php
 */

// Načtení PrestaShop prostředí
$prestashop_path = dirname(dirname(dirname(__FILE__)));
require_once($prestashop_path . '/config/config.inc.php');
require_once($prestashop_path . '/init.php');

echo "<h1>Oprava pozice sekce 'Vlastní moduly'</h1>";
echo "<p>Cíl: Umístit sekci 'Vlastní moduly' hned za 'KONFIGURACE'</p>";
echo "<hr>";

// 1. Najdeme všechny potřebné taby
echo "<h2>1. Analýza současného stavu</h2>";

$config_tab_id = Tab::getIdFromClassName('AdminParentPreferences'); // KONFIGURACE
$custom_modules_tab_id = Tab::getIdFromClassName('AdminCustomModules'); // VLASTNÍ MODULY

if (!$config_tab_id) {
    echo "❌ Sekce 'KONFIGURACE' nenalezena!<br>";
    exit;
}

if (!$custom_modules_tab_id) {
    echo "❌ Sekce 'Vlastní moduly' nenalezena!<br>";
    exit;
}

$config_tab = new Tab($config_tab_id);
$custom_tab = new Tab($custom_modules_tab_id);

echo "✅ KONFIGURACE nalezena - pozice: {$config_tab->position}<br>";
echo "✅ Vlastní moduly nalezeny - pozice: {$custom_tab->position}<br>";

$target_position = $config_tab->position + 1;
echo "🎯 Cílová pozice pro 'Vlastní moduly': {$target_position}<br>";

// 2. Zobrazíme současné pořadí top-level tabů
echo "<br><h3>Současné pořadí top-level sekcí:</h3>";
$sql = 'SELECT id_tab, class_name, position FROM `' . _DB_PREFIX_ . 'tab` WHERE id_parent = 0 ORDER BY position';
$tabs = Db::getInstance()->executeS($sql);

foreach ($tabs as $tab) {
    $highlight = '';
    $highlight_end = '';
    if ($tab['class_name'] == 'AdminParentPreferences') {
        $highlight = '<strong style="color: blue;">';
        $highlight_end = '</strong> ← KONFIGURACE';
    } elseif ($tab['class_name'] == 'AdminCustomModules') {
        $highlight = '<strong style="color: red;">';
        $highlight_end = '</strong> ← VLASTNÍ MODULY (současná pozice)';
    }
    echo "{$highlight}Pozice {$tab['position']}: {$tab['class_name']}{$highlight_end}<br>";
}

echo "<hr>";

// 3. Provedeme opravu pozice
echo "<h2>2. Oprava pozice</h2>";

if ($custom_tab->position == $target_position) {
    echo "✅ Sekce 'Vlastní moduly' je už na správné pozici!<br>";
} else {
    echo "🔧 Přesouvám sekci 'Vlastní moduly' na pozici {$target_position}...<br>";
    
    // Krok 1: Dočasně nastavíme vysokou pozici pro náš tab
    $temp_position = 9999;
    $sql = 'UPDATE `' . _DB_PREFIX_ . 'tab` 
            SET position = ' . (int)$temp_position . ' 
            WHERE id_tab = ' . (int)$custom_modules_tab_id;
    
    if (Db::getInstance()->execute($sql)) {
        echo "✅ Krok 1: Tab dočasně přesunut na pozici {$temp_position}<br>";
    } else {
        echo "❌ Chyba v kroku 1<br>";
        exit;
    }
    
    // Krok 2: Posuneme všechny taby s pozicí >= target_position o 1 doprava
    $sql = 'UPDATE `' . _DB_PREFIX_ . 'tab` 
            SET position = position + 1 
            WHERE position >= ' . (int)$target_position . ' AND id_parent = 0 
            AND position < ' . (int)$temp_position;
    
    if (Db::getInstance()->execute($sql)) {
        echo "✅ Krok 2: Ostatní taby posunuty doprava<br>";
    } else {
        echo "❌ Chyba v kroku 2<br>";
        exit;
    }
    
    // Krok 3: Nastavíme správnou pozici pro náš tab
    $sql = 'UPDATE `' . _DB_PREFIX_ . 'tab` 
            SET position = ' . (int)$target_position . ' 
            WHERE id_tab = ' . (int)$custom_modules_tab_id;
    
    if (Db::getInstance()->execute($sql)) {
        echo "✅ Krok 3: Sekce 'Vlastní moduly' umístěna na pozici {$target_position}<br>";
    } else {
        echo "❌ Chyba v kroku 3<br>";
        exit;
    }
}

echo "<hr>";

// 4. Zobrazíme nové pořadí
echo "<h2>3. Nové pořadí sekcí</h2>";
$sql = 'SELECT id_tab, class_name, position FROM `' . _DB_PREFIX_ . 'tab` WHERE id_parent = 0 ORDER BY position';
$tabs = Db::getInstance()->executeS($sql);

foreach ($tabs as $tab) {
    $highlight = '';
    $highlight_end = '';
    if ($tab['class_name'] == 'AdminParentPreferences') {
        $highlight = '<strong style="color: blue;">';
        $highlight_end = '</strong> ← KONFIGURACE';
    } elseif ($tab['class_name'] == 'AdminCustomModules') {
        $highlight = '<strong style="color: green;">';
        $highlight_end = '</strong> ← VLASTNÍ MODULY (nová pozice)';
    }
    echo "{$highlight}Pozice {$tab['position']}: {$tab['class_name']}{$highlight_end}<br>";
}

// 5. Ověříme, že modul PriceInquiry je v sekci Vlastní moduly
echo "<hr>";
echo "<h2>4. Kontrola modulu PriceInquiry</h2>";

$priceinquiry_tab_id = Tab::getIdFromClassName('AdminPriceInquiry');
if ($priceinquiry_tab_id) {
    $pi_tab = new Tab($priceinquiry_tab_id);
    if ($pi_tab->id_parent == $custom_modules_tab_id) {
        echo "✅ Modul 'Cena na dotaz' je správně umístěn v sekci 'Vlastní moduly'<br>";
    } else {
        echo "⚠️ Modul 'Cena na dotaz' není v sekci 'Vlastní moduly', opravuji...<br>";
        
        $sql = 'UPDATE `' . _DB_PREFIX_ . 'tab` 
                SET id_parent = ' . (int)$custom_modules_tab_id . ' 
                WHERE id_tab = ' . (int)$priceinquiry_tab_id;
        
        if (Db::getInstance()->execute($sql)) {
            echo "✅ Modul přesunut do sekce 'Vlastní moduly'<br>";
        } else {
            echo "❌ Chyba při přesunu modulu<br>";
        }
    }
} else {
    echo "❌ Tab 'AdminPriceInquiry' neexistuje<br>";
}

echo "<hr>";
echo "<h2>🎉 Oprava dokončena!</h2>";
echo "<p><strong>Očekávané pořadí menu:</strong></p>";
echo "<ol>";
echo "<li>Nástěnka</li>";
echo "<li>OBCHOD</li>";
echo "<li>ROZŠÍŘENÍ</li>";
echo "<li>KONFIGURACE</li>";
echo "<li><strong style='color: green;'>VLASTNÍ MODULY</strong> ← Nově umístěno</li>";
echo "<li>ADVANCE SEO</li>";
echo "</ol>";

echo "<p><strong>Doporučení:</strong></p>";
echo "<ul>";
echo "<li>Obnovte cache administrace</li>";
echo "<li>Odhlaste se a znovu přihlaste do administrace</li>";
echo "<li>Zkontrolujte, že se sekce 'Vlastní moduly' zobrazuje konzistentně</li>";
echo "</ul>";

echo "<p><em>Poznámka: Pokud se problém se 'zhroucením' sekce stále vyskytuje, může být způsoben cache nebo specifickým chováním tématu administrace.</em></p>";
?>
