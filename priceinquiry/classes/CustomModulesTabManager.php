<?php
/**
 * Univerzální třída pro správu tabů vlastních modulů
 * Umístění: hned po sekci "KONFIGURACE", nad "ADVANCE SEO"
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @license MIT
 */

class CustomModulesTabManager
{
    const CUSTOM_MODULES_CLASS_NAME = 'AdminCustomModules';
    const CUSTOM_MODULES_ICON = 'extension';
    
    /**
     * Vytvoří tab pro modul v sekci "Vlastní moduly"
     * 
     * @param string $class_name Název třídy controlleru (např. 'AdminKatalogy')
     * @param string $tab_name Zobrazovaný název tabu (např. 'Katalogy')
     * @param string $module_name Název modulu (např. 'katalogy')
     * @return bool
     */
    public static function createModuleTab($class_name, $tab_name, $module_name)
    {
        // Nejdříve vytvoř nebo najdi parent tab "Vlastní moduly"
        $parent_tab_id = self::createOrGetCustomModulesTab();
        
        // Zkontroluj, zda tab už neexistuje
        $existing_id = Tab::getIdFromClassName($class_name);
        if ($existing_id) {
            return true; // Tab už existuje
        }
        
        // Vytvoř nový tab
        $tab = new Tab();
        $tab->active = 1;
        $tab->class_name = $class_name;
        $tab->name = [];
        foreach (Language::getLanguages(true) as $lang) {
            $tab->name[$lang['id_lang']] = $tab_name;
        }
        $tab->id_parent = $parent_tab_id;
        $tab->module = $module_name;
        
        return $tab->add();
    }
    
    /**
     * Odstraní tab modulu a případně vyčistí prázdnou sekci "Vlastní moduly"
     * 
     * @param string $class_name Název třídy controlleru
     * @return bool
     */
    public static function removeModuleTab($class_name)
    {
        $tab_id = Tab::getIdFromClassName($class_name);
        if (!$tab_id) {
            return true; // Tab neexistuje
        }
        
        $tab = new Tab($tab_id);
        $result = $tab->delete();
        
        // Zkontroluj, zda je parent tab "Vlastní moduly" prázdný a případně ho smaž
        self::cleanupCustomModulesTabIfEmpty();
        
        return $result;
    }
    
    /**
     * Vytvoří nebo najde parent tab "Vlastní moduly"
     * Umístí ho hned po "KONFIGURACE", nad "ADVANCE SEO"
     * 
     * @return int ID parent tabu
     */
    public static function createOrGetCustomModulesTab()
    {
        // Zkus najít existující tab
        $existing_tab_id = Tab::getIdFromClassName(self::CUSTOM_MODULES_CLASS_NAME);
        if ($existing_tab_id) {
            return $existing_tab_id;
        }
        
        // Vytvoř nový parent tab
        $parent_tab = new Tab();
        $parent_tab->active = 1;
        $parent_tab->class_name = self::CUSTOM_MODULES_CLASS_NAME;
        $parent_tab->name = [];
        foreach (Language::getLanguages(true) as $lang) {
            $parent_tab->name[$lang['id_lang']] = 'Vlastní moduly';
        }
        
        // Umísti jako top-level tab (id_parent = 0) s pozicí za KONFIGURACE
        $parent_tab->id_parent = 0;
        $parent_tab->icon = self::CUSTOM_MODULES_ICON;
        
        // Najdi pozici za "KONFIGURACE" (AdminParentPreferences)
        $config_position = self::getTabPosition('AdminParentPreferences');
        if ($config_position === false || $config_position === null) {
            // Fallback - umísti na konec
            $parent_tab->position = 999;
        } else {
            $target_position = $config_position + 1;
            $parent_tab->position = $target_position;
        }
        
        // Přidej tab
        if ($parent_tab->add()) {
            // Po úspěšném přidání oprav pozice
            if ($config_position !== false && $config_position !== null) {
                self::fixTabPosition($parent_tab->id, $config_position + 1);
            }
            return $parent_tab->id;
        }
        
        // Fallback - pokud se nepodaří vytvořit vlastní sekci, použij Katalog
        return (int)Tab::getIdFromClassName('AdminCatalog');
    }
    
    /**
     * Získá pozici tabu podle class_name
     */
    private static function getTabPosition($class_name)
    {
        $sql = 'SELECT position FROM `' . _DB_PREFIX_ . 'tab` WHERE class_name = "' . pSQL($class_name) . '" AND id_parent = 0';
        $position = Db::getInstance()->getValue($sql);
        return $position ? (int)$position : false;
    }
    
    /**
     * Opraví pozici tabu na správné místo za KONFIGURACE
     */
    private static function fixTabPosition($tab_id, $target_position)
    {
        // Nejdříve přesuň všechny taby s pozicí >= target_position o 1 doprava (kromě našeho)
        $sql = 'UPDATE `' . _DB_PREFIX_ . 'tab` 
                SET position = position + 1 
                WHERE position >= ' . (int)$target_position . ' AND id_parent = 0 
                AND id_tab != ' . (int)$tab_id;
        
        Db::getInstance()->execute($sql);
        
        // Pak nastav správnou pozici pro náš tab
        $sql = 'UPDATE `' . _DB_PREFIX_ . 'tab` 
                SET position = ' . (int)$target_position . ' 
                WHERE id_tab = ' . (int)$tab_id;
        
        return Db::getInstance()->execute($sql);
    }
    
    /**
     * Smaže parent tab "Vlastní moduly" pokud je prázdný
     */
    public static function cleanupCustomModulesTabIfEmpty()
    {
        $custom_modules_tab_id = Tab::getIdFromClassName(self::CUSTOM_MODULES_CLASS_NAME);
        if (!$custom_modules_tab_id) {
            return; // Tab neexistuje
        }
        
        // Zkontroluj, zda má nějaké child taby
        $sql = 'SELECT COUNT(*) FROM `' . _DB_PREFIX_ . 'tab` WHERE `id_parent` = ' . (int)$custom_modules_tab_id . ' AND `active` = 1';
        $child_count = (int)Db::getInstance()->getValue($sql);
        
        // Pokud nemá žádné aktivní child taby, smaž ho
        if ($child_count === 0) {
            $parent_tab = new Tab($custom_modules_tab_id);
            $parent_tab->delete();
        }
    }
    
    /**
     * Zkontroluje, zda sekce "Vlastní moduly" existuje
     * 
     * @return bool
     */
    public static function customModulesSectionExists()
    {
        return (bool)Tab::getIdFromClassName(self::CUSTOM_MODULES_CLASS_NAME);
    }
    
    /**
     * Přesune existující tab modulu do sekce "Vlastní moduly"
     * 
     * @param string $class_name Název třídy controlleru
     * @return bool
     */
    public static function moveTabToCustomModules($class_name)
    {
        $tab_id = Tab::getIdFromClassName($class_name);
        if (!$tab_id) {
            return false; // Tab neexistuje
        }
        
        $parent_tab_id = self::createOrGetCustomModulesTab();
        
        $tab = new Tab($tab_id);
        $tab->id_parent = $parent_tab_id;
        
        return $tab->update();
    }
}
