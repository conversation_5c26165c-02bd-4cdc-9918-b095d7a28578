# <PERSON><PERSON><PERSON> "Cena na dotaz" pro PrestaShop

## Popis
Modul zobrazuje "Cena na dotaz" místo ceny 0 Kč a umožňuje zákazníkům poslat dotaz na cenu produktu.

## Verze 8.1.0
- ✅ Integrace s vlastní sekcí modulů v administraci
- ✅ Úklid kódu - odstranění debug logů a testovacích souborů
- ✅ Nový admin controller pro správu dotazů
- ✅ Automatické umístění v sekci "Vlastní moduly"

## Funkce
- Zobrazení "Cena na dotaz" u produktů s nulovou cenou
- Formulář pro dotaz na cenu s možností zadání množství
- E-mailové notifikace pro administrátora i zákazníka
- Správa dotazů v administraci
- Podpora pro varianty produktů
- Responzivní design

## Instalace
1. Nahrajte modul do složky `/modules/priceinquiry/`
2. Nainstalujte modul v administraci
3. Modul se automaticky zobrazí v sekci "Vlastní moduly"

## Konfigurace
V administraci přejděte do sekce **Vlastní moduly > Cena na dotaz** a nastavte:
- Zapnutí/vypnutí modulu
- E-mailovou adresu pro příjem dotazů
- Text tlačítka a text místo ceny
- Nastavení e-mailových notifikací

## Správa dotazů
V administraci můžete:
- Prohlížet všechny dotazy na cenu
- Označovat dotazy jako vyřešené
- Mazat nepotřebné dotazy
- Zobrazit detail každého dotazu

## Technické informace
- **Kompatibilita:** PrestaShop 1.7+ a 8.x
- **Autor:** Miroslav Urbánek
- **Verze:** 8.1.0
- **Licence:** Proprietární

## Integrace s vlastní sekcí modulů
Modul používá `CustomModulesTabManager` pro automatické vytvoření sekce "Vlastní moduly" v administraci. Tato sekce se zobrazuje hned po sekci "KONFIGURACE" a před "ADVANCE SEO".

### Automatické funkce:
- ✅ Vytvoření sekce při instalaci prvního custom modulu
- ✅ Odstranění prázdné sekce při odinstalaci posledního modulu
- ✅ Správné umístění v menu administrace
- ✅ Konzistentní chování napříč všemi custom moduly

## Struktura souborů
```
priceinquiry/
├── classes/
│   ├── CustomModulesTabManager.php    # Správa vlastní sekce modulů
│   └── index.php
├── controllers/
│   ├── admin/
│   │   ├── AdminPriceInquiryController.php  # Admin controller
│   │   └── index.php
│   ├── front/
│   │   ├── inquiry.php                # Frontend controller
│   │   └── index.php
│   └── index.php
├── mails/                             # E-mailové šablony
├── sql/                               # SQL skripty
├── translations/                      # Překlady
├── views/                             # Šablony a assety
├── config.xml                         # Konfigurace modulu
├── priceinquiry.php                   # Hlavní soubor modulu
└── README.md                          # Tento soubor
```

## Podpora
Pro technickou podporu kontaktujte autora modulu.
