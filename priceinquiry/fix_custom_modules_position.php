<?php
/**
 * Script pro opravu pozice sekce "Vlastní moduly" a přidání modulu PriceInquiry
 * 
 * Spustit v prohlížeči: /modules/priceinquiry/fix_custom_modules_position.php
 */

// Načtení PrestaShop prostředí
$prestashop_path = dirname(dirname(dirname(__FILE__)));
require_once($prestashop_path . '/config/config.inc.php');
require_once($prestashop_path . '/init.php');

// Načtení CustomModulesTabManager
require_once(dirname(__FILE__) . '/classes/CustomModulesTabManager.php');

echo "<h1>Oprava pozice sekce 'Vlastní moduly' a přidání modulu PriceInquiry</h1>";
echo "<hr>";

// 1. Zkontrolujeme současný stav
echo "<h2>1. Kontrola současného stavu</h2>";

$custom_modules_tab_id = Tab::getIdFromClassName('AdminCustomModules');
if ($custom_modules_tab_id) {
    $custom_tab = new Tab($custom_modules_tab_id);
    echo "✅ Sekce 'Vlastní moduly' existuje (ID: {$custom_modules_tab_id})<br>";
    echo "📍 Současná pozice: {$custom_tab->position}<br>";
} else {
    echo "❌ Sekce 'Vlastní moduly' neexistuje<br>";
}

$priceinquiry_tab_id = Tab::getIdFromClassName('AdminPriceInquiry');
if ($priceinquiry_tab_id) {
    $pi_tab = new Tab($priceinquiry_tab_id);
    echo "✅ Tab 'AdminPriceInquiry' existuje (ID: {$priceinquiry_tab_id})<br>";
    echo "📍 Parent ID: {$pi_tab->id_parent}<br>";
} else {
    echo "❌ Tab 'AdminPriceInquiry' neexistuje<br>";
}

// Najdeme pozici KONFIGURACE
$config_tab_id = Tab::getIdFromClassName('AdminParentPreferences');
if ($config_tab_id) {
    $config_tab = new Tab($config_tab_id);
    echo "✅ Sekce 'KONFIGURACE' nalezena (ID: {$config_tab_id})<br>";
    echo "📍 Pozice KONFIGURACE: {$config_tab->position}<br>";
} else {
    echo "❌ Sekce 'KONFIGURACE' nenalezena<br>";
}

echo "<hr>";

// 2. Opravíme pozici sekce "Vlastní moduly"
echo "<h2>2. Oprava pozice sekce 'Vlastní moduly'</h2>";

if ($custom_modules_tab_id && $config_tab_id) {
    $target_position = $config_tab->position + 1;
    
    echo "🎯 Cílová pozice pro 'Vlastní moduly': {$target_position}<br>";
    
    // Přesuneme všechny taby s pozicí >= target_position o 1 doprava (kromě našeho)
    $sql = 'UPDATE `' . _DB_PREFIX_ . 'tab` 
            SET position = position + 1 
            WHERE position >= ' . (int)$target_position . ' AND id_parent = 0 
            AND id_tab != ' . (int)$custom_modules_tab_id;
    
    if (Db::getInstance()->execute($sql)) {
        echo "✅ Posunuly se taby s vyšší pozicí<br>";
    } else {
        echo "❌ Chyba při posouvání tabů<br>";
    }
    
    // Nastavíme správnou pozici pro náš tab
    $sql = 'UPDATE `' . _DB_PREFIX_ . 'tab` 
            SET position = ' . (int)$target_position . ' 
            WHERE id_tab = ' . (int)$custom_modules_tab_id;
    
    if (Db::getInstance()->execute($sql)) {
        echo "✅ Pozice sekce 'Vlastní moduly' opravena na: {$target_position}<br>";
    } else {
        echo "❌ Chyba při nastavování pozice<br>";
    }
} else {
    echo "❌ Nelze opravit pozici - chybí potřebné taby<br>";
}

echo "<hr>";

// 3. Přidáme/přesuneme modul PriceInquiry do vlastní sekce
echo "<h2>3. Přidání modulu PriceInquiry do sekce 'Vlastní moduly'</h2>";

if ($priceinquiry_tab_id && $custom_modules_tab_id) {
    // Tab už existuje, jen ho přesuneme
    echo "🔄 Přesouvám existující tab do vlastní sekce...<br>";
    
    $pi_tab = new Tab($priceinquiry_tab_id);
    $pi_tab->id_parent = $custom_modules_tab_id;
    
    if ($pi_tab->update()) {
        echo "✅ Tab 'AdminPriceInquiry' přesunut do sekce 'Vlastní moduly'<br>";
    } else {
        echo "❌ Chyba při přesunu tabu<br>";
    }
} elseif (!$priceinquiry_tab_id && $custom_modules_tab_id) {
    // Tab neexistuje, vytvoříme ho
    echo "🆕 Vytvářím nový tab v vlastní sekci...<br>";
    
    $result = CustomModulesTabManager::createModuleTab('AdminPriceInquiry', 'Cena na dotaz', 'priceinquiry');
    
    if ($result) {
        echo "✅ Tab 'AdminPriceInquiry' vytvořen v sekci 'Vlastní moduly'<br>";
    } else {
        echo "❌ Chyba při vytváření tabu<br>";
    }
} else {
    echo "❌ Nelze přidat modul - chybí sekce 'Vlastní moduly'<br>";
}

echo "<hr>";

// 4. Finální kontrola
echo "<h2>4. Finální kontrola</h2>";

// Znovu načteme data
$custom_modules_tab_id = Tab::getIdFromClassName('AdminCustomModules');
$priceinquiry_tab_id = Tab::getIdFromClassName('AdminPriceInquiry');

if ($custom_modules_tab_id) {
    $custom_tab = new Tab($custom_modules_tab_id);
    echo "✅ Sekce 'Vlastní moduly' - pozice: {$custom_tab->position}<br>";
}

if ($priceinquiry_tab_id) {
    $pi_tab = new Tab($priceinquiry_tab_id);
    echo "✅ Tab 'AdminPriceInquiry' - parent ID: {$pi_tab->id_parent}<br>";
    
    if ($pi_tab->id_parent == $custom_modules_tab_id) {
        echo "🎉 <strong>ÚSPĚCH!</strong> Modul je správně umístěn v sekci 'Vlastní moduly'<br>";
    } else {
        echo "⚠️ Modul není v sekci 'Vlastní moduly'<br>";
    }
}

// Zobrazíme všechny top-level taby pro kontrolu
echo "<br><h3>Přehled top-level tabů (pozice):</h3>";
$sql = 'SELECT class_name, position FROM `' . _DB_PREFIX_ . 'tab` WHERE id_parent = 0 ORDER BY position';
$tabs = Db::getInstance()->executeS($sql);

foreach ($tabs as $tab) {
    $highlight = ($tab['class_name'] == 'AdminCustomModules') ? ' <strong style="color: green;">' : '';
    $highlight_end = ($tab['class_name'] == 'AdminCustomModules') ? '</strong>' : '';
    echo "{$highlight}Pozice {$tab['position']}: {$tab['class_name']}{$highlight_end}<br>";
}

echo "<hr>";
echo "<h2>✅ Script dokončen</h2>";
echo "<p>Nyní obnovte cache administrace a zkontrolujte menu.</p>";
echo "<p><strong>Tip:</strong> Pokud se změny neprojeví okamžitě, odhlaste se a znovu přihlaste do administrace.</p>";
?>
