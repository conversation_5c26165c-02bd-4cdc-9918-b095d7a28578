/**
 * Enhanced drag & drop handling for Katalogy admin
 * Umístit do /modules/katalogy/views/js/
 * Znovu implementováno pro explicitní AJAX volání po drag & drop.
 */

$(document).ready(function() {
    // Enhanced sortable for drag & drop
    if ($('#table-katalogy tbody').length > 0) {
        console.log('Katalogy: Initializing enhanced sortable');
        
        $('#table-katalogy tbody').sortable({
            helper: 'clone',
            cursor: 'move',
            axis: 'y',
            opacity: 0.8,
            placeholder: 'ui-sortable-placeholder',
            start: function(event, ui) {
                ui.placeholder.height(ui.item.height());
                console.log('Drag started');
            },
            stop: function(event, ui) {
                console.log('Drag stopped - START');
                
                var positions = {};
                var token = $('input[name="token"]').val();
                
                $('#table-katalogy tbody tr').each(function(index, element) {
                    var id_match = $(element).attr('id');
                    if (id_match) {
                        // PrestaShop obvykle používá id_table_X pro ID řádku
                        // Extrahujeme pouze číslo ID
                        var id_katalog = id_match.split('_')[2]; // Předpokládáme formát tr_SHOPID_ID_POSITION
                        positions[index] = id_katalog;
                    }
                });
                
                console.log('New positions:', positions);
                
                // Send AJAX request
                $.ajax({
                    type: 'POST',
                    url: 'index.php?controller=AdminKatalogy&token=' + token + '&ajax=1&action=updatePositions',
                    data: {
                        katalog: positions // Odesíláme pole pozic, aby odpovídalo backendu
                    },
                    dataType: 'json',
                    success: function(data) {
                        console.log('Drag & drop success callback:', data);
                        console.log('Data hasError:', data.hasError, 'Data updated:', data.updated);
                        if (data.hasError === false || data.updated > 0) {
                            showSuccessMessage('Pořadí bylo úspěšně aktualizováno (' + (data.updated || 'neznámý počet') + ' položek)');
                            // Aktualizuj pozice v tabulce bez reload
                            updateTablePositions();
                        } else {
                            showErrorMessage('Chyba při aktualizaci pořadí: ' + (data.errors ? data.errors.join(', ') : 'Neznámá chyba'));
                            // location.reload(); // Dočasně zakomentováno pro diagnostiku
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Drag & drop error callback:', {xhr, status, error});
                        console.log('Response text:', xhr.responseText);
                        showErrorMessage('AJAX chyba při aktualizaci pořadí');
                        // location.reload(); // Dočasně zakomentováno pro diagnostiku
                    },
                    complete: function(xhr, status) {
                        console.log('Drag & drop complete callback. Status:', status, 'Response:', xhr.responseText);
                    }
                });
            }
        });
    }

    // Funkce pro aktualizaci pozic v tabulce bez reload
    function updateTablePositions() {
        console.log('Updating table positions visually...');
        $('#table-katalogy tbody tr').each(function(index, element) {
            // Najdi buňku s pozicí. Může mít třídu 'position' nebo být v určitém sloupci.
            // PrestaShop obvykle generuje buňku s třídou 'pointer' a 'dragHandle' pro pozici.
            var positionCell = $(element).find('td.pointer.dragHandle').first();
            console.log('Row ID:', $(element).attr('id'), 'Position cell found:', positionCell.length > 0);
            if (positionCell.length > 0) {
                var newPosition = index + 1;
                console.log('Updating position for row ', $(element).attr('id'), 'to', newPosition);
                // Aktualizuj text pozice
                // PrestaShop obvykle vkládá číslo pozice přímo do buňky nebo do spanu uvnitř.
                // Zde předpokládáme, že chceme aktualizovat text uvnitř buňky.
                positionCell.text(newPosition);
            }
        });
    }

    // Helper functions for messages
    function showSuccessMessage(message) {
        console.log('SUCCESS: ' + message);

        // Použij PrestaShop notifikace pokud jsou dostupné
        if (typeof $.growl !== 'undefined') {
            $.growl.notice({title: 'Úspěch', message: message});
        } else if (typeof jAlert !== 'undefined') {
            jAlert(message, 'Úspěch');
        } else {
            // Fallback vlastní notifikace
            showCustomMessage(message, 'success');
        }
    }

    function showErrorMessage(message) {
        console.log('ERROR: ' + message);

        // Použij PrestaShop notifikace pokud jsou dostupné
        if (typeof $.growl !== 'undefined') {
            $.growl.error({title: 'Chyba', message: message});
        } else if (typeof jAlert !== 'undefined') {
            jAlert(message, 'Chyba');
        } else {
            // Fallback vlastní notifikace
            showCustomMessage(message, 'error');
        }
    }

    function showCustomMessage(message, type) {
        var alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
        var messageDiv = $('<div class="katalogy-message ' + type + '">' + message + '</div>');

        $('body').append(messageDiv);

        setTimeout(function() {
            messageDiv.fadeOut(function() {
                messageDiv.remove();
            });
        }, 4000);
    }
    
    // Debug information
    console.log('Katalogy admin drag & drop initialized (explicit AJAX)');
    console.log('jQuery version:', $.fn.jquery);
    console.log('Table exists:', $('#table-katalogy').length > 0);
    console.log('Sortable available:', typeof $.fn.sortable !== 'undefined');
});