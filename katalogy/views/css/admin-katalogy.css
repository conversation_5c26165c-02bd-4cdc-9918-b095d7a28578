/**
 * CSS pro administraci modulu Katalogy - Drag & Drop styly
 */

/* Základní styly pro drag & drop */
#table-katalogy tbody tr {
    cursor: move;
    transition: background-color 0.2s ease;
}

#table-katalogy tbody tr:hover {
    background-color: #f8f9fa !important;
}

/* Placeholder pro drag & drop */
.ui-sortable-placeholder {
    background-color: #e3f2fd !important;
    border: 2px dashed #2196f3 !important;
    height: 40px !important;
    visibility: visible !important;
}

.ui-sortable-placeholder td {
    border: none !important;
    background: transparent !important;
}

/* Helper p<PERSON><PERSON> ta<PERSON> */
.ui-sortable-helper {
    background-color: #fff3e0 !important;
    border: 2px solid #ff9800 !important;
    opacity: 0.8 !important;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2) !important;
}

/* <PERSON><PERSON><PERSON> sloupec */
#table-katalogy .position {
    width: 80px;
    text-align: center;
    font-weight: bold;
    color: #666;
}

/* <PERSON><PERSON><PERSON> pro pozice */
.position-actions {
    white-space: nowrap;
}

.position-actions a {
    display: inline-block;
    margin: 0 2px;
    padding: 2px 4px;
    text-decoration: none;
    border-radius: 3px;
    font-size: 12px;
    line-height: 1;
}

.position-actions a:hover {
    background-color: #f0f0f0;
}

.position-actions .icon-arrow-up,
.position-actions .icon-arrow-down {
    color: #666;
}

.position-actions .icon-arrow-up:hover {
    color: #28a745;
}

.position-actions .icon-arrow-down:hover {
    color: #dc3545;
}

/* Drag handle ikona */
.drag-handle {
    cursor: move;
    color: #999;
    font-size: 16px;
    padding: 5px;
}

.drag-handle:hover {
    color: #333;
}

/* Zprávy o úspěchu/chybě */
.katalogy-message {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 10px 15px;
    border-radius: 4px;
    z-index: 9999;
    font-weight: bold;
    min-width: 250px;
    text-align: center;
}

.katalogy-message.success {
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.katalogy-message.error {
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

/* Animace pro zprávy */
.katalogy-message {
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Responsive úpravy */
@media (max-width: 768px) {
    .position-actions {
        font-size: 11px;
    }
    
    .drag-handle {
        font-size: 14px;
        padding: 3px;
    }
    
    .katalogy-message {
        right: 10px;
        left: 10px;
        min-width: auto;
    }
}

/* Debug informace */
.katalogy-debug {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    padding: 10px;
    margin: 10px 0;
    border-radius: 4px;
    font-family: monospace;
    font-size: 12px;
    color: #666;
}

/* Zvýraznění aktivního řádku při drag */
#table-katalogy tbody tr.ui-sortable-helper {
    background-color: #fff3e0 !important;
}

#table-katalogy tbody tr.ui-sortable-helper td {
    background-color: transparent !important;
}

/* Lepší vzhled tabulky */
#table-katalogy {
    border-collapse: separate;
    border-spacing: 0;
}

#table-katalogy tbody tr {
    border-bottom: 1px solid #dee2e6;
}

#table-katalogy tbody tr:last-child {
    border-bottom: none;
}

/* Pozice číslo */
.position-number {
    display: inline-block;
    background: #e9ecef;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: bold;
    color: #495057;
    margin-right: 5px;
}

/* Loading indikátor */
.katalogy-loading {
    position: relative;
}

.katalogy-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    z-index: 1000;
}

.katalogy-loading::before {
    content: 'Ukládám...';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1001;
    background: #007bff;
    color: white;
    padding: 5px 10px;
    border-radius: 3px;
    font-size: 12px;
}
