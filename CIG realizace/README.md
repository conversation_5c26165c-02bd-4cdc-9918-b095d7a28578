# CIG Realizace - Systém správy objednávek

Kompletní webový systém pro správu objednávek s kalendářní vizualizací, workflow managementem a pokročilými funkcemi pro tým CIG Image.

## 🎯 Přehled projektu

CIG Realizace je moderní webová aplikace navržená pro efektivní správu objednávek, jej<PERSON> stavů a plánování výroby. Systém poskytuje intuitivní rozhraní pro různé role uživatelů a umožňuje sledování celého procesu od přijetí objednávky až po její dokončení.

### ✨ Klíčové funkce

- **📋 Správa objednávek** - Kompletní CRUD operace s pokročilými filtry
- **📅 Kalendářní zobrazení** - Vizualizace termínů dodání a plánování
- **🔄 Workflow management** - Řízení procesu výroby s rolemi
- **🔔 Notifikace** - Automatické upozornění na změny a termíny
- **📊 Reporty a analýzy** - Detailní statistiky a export dat
- **👥 Uživatelské role** - Admin, Obchodník, Grafik, Realizátor
- **📱 Responzivní design** - Optimalizováno pro všechna zařízení
- **♿ Accessibility** - Plná podpora pro screen readery a keyboard navigation
- **🔒 Bezpečnost** - CSRF ochrana, input sanitization, rate limiting

## 🚀 Rychlý start

### Požadavky

- **PHP 8.3+** s rozšířeními: PDO, PDO_MySQL, PDO_SQLite, mbstring, openssl, curl
- **MySQL 8.0+** nebo **SQLite 3.35+** pro databázi
- **Apache 2.4+** nebo **Nginx 1.18+** webový server

### Instalace

#### 1. Stažení projektu
```bash
git clone <repository-url> cig-realizace
cd cig-realizace
```

#### 2. Vývojové prostředí (lokální testování)
```bash
# Spuštění vestavěného PHP serveru
php -S localhost:8000

# Otevřete prohlížeč na http://localhost:8000
```

#### 3. Produkční nasazení
```bash
# Spuštění automatického deployment scriptu
sudo ./deploy/deploy.sh
```

#### 4. Webová instalace
1. Přejděte na `http://your-domain/install.php`
2. Postupujte podle pokynů instalátora
3. Zadejte databázové údaje
4. Vytvořte administrátorský účet

### Výchozí přihlašovací údaje

```
Administrátor: admin / admin123
Obchodník:     sales_test / test123
Grafik:        designer_test / test123
Realizátor:    producer_test / test123
```

⚠️ **Důležité:** Po prvním přihlášení si změňte hesla!

## 📋 Funkce systému

### ✅ Dokončené fáze

#### Fáze 01 - Databázová struktura ✅
- [x] Konfigurace databáze (SQLite/MySQL)
- [x] Tabulky pro uživatele, objednávky, položky, historii
- [x] Výchozí data a uživatelé
- [x] Webový instalátor

#### Fáze 02 - Systém autentifikace ✅
- [x] Přihlašovací systém s rolemi
- [x] Dashboard pro všechny role
- [x] Administrace uživatelů
- [x] Bezpečnostní opatření (CSRF, session timeout)

#### Fáze 03 - Import CSV dat ✅
- [x] Upload a parsování CSV souborů
- [x] Mapování obchodních zástupců
- [x] Import objednávek a položek
- [x] Validace a error handling

#### Fáze 04 - Dashboard a správa objednávek ✅
- [x] Seznam čekajících objednávek
- [x] Přehled všech objednávek
- [x] Rozbalovací detail objednávky
- [x] Filtrace a vyhledávání
- [x] Paginace a statistiky
- [x] AJAX funkcionalita

#### Fáze 05 - Workflow a kalendář ✅
- [x] Vylepšený detail objednávky s workflow
- [x] Správa stavů objednávek a položek
- [x] Přiřazování technologií
- [x] Kalendářní zobrazení schválených objednávek
- [x] Historie změn objednávek
- [x] AJAX workflow funkcionalita

#### Fáze 06 - Historie objednávek a notifikace ✅
- [x] Centralizované logování změn (HistoryLogger)
- [x] Notifikační systém s real-time aktualizacemi
- [x] Stránka historie objednávky s filtry
- [x] Activity feed na dashboardu
- [x] Export historie do CSV
- [x] Automatické notifikace při změnách

#### Fáze 07 - Reporty a export ✅
- [x] Hlavní stránka reportů s přehledem statistik
- [x] Systémová aktivita s pokročilými filtry
- [x] Reporty aktivit uživatelů
- [x] Export do CSV, Excel a PDF formátů
- [x] Interaktivní grafy s Chart.js
- [x] AJAX endpointy pro real-time data

#### Fáze 08 - Email Notifications System ✅
- [x] Kompletní email notifikační systém
- [x] SMTP konfigurace pro Webglobe hosting
- [x] 6 typů email notifikací (dokončení objednávky, změny stavů, atd.)
- [x] Queue systém s retry logikou a rate limitingem
- [x] Uživatelské preference pro notifikace
- [x] Administrační nástroje pro správu emailů
- [x] Automatizace prostřednictvím cron jobů
- [x] Email tracking a statistiky doručení

#### Fáze 09 - Filtering and Search Functionality ✅
- [x] Globální vyhledávací lišta v navigaci
- [x] Pokročilé vyhledávání napříč objednávkami, katalogy, technologiemi
- [x] Auto-complete návrhy s real-time suggestions
- [x] Pokročilé filtry s kombinacemi podmínek
- [x] Uložené filtry a rychlé předvolby
- [x] Export filtrovaných dat do CSV a Excel
- [x] Full-text search s relevance scoring
- [x] Dedikovaná vyhledávací stránka
- [x] Search analytics a výkonové optimalizace

#### Fáze 10 - UI/UX vylepšení a finální testování ✅
- [x] Responzivní design optimalizace (mobile-first approach)
- [x] Accessibility vylepšení (ARIA, keyboard navigation, screen readers)
- [x] Loading states a UI feedback komponenty
- [x] Unit testy pro core funkce (OrderTest.php)
- [x] Integrační testy pro autentifikaci (AuthTest.php)
- [x] Funkční testy celého systému (SystemTest.php)
- [x] Performance testy a load testing (LoadTest.php)
- [x] Produkční konfigurace a security hardening
- [x] Deployment skripty a automatizace
- [x] Kompletní dokumentace (uživatelská + administrátorská)
- [x] Performance monitoring a error logging
- [x] Maintenance stránka a utility nástroje

## 🎉 PROJEKT DOKONČEN!

**Všech 10 fází bylo úspěšně implementováno!**

Systém CIG Realizace je nyní kompletně připraven pro produkční nasazení s:
- ✅ Plnou funkcionalitou všech modulů
- ✅ Responzivním a přístupným UI
- ✅ Komplexním testovacím pokrytím
- ✅ Produkční konfigurací a bezpečností
- ✅ Kompletní dokumentací
- ✅ Monitoring a maintenance nástroji

## 📖 Dokumentace

### Uživatelská dokumentace
- **[Uživatelská příručka](docs/USER_MANUAL.md)** - Kompletní návod pro uživatele
- **[Administrátorská příručka](docs/ADMIN_GUIDE.md)** - Instalace a správa systému

### Technická dokumentace
- **[Průběh realizace](PROGRESS.md)** - Detailní historie vývoje
- **[Fáze 10 - Finální testování](10_UI_UX_TESTING.md)** - UI/UX vylepšení a testy

## 👥 Uživatelské role

- **Admin** - Plný přístup k systému, správa uživatelů, systémové nastavení
- **Obchodník** - Správa objednávek, import CSV, obchodní reporty
- **Grafik** - Správa náhledů, grafické materiály, komunikace s klienty
- **Realizátor** - Správa výroby, správa zásob, dokončování objednávek

## 🏗️ Architektura systému

### Technologie
- **Backend:** PHP 8.3, PDO, SQLite/MySQL
- **Frontend:** HTML5, CSS3, JavaScript ES6, Bootstrap 5
- **Icons:** Font Awesome 6
- **Testing:** PHPUnit, Custom test suite
- **Security:** CSRF tokens, Input sanitization, Rate limiting
- **Server:** Apache 2.4+ (produkce)
- **Hosting:** Webglobe (doporučeno)

### Databázová struktura
- **users** - Uživatelé a jejich role
- **orders** - Objednávky s kódy a stavy
- **order_items** - Položky objednávek
- **order_history** - Historie změn
- **notifications** - Systémové notifikace
- **email_queue** - Fronta emailů

## 🧪 Testování

### Spuštění testů
```bash
# Unit testy
php tests/unit/OrderTest.php

# Integrační testy
php tests/integration/AuthTest.php

# Funkční testy
php tests/functional/SystemTest.php

# Performance testy
php tests/performance/LoadTest.php
```

### Test coverage
- ✅ Unit testy pro core funkce
- ✅ Integration testy pro autentifikaci
- ✅ Functional testy pro user flows
- ✅ Performance testy pro load testing
- ✅ Security testy pro vulnerabilities

## 🔒 Bezpečnost

### Implementovaná opatření
- **CSRF Protection** - Tokeny pro všechny formuláře
- **Input Sanitization** - Validace a čištění všech vstupů
- **SQL Injection Prevention** - Prepared statements
- **XSS Protection** - HTML escaping
- **Session Security** - Secure cookies, regenerace ID
- **Rate Limiting** - Ochrana proti brute force útokům
- **File Protection** - .htaccess pravidla pro citlivé soubory

## 📊 Struktura dat

### Objednávky (CSV import)
- **Číslo dokladu** → Kód objednávky
- **Katalog** → Kód položky
- **Množství** → Počet kusů
- **Datum vystavení** → Datum objednávky
- **Datum vystavení OV** → Datum objednání zboží
- **Datum vytvoření DLP** → Datum naskladnění

### Obchodní zástupci
- **VP** = Vláďa
- **J** = Jirka
- **NK** = Nikol
- **MR** = Mirka
- **D** = Daniela
- **CI** = Czech Image

## 🔧 Konfigurace

### Vývojové prostředí
- Automaticky používá SQLite databázi
- Databáze: `data/cig_realizace.db`
- Konfigurace: `config/database.php`

### Produkční prostředí
- Automaticky přepne na MySQL
- Upravte `config/database.php` pro produkční údaje
- Nastavte správné přihlašovací údaje

## 📁 Struktura projektu

```
cig-realizace/
├── config/
│   ├── database.php          # Konfigurace databáze
│   └── email.php             # Konfigurace email systému
├── classes/
│   ├── EmailManager.php      # Správa odesílání emailů
│   ├── NotificationQueue.php # Fronta email notifikací
│   ├── SearchEngine.php      # Pokročilé vyhledávání
│   └── FilterManager.php     # Správa filtrů a exportu
├── templates/email/          # Email templates
│   ├── order_completed.php   # Dokončení objednávky
│   ├── preview_status_changed.php # Změna stavu náhledu
│   ├── overdue_alert.php     # Upozornění na zpoždění
│   ├── daily_summary.php     # Denní souhrn
│   ├── weekly_summary.php    # Týdenní souhrn
│   └── system_alert.php      # Systémové alerty
├── cron/
│   ├── send_notifications.php # Zpracování email fronty
│   └── daily_reports.php     # Denní reporty a alerty
├── admin/
│   ├── notifications.php     # Správa notifikací
│   └── email_queue.php       # Správa email fronty
├── user/
│   └── notification_settings.php # Uživatelské nastavení
├── search/
│   └── index.php              # Pokročilé vyhledávání
├── sql/
│   ├── 01_create_database.sql # Vytvoření databáze (MySQL)
│   ├── 02_create_tables.sql   # Struktura tabulek
│   ├── 03_insert_default_data.sql # Výchozí data
│   ├── 04_indexes.sql         # Optimalizační indexy
│   ├── 06_email_system.sql    # Email systém tabulky
│   ├── 07_order_completion.sql # Dokončení objednávek
│   └── 08_search_optimization.sql # Vyhledávací optimalizace
├── data/
│   └── cig_realizace.db       # SQLite databáze (vývoj)
├── logs/                      # Log soubory
├── index.php                  # Hlavní stránka
├── install.php                # Instalátor databáze
├── test_email_system.php      # Test email systému
├── setup_cron.sh              # Nastavení cron jobů
├── EMAIL_SYSTEM_DOCS.md       # Dokumentace email systému
├── PROGRESS.md                # Průběh realizace
└── README.md                  # Dokumentace
```

## 🐛 Řešení problémů

### Chyba připojení k databázi
1. Zkontrolujte, zda existuje adresář `data/`
2. Ověřte oprávnění pro zápis
3. Pro MySQL zkontrolujte přihlašovací údaje

### Instalace se nespustí
1. Zkontrolujte PHP verzi: `php --version`
2. Ověřte, zda jsou dostupné PDO rozšíření
3. Zkontrolujte oprávnění souborů

### Email systém nefunguje
1. Zkontrolujte SMTP konfiguraci v `config/email.php`
2. Ověřte, že cron joby běží: `crontab -l`
3. Zkontrolujte logy: `tail -f logs/email_cron.log`
4. Spusťte test: `php test_email_system.php`

### Nastavení cron jobů
1. Spusťte: `./setup_cron.sh`
2. Nebo manuálně: `crontab -e`
3. Přidejte řádky pro zpracování emailů a denní reporty

## 📊 Monitoring a údržba

### Performance monitoring
```bash
# Spuštění performance monitoru
php utils/performance_monitor.php

# Analýza error logů
php utils/error_logger.php stats 24

# Čištění starých logů
php utils/error_logger.php clean 30
```

### Cron jobs
```bash
# Email notifikace (každých 5 minut)
*/5 * * * * php /var/www/cig-realizace/cron/send_notifications.php

# Denní reporty (6:00)
0 6 * * * php /var/www/cig-realizace/cron/daily_reports.php
```

## 🤝 Podpora

### Kontakt
- **Email:** <EMAIL>
- **Telefon:** +420 XXX XXX XXX
- **Pracovní doba:** Po-Pá 8:00-17:00

### Řešení problémů
1. Zkontrolujte [dokumentaci](docs/)
2. Prohledejte [známé problémy](docs/USER_MANUAL.md#řešení-problémů)
3. Kontaktujte technickou podporu

## 📝 Changelog

### Verze 1.0.0 (2025-01-25)
- ✅ Kompletní implementace všech 10 fází
- ✅ Plná funkcionalita systému
- ✅ Responzivní design a accessibility
- ✅ Testovací pokrytí
- ✅ Produkční konfigurace
- ✅ Kompletní dokumentace

## 📄 Licence

Tento projekt je proprietární software společnosti CIG Image. Všechna práva vyhrazena.

---

**CIG Realizace v1.0.0** - Systém správy objednávek pro CIG Image
Vyvinuto s ❤️ pro efektivní správu výrobních procesů
