-- CIG Realizace - Performance Indexes Script
-- Phase 01: Database Structure and Configuration

-- Additional indexes for better performance (SQLite compatible)

-- Additional composite indexes for orders
CREATE INDEX IF NOT EXISTS idx_orders_sales_rep_date ON orders(sales_rep, order_date);
CREATE INDEX IF NOT EXISTS idx_orders_preview_status_date ON orders(preview_status, preview_status_date);
CREATE INDEX IF NOT EXISTS idx_orders_expected_delivery ON orders(expected_delivery_date);
CREATE INDEX IF NOT EXISTS idx_orders_completed_date ON orders(completed_date);

-- Additional indexes for order_items
CREATE INDEX IF NOT EXISTS idx_order_items_order_catalog ON order_items(order_id, catalog_code);
CREATE INDEX IF NOT EXISTS idx_order_items_goods_ordered_date ON order_items(goods_ordered_date);
CREATE INDEX IF NOT EXISTS idx_order_items_goods_stocked_date ON order_items(goods_stocked_date);
CREATE INDEX IF NOT EXISTS idx_order_items_technology_assignment ON order_items(technology_assignment);

-- Additional indexes for order_history
CREATE INDEX IF NOT EXISTS idx_order_history_order_action ON order_history(order_id, action_type);
CREATE INDEX IF NOT EXISTS idx_order_history_user_action ON order_history(user_id, action_type);

-- Additional indexes for users
CREATE INDEX IF NOT EXISTS idx_users_role_active ON users(role, is_active);

-- Additional indexes for technologies
CREATE INDEX IF NOT EXISTS idx_technologies_name_active ON technologies(name, is_active);
