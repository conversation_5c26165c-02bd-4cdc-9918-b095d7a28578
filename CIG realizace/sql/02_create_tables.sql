-- CIG Realizace - Table Creation Script
-- Phase 01: Database Structure and Configuration

-- Table: users
CREATE TABLE users (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  username VARCHAR(50) NOT NULL UNIQUE,
  password VARCHAR(255) NOT NULL,
  email VARCHAR(100) NOT NULL UNIQUE,
  role VARCHAR(20) NOT NULL CHECK (role IN ('admin','obchodnik','grafik','realizator')),
  full_name VARCHAR(100) NOT NULL,
  is_active INTEGER NOT NULL DEFAULT 1,
  created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Table: technologies
CREATE TABLE technologies (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  is_active INTEGER NOT NULL DEFAULT 1,
  created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Table: orders
CREATE TABLE orders (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  order_code VARCHAR(20) NOT NULL UNIQUE,
  sales_rep_id INTEGER DEFAULT NULL,
  sales_rep VARCHAR(10) NOT NULL, -- VP, J, NK, MR, D, CI
  sales_rep_name VARCHAR(50) DEFAULT NULL,
  order_date DATE NOT NULL,
  status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending','in_progress','completed','cancelled')),
  preview_status VARCHAR(20) NOT NULL DEFAULT 'not_created' CHECK (preview_status IN ('not_created','sent_to_client','approved')),
  preview_status_date DATETIME NULL DEFAULT NULL,
  preview_approved_date DATE NULL DEFAULT NULL,
  expected_delivery_date DATE NULL DEFAULT NULL,
  is_completed INTEGER NOT NULL DEFAULT 0,
  completed_date DATETIME NULL DEFAULT NULL,
  notes TEXT,
  created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (sales_rep_id) REFERENCES users(id) ON DELETE SET NULL
);

CREATE INDEX idx_orders_sales_rep ON orders(sales_rep);
CREATE INDEX idx_orders_order_date ON orders(order_date);
CREATE INDEX idx_orders_preview_status ON orders(preview_status);
CREATE INDEX idx_orders_is_completed ON orders(is_completed);

-- Table: order_items
CREATE TABLE order_items (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  order_id INTEGER NOT NULL,
  catalog_code VARCHAR(50) DEFAULT NULL,
  quantity DECIMAL(10,4) NOT NULL DEFAULT 0.0000,
  goods_ordered_date DATE NULL DEFAULT NULL,
  goods_stocked_date DATE NULL DEFAULT NULL,
  inventory_status VARCHAR(20) NOT NULL DEFAULT 'not_in_stock' CHECK (inventory_status IN ('not_in_stock','ordered','in_stock')),
  technology_assignment VARCHAR(100) DEFAULT NULL,
  is_relevant INTEGER NOT NULL DEFAULT 1,
  notes TEXT,
  created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE
);

CREATE INDEX idx_order_items_order_id ON order_items(order_id);
CREATE INDEX idx_order_items_catalog_code ON order_items(catalog_code);
CREATE INDEX idx_order_items_inventory_status ON order_items(inventory_status);

-- Table: order_history
CREATE TABLE order_history (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  order_id INTEGER NOT NULL,
  user_id INTEGER NOT NULL,
  action_type VARCHAR(50) NOT NULL,
  old_value TEXT,
  new_value TEXT,
  description TEXT NOT NULL,
  created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

CREATE INDEX idx_order_history_order_id ON order_history(order_id);
CREATE INDEX idx_order_history_user_id ON order_history(user_id);
CREATE INDEX idx_order_history_action_type ON order_history(action_type);
CREATE INDEX idx_order_history_created_at ON order_history(created_at);

-- Table: import_logs
CREATE TABLE import_logs (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  filename VARCHAR(255) NOT NULL,
  user_id INTEGER NOT NULL,
  status VARCHAR(20) NOT NULL CHECK (status IN ('success','error','warning')),
  total_rows INTEGER NOT NULL DEFAULT 0,
  orders_created INTEGER NOT NULL DEFAULT 0,
  items_created INTEGER NOT NULL DEFAULT 0,
  errors_count INTEGER NOT NULL DEFAULT 0,
  warnings_count INTEGER NOT NULL DEFAULT 0,
  processing_time DECIMAL(10,2) DEFAULT NULL,
  error_details TEXT,
  created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

CREATE INDEX idx_import_logs_user_id ON import_logs(user_id);
CREATE INDEX idx_import_logs_status ON import_logs(status);
CREATE INDEX idx_import_logs_created_at ON import_logs(created_at);
