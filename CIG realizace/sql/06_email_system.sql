-- CIG Realizace - Phase 08: Email Notifications System
-- Add email queue and user notification preferences tables

-- Table: notification_queue
-- Queue for email notifications to be sent
CREATE TABLE notification_queue (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  recipient_email VARCHAR(255) NOT NULL,
  recipient_name VARCHAR(255) DEFAULT NULL,
  subject VARCHAR(255) NOT NULL,
  template VARCHAR(100) NOT NULL,
  template_data TEXT DEFAULT NULL, -- JSON data for template
  priority INTEGER NOT NULL DEFAULT 5, -- 1=highest, 10=lowest
  attempts INTEGER NOT NULL DEFAULT 0,
  max_attempts INTEGER NOT NULL DEFAULT 3,
  status VARCHAR(20) NOT NULL DEFAULT 'pending', -- pending, sent, failed
  scheduled_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  sent_at DATETIME NULL DEFAULT NULL,
  error_message TEXT NULL DEFAULT NULL,
  created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_notification_queue_status ON notification_queue(status);
CREATE INDEX idx_notification_queue_scheduled_at ON notification_queue(scheduled_at);
CREATE INDEX idx_notification_queue_priority ON notification_queue(priority);
CREATE INDEX idx_notification_queue_recipient_email ON notification_queue(recipient_email);

-- Table: user_notification_preferences
-- User preferences for email notifications
CREATE TABLE user_notification_preferences (
  user_id INTEGER PRIMARY KEY,
  order_completion INTEGER NOT NULL DEFAULT 1, -- Boolean: receive order completion emails
  preview_status_change INTEGER NOT NULL DEFAULT 1, -- Boolean: receive preview status change emails
  overdue_alerts INTEGER NOT NULL DEFAULT 1, -- Boolean: receive overdue alerts
  daily_summary INTEGER NOT NULL DEFAULT 0, -- Boolean: receive daily summary
  weekly_summary INTEGER NOT NULL DEFAULT 1, -- Boolean: receive weekly summary
  email_format VARCHAR(10) NOT NULL DEFAULT 'html', -- html or text
  frequency VARCHAR(20) NOT NULL DEFAULT 'immediate', -- immediate, daily, weekly
  last_daily_summary DATETIME NULL DEFAULT NULL,
  last_weekly_summary DATETIME NULL DEFAULT NULL,
  created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Table: email_delivery_tracking
-- Track email delivery status and opens
CREATE TABLE email_delivery_tracking (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  queue_id INTEGER NOT NULL,
  recipient_email VARCHAR(255) NOT NULL,
  delivered_at DATETIME NULL DEFAULT NULL,
  opened_at DATETIME NULL DEFAULT NULL,
  clicked_at DATETIME NULL DEFAULT NULL,
  bounced_at DATETIME NULL DEFAULT NULL,
  bounce_reason TEXT NULL DEFAULT NULL,
  user_agent TEXT NULL DEFAULT NULL,
  ip_address VARCHAR(45) NULL DEFAULT NULL,
  created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (queue_id) REFERENCES notification_queue(id) ON DELETE CASCADE
);

CREATE INDEX idx_email_delivery_tracking_queue_id ON email_delivery_tracking(queue_id);
CREATE INDEX idx_email_delivery_tracking_recipient_email ON email_delivery_tracking(recipient_email);

-- Insert default notification preferences for existing users
INSERT INTO user_notification_preferences (user_id, created_at, updated_at)
SELECT id, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP 
FROM users 
WHERE id NOT IN (SELECT user_id FROM user_notification_preferences);

-- Table: email_templates_cache
-- Cache for compiled email templates
CREATE TABLE email_templates_cache (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  template_name VARCHAR(100) NOT NULL UNIQUE,
  html_content TEXT NOT NULL,
  text_content TEXT NOT NULL,
  last_modified DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_email_templates_cache_template_name ON email_templates_cache(template_name);
CREATE INDEX idx_email_templates_cache_last_modified ON email_templates_cache(last_modified);

-- Table: email_statistics
-- Statistics for email campaigns and delivery
CREATE TABLE email_statistics (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  date DATE NOT NULL,
  template_name VARCHAR(100) NOT NULL,
  emails_sent INTEGER NOT NULL DEFAULT 0,
  emails_delivered INTEGER NOT NULL DEFAULT 0,
  emails_opened INTEGER NOT NULL DEFAULT 0,
  emails_clicked INTEGER NOT NULL DEFAULT 0,
  emails_bounced INTEGER NOT NULL DEFAULT 0,
  emails_failed INTEGER NOT NULL DEFAULT 0,
  created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(date, template_name)
);

CREATE INDEX idx_email_statistics_date ON email_statistics(date);
CREATE INDEX idx_email_statistics_template_name ON email_statistics(template_name);
