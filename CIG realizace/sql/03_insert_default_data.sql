-- CIG Realizace - Default Data Insert Script
-- Phase 01: Database Structure and Configuration

-- Insert default admin user
-- Password: admin123 (hashed with password_hash())
INSERT INTO users (username, password, email, role, full_name) VALUES
('admin', '$2y$12$LopvwnwwmCJ4IoKnyIj5xe7pfFSCI4M.91DRi7ep5Ryk0V0mceZCy', '<EMAIL>', 'admin', 'Administrátor systému');

-- Insert sales representatives as users
-- All passwords are: admin123 for testing
INSERT INTO users (username, password, email, role, full_name) VALUES
('vlada', '$2y$12$LopvwnwwmCJ4IoKnyIj5xe7pfFSCI4M.91DRi7ep5Ryk0V0mceZCy', '<EMAIL>', 'obchodnik', 'Vláďa'),
('jirka', '$2y$12$LopvwnwwmCJ4IoKnyIj5xe7pfFSCI4M.91DRi7ep5Ryk0V0mceZCy', '<EMAIL>', 'obchodnik', 'Jirka'),
('nikol', '$2y$12$LopvwnwwmCJ4IoKnyIj5xe7pfFSCI4M.91DRi7ep5Ryk0V0mceZCy', '<EMAIL>', 'obchodnik', 'Nikol'),
('mirka', '$2y$12$LopvwnwwmCJ4IoKnyIj5xe7pfFSCI4M.91DRi7ep5Ryk0V0mceZCy', '<EMAIL>', 'obchodnik', 'Mirka'),
('daniela', '$2y$12$LopvwnwwmCJ4IoKnyIj5xe7pfFSCI4M.91DRi7ep5Ryk0V0mceZCy', '<EMAIL>', 'obchodnik', 'Daniela');

-- Insert default roles for production team
-- All passwords are: admin123 for testing
INSERT INTO users (username, password, email, role, full_name) VALUES
('grafik1', '$2y$12$LopvwnwwmCJ4IoKnyIj5xe7pfFSCI4M.91DRi7ep5Ryk0V0mceZCy', '<EMAIL>', 'grafik', 'Grafik'),
('realizator1', '$2y$12$LopvwnwwmCJ4IoKnyIj5xe7pfFSCI4M.91DRi7ep5Ryk0V0mceZCy', '<EMAIL>', 'realizator', 'Realizátor');

-- Insert basic technologies from CSV analysis
INSERT INTO technologies (name, description) VALUES
('PVY', 'Poštovné'),
('Dopravné', 'Dopravné'),
('TA1', 'Technologie TA1'),
('TA2', 'Technologie TA2'),
('TA3', 'Technologie TA3'),
('TR1', 'Technologie TR1'),
('S1', 'Technologie S1'),
('S2', 'Technologie S2'),
('S3', 'Technologie S3'),
('RS1', 'Technologie RS1'),
('L1', 'Technologie L1'),
('L3', 'Technologie L3'),
('SP', 'Technologie SP'),
('UV2', 'Technologie UV2'),
('OSA', 'OSA technologie');

-- Insert sales rep mappings for easier identification
-- This will help with automatic assignment based on order codes
CREATE TABLE IF NOT EXISTS sales_rep_mapping (
  prefix VARCHAR(10) NOT NULL PRIMARY KEY,
  full_name VARCHAR(50) NOT NULL,
  user_id INTEGER DEFAULT NULL,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

INSERT INTO sales_rep_mapping (prefix, full_name, user_id) VALUES
('VP', 'Vláďa', (SELECT id FROM users WHERE username = 'vlada')),
('J', 'Jirka', (SELECT id FROM users WHERE username = 'jirka')),
('NK', 'Nikol', (SELECT id FROM users WHERE username = 'nikol')),
('MR', 'Mirka', (SELECT id FROM users WHERE username = 'mirka')),
('D', 'Daniela', (SELECT id FROM users WHERE username = 'daniela')),
('CI', 'Czech Image', NULL);
