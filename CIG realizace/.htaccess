# CIG Realizace - Security Configuration
# Apache .htaccess file for production security

# Enable rewrite engine
RewriteEngine On

# Security Headers
<IfModule mod_headers.c>
    # Prevent MIME type sniffing
    Header always set X-Content-Type-Options nosniff
    
    # Prevent clickjacking
    Header always set X-Frame-Options DENY
    
    # Enable XSS protection
    Header always set X-XSS-Protection "1; mode=block"
    
    # Strict Transport Security (HTTPS only)
    Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains"
    
    # Referrer Policy
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    
    # Content Security Policy
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; font-src 'self' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; img-src 'self' data: https:; connect-src 'self'; frame-ancestors 'none';"
</IfModule>

# Deny access to sensitive files and directories
<FilesMatch "\.(env|log|sql|md|txt|json|lock|yml|yaml|ini|conf)$">
    Require all denied
</FilesMatch>

# Deny access to configuration files
<Files "*.php">
    <RequireAll>
        Require all granted
        <RequireAny>
            Require not expr "%{REQUEST_URI} =~ m#^/config/#"
            Require not expr "%{REQUEST_URI} =~ m#^/includes/#"
            Require not expr "%{REQUEST_URI} =~ m#^/classes/#"
            Require not expr "%{REQUEST_URI} =~ m#^/sql/#"
            Require not expr "%{REQUEST_URI} =~ m#^/tests/#"
            Require not expr "%{REQUEST_URI} =~ m#^/utils/#"
            Require not expr "%{REQUEST_URI} =~ m#^/deploy/#"
            Require not expr "%{REQUEST_URI} =~ m#^/maintenance/#"
        </RequireAny>
    </RequireAll>
</Files>

# Protect specific directories
<Directory "config">
    Require all denied
</Directory>

<Directory "includes">
    Require all denied
</Directory>

<Directory "classes">
    Require all denied
</Directory>

<Directory "sql">
    Require all denied
</Directory>

<Directory "tests">
    Require all denied
</Directory>

<Directory "utils">
    Require all denied
</Directory>

<Directory "deploy">
    Require all denied
</Directory>

<Directory "maintenance">
    Require all denied
</Directory>

<Directory "logs">
    Require all denied
</Directory>

<Directory "data">
    Require all denied
</Directory>

# Protect backup files
<FilesMatch "\.(bak|backup|old|orig|save|swp|tmp)$">
    Require all denied
</FilesMatch>

# Protect version control files
<FilesMatch "^\.">
    Require all denied
</FilesMatch>

# Disable directory browsing
Options -Indexes

# Disable server signature
ServerSignature Off

# Custom error pages
ErrorDocument 403 /errors/403.html
ErrorDocument 404 /errors/404.html
ErrorDocument 500 /errors/500.html

# Enable compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>

# Cache static files
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
    ExpiresByType font/woff "access plus 1 month"
    ExpiresByType font/woff2 "access plus 1 month"
</IfModule>

# Prevent access to PHP files in uploads directory
<Directory "uploads">
    <FilesMatch "\.php$">
        Require all denied
    </FilesMatch>
</Directory>

# Rate limiting (if mod_evasive is available)
<IfModule mod_evasive24.c>
    DOSHashTableSize    2048
    DOSPageCount        10
    DOSSiteCount        50
    DOSPageInterval     1
    DOSSiteInterval     1
    DOSBlockingPeriod   600
</IfModule>

# Prevent hotlinking
<IfModule mod_rewrite.c>
    RewriteCond %{HTTP_REFERER} !^$
    RewriteCond %{HTTP_REFERER} !^https?://(www\.)?cigimage\.cz [NC]
    RewriteCond %{HTTP_REFERER} !^https?://(www\.)?localhost [NC]
    RewriteRule \.(jpg|jpeg|png|gif|svg|css|js)$ - [F,L]
</IfModule>

# PHP security settings
<IfModule mod_php.c>
    php_flag display_errors Off
    php_flag log_errors On
    php_flag expose_php Off
    php_value max_execution_time 30
    php_value max_input_time 30
    php_value memory_limit 128M
    php_value post_max_size 10M
    php_value upload_max_filesize 10M
    php_value session.cookie_httponly 1
    php_value session.cookie_secure 1
    php_value session.use_strict_mode 1
</IfModule>

# Redirect HTTP to HTTPS (uncomment for production with SSL)
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Pretty URLs (if needed)
# RewriteCond %{REQUEST_FILENAME} !-f
# RewriteCond %{REQUEST_FILENAME} !-d
# RewriteRule ^([^/]+)/?$ index.php?page=$1 [L,QSA]
