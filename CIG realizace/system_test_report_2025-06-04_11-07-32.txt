=== CIG Realizace System Test Report ===
Generated: 2025-06-04 11:07:32
Base URL: http://localhost:8000

=== Summary ===
Total Tests: 32
Passed: 25
Failed: 7
Success Rate: 78.13%

=== Detailed Results ===

Database:
  [PASS] Connection Test: Database accessible

Auth:
  [PASS] Login Page Access: HTTP 200
  [PASS] Admin Login: Login successful
  [PASS] Failed Login Handling: Failed login properly handled
  [PASS] Logout: Logout successful

Dashboard:
  [PASS] Page Load: HTTP 200
  [FAIL] Statistics Cards: Statistics cards present
  [FAIL] Navigation Menu: Navigation menu present
  [FAIL] Notifications: Notifications dropdown present

Orders:
  [PASS] Pending Orders Page: HTTP 200
  [PASS] All Orders Page: HTTP 200
  [PASS] Order Detail AJAX: AJAX endpoint accessible
  [PASS] Order History: Order history accessible

Calendar:
  [PASS] Calendar Page: HTTP 200
  [PASS] Calendar Data AJAX: Calendar data endpoint accessible

Search:
  [PASS] Search Page: HTTP 200
  [PASS] Search Suggestions: Search suggestions endpoint accessible

Notifications:
  [PASS] Get Notifications: Notifications endpoint accessible
  [PASS] Settings Page: Notification settings accessible

Reports:
  [PASS] Reports Page: HTTP 200
  [PASS] System Activity: System activity accessible
  [PASS] System Stats AJAX: System stats endpoint accessible

Email:
  [PASS] Email Queue Page: Email queue accessible
  [PASS] Email Notifications: Email notifications accessible

Accessibility:
  [PASS] CSS File: Accessibility CSS accessible
  [PASS] Responsive CSS: Responsive CSS accessible
  [PASS] UI Enhancements JS: UI enhancements JS accessible

Security:
  [FAIL] File Protection: database.php: File exposed!
  [FAIL] File Protection: email.php: File exposed!
  [FAIL] File Protection: .env: File exposed!
  [PASS] File Protection: php_errors.log: File protected
  [FAIL] CSRF Protection: CSRF protection active

=== Assessment ===
ACCEPTABLE: System is functional but needs attention.
