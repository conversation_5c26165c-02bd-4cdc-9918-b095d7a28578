<?php
/**
 * Email Template: Overdue Alert
 * CIG Realizace - Phase 08
 * 
 * Available variables:
 * - $admin_name
 * - $overdue_count
 * - $orders (array of overdue orders)
 * - $alert_date
 * - $dashboard_url
 */
?>
<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Upozornění na z<PERSON>ž<PERSON>ky</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: #f4f4f4;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .header {
            background: linear-gradient(135deg, #dc3545, #fd7e14);
            color: white;
            padding: 30px 20px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: bold;
        }
        .header h2 {
            margin: 10px 0 0 0;
            font-size: 18px;
            font-weight: normal;
            opacity: 0.9;
        }
        .content {
            padding: 30px 20px;
        }
        .greeting {
            font-size: 16px;
            margin-bottom: 20px;
        }
        .alert-summary {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-left: 4px solid #dc3545;
            padding: 20px;
            margin: 20px 0;
            border-radius: 4px;
        }
        .alert-summary h3 {
            margin: 0 0 15px 0;
            color: #721c24;
            font-size: 18px;
        }
        .alert-summary .count {
            font-size: 24px;
            font-weight: bold;
            color: #dc3545;
        }
        .orders-list {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 20px;
            margin: 20px 0;
            border-radius: 4px;
            max-height: 300px;
            overflow-y: auto;
        }
        .orders-list h3 {
            margin: 0 0 15px 0;
            color: #856404;
            font-size: 18px;
        }
        .order-item {
            background: white;
            border: 1px solid #e9ecef;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            border-left: 4px solid #dc3545;
        }
        .order-item h4 {
            margin: 0 0 10px 0;
            color: #2c3e50;
            font-size: 16px;
        }
        .order-item p {
            margin: 5px 0;
            font-size: 14px;
            color: #6c757d;
        }
        .order-item .overdue-days {
            background: #dc3545;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            display: inline-block;
        }
        .cta-button {
            display: inline-block;
            background: linear-gradient(135deg, #dc3545, #fd7e14);
            color: white;
            text-decoration: none;
            padding: 12px 30px;
            border-radius: 5px;
            font-weight: bold;
            margin: 20px 0;
            transition: all 0.3s ease;
        }
        .cta-button:hover {
            background: linear-gradient(135deg, #c82333, #e8690b);
            transform: translateY(-2px);
        }
        .footer {
            background: #f8f9fa;
            padding: 20px;
            text-align: center;
            border-top: 1px solid #e9ecef;
            font-size: 14px;
            color: #6c757d;
        }
        .footer p {
            margin: 5px 0;
        }
        .company-logo {
            font-size: 24px;
            font-weight: bold;
            color: white;
            margin-bottom: 5px;
        }
        .urgent-badge {
            background: #dc3545;
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        /* Responsive design */
        @media only screen and (max-width: 600px) {
            .container {
                width: 100% !important;
            }
            .header {
                padding: 20px 15px;
            }
            .content {
                padding: 20px 15px;
            }
            .orders-list {
                padding: 15px;
            }
            .order-item {
                padding: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="company-logo">CIG Realizace</div>
            <h1>⚠️ Upozornění na zpoždění</h1>
            <h2><span class="urgent-badge">Urgentní</span></h2>
        </div>
        
        <!-- Content -->
        <div class="content">
            <div class="greeting">
                <p>Dobrý den <?= htmlspecialchars($admin_name) ?>,</p>
                
                <p>systém detekoval objednávky, které překročily plánovaný termín dodání a vyžadují Vaši pozornost.</p>
            </div>
            
            <div class="alert-summary">
                <h3>📊 Souhrn zpožděných objednávek:</h3>
                <p>Celkem zpožděných objednávek: <span class="count"><?= htmlspecialchars($overdue_count) ?></span></p>
                <p>Datum kontroly: <strong><?= htmlspecialchars($alert_date) ?></strong></p>
            </div>
            
            <?php if (!empty($orders) && count($orders) > 0): ?>
            <div class="orders-list">
                <h3>📋 Seznam zpožděných objednávek:</h3>
                
                <?php foreach (array_slice($orders, 0, 10) as $order): ?>
                    <?php 
                    $daysOverdue = floor((time() - strtotime($order['expected_delivery_date'])) / (60 * 60 * 24));
                    ?>
                    <div class="order-item">
                        <h4>
                            <?= htmlspecialchars($order['order_code']) ?>
                            <span class="overdue-days"><?= $daysOverdue ?> dní po termínu</span>
                        </h4>
                        <p><strong>Obchodník:</strong> <?= htmlspecialchars($order['sales_rep']) ?></p>
                        <p><strong>Plánovaný termín:</strong> <?= date('d.m.Y', strtotime($order['expected_delivery_date'])) ?></p>
                        <p><strong>Stav náhledu:</strong> <?= htmlspecialchars($order['preview_status']) ?></p>
                        <?php if (!empty($order['technologies'])): ?>
                        <p><strong>Technologie:</strong> <?= htmlspecialchars($order['technologies']) ?></p>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
                
                <?php if (count($orders) > 10): ?>
                <p style="text-align: center; margin-top: 20px; font-style: italic; color: #6c757d;">
                    ... a dalších <?= count($orders) - 10 ?> objednávek
                </p>
                <?php endif; ?>
            </div>
            <?php endif; ?>
            
            <p><strong>Doporučené akce:</strong></p>
            <ul>
                <li>Zkontrolujte stav výroby zpožděných objednávek</li>
                <li>Kontaktujte příslušné obchodníky</li>
                <li>Aktualizujte termíny dodání</li>
                <li>Informujte klienty o zpoždění</li>
            </ul>
            
            <div style="text-align: center;">
                <a href="<?= htmlspecialchars($dashboard_url) ?>" class="cta-button">
                    🏠 Přejít na dashboard
                </a>
            </div>
            
            <p>Tento alert je odesílán automaticky každý den pro zajištění včasného dodání objednávek.</p>
            
            <p>S pozdravem,<br>
            <strong>Systém CIG Realizace</strong></p>
        </div>
        
        <!-- Footer -->
        <div class="footer">
            <p><strong>CIG Image s.r.o.</strong></p>
            <p>Systém pro správu objednávek</p>
            <p>Tento email byl odeslán automaticky. Prosím neodpovídejte na tuto zprávu.</p>
            <p><a href="{{unsubscribe_url}}" style="color: #6c757d; text-decoration: none;">Odhlásit se z notifikací</a></p>
        </div>
    </div>
</body>
</html>
