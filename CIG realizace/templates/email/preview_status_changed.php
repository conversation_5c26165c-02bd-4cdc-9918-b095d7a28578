<?php
/**
 * Email Template: Preview Status Changed
 * CIG Realizace - Phase 08
 * 
 * Available variables:
 * - $recipient_name
 * - $order_code
 * - $old_status_text
 * - $new_status_text
 * - $changed_by
 * - $change_date
 * - $order_url
 * - $approved (boolean)
 * - $delivery_date (if approved)
 */

// Determine status color and icon
$statusColors = [
    'Nevytvořen' => '#6c757d',
    'Probíhá' => '#ffc107',
    'Odesláno klientovi' => '#17a2b8',
    'Schváleno' => '#28a745',
    'Zamítnuto' => '#dc3545',
    'Potřebuje úpravu' => '#fd7e14'
];

$statusIcons = [
    'Nevytvořen' => '⏳',
    'Probíhá' => '🔄',
    'Odesláno klientovi' => '📤',
    'Schváleno' => '✅',
    'Zamítnuto' => '❌',
    'Potřebuje úpravu' => '🔧'
];

$newStatusColor = $statusColors[$new_status_text] ?? '#6c757d';
$newStatusIcon = $statusIcons[$new_status_text] ?? '📋';
?>
<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Změna stavu náhledu - <?= htmlspecialchars($order_code) ?></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: #f4f4f4;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .header {
            background: linear-gradient(135deg, #6f42c1, #e83e8c);
            color: white;
            padding: 30px 20px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: bold;
        }
        .header h2 {
            margin: 10px 0 0 0;
            font-size: 18px;
            font-weight: normal;
            opacity: 0.9;
        }
        .content {
            padding: 30px 20px;
        }
        .greeting {
            font-size: 16px;
            margin-bottom: 20px;
        }
        .status-change {
            background: #f8f9fa;
            border-left: 4px solid <?= $newStatusColor ?>;
            padding: 20px;
            margin: 20px 0;
            border-radius: 4px;
        }
        .status-change h3 {
            margin: 0 0 15px 0;
            color: #2c3e50;
            font-size: 18px;
        }
        .status-flow {
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        .status-box {
            background: #e9ecef;
            padding: 10px 15px;
            border-radius: 5px;
            margin: 5px;
            font-weight: bold;
            min-width: 120px;
            text-align: center;
        }
        .status-box.old {
            background: #6c757d;
            color: white;
        }
        .status-box.new {
            background: <?= $newStatusColor ?>;
            color: white;
        }
        .arrow {
            font-size: 20px;
            margin: 0 10px;
            color: #6c757d;
        }
        .change-details {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
        .change-details p {
            margin: 5px 0;
        }
        .change-details strong {
            color: #856404;
        }
        .delivery-info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
        .delivery-info h4 {
            margin: 0 0 10px 0;
            color: #0c5460;
        }
        .cta-button {
            display: inline-block;
            background: linear-gradient(135deg, #6f42c1, #e83e8c);
            color: white;
            text-decoration: none;
            padding: 12px 30px;
            border-radius: 5px;
            font-weight: bold;
            margin: 20px 0;
            transition: all 0.3s ease;
        }
        .cta-button:hover {
            background: linear-gradient(135deg, #5a32a3, #d91a72);
            transform: translateY(-2px);
        }
        .footer {
            background: #f8f9fa;
            padding: 20px;
            text-align: center;
            border-top: 1px solid #e9ecef;
            font-size: 14px;
            color: #6c757d;
        }
        .footer p {
            margin: 5px 0;
        }
        .company-logo {
            font-size: 24px;
            font-weight: bold;
            color: white;
            margin-bottom: 5px;
        }
        
        /* Responsive design */
        @media only screen and (max-width: 600px) {
            .container {
                width: 100% !important;
            }
            .header {
                padding: 20px 15px;
            }
            .content {
                padding: 20px 15px;
            }
            .status-flow {
                flex-direction: column;
            }
            .arrow {
                transform: rotate(90deg);
                margin: 10px 0;
            }
            .status-box {
                min-width: 200px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="company-logo">CIG Realizace</div>
            <h1>Změna stavu náhledu</h1>
            <h2><?= $newStatusIcon ?> <?= htmlspecialchars($new_status_text) ?></h2>
        </div>
        
        <!-- Content -->
        <div class="content">
            <div class="greeting">
                <p>Dobrý den <?= htmlspecialchars($recipient_name) ?>,</p>
                
                <p>informujeme Vás o změně stavu náhledu pro objednávku <strong><?= htmlspecialchars($order_code) ?></strong>.</p>
            </div>
            
            <div class="status-change">
                <h3>📊 Změna stavu:</h3>
                
                <div class="status-flow">
                    <div class="status-box old">
                        <?= htmlspecialchars($old_status_text) ?>
                    </div>
                    <div class="arrow">→</div>
                    <div class="status-box new">
                        <?= $newStatusIcon ?> <?= htmlspecialchars($new_status_text) ?>
                    </div>
                </div>
            </div>
            
            <div class="change-details">
                <p><strong>Změnu provedl:</strong> <?= htmlspecialchars($changed_by) ?></p>
                <p><strong>Datum změny:</strong> <?= htmlspecialchars($change_date) ?></p>
            </div>
            
            <?php if (isset($approved) && $approved && !empty($delivery_date)): ?>
            <div class="delivery-info">
                <h4>🚚 Informace o dodání</h4>
                <p>Náhled byl schválen a objednávka je naplánována k dodání.</p>
                <p><strong>Očekávaný termín dodání:</strong> <?= htmlspecialchars($delivery_date) ?></p>
            </div>
            <?php endif; ?>
            
            <p>Pro zobrazení kompletního detailu objednávky klikněte na tlačítko níže:</p>
            
            <div style="text-align: center;">
                <a href="<?= htmlspecialchars($order_url) ?>" class="cta-button">
                    📄 Zobrazit detail objednávky
                </a>
            </div>
            
            <p>S pozdravem,<br>
            <strong>Tým CIG Realizace</strong></p>
        </div>
        
        <!-- Footer -->
        <div class="footer">
            <p><strong>CIG Image s.r.o.</strong></p>
            <p>Systém pro správu objednávek</p>
            <p>Tento email byl odeslán automaticky. Prosím neodpovídejte na tuto zprávu.</p>
            <p><a href="{{unsubscribe_url}}" style="color: #6c757d; text-decoration: none;">Odhlásit se z notifikací</a></p>
        </div>
    </div>
</body>
</html>
