<?php
/**
 * Email Template: Weekly Summary
 * CIG Realizace - Phase 08
 * 
 * Available variables:
 * - $user_name
 * - $week_start
 * - $week_end
 * - $new_orders
 * - $completed_orders
 * - $pending_previews
 * - $overdue_orders
 * - $total_activity
 * - $dashboard_url
 */
?>
<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Týdenní souhrn - <?= htmlspecialchars($week_start) ?> - <?= htmlspecialchars($week_end) ?></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: #f4f4f4;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .header {
            background: linear-gradient(135deg, #6f42c1, #20c997);
            color: white;
            padding: 30px 20px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: bold;
        }
        .header h2 {
            margin: 10px 0 0 0;
            font-size: 18px;
            font-weight: normal;
            opacity: 0.9;
        }
        .content {
            padding: 30px 20px;
        }
        .greeting {
            font-size: 16px;
            margin-bottom: 20px;
        }
        .week-period {
            background: #e7f3ff;
            border: 1px solid #b3d7ff;
            border-left: 4px solid #007bff;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
            text-align: center;
        }
        .week-period h3 {
            margin: 0;
            color: #004085;
            font-size: 18px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            transition: transform 0.2s ease;
        }
        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .stat-card.new-orders {
            border-left: 4px solid #007bff;
        }
        .stat-card.completed {
            border-left: 4px solid #28a745;
        }
        .stat-card.pending {
            border-left: 4px solid #ffc107;
        }
        .stat-card.overdue {
            border-left: 4px solid #dc3545;
        }
        .stat-number {
            font-size: 32px;
            font-weight: bold;
            margin: 0;
            line-height: 1;
        }
        .stat-label {
            font-size: 14px;
            color: #6c757d;
            margin: 8px 0 0 0;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .stat-icon {
            font-size: 24px;
            margin-bottom: 10px;
        }
        .summary-section {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-left: 4px solid #6f42c1;
            padding: 20px;
            margin: 20px 0;
            border-radius: 4px;
        }
        .summary-section h3 {
            margin: 0 0 15px 0;
            color: #2c3e50;
            font-size: 18px;
        }
        .activity-summary {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
        .activity-summary p {
            margin: 5px 0;
        }
        .performance-indicator {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
        .performance-indicator h4 {
            margin: 0 0 10px 0;
            color: #0c5460;
        }
        .progress-bar {
            background: #e9ecef;
            border-radius: 10px;
            height: 20px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            background: linear-gradient(90deg, #28a745, #20c997);
            height: 100%;
            border-radius: 10px;
            transition: width 0.3s ease;
        }
        .cta-button {
            display: inline-block;
            background: linear-gradient(135deg, #6f42c1, #20c997);
            color: white;
            text-decoration: none;
            padding: 12px 30px;
            border-radius: 5px;
            font-weight: bold;
            margin: 20px 0;
            transition: all 0.3s ease;
        }
        .cta-button:hover {
            background: linear-gradient(135deg, #5a32a3, #1a9f7a);
            transform: translateY(-2px);
        }
        .footer {
            background: #f8f9fa;
            padding: 20px;
            text-align: center;
            border-top: 1px solid #e9ecef;
            font-size: 14px;
            color: #6c757d;
        }
        .footer p {
            margin: 5px 0;
        }
        .company-logo {
            font-size: 24px;
            font-weight: bold;
            color: white;
            margin-bottom: 5px;
        }
        .week-badge {
            background: rgba(255,255,255,0.2);
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: bold;
        }
        
        /* Responsive design */
        @media only screen and (max-width: 600px) {
            .container {
                width: 100% !important;
            }
            .header {
                padding: 20px 15px;
            }
            .content {
                padding: 20px 15px;
            }
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 10px;
            }
            .stat-card {
                padding: 15px;
            }
            .stat-number {
                font-size: 24px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="company-logo">CIG Realizace</div>
            <h1>📊 Týdenní souhrn</h1>
            <h2><span class="week-badge"><?= htmlspecialchars($week_start) ?> - <?= htmlspecialchars($week_end) ?></span></h2>
        </div>
        
        <!-- Content -->
        <div class="content">
            <div class="greeting">
                <p>Dobrý den <?= htmlspecialchars($user_name) ?>,</p>
                
                <p>zde je Váš týdenní souhrn aktivity v systému CIG Realizace za období od <?= htmlspecialchars($week_start) ?> do <?= htmlspecialchars($week_end) ?>.</p>
            </div>
            
            <div class="week-period">
                <h3>📅 Sledované období</h3>
                <p><strong><?= htmlspecialchars($week_start) ?> - <?= htmlspecialchars($week_end) ?></strong></p>
            </div>
            
            <div class="stats-grid">
                <div class="stat-card new-orders">
                    <div class="stat-icon">📝</div>
                    <div class="stat-number"><?= htmlspecialchars($new_orders) ?></div>
                    <div class="stat-label">Nové objednávky</div>
                </div>
                
                <div class="stat-card completed">
                    <div class="stat-icon">✅</div>
                    <div class="stat-number"><?= htmlspecialchars($completed_orders) ?></div>
                    <div class="stat-label">Dokončené</div>
                </div>
                
                <div class="stat-card pending">
                    <div class="stat-icon">⏳</div>
                    <div class="stat-number"><?= htmlspecialchars($pending_previews) ?></div>
                    <div class="stat-label">Čekající náhledy</div>
                </div>
                
                <div class="stat-card overdue">
                    <div class="stat-icon">⚠️</div>
                    <div class="stat-number"><?= htmlspecialchars($overdue_orders) ?></div>
                    <div class="stat-label">Po termínu</div>
                </div>
            </div>
            
            <div class="summary-section">
                <h3>📈 Týdenní přehled</h3>
                
                <div class="activity-summary">
                    <p><strong>Celková aktivita tento týden:</strong> <?= htmlspecialchars($total_activity) ?> akcí</p>
                    
                    <?php if ($new_orders > 0): ?>
                    <p>✨ Bylo vytvořeno <strong><?= htmlspecialchars($new_orders) ?></strong> nových objednávek</p>
                    <?php endif; ?>
                    
                    <?php if ($completed_orders > 0): ?>
                    <p>🎉 Bylo dokončeno <strong><?= htmlspecialchars($completed_orders) ?></strong> objednávek</p>
                    <?php endif; ?>
                    
                    <?php if ($overdue_orders > 0): ?>
                    <p>⚠️ <strong><?= htmlspecialchars($overdue_orders) ?></strong> objednávek je po termínu dodání</p>
                    <?php endif; ?>
                    
                    <?php if ($pending_previews > 0): ?>
                    <p>⏳ <strong><?= htmlspecialchars($pending_previews) ?></strong> náhledů čeká na zpracování</p>
                    <?php endif; ?>
                </div>
                
                <?php if ($total_activity == 0): ?>
                <p style="text-align: center; color: #6c757d; font-style: italic;">
                    Tento týden nebyla zaznamenána žádná aktivita.
                </p>
                <?php endif; ?>
            </div>
            
            <?php if ($completed_orders > 0 && $new_orders > 0): ?>
            <div class="performance-indicator">
                <h4>📊 Výkonnost týdne</h4>
                <p>Poměr dokončených k novým objednávkám:</p>
                <?php 
                $completionRate = round(($completed_orders / $new_orders) * 100);
                $completionRate = min(100, $completionRate); // Cap at 100%
                ?>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: <?= $completionRate ?>%"></div>
                </div>
                <p style="text-align: center; margin: 5px 0;">
                    <strong><?= $completionRate ?>%</strong> 
                    (<?= $completed_orders ?> dokončeno z <?= $new_orders ?> nových)
                </p>
            </div>
            <?php endif; ?>
            
            <?php if ($overdue_orders > 0): ?>
            <div style="background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 4px; margin: 20px 0;">
                <p style="margin: 0; color: #721c24;">
                    <strong>⚠️ Upozornění:</strong> Máte <?= htmlspecialchars($overdue_orders) ?> objednávek po termínu. 
                    Doporučujeme zkontrolovat jejich stav a aktualizovat termíny dodání.
                </p>
            </div>
            <?php endif; ?>
            
            <div style="text-align: center;">
                <a href="<?= htmlspecialchars($dashboard_url) ?>" class="cta-button">
                    🏠 Přejít na dashboard
                </a>
            </div>
            
            <p>Děkujeme za Vaši práci tento týden a přejeme úspěšný týden následující!</p>
            
            <p>S pozdravem,<br>
            <strong>Tým CIG Realizace</strong></p>
        </div>
        
        <!-- Footer -->
        <div class="footer">
            <p><strong>CIG Image s.r.o.</strong></p>
            <p>Systém pro správu objednávek</p>
            <p>Tento email byl odeslán automaticky. Prosím neodpovídejte na tuto zprávu.</p>
            <p><a href="{{unsubscribe_url}}" style="color: #6c757d; text-decoration: none;">Odhlásit se z notifikací</a></p>
        </div>
    </div>
</body>
</html>
