# Phase 01 - Database Structure and Configuration

## Objective
Set up the MySQL database structure and basic configuration files for the application.

## Tasks

### 1. Create Database Configuration
- Create `config/database.php` with connection settings
- Set up environment-specific configurations
- Include error handling for database connections

### 2. Design Database Schema
Create the following tables:

#### users
```sql
- id (INT, PRIMARY KEY, AUTO_INCREMENT)
- username (VARCHAR(50), UNIQUE)
- password (VARCHAR(255), hashed)
- email (VARCHAR(100))
- role (ENUM: 'admin', 'obchodnik', 'grafik', 'realizator')
- full_name (VARCHAR(100))
- created_at (TIMESTAMP)
- updated_at (TIMESTAMP)
```

#### orders
```sql
- id (INT, PRIMARY KEY, AUTO_INCREMENT)
- order_code (VARCHAR(20), UNIQUE)
- sales_rep (VARCHAR(10)) -- VP, J, NK, MR, D, CI
- sales_rep_name (VARCHAR(50))
- order_date (DATE)
- preview_status (ENUM: 'not_created', 'sent_to_client', 'approved')
- preview_status_date (TIMES<PERSON>MP, NULL)
- preview_approved_date (DATE, NULL)
- expected_delivery_date (DATE, NULL)
- is_completed (BOOLEAN, DEFAULT FALSE)
- completed_date (TIMESTAMP, NULL)
- created_at (TIMESTAMP)
- updated_at (TIMESTAMP)
```

#### order_items
```sql
- id (INT, PRIMARY KEY, AUTO_INCREMENT)
- order_id (INT, FOREIGN KEY)
- catalog_code (VARCHAR(50))
- quantity (DECIMAL(10,4))
- goods_ordered_date (DATE, NULL)
- goods_stocked_date (DATE, NULL)
- inventory_status (ENUM: 'not_in_stock', 'ordered', 'in_stock')
- technology_assignment (VARCHAR(100), NULL)
- is_relevant (BOOLEAN, DEFAULT TRUE)
- created_at (TIMESTAMP)
- updated_at (TIMESTAMP)
```

#### order_history
```sql
- id (INT, PRIMARY KEY, AUTO_INCREMENT)
- order_id (INT, FOREIGN KEY)
- user_id (INT, FOREIGN KEY)
- action_type (VARCHAR(50))
- old_value (TEXT, NULL)
- new_value (TEXT, NULL)
- description (TEXT)
- created_at (TIMESTAMP)
```

#### technologies
```sql
- id (INT, PRIMARY KEY, AUTO_INCREMENT)
- name (VARCHAR(100))
- description (TEXT, NULL)
- is_active (BOOLEAN, DEFAULT TRUE)
- created_at (TIMESTAMP)
```

### 3. Create SQL Files
- `sql/01_create_database.sql` - Database creation
- `sql/02_create_tables.sql` - Table structures
- `sql/03_insert_default_data.sql` - Default users and technologies
- `sql/04_indexes.sql` - Performance indexes

### 4. Files to Create
1. `config/database.php`
2. `sql/01_create_database.sql`
3. `sql/02_create_tables.sql`
4. `sql/03_insert_default_data.sql`
5. `sql/04_indexes.sql`
6. `install.php` - Database setup script

## Expected Outcome
- Functional database structure
- Connection configuration ready
- Default admin user created
- Basic technologies catalog populated

## Next Phase
Phase 02 - User Authentication System
