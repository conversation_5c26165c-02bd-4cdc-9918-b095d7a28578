<?php
/**
 * CIG Realizace - Performance Load Tests
 * Performance and load testing for the application
 */

class LoadTest {
    private $baseUrl;
    private $testResults = [];
    private $sessionCookie = '';
    
    public function __construct($baseUrl = 'http://localhost:8000') {
        $this->baseUrl = rtrim($baseUrl, '/');
    }
    
    /**
     * Run complete load test suite
     */
    public function runLoadTest() {
        echo "Starting Load Test Suite...\n";
        echo "Base URL: {$this->baseUrl}\n\n";
        
        // Login first to get session
        $this->performLogin();
        
        // Run individual tests
        $this->testDashboardLoad();
        $this->testOrderListLoad();
        $this->testCalendarLoad();
        $this->testSearchPerformance();
        $this->testAjaxEndpoints();
        $this->testDatabaseQueries();
        
        return $this->generateReport();
    }
    
    /**
     * Perform login to get session cookie
     */
    private function performLogin() {
        echo "Performing login...\n";
        
        $loginData = [
            'username' => 'admin',
            'password' => 'admin123'
        ];
        
        $response = $this->makeRequest('/index.php', 'POST', $loginData);
        
        if ($response['http_code'] === 200 || $response['http_code'] === 302) {
            echo "Login successful\n\n";
        } else {
            echo "Login failed with HTTP code: {$response['http_code']}\n\n";
        }
    }
    
    /**
     * Test dashboard loading performance
     */
    private function testDashboardLoad() {
        echo "Testing Dashboard Load Performance...\n";
        
        $iterations = 50;
        $startTime = microtime(true);
        $successCount = 0;
        $totalResponseTime = 0;
        
        for ($i = 0; $i < $iterations; $i++) {
            $requestStart = microtime(true);
            $response = $this->makeRequest('/dashboard.php');
            $requestEnd = microtime(true);
            
            $responseTime = $requestEnd - $requestStart;
            $totalResponseTime += $responseTime;
            
            if ($response['http_code'] === 200) {
                $successCount++;
            }
            
            // Progress indicator
            if (($i + 1) % 10 === 0) {
                echo "  Completed " . ($i + 1) . "/$iterations requests\n";
            }
        }
        
        $endTime = microtime(true);
        
        $this->testResults['dashboard'] = [
            'test_name' => 'Dashboard Load',
            'iterations' => $iterations,
            'success_count' => $successCount,
            'success_rate' => ($successCount / $iterations) * 100,
            'total_time' => $endTime - $startTime,
            'avg_response_time' => $totalResponseTime / $iterations,
            'requests_per_second' => $iterations / ($endTime - $startTime)
        ];
        
        echo "Dashboard test completed\n\n";
    }
    
    /**
     * Test order list loading performance
     */
    private function testOrderListLoad() {
        echo "Testing Order List Load Performance...\n";
        
        $endpoints = [
            '/orders/pending.php',
            '/orders/index.php'
        ];
        
        foreach ($endpoints as $endpoint) {
            $iterations = 30;
            $startTime = microtime(true);
            $successCount = 0;
            $totalResponseTime = 0;
            
            for ($i = 0; $i < $iterations; $i++) {
                $requestStart = microtime(true);
                $response = $this->makeRequest($endpoint);
                $requestEnd = microtime(true);
                
                $responseTime = $requestEnd - $requestStart;
                $totalResponseTime += $responseTime;
                
                if ($response['http_code'] === 200) {
                    $successCount++;
                }
            }
            
            $endTime = microtime(true);
            
            $testName = 'Order List - ' . basename($endpoint, '.php');
            $this->testResults[basename($endpoint, '.php')] = [
                'test_name' => $testName,
                'iterations' => $iterations,
                'success_count' => $successCount,
                'success_rate' => ($successCount / $iterations) * 100,
                'total_time' => $endTime - $startTime,
                'avg_response_time' => $totalResponseTime / $iterations,
                'requests_per_second' => $iterations / ($endTime - $startTime)
            ];
        }
        
        echo "Order list tests completed\n\n";
    }
    
    /**
     * Test calendar loading performance
     */
    private function testCalendarLoad() {
        echo "Testing Calendar Load Performance...\n";
        
        $iterations = 25;
        $startTime = microtime(true);
        $successCount = 0;
        $totalResponseTime = 0;
        
        for ($i = 0; $i < $iterations; $i++) {
            $requestStart = microtime(true);
            $response = $this->makeRequest('/calendar/index.php');
            $requestEnd = microtime(true);
            
            $responseTime = $requestEnd - $requestStart;
            $totalResponseTime += $responseTime;
            
            if ($response['http_code'] === 200) {
                $successCount++;
            }
        }
        
        $endTime = microtime(true);
        
        $this->testResults['calendar'] = [
            'test_name' => 'Calendar Load',
            'iterations' => $iterations,
            'success_count' => $successCount,
            'success_rate' => ($successCount / $iterations) * 100,
            'total_time' => $endTime - $startTime,
            'avg_response_time' => $totalResponseTime / $iterations,
            'requests_per_second' => $iterations / ($endTime - $startTime)
        ];
        
        echo "Calendar test completed\n\n";
    }
    
    /**
     * Test search performance
     */
    private function testSearchPerformance() {
        echo "Testing Search Performance...\n";
        
        $searchTerms = ['25VP', 'Jirka', 'TA1', 'approved', 'test'];
        $totalIterations = 0;
        $totalSuccessCount = 0;
        $totalResponseTime = 0;
        $startTime = microtime(true);
        
        foreach ($searchTerms as $term) {
            for ($i = 0; $i < 20; $i++) {
                $requestStart = microtime(true);
                $response = $this->makeRequest('/ajax/search_suggestions.php?q=' . urlencode($term));
                $requestEnd = microtime(true);
                
                $responseTime = $requestEnd - $requestStart;
                $totalResponseTime += $responseTime;
                $totalIterations++;
                
                if ($response['http_code'] === 200) {
                    $totalSuccessCount++;
                }
            }
        }
        
        $endTime = microtime(true);
        
        $this->testResults['search'] = [
            'test_name' => 'Search Performance',
            'iterations' => $totalIterations,
            'success_count' => $totalSuccessCount,
            'success_rate' => ($totalSuccessCount / $totalIterations) * 100,
            'total_time' => $endTime - $startTime,
            'avg_response_time' => $totalResponseTime / $totalIterations,
            'requests_per_second' => $totalIterations / ($endTime - $startTime)
        ];
        
        echo "Search test completed\n\n";
    }
    
    /**
     * Test AJAX endpoints performance
     */
    private function testAjaxEndpoints() {
        echo "Testing AJAX Endpoints Performance...\n";
        
        $endpoints = [
            '/ajax/get_order_detail.php?id=1',
            '/ajax/get_calendar_data.php?month=2025-01',
            '/ajax/get_system_stats.php?type=overview',
            '/ajax/notifications.php?action=get_unread'
        ];
        
        foreach ($endpoints as $endpoint) {
            $iterations = 20;
            $startTime = microtime(true);
            $successCount = 0;
            $totalResponseTime = 0;
            
            for ($i = 0; $i < $iterations; $i++) {
                $requestStart = microtime(true);
                $response = $this->makeRequest($endpoint);
                $requestEnd = microtime(true);
                
                $responseTime = $requestEnd - $requestStart;
                $totalResponseTime += $responseTime;
                
                if ($response['http_code'] === 200) {
                    $successCount++;
                }
            }
            
            $endTime = microtime(true);
            
            $testName = 'AJAX - ' . basename($endpoint, '.php');
            $this->testResults['ajax_' . basename($endpoint, '.php')] = [
                'test_name' => $testName,
                'iterations' => $iterations,
                'success_count' => $successCount,
                'success_rate' => ($successCount / $iterations) * 100,
                'total_time' => $endTime - $startTime,
                'avg_response_time' => $totalResponseTime / $iterations,
                'requests_per_second' => $iterations / ($endTime - $startTime)
            ];
        }
        
        echo "AJAX endpoints tests completed\n\n";
    }
    
    /**
     * Test database query performance
     */
    private function testDatabaseQueries() {
        echo "Testing Database Query Performance...\n";
        
        // This would require direct database access
        // For now, we'll test through endpoints that hit the database heavily
        
        $dbIntensiveEndpoints = [
            '/reports/index.php',
            '/admin/system_activity.php',
            '/search/index.php'
        ];
        
        foreach ($dbIntensiveEndpoints as $endpoint) {
            $iterations = 15;
            $startTime = microtime(true);
            $successCount = 0;
            $totalResponseTime = 0;
            
            for ($i = 0; $i < $iterations; $i++) {
                $requestStart = microtime(true);
                $response = $this->makeRequest($endpoint);
                $requestEnd = microtime(true);
                
                $responseTime = $requestEnd - $requestStart;
                $totalResponseTime += $responseTime;
                
                if ($response['http_code'] === 200) {
                    $successCount++;
                }
            }
            
            $endTime = microtime(true);
            
            $testName = 'DB Heavy - ' . basename($endpoint, '.php');
            $this->testResults['db_' . basename($endpoint, '.php')] = [
                'test_name' => $testName,
                'iterations' => $iterations,
                'success_count' => $successCount,
                'success_rate' => ($successCount / $iterations) * 100,
                'total_time' => $endTime - $startTime,
                'avg_response_time' => $totalResponseTime / $iterations,
                'requests_per_second' => $iterations / ($endTime - $startTime)
            ];
        }
        
        echo "Database query tests completed\n\n";
    }
    
    /**
     * Make HTTP request
     */
    private function makeRequest($endpoint, $method = 'GET', $data = null) {
        $url = $this->baseUrl . $endpoint;
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        
        // Include session cookie if available
        if ($this->sessionCookie) {
            curl_setopt($ch, CURLOPT_COOKIE, $this->sessionCookie);
        }
        
        // Capture cookies
        curl_setopt($ch, CURLOPT_HEADER, true);
        
        if ($method === 'POST' && $data) {
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
        }
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        
        // Extract cookies for session management
        if (preg_match('/Set-Cookie: ([^;]+)/', $response, $matches)) {
            $this->sessionCookie = $matches[1];
        }
        
        curl_close($ch);
        
        return [
            'response' => $response,
            'http_code' => $httpCode
        ];
    }
    
    /**
     * Generate performance report
     */
    private function generateReport() {
        echo "Generating Performance Report...\n\n";
        
        $report = "=== CIG Realizace Performance Test Report ===\n";
        $report .= "Generated: " . date('Y-m-d H:i:s') . "\n";
        $report .= "Base URL: {$this->baseUrl}\n\n";
        
        $totalRequests = 0;
        $totalSuccessful = 0;
        $overallStartTime = microtime(true);
        
        foreach ($this->testResults as $test) {
            $report .= "Test: {$test['test_name']}\n";
            $report .= "  Iterations: {$test['iterations']}\n";
            $report .= "  Success Rate: " . number_format($test['success_rate'], 2) . "%\n";
            $report .= "  Average Response Time: " . number_format($test['avg_response_time'] * 1000, 2) . " ms\n";
            $report .= "  Requests per Second: " . number_format($test['requests_per_second'], 2) . "\n";
            $report .= "  Total Time: " . number_format($test['total_time'], 2) . " seconds\n\n";
            
            $totalRequests += $test['iterations'];
            $totalSuccessful += $test['success_count'];
        }
        
        $overallSuccessRate = ($totalSuccessful / $totalRequests) * 100;
        
        $report .= "=== Overall Summary ===\n";
        $report .= "Total Requests: $totalRequests\n";
        $report .= "Successful Requests: $totalSuccessful\n";
        $report .= "Overall Success Rate: " . number_format($overallSuccessRate, 2) . "%\n\n";
        
        // Performance benchmarks
        $report .= "=== Performance Benchmarks ===\n";
        foreach ($this->testResults as $test) {
            $avgMs = $test['avg_response_time'] * 1000;
            $status = $avgMs < 500 ? 'EXCELLENT' : ($avgMs < 1000 ? 'GOOD' : ($avgMs < 2000 ? 'ACCEPTABLE' : 'NEEDS IMPROVEMENT'));
            $report .= "{$test['test_name']}: " . number_format($avgMs, 2) . " ms - $status\n";
        }
        
        echo $report;
        
        // Save report to file
        $filename = 'performance_report_' . date('Y-m-d_H-i-s') . '.txt';
        file_put_contents($filename, $report);
        echo "\nReport saved to: $filename\n";
        
        return $this->testResults;
    }
}

// Run the test if called directly
if (php_sapi_name() === 'cli') {
    $baseUrl = $argv[1] ?? 'http://localhost:8000';
    $loadTest = new LoadTest($baseUrl);
    $results = $loadTest->runLoadTest();
    
    // Exit with appropriate code
    $overallSuccess = true;
    foreach ($results as $test) {
        if ($test['success_rate'] < 95 || $test['avg_response_time'] > 2) {
            $overallSuccess = false;
            break;
        }
    }
    
    exit($overallSuccess ? 0 : 1);
}
?>
