<?php
/**
 * CIG Realizace - Comprehensive System Test
 * End-to-end functional testing of the complete system
 */

class SystemTest {
    private $baseUrl;
    private $testResults = [];
    private $sessionCookie = '';
    
    public function __construct($baseUrl = 'http://localhost:8000') {
        $this->baseUrl = rtrim($baseUrl, '/');
    }
    
    /**
     * Run complete system test suite
     */
    public function runSystemTests() {
        echo "Starting Comprehensive System Tests...\n";
        echo "Base URL: {$this->baseUrl}\n\n";
        
        // Test sequence
        $this->testDatabaseConnection();
        $this->testAuthentication();
        $this->testDashboard();
        $this->testOrderManagement();
        $this->testCalendarView();
        $this->testSearchFunctionality();
        $this->testNotifications();
        $this->testReports();
        $this->testEmailSystem();
        $this->testAccessibility();
        $this->testSecurity();
        
        return $this->generateTestReport();
    }
    
    /**
     * Test database connection and basic queries
     */
    private function testDatabaseConnection() {
        echo "Testing Database Connection...\n";
        
        try {
            // Test basic database connectivity through the application
            $response = $this->makeRequest('/install.php');
            
            if ($response['http_code'] === 200) {
                $this->addTestResult('Database', 'Connection Test', true, 'Database accessible');
            } else {
                $this->addTestResult('Database', 'Connection Test', false, 'HTTP ' . $response['http_code']);
            }
            
        } catch (Exception $e) {
            $this->addTestResult('Database', 'Connection Test', false, $e->getMessage());
        }
        
        echo "Database tests completed.\n\n";
    }
    
    /**
     * Test authentication system
     */
    private function testAuthentication() {
        echo "Testing Authentication System...\n";
        
        // Test login page access
        $response = $this->makeRequest('/index.php');
        $this->addTestResult('Auth', 'Login Page Access', 
            $response['http_code'] === 200, 
            'HTTP ' . $response['http_code']);
        
        // Test successful login
        $loginData = [
            'username' => 'admin',
            'password' => 'admin123'
        ];
        
        $response = $this->makeRequest('/index.php', 'POST', $loginData);
        $loginSuccess = ($response['http_code'] === 200 || $response['http_code'] === 302);
        $this->addTestResult('Auth', 'Admin Login', $loginSuccess, 
            $loginSuccess ? 'Login successful' : 'Login failed');
        
        // Test failed login
        $badLoginData = [
            'username' => 'admin',
            'password' => 'wrongpassword'
        ];
        
        $response = $this->makeRequest('/index.php', 'POST', $badLoginData);
        $this->addTestResult('Auth', 'Failed Login Handling', 
            $response['http_code'] === 200, 
            'Failed login properly handled');
        
        // Test logout
        if ($loginSuccess) {
            $response = $this->makeRequest('/logout.php');
            $this->addTestResult('Auth', 'Logout', 
                ($response['http_code'] === 200 || $response['http_code'] === 302), 
                'Logout successful');
            
            // Re-login for subsequent tests
            $this->makeRequest('/index.php', 'POST', $loginData);
        }
        
        echo "Authentication tests completed.\n\n";
    }
    
    /**
     * Test dashboard functionality
     */
    private function testDashboard() {
        echo "Testing Dashboard...\n";
        
        $response = $this->makeRequest('/dashboard.php');
        $this->addTestResult('Dashboard', 'Page Load', 
            $response['http_code'] === 200, 
            'HTTP ' . $response['http_code']);
        
        // Check for key dashboard elements
        if ($response['http_code'] === 200) {
            $content = $response['response'];
            
            $this->addTestResult('Dashboard', 'Statistics Cards', 
                strpos($content, 'stat-card') !== false, 
                'Statistics cards present');
            
            $this->addTestResult('Dashboard', 'Navigation Menu', 
                strpos($content, 'navbar') !== false, 
                'Navigation menu present');
            
            $this->addTestResult('Dashboard', 'Notifications', 
                strpos($content, 'notificationsDropdown') !== false, 
                'Notifications dropdown present');
        }
        
        echo "Dashboard tests completed.\n\n";
    }
    
    /**
     * Test order management functionality
     */
    private function testOrderManagement() {
        echo "Testing Order Management...\n";
        
        // Test pending orders page
        $response = $this->makeRequest('/orders/pending.php');
        $this->addTestResult('Orders', 'Pending Orders Page', 
            $response['http_code'] === 200, 
            'HTTP ' . $response['http_code']);
        
        // Test all orders page
        $response = $this->makeRequest('/orders/index.php');
        $this->addTestResult('Orders', 'All Orders Page', 
            $response['http_code'] === 200, 
            'HTTP ' . $response['http_code']);
        
        // Test order detail AJAX endpoint
        $response = $this->makeRequest('/ajax/get_order_detail.php?id=1');
        $this->addTestResult('Orders', 'Order Detail AJAX', 
            $response['http_code'] === 200, 
            'AJAX endpoint accessible');
        
        // Test order history
        $response = $this->makeRequest('/orders/history.php?id=1');
        $this->addTestResult('Orders', 'Order History', 
            $response['http_code'] === 200, 
            'Order history accessible');
        
        echo "Order management tests completed.\n\n";
    }
    
    /**
     * Test calendar view
     */
    private function testCalendarView() {
        echo "Testing Calendar View...\n";
        
        $response = $this->makeRequest('/calendar/index.php');
        $this->addTestResult('Calendar', 'Calendar Page', 
            $response['http_code'] === 200, 
            'HTTP ' . $response['http_code']);
        
        // Test calendar data AJAX
        $response = $this->makeRequest('/ajax/get_calendar_data.php?month=2025-01');
        $this->addTestResult('Calendar', 'Calendar Data AJAX', 
            $response['http_code'] === 200, 
            'Calendar data endpoint accessible');
        
        echo "Calendar tests completed.\n\n";
    }
    
    /**
     * Test search functionality
     */
    private function testSearchFunctionality() {
        echo "Testing Search Functionality...\n";
        
        // Test search page
        $response = $this->makeRequest('/search/index.php');
        $this->addTestResult('Search', 'Search Page', 
            $response['http_code'] === 200, 
            'HTTP ' . $response['http_code']);
        
        // Test search suggestions
        $response = $this->makeRequest('/ajax/search_suggestions.php?q=test');
        $this->addTestResult('Search', 'Search Suggestions', 
            $response['http_code'] === 200, 
            'Search suggestions endpoint accessible');
        
        echo "Search tests completed.\n\n";
    }
    
    /**
     * Test notifications system
     */
    private function testNotifications() {
        echo "Testing Notifications System...\n";
        
        // Test notifications AJAX endpoint
        $response = $this->makeRequest('/ajax/notifications.php?action=get_unread');
        $this->addTestResult('Notifications', 'Get Notifications', 
            $response['http_code'] === 200, 
            'Notifications endpoint accessible');
        
        // Test notification settings page
        $response = $this->makeRequest('/user/notification_settings.php');
        $this->addTestResult('Notifications', 'Settings Page', 
            $response['http_code'] === 200, 
            'Notification settings accessible');
        
        echo "Notifications tests completed.\n\n";
    }
    
    /**
     * Test reports functionality
     */
    private function testReports() {
        echo "Testing Reports...\n";
        
        // Test reports main page
        $response = $this->makeRequest('/reports/index.php');
        $this->addTestResult('Reports', 'Reports Page', 
            $response['http_code'] === 200, 
            'HTTP ' . $response['http_code']);
        
        // Test system activity
        $response = $this->makeRequest('/admin/system_activity.php');
        $this->addTestResult('Reports', 'System Activity', 
            $response['http_code'] === 200, 
            'System activity accessible');
        
        // Test system stats AJAX
        $response = $this->makeRequest('/ajax/get_system_stats.php?type=overview');
        $this->addTestResult('Reports', 'System Stats AJAX', 
            $response['http_code'] === 200, 
            'System stats endpoint accessible');
        
        echo "Reports tests completed.\n\n";
    }
    
    /**
     * Test email system
     */
    private function testEmailSystem() {
        echo "Testing Email System...\n";
        
        // Test email queue page
        $response = $this->makeRequest('/admin/email_queue.php');
        $this->addTestResult('Email', 'Email Queue Page', 
            $response['http_code'] === 200, 
            'Email queue accessible');
        
        // Test email notifications page
        $response = $this->makeRequest('/admin/notifications.php');
        $this->addTestResult('Email', 'Email Notifications', 
            $response['http_code'] === 200, 
            'Email notifications accessible');
        
        echo "Email system tests completed.\n\n";
    }
    
    /**
     * Test accessibility features
     */
    private function testAccessibility() {
        echo "Testing Accessibility Features...\n";
        
        // Test if accessibility CSS is loaded
        $response = $this->makeRequest('/assets/css/accessibility.css');
        $this->addTestResult('Accessibility', 'CSS File', 
            $response['http_code'] === 200, 
            'Accessibility CSS accessible');
        
        // Test if responsive CSS is loaded
        $response = $this->makeRequest('/assets/css/responsive.css');
        $this->addTestResult('Accessibility', 'Responsive CSS', 
            $response['http_code'] === 200, 
            'Responsive CSS accessible');
        
        // Test UI enhancements JS
        $response = $this->makeRequest('/assets/js/ui-enhancements.js');
        $this->addTestResult('Accessibility', 'UI Enhancements JS', 
            $response['http_code'] === 200, 
            'UI enhancements JS accessible');
        
        echo "Accessibility tests completed.\n\n";
    }
    
    /**
     * Test security features
     */
    private function testSecurity() {
        echo "Testing Security Features...\n";
        
        // Test that sensitive files are not accessible
        $sensitiveFiles = [
            '/config/database.php',
            '/config/email.php',
            '/.env',
            '/logs/php_errors.log'
        ];
        
        foreach ($sensitiveFiles as $file) {
            $response = $this->makeRequest($file);
            $this->addTestResult('Security', 'File Protection: ' . basename($file), 
                $response['http_code'] !== 200, 
                $response['http_code'] === 200 ? 'File exposed!' : 'File protected');
        }
        
        // Test CSRF protection (should fail without token)
        $response = $this->makeRequest('/ajax/update_preview_status.php', 'POST', [
            'order_id' => 1,
            'status' => 'approved'
        ]);
        
        $this->addTestResult('Security', 'CSRF Protection', 
            $response['http_code'] !== 200 || strpos($response['response'], 'error') !== false, 
            'CSRF protection active');
        
        echo "Security tests completed.\n\n";
    }
    
    /**
     * Add test result
     */
    private function addTestResult($category, $test, $passed, $message) {
        $this->testResults[] = [
            'category' => $category,
            'test' => $test,
            'passed' => $passed,
            'message' => $message,
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        $status = $passed ? '✓ PASS' : '✗ FAIL';
        echo "  $status: $test - $message\n";
    }
    
    /**
     * Make HTTP request
     */
    private function makeRequest($endpoint, $method = 'GET', $data = null) {
        $url = $this->baseUrl . $endpoint;
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_HEADER, true);
        
        // Include session cookie if available
        if ($this->sessionCookie) {
            curl_setopt($ch, CURLOPT_COOKIE, $this->sessionCookie);
        }
        
        if ($method === 'POST' && $data) {
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
        }
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        
        // Extract cookies for session management
        if (preg_match('/Set-Cookie: ([^;]+)/', $response, $matches)) {
            $this->sessionCookie = $matches[1];
        }
        
        curl_close($ch);
        
        return [
            'response' => $response,
            'http_code' => $httpCode
        ];
    }
    
    /**
     * Generate comprehensive test report
     */
    private function generateTestReport() {
        echo "Generating Test Report...\n\n";
        
        $totalTests = count($this->testResults);
        $passedTests = count(array_filter($this->testResults, function($test) {
            return $test['passed'];
        }));
        $failedTests = $totalTests - $passedTests;
        $successRate = $totalTests > 0 ? ($passedTests / $totalTests) * 100 : 0;
        
        $report = "=== CIG Realizace System Test Report ===\n";
        $report .= "Generated: " . date('Y-m-d H:i:s') . "\n";
        $report .= "Base URL: {$this->baseUrl}\n\n";
        
        $report .= "=== Summary ===\n";
        $report .= "Total Tests: $totalTests\n";
        $report .= "Passed: $passedTests\n";
        $report .= "Failed: $failedTests\n";
        $report .= "Success Rate: " . number_format($successRate, 2) . "%\n\n";
        
        // Group results by category
        $categories = [];
        foreach ($this->testResults as $result) {
            $categories[$result['category']][] = $result;
        }
        
        $report .= "=== Detailed Results ===\n";
        foreach ($categories as $category => $tests) {
            $report .= "\n$category:\n";
            foreach ($tests as $test) {
                $status = $test['passed'] ? 'PASS' : 'FAIL';
                $report .= "  [$status] {$test['test']}: {$test['message']}\n";
            }
        }
        
        // Overall assessment
        $report .= "\n=== Assessment ===\n";
        if ($successRate >= 95) {
            $report .= "EXCELLENT: System is functioning very well.\n";
        } elseif ($successRate >= 85) {
            $report .= "GOOD: System is functioning well with minor issues.\n";
        } elseif ($successRate >= 70) {
            $report .= "ACCEPTABLE: System is functional but needs attention.\n";
        } else {
            $report .= "NEEDS IMPROVEMENT: System has significant issues.\n";
        }
        
        echo $report;
        
        // Save report to file
        $filename = 'system_test_report_' . date('Y-m-d_H-i-s') . '.txt';
        file_put_contents($filename, $report);
        echo "\nReport saved to: $filename\n";
        
        return [
            'total_tests' => $totalTests,
            'passed_tests' => $passedTests,
            'failed_tests' => $failedTests,
            'success_rate' => $successRate,
            'results' => $this->testResults
        ];
    }
}

// Run the test if called directly
if (php_sapi_name() === 'cli') {
    $baseUrl = $argv[1] ?? 'http://localhost:8000';
    $systemTest = new SystemTest($baseUrl);
    $results = $systemTest->runSystemTests();
    
    // Exit with appropriate code
    exit($results['success_rate'] >= 85 ? 0 : 1);
}
?>
