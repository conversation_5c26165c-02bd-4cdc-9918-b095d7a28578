<?php
/**
 * CIG Realizace - Order Unit Tests
 * Unit tests for order management functionality
 */

require_once __DIR__ . '/../../includes/session.php';
require_once __DIR__ . '/../../includes/order_functions.php';
require_once __DIR__ . '/../../classes/HistoryLogger.php';

use PHPUnit\Framework\TestCase;

class OrderTest extends TestCase {
    private $pdo;
    private $testOrderId;
    private $testUserId = 1; // Admin user
    
    protected function setUp(): void {
        // Setup test database connection
        $config = require __DIR__ . '/../../config/database.php';
        $this->pdo = new PDO(
            "sqlite::memory:",
            null,
            null,
            [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
        );
        
        // Create test tables
        $this->createTestTables();
        $this->insertTestData();
    }
    
    protected function tearDown(): void {
        $this->pdo = null;
    }
    
    private function createTestTables() {
        // Create orders table
        $this->pdo->exec("
            CREATE TABLE orders (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                order_code VARCHAR(50) NOT NULL UNIQUE,
                sales_rep VARCHAR(10) NOT NULL,
                order_date DATE NOT NULL,
                preview_status VARCHAR(20) DEFAULT 'not_created',
                preview_approved_date DATE NULL,
                expected_delivery_date DATE NULL,
                order_status VARCHAR(20) DEFAULT 'pending',
                completed_date DATE NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ");
        
        // Create order_items table
        $this->pdo->exec("
            CREATE TABLE order_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                order_id INTEGER NOT NULL,
                catalog_code VARCHAR(50) NOT NULL,
                quantity INTEGER NOT NULL DEFAULT 1,
                technology VARCHAR(100) NULL,
                inventory_status VARCHAR(20) DEFAULT 'pending',
                is_relevant BOOLEAN DEFAULT 1,
                goods_ordered_date DATE NULL,
                goods_stocked_date DATE NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE
            )
        ");
        
        // Create order_history table
        $this->pdo->exec("
            CREATE TABLE order_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                order_id INTEGER NOT NULL,
                user_id INTEGER NOT NULL,
                action_type VARCHAR(50) NOT NULL,
                description TEXT NOT NULL,
                additional_data TEXT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE
            )
        ");
        
        // Create users table
        $this->pdo->exec("
            CREATE TABLE users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username VARCHAR(50) NOT NULL UNIQUE,
                full_name VARCHAR(100) NOT NULL,
                role VARCHAR(20) NOT NULL,
                is_active BOOLEAN DEFAULT 1
            )
        ");
    }
    
    private function insertTestData() {
        // Insert test user
        $this->pdo->exec("
            INSERT INTO users (id, username, full_name, role, is_active) 
            VALUES (1, 'admin', 'Test Admin', 'admin', 1)
        ");
    }
    
    public function testCreateOrder() {
        $orderData = [
            'order_code' => '25TEST-001',
            'sales_rep' => 'VP',
            'order_date' => '2025-01-01'
        ];
        
        $stmt = $this->pdo->prepare("
            INSERT INTO orders (order_code, sales_rep, order_date) 
            VALUES (:order_code, :sales_rep, :order_date)
        ");
        
        $result = $stmt->execute($orderData);
        $this->assertTrue($result);
        
        $orderId = $this->pdo->lastInsertId();
        $this->assertIsNumeric($orderId);
        $this->assertGreaterThan(0, $orderId);
        
        // Verify order was created
        $stmt = $this->pdo->prepare("SELECT * FROM orders WHERE id = ?");
        $stmt->execute([$orderId]);
        $order = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $this->assertEquals($orderData['order_code'], $order['order_code']);
        $this->assertEquals($orderData['sales_rep'], $order['sales_rep']);
        $this->assertEquals($orderData['order_date'], $order['order_date']);
        $this->assertEquals('not_created', $order['preview_status']);
        
        $this->testOrderId = $orderId;
    }
    
    public function testUpdatePreviewStatus() {
        // First create an order
        $this->testCreateOrder();
        
        $stmt = $this->pdo->prepare("
            UPDATE orders 
            SET preview_status = :status, 
                preview_approved_date = :approved_date,
                expected_delivery_date = :delivery_date
            WHERE id = :id
        ");
        
        $approvedDate = '2025-01-15';
        $deliveryDate = '2025-01-29'; // 14 days later
        
        $result = $stmt->execute([
            'status' => 'approved',
            'approved_date' => $approvedDate,
            'delivery_date' => $deliveryDate,
            'id' => $this->testOrderId
        ]);
        
        $this->assertTrue($result);
        
        // Verify update
        $stmt = $this->pdo->prepare("SELECT * FROM orders WHERE id = ?");
        $stmt->execute([$this->testOrderId]);
        $order = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $this->assertEquals('approved', $order['preview_status']);
        $this->assertEquals($approvedDate, $order['preview_approved_date']);
        $this->assertEquals($deliveryDate, $order['expected_delivery_date']);
    }
    
    public function testCalculateDeliveryDate() {
        $approvalDate = '2025-01-15';
        $expectedDate = date('Y-m-d', strtotime($approvalDate . ' + 14 days'));
        
        $this->assertEquals('2025-01-29', $expectedDate);
        
        // Test with weekend
        $fridayDate = '2025-01-17'; // Friday
        $mondayDate = date('Y-m-d', strtotime($fridayDate . ' + 14 days'));
        $this->assertEquals('2025-01-31', $mondayDate);
    }
    
    public function testAddOrderItem() {
        // First create an order
        $this->testCreateOrder();
        
        $itemData = [
            'order_id' => $this->testOrderId,
            'catalog_code' => 'TEST-CATALOG-001',
            'quantity' => 5,
            'technology' => 'Digital Print'
        ];
        
        $stmt = $this->pdo->prepare("
            INSERT INTO order_items (order_id, catalog_code, quantity, technology) 
            VALUES (:order_id, :catalog_code, :quantity, :technology)
        ");
        
        $result = $stmt->execute($itemData);
        $this->assertTrue($result);
        
        $itemId = $this->pdo->lastInsertId();
        $this->assertIsNumeric($itemId);
        $this->assertGreaterThan(0, $itemId);
        
        // Verify item was created
        $stmt = $this->pdo->prepare("SELECT * FROM order_items WHERE id = ?");
        $stmt->execute([$itemId]);
        $item = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $this->assertEquals($itemData['order_id'], $item['order_id']);
        $this->assertEquals($itemData['catalog_code'], $item['catalog_code']);
        $this->assertEquals($itemData['quantity'], $item['quantity']);
        $this->assertEquals($itemData['technology'], $item['technology']);
        $this->assertEquals('pending', $item['inventory_status']);
        $this->assertEquals(1, $item['is_relevant']);
    }
    
    public function testUpdateItemTechnology() {
        // First create order and item
        $this->testCreateOrder();
        $this->testAddOrderItem();
        
        $stmt = $this->pdo->prepare("SELECT id FROM order_items WHERE order_id = ? LIMIT 1");
        $stmt->execute([$this->testOrderId]);
        $item = $stmt->fetch(PDO::FETCH_ASSOC);
        $itemId = $item['id'];
        
        $newTechnology = 'UV Print';
        
        $stmt = $this->pdo->prepare("UPDATE order_items SET technology = ? WHERE id = ?");
        $result = $stmt->execute([$newTechnology, $itemId]);
        
        $this->assertTrue($result);
        
        // Verify update
        $stmt = $this->pdo->prepare("SELECT technology FROM order_items WHERE id = ?");
        $stmt->execute([$itemId]);
        $item = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $this->assertEquals($newTechnology, $item['technology']);
    }
    
    public function testToggleItemRelevance() {
        // First create order and item
        $this->testCreateOrder();
        $this->testAddOrderItem();
        
        $stmt = $this->pdo->prepare("SELECT id FROM order_items WHERE order_id = ? LIMIT 1");
        $stmt->execute([$this->testOrderId]);
        $item = $stmt->fetch(PDO::FETCH_ASSOC);
        $itemId = $item['id'];
        
        // Toggle to irrelevant
        $stmt = $this->pdo->prepare("UPDATE order_items SET is_relevant = 0 WHERE id = ?");
        $result = $stmt->execute([$itemId]);
        
        $this->assertTrue($result);
        
        // Verify update
        $stmt = $this->pdo->prepare("SELECT is_relevant FROM order_items WHERE id = ?");
        $stmt->execute([$itemId]);
        $item = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $this->assertEquals(0, $item['is_relevant']);
        
        // Toggle back to relevant
        $stmt = $this->pdo->prepare("UPDATE order_items SET is_relevant = 1 WHERE id = ?");
        $result = $stmt->execute([$itemId]);
        
        $this->assertTrue($result);
        
        // Verify update
        $stmt = $this->pdo->prepare("SELECT is_relevant FROM order_items WHERE id = ?");
        $stmt->execute([$itemId]);
        $item = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $this->assertEquals(1, $item['is_relevant']);
    }
    
    public function testCompleteOrder() {
        // First create an order
        $this->testCreateOrder();
        
        $completedDate = date('Y-m-d');
        
        $stmt = $this->pdo->prepare("
            UPDATE orders 
            SET order_status = 'completed', completed_date = ? 
            WHERE id = ?
        ");
        
        $result = $stmt->execute([$completedDate, $this->testOrderId]);
        $this->assertTrue($result);
        
        // Verify completion
        $stmt = $this->pdo->prepare("SELECT * FROM orders WHERE id = ?");
        $stmt->execute([$this->testOrderId]);
        $order = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $this->assertEquals('completed', $order['order_status']);
        $this->assertEquals($completedDate, $order['completed_date']);
    }
    
    public function testLogOrderHistory() {
        // First create an order
        $this->testCreateOrder();
        
        $historyData = [
            'order_id' => $this->testOrderId,
            'user_id' => $this->testUserId,
            'action_type' => 'preview_status_changed',
            'description' => 'Stav náhledu změněn na: schváleno'
        ];
        
        $stmt = $this->pdo->prepare("
            INSERT INTO order_history (order_id, user_id, action_type, description) 
            VALUES (:order_id, :user_id, :action_type, :description)
        ");
        
        $result = $stmt->execute($historyData);
        $this->assertTrue($result);
        
        $historyId = $this->pdo->lastInsertId();
        $this->assertIsNumeric($historyId);
        $this->assertGreaterThan(0, $historyId);
        
        // Verify history was logged
        $stmt = $this->pdo->prepare("SELECT * FROM order_history WHERE id = ?");
        $stmt->execute([$historyId]);
        $history = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $this->assertEquals($historyData['order_id'], $history['order_id']);
        $this->assertEquals($historyData['user_id'], $history['user_id']);
        $this->assertEquals($historyData['action_type'], $history['action_type']);
        $this->assertEquals($historyData['description'], $history['description']);
    }
    
    public function testGetOrderStatistics() {
        // Create multiple test orders with different statuses
        $orders = [
            ['25TEST-001', 'VP', '2025-01-01', 'not_created'],
            ['25TEST-002', 'J', '2025-01-02', 'sent_to_client'],
            ['25TEST-003', 'NK', '2025-01-03', 'approved'],
            ['25TEST-004', 'MR', '2025-01-04', 'approved']
        ];
        
        foreach ($orders as $order) {
            $stmt = $this->pdo->prepare("
                INSERT INTO orders (order_code, sales_rep, order_date, preview_status) 
                VALUES (?, ?, ?, ?)
            ");
            $stmt->execute($order);
        }
        
        // Get statistics
        $stmt = $this->pdo->query("
            SELECT 
                COUNT(*) as total_orders,
                SUM(CASE WHEN preview_status = 'not_created' THEN 1 ELSE 0 END) as not_created,
                SUM(CASE WHEN preview_status = 'sent_to_client' THEN 1 ELSE 0 END) as sent_to_client,
                SUM(CASE WHEN preview_status = 'approved' THEN 1 ELSE 0 END) as approved
            FROM orders
        ");
        
        $stats = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $this->assertEquals(4, $stats['total_orders']);
        $this->assertEquals(1, $stats['not_created']);
        $this->assertEquals(1, $stats['sent_to_client']);
        $this->assertEquals(2, $stats['approved']);
    }
}
