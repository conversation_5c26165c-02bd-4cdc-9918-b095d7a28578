<?php
/**
 * CIG Realizace - Authentication Integration Tests
 * Integration tests for authentication and authorization system
 */

require_once __DIR__ . '/../../classes/Auth.php';
require_once __DIR__ . '/../../classes/User.php';
require_once __DIR__ . '/../../includes/session.php';

use PHPUnit\Framework\TestCase;

class AuthTest extends TestCase {
    private $auth;
    private $userManager;
    private $testUserId;
    private $pdo;
    
    protected function setUp(): void {
        // Start session for testing
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        // Setup test database connection
        $this->pdo = new PDO(
            "sqlite::memory:",
            null,
            null,
            [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
        );
        
        // Create test tables
        $this->createTestTables();
        
        // Initialize classes with test database
        $this->auth = new Auth($this->pdo);
        $this->userManager = new User($this->pdo);
        
        // Create test user
        $this->createTestUser();
    }
    
    protected function tearDown(): void {
        // Clean up session
        $_SESSION = [];
        
        // Clean up test user
        if ($this->testUserId && $this->userManager) {
            $this->userManager->deleteUser($this->testUserId);
        }
        
        $this->pdo = null;
    }
    
    private function createTestTables() {
        // Create users table
        $this->pdo->exec("
            CREATE TABLE users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username VARCHAR(50) NOT NULL UNIQUE,
                password VARCHAR(255) NOT NULL,
                email VARCHAR(100) NOT NULL UNIQUE,
                full_name VARCHAR(100) NOT NULL,
                role VARCHAR(20) NOT NULL DEFAULT 'obchodnik',
                is_active BOOLEAN DEFAULT 1,
                last_login TIMESTAMP NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ");
        
        // Create login_attempts table for security
        $this->pdo->exec("
            CREATE TABLE login_attempts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username VARCHAR(50) NOT NULL,
                ip_address VARCHAR(45) NOT NULL,
                success BOOLEAN DEFAULT 0,
                attempted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ");
    }
    
    private function createTestUser() {
        $userData = [
            'username' => 'test_user',
            'password' => 'test_password123',
            'email' => '<EMAIL>',
            'full_name' => 'Test User',
            'role' => 'obchodnik'
        ];
        
        $this->testUserId = $this->userManager->createUser($userData);
        $this->assertIsNumeric($this->testUserId);
        $this->assertGreaterThan(0, $this->testUserId);
    }
    
    public function testSuccessfulLogin() {
        $result = $this->auth->login('test_user', 'test_password123');
        $this->assertTrue($result);
        $this->assertTrue($this->auth->isLoggedIn());
        
        // Check session data
        $this->assertEquals($this->testUserId, $_SESSION['user_id']);
        $this->assertEquals('test_user', $_SESSION['username']);
        $this->assertEquals('obchodnik', $_SESSION['role']);
        $this->assertEquals('Test User', $_SESSION['full_name']);
    }
    
    public function testFailedLoginWrongPassword() {
        $result = $this->auth->login('test_user', 'wrong_password');
        $this->assertFalse($result);
        $this->assertFalse($this->auth->isLoggedIn());
        
        // Check session is clean
        $this->assertArrayNotHasKey('user_id', $_SESSION);
    }
    
    public function testFailedLoginNonexistentUser() {
        $result = $this->auth->login('nonexistent_user', 'any_password');
        $this->assertFalse($result);
        $this->assertFalse($this->auth->isLoggedIn());
    }
    
    public function testLoginWithInactiveUser() {
        // Deactivate test user
        $stmt = $this->pdo->prepare("UPDATE users SET is_active = 0 WHERE id = ?");
        $stmt->execute([$this->testUserId]);
        
        $result = $this->auth->login('test_user', 'test_password123');
        $this->assertFalse($result);
        $this->assertFalse($this->auth->isLoggedIn());
    }
    
    public function testLogout() {
        // First login
        $this->auth->login('test_user', 'test_password123');
        $this->assertTrue($this->auth->isLoggedIn());
        
        // Then logout
        $this->auth->logout();
        $this->assertFalse($this->auth->isLoggedIn());
        
        // Check session is clean
        $this->assertEmpty($_SESSION);
    }
    
    public function testRoleBasedAccess() {
        $this->auth->login('test_user', 'test_password123');
        
        // Test obchodnik permissions
        $this->assertTrue($this->auth->hasPermission('view_orders'));
        $this->assertTrue($this->auth->hasPermission('edit_orders'));
        $this->assertFalse($this->auth->hasPermission('admin_access'));
        $this->assertFalse($this->auth->hasPermission('manage_users'));
    }
    
    public function testAdminRoleAccess() {
        // Create admin user
        $adminData = [
            'username' => 'admin_user',
            'password' => 'admin_password123',
            'email' => '<EMAIL>',
            'full_name' => 'Admin User',
            'role' => 'admin'
        ];
        
        $adminId = $this->userManager->createUser($adminData);
        $this->auth->login('admin_user', 'admin_password123');
        
        // Test admin permissions
        $this->assertTrue($this->auth->hasPermission('view_orders'));
        $this->assertTrue($this->auth->hasPermission('edit_orders'));
        $this->assertTrue($this->auth->hasPermission('admin_access'));
        $this->assertTrue($this->auth->hasPermission('manage_users'));
        
        // Clean up
        $this->userManager->deleteUser($adminId);
    }
    
    public function testSessionSecurity() {
        $this->auth->login('test_user', 'test_password123');
        $originalSessionId = session_id();
        
        // Simulate session regeneration (should happen on login)
        session_regenerate_id(true);
        $newSessionId = session_id();
        
        $this->assertNotEquals($originalSessionId, $newSessionId);
        $this->assertTrue($this->auth->isLoggedIn());
    }
    
    public function testSessionTimeout() {
        $this->auth->login('test_user', 'test_password123');
        
        // Simulate expired session
        $_SESSION['last_activity'] = time() - 7200; // 2 hours ago
        
        $isValid = $this->auth->checkSessionTimeout(3600); // 1 hour timeout
        $this->assertFalse($isValid);
        $this->assertFalse($this->auth->isLoggedIn());
    }
    
    public function testPasswordHashing() {
        $password = 'test_password123';
        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
        
        $this->assertNotEquals($password, $hashedPassword);
        $this->assertTrue(password_verify($password, $hashedPassword));
        $this->assertFalse(password_verify('wrong_password', $hashedPassword));
    }
    
    public function testBruteForceProtection() {
        // Simulate multiple failed login attempts
        for ($i = 0; $i < 5; $i++) {
            $this->auth->login('test_user', 'wrong_password');
        }
        
        // Check if account is locked
        $stmt = $this->pdo->prepare("
            SELECT COUNT(*) as attempts 
            FROM login_attempts 
            WHERE username = ? AND success = 0 AND attempted_at > datetime('now', '-15 minutes')
        ");
        $stmt->execute(['test_user']);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $this->assertEquals(5, $result['attempts']);
        
        // Should be locked now
        $result = $this->auth->login('test_user', 'test_password123');
        $this->assertFalse($result); // Should fail due to lockout
    }
    
    public function testCSRFTokenGeneration() {
        $this->auth->login('test_user', 'test_password123');
        
        $token1 = $this->auth->generateCSRFToken();
        $token2 = $this->auth->generateCSRFToken();
        
        $this->assertNotEmpty($token1);
        $this->assertNotEmpty($token2);
        $this->assertEquals($token1, $token2); // Should be same for same session
        $this->assertEquals(64, strlen($token1)); // Should be 64 characters (32 bytes hex)
    }
    
    public function testCSRFTokenValidation() {
        $this->auth->login('test_user', 'test_password123');
        
        $token = $this->auth->generateCSRFToken();
        
        $this->assertTrue($this->auth->validateCSRFToken($token));
        $this->assertFalse($this->auth->validateCSRFToken('invalid_token'));
        $this->assertFalse($this->auth->validateCSRFToken(''));
    }
    
    public function testUserDataRetrieval() {
        $this->auth->login('test_user', 'test_password123');
        
        $userData = $this->auth->getCurrentUser();
        
        $this->assertEquals($this->testUserId, $userData['id']);
        $this->assertEquals('test_user', $userData['username']);
        $this->assertEquals('Test User', $userData['full_name']);
        $this->assertEquals('obchodnik', $userData['role']);
        $this->assertEquals('<EMAIL>', $userData['email']);
    }
    
    public function testLastLoginUpdate() {
        $beforeLogin = date('Y-m-d H:i:s');
        
        $this->auth->login('test_user', 'test_password123');
        
        $stmt = $this->pdo->prepare("SELECT last_login FROM users WHERE id = ?");
        $stmt->execute([$this->testUserId]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $this->assertNotNull($user['last_login']);
        $this->assertGreaterThanOrEqual($beforeLogin, $user['last_login']);
    }
    
    public function testMultipleRolePermissions() {
        // Test different roles
        $roles = [
            'admin' => ['view_orders', 'edit_orders', 'admin_access', 'manage_users'],
            'obchodnik' => ['view_orders', 'edit_orders'],
            'grafik' => ['view_orders', 'edit_previews'],
            'realizator' => ['view_orders', 'edit_production']
        ];
        
        foreach ($roles as $role => $expectedPermissions) {
            // Create user with specific role
            $userData = [
                'username' => $role . '_test',
                'password' => 'password123',
                'email' => $role . '@example.com',
                'full_name' => ucfirst($role) . ' Test',
                'role' => $role
            ];
            
            $userId = $this->userManager->createUser($userData);
            $this->auth->logout();
            $this->auth->login($role . '_test', 'password123');
            
            foreach ($expectedPermissions as $permission) {
                $this->assertTrue(
                    $this->auth->hasPermission($permission),
                    "Role {$role} should have permission {$permission}"
                );
            }
            
            // Clean up
            $this->userManager->deleteUser($userId);
        }
    }
}
