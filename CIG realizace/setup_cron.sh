#!/bin/bash
# CIG Realizace - Email System Cron Setup
# Phase 08

echo "Setting up cron jobs for CIG Realizace Email System..."

# Get the current directory
PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

echo "Project directory: $PROJECT_DIR"

# Make cron scripts executable
chmod +x "$PROJECT_DIR/cron/send_notifications.php"
chmod +x "$PROJECT_DIR/cron/daily_reports.php"

echo "Made cron scripts executable"

# Create cron job entries
CRON_ENTRIES="
# CIG Realizace Email System Cron Jobs
# Process email queue every 5 minutes
*/5 * * * * /usr/bin/php \"$PROJECT_DIR/cron/send_notifications.php\" >> \"$PROJECT_DIR/logs/email_cron.log\" 2>&1

# Send daily reports every day at 8:00 AM
0 8 * * * /usr/bin/php \"$PROJECT_DIR/cron/daily_reports.php\" >> \"$PROJECT_DIR/logs/daily_reports.log\" 2>&1
"

echo "Cron job entries to add:"
echo "$CRON_ENTRIES"

# Check if crontab exists
if crontab -l >/dev/null 2>&1; then
    echo ""
    echo "Current crontab:"
    crontab -l
    echo ""
    echo "To add the email system cron jobs, run:"
    echo "(crontab -l; echo '$CRON_ENTRIES') | crontab -"
else
    echo ""
    echo "No existing crontab found."
    echo "To create crontab with email system jobs, run:"
    echo "echo '$CRON_ENTRIES' | crontab -"
fi

echo ""
echo "Manual setup instructions:"
echo "1. Run 'crontab -e' to edit your crontab"
echo "2. Add the following lines:"
echo "$CRON_ENTRIES"
echo ""
echo "3. Save and exit the editor"
echo ""
echo "To verify cron jobs are installed:"
echo "crontab -l"
echo ""
echo "To view cron logs:"
echo "tail -f $PROJECT_DIR/logs/email_cron.log"
echo "tail -f $PROJECT_DIR/logs/daily_reports.log"
echo ""
echo "Setup complete!"
