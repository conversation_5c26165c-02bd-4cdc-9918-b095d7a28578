<?php
// Include authentication check and notification manager
require_once 'includes/auth_check.php';
require_once 'classes/NotificationManager.php';
require_once 'classes/HistoryLogger.php';

// Get user information
$current_user = getCurrentUser();
$user_id = $current_user['id'];
$username = $current_user['username'];
$role = $current_user['role'];
$full_name = $current_user['full_name'];

// Connect to database
try {
    $pdo = getDbConnection();
    
    // Get basic statistics
    $stats = [];
    
    // Total orders
    $stmt = $pdo->query("SELECT COUNT(*) as total_orders FROM orders");
    $stats['orders'] = $stmt->fetch()['total_orders'] ?? 0;
    
    // Pending orders (not created preview)
    $stmt = $pdo->query("SELECT COUNT(*) as pending_orders FROM orders WHERE status = 'pending'");
    $stats['pending'] = $stmt->fetch()['pending_orders'] ?? 0;

    // In progress orders (preview created but not completed)
    $stmt = $pdo->query("SELECT COUNT(*) as in_progress FROM orders WHERE status = 'in_progress'");
    $stats['in_progress'] = $stmt->fetch()['in_progress'] ?? 0;
    
    // Completed orders
    $stmt = $pdo->query("SELECT COUNT(*) as completed_orders FROM orders WHERE status = 'completed'");
    $stats['completed'] = $stmt->fetch()['completed_orders'] ?? 0;

    // Recent orders (last 10)
    $stmt = $pdo->query("
        SELECT o.order_code, o.order_date, o.status, o.preview_status, o.is_completed,
               o.sales_rep_name
        FROM orders o
        ORDER BY o.created_at DESC
        LIMIT 10
    ");
    $recent_orders = $stmt->fetchAll();

    // Get user notifications
    $notifications = NotificationManager::getUserNotifications($user_id, 5);
    $unread_count = NotificationManager::getUnreadCount($user_id);

    // Get recent activity
    $recent_activity = HistoryLogger::getRecentActivity(10);
    
} catch (PDOException $e) {
    $db_error = $e->getMessage();
}

// Role-based menu items
$menu_items = [
    'admin' => [
        ['icon' => 'fas fa-search', 'title' => 'Pokročilé vyhledávání', 'url' => 'search/index.php', 'desc' => 'Vyhledávání v objednávkách a katalozích'],
        ['icon' => 'fas fa-users', 'title' => 'Správa uživatelů', 'url' => 'admin/users.php', 'desc' => 'Přidávání a úprava uživatelů'],
        ['icon' => 'fas fa-list-alt', 'title' => 'Všechny objednávky', 'url' => 'orders/index.php', 'desc' => 'Kompletní přehled objednávek'],
        ['icon' => 'fas fa-clock', 'title' => 'Čekající objednávky', 'url' => 'orders/pending.php', 'desc' => 'Objednávky čekající na zpracování'],
        ['icon' => 'fas fa-file-import', 'title' => 'Import CSV', 'url' => 'admin/import.php', 'desc' => 'Import objednávek ze souborů'],
        ['icon' => 'fas fa-calendar-alt', 'title' => 'Kalendář', 'url' => 'calendar/index.php', 'desc' => 'Kalendářní pohled na objednávky'],
        ['icon' => 'fas fa-chart-line', 'title' => 'Reporty a statistiky', 'url' => 'reports/index.php', 'desc' => 'Detailní reporty a analýzy'],
        ['icon' => 'fas fa-cog', 'title' => 'Nastavení', 'url' => 'settings.php', 'desc' => 'Konfigurace systému']
    ],
    'obchodnik' => [
        ['icon' => 'fas fa-search', 'title' => 'Pokročilé vyhledávání', 'url' => 'search/index.php', 'desc' => 'Vyhledávání v objednávkách a katalozích'],
        ['icon' => 'fas fa-list-alt', 'title' => 'Moje objednávky', 'url' => 'orders/index.php', 'desc' => 'Objednávky pod mou správou'],
        ['icon' => 'fas fa-clock', 'title' => 'Čekající objednávky', 'url' => 'orders/pending.php', 'desc' => 'Objednávky čekající na zpracování'],
        ['icon' => 'fas fa-eye', 'title' => 'Náhledy', 'url' => 'previews.php', 'desc' => 'Správa náhledů objednávek'],
        ['icon' => 'fas fa-calendar-alt', 'title' => 'Kalendář', 'url' => 'calendar/index.php', 'desc' => 'Plánování objednávek'],
        ['icon' => 'fas fa-file-import', 'title' => 'Import CSV', 'url' => 'import.php', 'desc' => 'Import nových objednávek']
    ],
    'grafik' => [
        ['icon' => 'fas fa-search', 'title' => 'Pokročilé vyhledávání', 'url' => 'search/index.php', 'desc' => 'Vyhledávání v objednávkách a katalozích'],
        ['icon' => 'fas fa-paint-brush', 'title' => 'Grafické práce', 'url' => 'graphics.php', 'desc' => 'Objednávky k vytvoření náhledu'],
        ['icon' => 'fas fa-eye', 'title' => 'Náhledy', 'url' => 'previews.php', 'desc' => 'Správa vytvořených náhledů'],
        ['icon' => 'fas fa-calendar-alt', 'title' => 'Kalendář', 'url' => 'calendar/index.php', 'desc' => 'Plánování grafických prací']
    ],
    'realizator' => [
        ['icon' => 'fas fa-search', 'title' => 'Pokročilé vyhledávání', 'url' => 'search/index.php', 'desc' => 'Vyhledávání v objednávkách a katalozích'],
        ['icon' => 'fas fa-cogs', 'title' => 'Výroba', 'url' => 'production.php', 'desc' => 'Schválené objednávky k výrobě'],
        ['icon' => 'fas fa-check-circle', 'title' => 'Dokončené', 'url' => 'completed.php', 'desc' => 'Dokončené objednávky'],
        ['icon' => 'fas fa-calendar-alt', 'title' => 'Kalendář', 'url' => 'calendar/index.php', 'desc' => 'Plánování výroby']
    ]
];

$current_menu = $menu_items[$role] ?? [];
?>
<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CIG Realizace - Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/responsive.css" rel="stylesheet">
    <link href="assets/css/accessibility.css" rel="stylesheet">
    <style>
        .dashboard-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px 0;
        }
        .stat-card {
            transition: transform 0.3s;
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .stat-card:hover {
            transform: translateY(-5px);
        }
        .menu-card {
            transition: all 0.3s;
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            height: 100%;
        }
        .menu-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        .recent-orders {
            max-height: 400px;
            overflow-y: auto;
        }
        .activity-timeline {
            max-height: 400px;
            overflow-y: auto;
        }
        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px solid #e9ecef;
        }
        .activity-item {
            padding: 10px 0;
            border-bottom: 1px solid #f8f9fa;
        }
        .activity-item:last-child {
            border-bottom: none;
        }
        .activity-description {
            font-size: 0.95rem;
            line-height: 1.4;
            margin-bottom: 5px;
        }
        .activity-meta {
            font-size: 0.8rem;
        }
    </style>
</head>
<body class="bg-light">
    <!-- Skip to main content link for accessibility -->
    <a href="#main-content" class="skip-link">Přejít na hlavní obsah</a>

    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-clipboard-list me-2"></i>CIG Realizace
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <!-- Global Search -->
                <div class="navbar-nav me-auto">
                    <form class="d-flex" action="search/index.php" method="GET">
                        <div class="input-group" style="width: 300px;">
                            <input type="text" class="form-control" name="q"
                                   placeholder="Vyhledat objednávky, katalogy..."
                                   autocomplete="off">
                            <button class="btn btn-outline-light" type="submit">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </form>
                </div>

                <ul class="navbar-nav ms-auto">
                    <!-- Notifications -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle position-relative" href="#" id="notificationsDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-bell"></i>
                            <?php if ($unread_count > 0): ?>
                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                                <?= $unread_count > 9 ? '9+' : $unread_count ?>
                            </span>
                            <?php endif; ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" style="width: 350px; max-height: 400px; overflow-y: auto;">
                            <li class="dropdown-header d-flex justify-content-between align-items-center">
                                <span>Notifikace</span>
                                <?php if ($unread_count > 0): ?>
                                <button class="btn btn-sm btn-link p-0" id="markAllRead">Označit vše jako přečtené</button>
                                <?php endif; ?>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <?php if (empty($notifications)): ?>
                            <li class="dropdown-item-text text-center text-muted">Žádné notifikace</li>
                            <?php else: ?>
                            <?php foreach ($notifications as $notification): ?>
                            <li>
                                <a class="dropdown-item <?= $notification['is_read'] ? '' : 'bg-light' ?>"
                                   href="<?= $notification['order_id'] ? 'orders/detail.php?id=' . $notification['order_id'] : '#' ?>">
                                    <div class="d-flex">
                                        <div class="flex-shrink-0">
                                            <i class="<?= NotificationManager::formatNotification($notification)['icon'] ?> text-<?= NotificationManager::formatNotification($notification)['color'] ?>"></i>
                                        </div>
                                        <div class="flex-grow-1 ms-2">
                                            <div class="fw-bold"><?= htmlspecialchars($notification['title']) ?></div>
                                            <div class="small text-muted"><?= htmlspecialchars($notification['message']) ?></div>
                                            <div class="small text-muted"><?= NotificationManager::formatNotification($notification)['time_ago'] ?></div>
                                        </div>
                                    </div>
                                </a>
                            </li>
                            <?php endforeach; ?>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-center" href="notifications.php">Zobrazit všechny notifikace</a></li>
                            <?php endif; ?>
                        </ul>
                    </li>

                    <!-- User Menu -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i><?= htmlspecialchars($full_name) ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user-edit me-2"></i>Profil</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="logout.php"><i class="fas fa-sign-out-alt me-2"></i>Odhlásit se</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Dashboard Header -->
    <section class="dashboard-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-2">Vítejte, <?= htmlspecialchars($full_name) ?>!</h1>
                    <p class="mb-0">Role: <span class="badge bg-light text-dark"><?= ucfirst($role) ?></span></p>
                </div>
                <div class="col-md-4 text-end">
                    <p class="mb-0"><i class="fas fa-clock me-1"></i>Přihlášen: <?= date('d.m.Y H:i', $_SESSION['login_time']) ?></p>
                </div>
            </div>
        </div>
    </section>

    <!-- Statistics -->
    <section class="py-5" id="main-content">
        <div class="container">
            <h2 class="mb-4">Přehled systému</h2>
            <div class="row">
                <div class="col-md-3 mb-4">
                    <div class="card stat-card text-center">
                        <div class="card-body">
                            <i class="fas fa-list-alt fa-2x text-primary mb-3"></i>
                            <h3 class="text-primary"><?= $stats['orders'] ?></h3>
                            <p class="mb-0">Celkem objednávek</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-4">
                    <div class="card stat-card text-center">
                        <div class="card-body">
                            <i class="fas fa-clock fa-2x text-warning mb-3"></i>
                            <h3 class="text-warning"><?= $stats['pending'] ?></h3>
                            <p class="mb-0">Čekající objednávky</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-4">
                    <div class="card stat-card text-center">
                        <div class="card-body">
                            <i class="fas fa-cogs fa-2x text-info mb-3"></i>
                            <h3 class="text-info"><?= $stats['in_progress'] ?></h3>
                            <p class="mb-0">Ve zpracování</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-4">
                    <div class="card stat-card text-center">
                        <div class="card-body">
                            <i class="fas fa-check-circle fa-2x text-success mb-3"></i>
                            <h3 class="text-success"><?= $stats['completed'] ?></h3>
                            <p class="mb-0">Dokončené</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <section class="pb-5">
        <div class="container">
            <div class="row">
                <!-- Menu Items -->
                <div class="col-md-8">
                    <h3 class="mb-4">Dostupné funkce</h3>
                    <div class="row">
                        <?php foreach ($current_menu as $item): ?>
                        <div class="col-md-6 mb-4">
                            <div class="card menu-card">
                                <div class="card-body text-center">
                                    <i class="<?= $item['icon'] ?> fa-3x text-primary mb-3"></i>
                                    <h5 class="card-title"><?= $item['title'] ?></h5>
                                    <p class="card-text text-muted"><?= $item['desc'] ?></p>
                                    <a href="<?= $item['url'] ?>" class="btn btn-primary">Otevřít</a>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>

                <!-- Recent Orders -->
                <div class="col-md-4">
                    <h3 class="mb-4">Nejnovější objednávky</h3>
                    <div class="card">
                        <div class="card-body recent-orders">
                            <?php if (empty($recent_orders)): ?>
                            <p class="text-muted text-center">Žádné objednávky</p>
                            <?php else: ?>
                            <?php foreach ($recent_orders as $order): ?>
                            <div class="d-flex justify-content-between align-items-center border-bottom py-2">
                                <div>
                                    <strong><?= htmlspecialchars($order['order_code']) ?></strong><br>
                                    <small class="text-muted"><?= date('d.m.Y', strtotime($order['order_date'])) ?></small>
                                </div>
                                <div class="text-end">
                                    <?php if ($order['status'] === 'completed'): ?>
                                    <span class="badge bg-success">Dokončeno</span>
                                    <?php elseif ($order['status'] === 'in_progress'): ?>
                                    <span class="badge bg-info">Ve zpracování</span>
                                    <?php elseif ($order['status'] === 'pending'): ?>
                                    <span class="badge bg-warning">Čeká</span>
                                    <?php else: ?>
                                    <span class="badge bg-secondary"><?= ucfirst($order['status']) ?></span>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Recent Activity -->
    <section class="pb-5">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <h3 class="mb-4">Nedávná aktivita v systému</h3>
                    <div class="card">
                        <div class="card-body">
                            <?php if (empty($recent_activity)): ?>
                            <p class="text-muted text-center">Žádná nedávná aktivita</p>
                            <?php else: ?>
                            <div class="activity-timeline">
                                <?php foreach ($recent_activity as $activity): ?>
                                <div class="activity-item d-flex mb-3">
                                    <div class="flex-shrink-0">
                                        <div class="activity-icon">
                                            <i class="<?= getActivityIcon($activity['action_type']) ?> text-<?= getActivityColor($activity['action_type']) ?>"></i>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <div class="activity-content">
                                            <div class="activity-description">
                                                <strong><?= htmlspecialchars($activity['order_code']) ?></strong>:
                                                <?= htmlspecialchars($activity['description']) ?>
                                            </div>
                                            <div class="activity-meta text-muted small">
                                                <i class="fas fa-user me-1"></i><?= htmlspecialchars($activity['full_name'] ?: $activity['username']) ?>
                                                <i class="fas fa-clock ms-2 me-1"></i><?= date('d.m.Y H:i', strtotime($activity['created_at'])) ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                            <div class="text-center mt-3">
                                <a href="activity.php" class="btn btn-outline-primary">Zobrazit všechnu aktivitu</a>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="assets/js/ui-enhancements.js"></script>
    <script>
        // Handle mark all notifications as read
        $('#markAllRead').on('click', function(e) {
            e.preventDefault();

            $.ajax({
                url: 'ajax/notifications.php',
                method: 'POST',
                data: { action: 'mark_all_as_read' },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        location.reload();
                    }
                }
            });
        });

        // Auto-refresh notifications every 30 seconds
        setInterval(function() {
            $.ajax({
                url: 'ajax/notifications.php',
                method: 'GET',
                data: { action: 'get_unread_count' },
                dataType: 'json',
                success: function(response) {
                    if (response.success && response.unread_count > 0) {
                        const badge = $('.navbar .badge');
                        if (badge.length) {
                            badge.text(response.unread_count > 9 ? '9+' : response.unread_count);
                        } else {
                            $('.navbar .fa-bell').after('<span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">' +
                                (response.unread_count > 9 ? '9+' : response.unread_count) + '</span>');
                        }
                    }
                }
            });
        }, 30000);
    </script>
</body>
</html>

<?php
// Helper functions for activity display
function getActivityIcon($actionType) {
    $icons = [
        'preview_status_change' => 'fas fa-eye',
        'delivery_date_change' => 'fas fa-calendar-alt',
        'technology_assignment' => 'fas fa-cogs',
        'item_relevance_change' => 'fas fa-toggle-on',
        'inventory_status_change' => 'fas fa-boxes',
        'order_completion' => 'fas fa-check-circle',
        'order_created' => 'fas fa-plus-circle',
        'order_updated' => 'fas fa-edit'
    ];

    return $icons[$actionType] ?? 'fas fa-info-circle';
}

function getActivityColor($actionType) {
    $colors = [
        'preview_status_change' => 'primary',
        'delivery_date_change' => 'warning',
        'technology_assignment' => 'info',
        'item_relevance_change' => 'secondary',
        'inventory_status_change' => 'success',
        'order_completion' => 'success',
        'order_created' => 'success',
        'order_updated' => 'info'
    ];

    return $colors[$actionType] ?? 'secondary';
}
?>
