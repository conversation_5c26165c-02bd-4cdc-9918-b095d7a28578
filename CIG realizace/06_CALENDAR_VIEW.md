# Phase 06 - Calendar View for Approved Orders

## Objective
Create calendar visualization for approved orders showing production timeline and delivery dates.

## Tasks

### 1. Calendar Interface
- `calendar/index.php` - Main calendar view
- Month/week/day view options
- Navigation between months
- Order blocks with timeline visualization

### 2. Calendar Logic
- Display orders with preview_status = 'approved'
- Show 14-day production timeline
- Start date = preview_approved_date
- End date = expected_delivery_date (editable)

### 3. Order Timeline Display
- Visual blocks spanning multiple days
- Color coding by sales representative
- Order information on hover/click
- Drag-and-drop to adjust delivery dates

### 4. Calendar Features
- Filter by sales representative
- Filter by technology type
- Search by order code
- Export calendar view

## Files to Create

### Calendar Core
1. `calendar/index.php`
2. `calendar/calendar_grid.php`
3. `calendar/order_block.php`
4. `calendar/calendar_navigation.php`

### AJAX Handlers
5. `ajax/get_calendar_data.php`
6. `ajax/update_delivery_date.php`
7. `ajax/get_order_tooltip.php`

### Calendar Components
8. `includes/calendar_filters.php`
9. `includes/calendar_legend.php`

### Assets
10. `assets/css/calendar.css`
11. `assets/js/calendar.js`
12. `assets/js/calendar_drag.js`

## Calendar Layout

### Header Section
```html
<div class="calendar-header">
    <div class="calendar-navigation">
        <button id="prev-month">‹ Previous</button>
        <h2 id="current-month">January 2025</h2>
        <button id="next-month">Next ›</button>
    </div>
    <div class="calendar-filters">
        <select id="sales-rep-filter">
            <option value="">All Sales Reps</option>
            <option value="VP">Vláďa</option>
            <option value="J">Jirka</option>
            <!-- ... -->
        </select>
        <input type="text" id="order-search" placeholder="Search order...">
    </div>
</div>
```

### Calendar Grid
```html
<div class="calendar-grid">
    <div class="calendar-header-row">
        <div class="day-header">Mon</div>
        <div class="day-header">Tue</div>
        <!-- ... -->
    </div>
    <div class="calendar-body">
        <div class="calendar-week">
            <div class="calendar-day" data-date="2025-01-01">
                <div class="day-number">1</div>
                <div class="day-orders">
                    <!-- Order blocks here -->
                </div>
            </div>
            <!-- ... -->
        </div>
    </div>
</div>
```

### Order Block Structure
```html
<div class="order-block" 
     data-order-id="123"
     data-start-date="2025-01-15"
     data-end-date="2025-01-29"
     style="background-color: #3498db;">
    <div class="order-code">25VP-00001</div>
    <div class="order-sales-rep">Vláďa</div>
    <div class="order-delivery">29.01.2025</div>
</div>
```

## Color Coding System

### Sales Representative Colors
```css
.sales-rep-VP { background-color: #3498db; } /* Blue */
.sales-rep-J { background-color: #e74c3c; }  /* Red */
.sales-rep-NK { background-color: #2ecc71; } /* Green */
.sales-rep-MR { background-color: #f39c12; } /* Orange */
.sales-rep-D { background-color: #9b59b6; }  /* Purple */
.sales-rep-CI { background-color: #1abc9c; } /* Teal */
```

### Status Indicators
```css
.order-block.overdue { border: 2px solid #e74c3c; }
.order-block.due-soon { border: 2px solid #f39c12; }
.order-block.on-track { border: 2px solid #2ecc71; }
```

## Calendar Data Structure

### Database Query
```sql
SELECT o.id, o.order_code, o.sales_rep, o.sales_rep_name,
       o.preview_approved_date, o.expected_delivery_date,
       COUNT(oi.id) as items_count,
       GROUP_CONCAT(DISTINCT oi.technology_assignment) as technologies
FROM orders o
LEFT JOIN order_items oi ON o.id = oi.order_id AND oi.is_relevant = TRUE
WHERE o.preview_status = 'approved' 
  AND o.is_completed = FALSE
  AND o.preview_approved_date >= ?
  AND o.expected_delivery_date <= ?
GROUP BY o.id
ORDER BY o.preview_approved_date
```

### JSON Response Format
```json
{
    "orders": [
        {
            "id": 123,
            "order_code": "25VP-00001",
            "sales_rep": "VP",
            "sales_rep_name": "Vláďa",
            "start_date": "2025-01-15",
            "end_date": "2025-01-29",
            "items_count": 5,
            "technologies": ["TA1 99 1", "S1 49 1"]
        }
    ]
}
```

## Interactive Features

### Drag and Drop
```javascript
$('.order-block').draggable({
    axis: 'x',
    grid: [dayWidth, 0],
    stop: function(event, ui) {
        var orderId = $(this).data('order-id');
        var newDate = calculateNewDate(ui.position.left);
        updateDeliveryDate(orderId, newDate);
    }
});
```

### Order Tooltip
```javascript
$('.order-block').hover(function() {
    var orderId = $(this).data('order-id');
    $.ajax({
        url: '../ajax/get_order_tooltip.php',
        data: {order_id: orderId},
        success: function(response) {
            showTooltip(response);
        }
    });
});
```

### Delivery Date Update
```javascript
function updateDeliveryDate(orderId, newDate) {
    $.ajax({
        url: '../ajax/update_delivery_date.php',
        method: 'POST',
        data: {
            order_id: orderId,
            delivery_date: newDate
        },
        success: function(response) {
            if (response.success) {
                showNotification('Delivery date updated');
                refreshCalendar();
            }
        }
    });
}
```

## Calendar Views

### Month View (Default)
- Full month grid
- Order blocks span multiple days
- Compact view with essential info

### Week View
- 7-day detailed view
- Larger order blocks
- More detailed information

### Day View
- Single day focus
- List format with full details
- Timeline view

## Filtering Options

### Sales Representative Filter
- Dropdown with all sales reps
- Show/hide orders by rep
- Color legend update

### Technology Filter
- Multi-select dropdown
- Filter by technology assignment
- Show orders with specific technologies

### Date Range Filter
- Custom date range picker
- Quick presets (This week, Next month, etc.)
- Export filtered results

## Performance Optimization
- AJAX loading for calendar data
- Lazy loading for large date ranges
- Caching for frequently accessed months
- Efficient database queries with indexes

## Expected Outcome
- Functional calendar view
- Visual order timeline display
- Interactive delivery date updates
- Filtering and search capabilities
- Responsive design for mobile

## Next Phase
Phase 07 - Order History Tracking
