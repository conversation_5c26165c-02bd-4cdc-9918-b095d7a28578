# CIG Realizace - Project Overview

## Project Description
Web application for managing orders, their status, and production scheduling with calendar visualization.

## Technology Stack
- **Backend:** PHP 8.3
- **Database:** MySQL 8
- **Server:** Apache
- **Frontend:** HTML, CSS, JavaScript, Bootstrap
- **Hosting:** Webglobe

## User Roles
- **Admin** - Full system access
- **<PERSON><PERSON><PERSON><PERSON><PERSON> (Sales)** - Order management
- **<PERSON><PERSON> (Designer)** - Preview management
- **<PERSON><PERSON><PERSON><PERSON> (Producer)** - Production management

## Core Features
1. User authentication with role-based access
2. Order overview (pending orders)
3. Calendar view for approved orders
4. Order detail with history tracking
5. Status management (preview and inventory)
6. Email notifications
7. Filtering and search capabilities
8. CSV data import

## Order Status Flow
1. **Order Created** → Pending orders list
2. **Preview Status Change** → Manual status updates with timestamp
3. **Preview Approved** → Moves to calendar view
4. **Production Scheduling** → 14-day default timeline
5. **Order Completion** → Moves to completed orders + email notification

## Implementation Phases
The project is divided into 10 implementation phases, each adding specific functionality:

1. **Phase 01** - Database structure and configuration
2. **Phase 02** - User authentication system
3. **Phase 03** - CSV data import functionality
4. **Phase 04** - Basic dashboard and pending orders
5. **Phase 05** - Order detail view and status management
6. **Phase 06** - Calendar view for approved orders
7. **Phase 07** - Order history tracking
8. **Phase 08** - Email notifications system
9. **Phase 09** - Filtering and search functionality
10. **Phase 10** - UI/UX improvements and final testing

## Data Structure (from CSV)
- **Order Code:** Column "Číslo dokladu"
- **Technology Types:** Column "Katalog"
- **Quantity:** Column "Množ.hlavní"
- **Order Creation:** Column "Datum vystavení"
- **Goods Ordered:** Column "Datum vystavení OV"
- **Goods Stocked:** Column "Datum vytvoření DLP"

## Sales Representatives (by prefix)
- VP = Vláďa
- J = Jirka
- NK = Nikol
- MR = Mirka
- D = Daniela
- CI = Czech Image
