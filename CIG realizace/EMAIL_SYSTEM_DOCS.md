# CIG Realizace - Email Notifications System Documentation

## Přehled

Email Notifications System je kompletní řešení pro automatické odesílání emailových notifikací v systému CIG Realizace. Systém podporuje různé typy notifikací, správu front, uživatelské preference a detailní tracking.

## Komponenty systému

### 1. Konfigurace
- `config/email.php` - <PERSON>lavní konfigurace SMTP a email nastavení
- Podpora pro Webglobe hosting SMTP
- Testovací režim pro development
- Rate limiting a retry logika

### 2. <PERSON><PERSON><PERSON><PERSON> třídy
- `EmailManager.php` - Hlavní třída pro odesílání emailů
- `NotificationQueue.php` - Správa fronty emailů
- `email_functions.php` - Pomocn<PERSON> funkce

### 3. Email templates
- `order_completed.php` - Dokončení objednávky
- `preview_status_changed.php` - Zm<PERSON>na stavu náhledu
- `overdue_alert.php` - Upozornění na zpoždění
- `daily_summary.php` - Den<PERSON><PERSON> souhrn
- `weekly_summary.php` - Týdenn<PERSON> souhrn
- `system_alert.php` - Systémové alerty

### 4. Databázové tabulky
- `notification_queue` - Fronta emailů k odeslání
- `user_notification_preferences` - Uživatelské preference
- `email_delivery_tracking` - Tracking doručení
- `email_statistics` - Statistiky odesílání

## Typy notifikací

### 1. Order Completion (Dokončení objednávky)
- **Trigger:** Označení objednávky jako dokončené
- **Příjemci:** Obchodník zodpovědný za objednávku
- **Template:** `order_completed.php`

### 2. Preview Status Change (Změna stavu náhledu)
- **Trigger:** Změna stavu náhledu objednávky
- **Příjemci:** Obchodník + administrátoři (pro důležité změny)
- **Template:** `preview_status_changed.php`

### 3. Overdue Alert (Upozornění na zpoždění)
- **Trigger:** Denní kontrola zpožděných objednávek
- **Příjemci:** Administrátoři
- **Template:** `overdue_alert.php`

### 4. Daily Summary (Denní souhrn)
- **Trigger:** Denně ráno pro uživatele s povoleným souhrnem
- **Příjemci:** Uživatelé s aktivním daily_summary
- **Template:** `daily_summary.php`

### 5. Weekly Summary (Týdenní souhrn)
- **Trigger:** Každé pondělí pro uživatele s povoleným souhrnem
- **Příjemci:** Uživatelé s aktivním weekly_summary
- **Template:** `weekly_summary.php`

## Uživatelské preference

Každý uživatel může nastavit:
- **Typy notifikací:** Které notifikace chce dostávat
- **Formát emailů:** HTML nebo plain text
- **Frekvence:** Okamžitě, denní souhrn, týdenní souhrn
- **Odhlášení:** Možnost odhlásit se z konkrétních typů

### Nastavení přes web interface:
- URL: `/user/notification_settings.php`
- AJAX endpoint: `/ajax/update_notification_prefs.php`

## Administrace

### 1. Správa notifikací
- URL: `/admin/notifications.php`
- Testování konfigurace
- Odesílání test emailů
- Zpracování fronty
- Statistiky doručení

### 2. Správa email fronty
- URL: `/admin/email_queue.php`
- Zobrazení čekajících emailů
- Retry neúspěšných emailů
- Mazání emailů z fronty
- Filtrování podle stavu a template

## Cron Jobs

### 1. Zpracování email fronty
```bash
*/5 * * * * /usr/bin/php /path/to/cron/send_notifications.php
```
- Spouští se každých 5 minut
- Zpracovává čekající emaily ve frontě
- Retry neúspěšných pokusů
- Čištění starých emailů

### 2. Denní reporty
```bash
0 8 * * * /usr/bin/php /path/to/cron/daily_reports.php
```
- Spouští se každý den v 8:00
- Odesílá denní souhrny
- Kontroluje zpožděné objednávky
- Týdenní souhrny (pondělí)
- Systémové health check

## Konfigurace SMTP

### Webglobe hosting
```php
'smtp_host' => 'smtp.webglobe.com',
'smtp_port' => 587,
'smtp_username' => '<EMAIL>',
'smtp_password' => 'your_password',
'smtp_encryption' => 'tls',
```

### Development
```php
'debug_mode' => true,
'test_mode' => true,
'test_recipient' => 'developer@localhost',
```

## Rate Limiting

Systém obsahuje rate limiting pro prevenci spamu:
- **Hodinový limit:** 100 emailů na příjemce
- **Denní limit:** 500 emailů na příjemce
- Automatické zpoždění při překročení

## Tracking a statistiky

### Email tracking
- **Open tracking:** 1x1 pixel v emailech
- **Click tracking:** Sledování kliknutí na odkazy
- **Delivery tracking:** Stav doručení
- **Bounce handling:** Zpracování nedoručených emailů

### Statistiky
- Denní statistiky odesílání
- Úspěšnost doručení
- Open rate a click rate
- Bounce rate

## Testování

### Test script
```bash
php test_email_system.php
```

Testuje:
- Konfiguraci emailů
- Databázové tabulky
- Email templates
- Funkcionalitu fronty
- Kompilaci templates
- Uživatelské preference

### Manuální testování
1. Přihlaste se jako admin
2. Jděte na `/admin/notifications.php`
3. Použijte "Test email" funkci
4. Zkontrolujte email frontu v `/admin/email_queue.php`

## Troubleshooting

### Časté problémy

1. **Emaily se neodesílají**
   - Zkontrolujte SMTP konfiguraci
   - Ověřte, že cron job běží
   - Zkontrolujte logy v `/logs/email_cron.log`

2. **Emaily jsou ve frontě ale neodesílají se**
   - Zkontrolujte SMTP přihlašovací údaje
   - Ověřte síťové připojení
   - Zkontrolujte rate limiting

3. **Uživatelé nedostávají notifikace**
   - Zkontrolujte uživatelské preference
   - Ověřte emailové adresy uživatelů
   - Zkontrolujte spam složky

### Logy
- `/logs/email_cron.log` - Cron job pro odesílání
- `/logs/daily_reports.log` - Denní reporty
- PHP error log - Obecné chyby

### Debug režim
Zapněte debug režim v `config/email.php`:
```php
'debug_mode' => true,
'log_all_emails' => true,
```

## Bezpečnost

### Ochrana dat
- Emailové adresy jsou chráněny rate limitingem
- Unsubscribe tokeny jsou časově omezené
- Tracking data jsou anonymizovaná

### GDPR compliance
- Uživatelé mohou spravovat své preference
- Možnost úplného odhlášení
- Automatické mazání starých dat

## Rozšíření systému

### Přidání nového typu notifikace

1. **Vytvořte email template**
   ```php
   // templates/email/new_notification.php
   ```

2. **Přidejte metodu do EmailManager**
   ```php
   public function sendNewNotification($data) {
       // Implementation
   }
   ```

3. **Přidejte helper funkci**
   ```php
   // includes/email_functions.php
   function sendNewNotificationEmail($data) {
       // Implementation
   }
   ```

4. **Aktualizujte uživatelské preference**
   - Přidejte sloupec do `user_notification_preferences`
   - Aktualizujte formulář v `user/notification_settings.php`

### Integrace s externími službami
Systém je navržen pro snadnou integraci s:
- SendGrid
- Mailgun
- Amazon SES
- Další SMTP služby

## Maintenance

### Pravidelné úkoly
- **Denně:** Kontrola logů a statistik
- **Týdně:** Čištění starých dat
- **Měsíčně:** Analýza delivery rate
- **Kvartálně:** Aktualizace SMTP hesel

### Monitoring
Doporučujeme monitorovat:
- Velikost email fronty
- Delivery rate
- Bounce rate
- Disk space pro logy
- SMTP server dostupnost
