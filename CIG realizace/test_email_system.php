<?php
/**
 * Test Email System
 * CIG Realizace - Phase 08
 * 
 * This script tests the email notification system functionality
 */

require_once 'config/database.php';
require_once 'config/email.php';
require_once 'includes/email_functions.php';
require_once 'classes/EmailManager.php';
require_once 'classes/NotificationQueue.php';

echo "<h1>CIG Realizace - Email System Test</h1>\n";
echo "<pre>\n";

// Test 1: Configuration check
echo "=== Test 1: Email Configuration ===\n";
$configTests = testEmailConfiguration();
foreach ($configTests as $test => $result) {
    $status = $result ? "✅ PASS" : "❌ FAIL";
    echo "$test: $status\n";
}
echo "\n";

// Test 2: Database tables check
echo "=== Test 2: Database Tables ===\n";
try {
    $pdo = getDbConnection();
    
    $requiredTables = [
        'notification_queue',
        'user_notification_preferences', 
        'email_delivery_tracking',
        'email_statistics'
    ];
    
    foreach ($requiredTables as $table) {
        $stmt = $pdo->prepare("SELECT name FROM sqlite_master WHERE type='table' AND name=?");
        $stmt->execute([$table]);
        
        if ($stmt->fetch()) {
            echo "$table: ✅ EXISTS\n";
        } else {
            echo "$table: ❌ MISSING\n";
        }
    }
} catch (Exception $e) {
    echo "Database error: " . $e->getMessage() . "\n";
}
echo "\n";

// Test 3: Email templates check
echo "=== Test 3: Email Templates ===\n";
$templateDir = getEmailTemplateDir();
$requiredTemplates = [
    'order_completed.php',
    'preview_status_changed.php',
    'overdue_alert.php',
    'daily_summary.php',
    'weekly_summary.php',
    'system_alert.php'
];

foreach ($requiredTemplates as $template) {
    if (file_exists($templateDir . $template)) {
        echo "$template: ✅ EXISTS\n";
    } else {
        echo "$template: ❌ MISSING\n";
    }
}
echo "\n";

// Test 4: Queue functionality
echo "=== Test 4: Email Queue ===\n";
try {
    $queue = new NotificationQueue();
    
    // Test queueing an email
    $testId = $queue->queueEmail(
        '<EMAIL>',
        'Test User',
        'Test Email - ' . date('Y-m-d H:i:s'),
        'order_completed',
        [
            'order_code' => 'TEST-001',
            'sales_rep_name' => 'Test User',
            'order_date' => date('d.m.Y'),
            'completion_date' => date('d.m.Y H:i'),
            'items_count' => 1,
            'technologies' => 'Test',
            'order_url' => '#'
        ],
        10 // Low priority for test
    );
    
    if ($testId) {
        echo "Queue email: ✅ SUCCESS (ID: $testId)\n";
        
        // Get queue stats
        $stats = $queue->getQueueStatistics();
        echo "Queue stats: " . json_encode($stats) . "\n";
        
        // Clean up test email
        $pdo = getDbConnection();
        $stmt = $pdo->prepare("DELETE FROM notification_queue WHERE id = ?");
        $stmt->execute([$testId]);
        echo "Cleanup: ✅ Test email removed\n";
    } else {
        echo "Queue email: ❌ FAILED\n";
    }
} catch (Exception $e) {
    echo "Queue test error: " . $e->getMessage() . "\n";
}
echo "\n";

// Test 5: EmailManager functionality
echo "=== Test 5: EmailManager ===\n";
try {
    $emailManager = new EmailManager();
    
    // Test template compilation
    $testData = [
        'order_code' => 'TEST-001',
        'sales_rep_name' => 'Test User',
        'order_date' => date('d.m.Y'),
        'completion_date' => date('d.m.Y H:i'),
        'items_count' => 1,
        'technologies' => 'Test',
        'order_url' => '#'
    ];
    
    // Use reflection to test private method
    $reflection = new ReflectionClass($emailManager);
    $method = $reflection->getMethod('compileTemplate');
    $method->setAccessible(true);
    
    $compiled = $method->invoke($emailManager, 'order_completed', $testData);
    
    if ($compiled && isset($compiled['html']) && isset($compiled['text'])) {
        echo "Template compilation: ✅ SUCCESS\n";
        echo "HTML length: " . strlen($compiled['html']) . " chars\n";
        echo "Text length: " . strlen($compiled['text']) . " chars\n";
    } else {
        echo "Template compilation: ❌ FAILED\n";
    }
} catch (Exception $e) {
    echo "EmailManager test error: " . $e->getMessage() . "\n";
}
echo "\n";

// Test 6: User preferences
echo "=== Test 6: User Preferences ===\n";
try {
    // Test with user ID 1 (should exist)
    $prefs = getUserNotificationPreferences(1);
    
    if ($prefs) {
        echo "Get preferences: ✅ SUCCESS\n";
        echo "User ID: " . $prefs['user_id'] . "\n";
        echo "Order completion: " . ($prefs['order_completion'] ? 'ON' : 'OFF') . "\n";
        echo "Email format: " . $prefs['email_format'] . "\n";
    } else {
        echo "Get preferences: ❌ FAILED (user might not exist)\n";
    }
    
    // Test updating preferences
    $testPrefs = [
        'order_completion' => 1,
        'preview_status_change' => 1,
        'overdue_alerts' => 1,
        'daily_summary' => 0,
        'weekly_summary' => 1,
        'email_format' => 'html',
        'frequency' => 'immediate'
    ];
    
    $updated = updateUserNotificationPreferences(1, $testPrefs);
    echo "Update preferences: " . ($updated ? "✅ SUCCESS" : "❌ FAILED") . "\n";
    
} catch (Exception $e) {
    echo "User preferences test error: " . $e->getMessage() . "\n";
}
echo "\n";

// Test 7: Helper functions
echo "=== Test 7: Helper Functions ===\n";
try {
    // Test overdue orders
    $overdueOrders = getOverdueOrders();
    echo "Get overdue orders: ✅ SUCCESS (" . count($overdueOrders) . " found)\n";
    
    // Test email delivery stats
    $deliveryStats = getEmailDeliveryStats(7);
    echo "Get delivery stats: ✅ SUCCESS (" . count($deliveryStats) . " days)\n";
    
    // Test email enabled check
    $emailEnabled = isEmailEnabled();
    echo "Email enabled: " . ($emailEnabled ? "✅ YES" : "❌ NO") . "\n";
    
} catch (Exception $e) {
    echo "Helper functions test error: " . $e->getMessage() . "\n";
}
echo "\n";

// Test 8: Cron job simulation
echo "=== Test 8: Cron Job Simulation ===\n";
try {
    // Test processing email queue
    $processed = processEmailQueue(5); // Process max 5 emails
    echo "Process queue: ✅ SUCCESS ($processed emails processed)\n";
    
    // Test cleaning old emails
    $cleaned = cleanOldEmailQueue(30);
    echo "Clean old emails: ✅ SUCCESS ($cleaned emails cleaned)\n";
    
} catch (Exception $e) {
    echo "Cron job test error: " . $e->getMessage() . "\n";
}
echo "\n";

echo "=== Email System Test Complete ===\n";
echo "Check the results above for any issues that need to be addressed.\n";
echo "</pre>\n";

// Show current email configuration
echo "<h2>Current Email Configuration</h2>\n";
echo "<pre>\n";
$config = getEmailConfig();
echo "SMTP Host: " . $config['smtp_host'] . "\n";
echo "SMTP Port: " . $config['smtp_port'] . "\n";
echo "From Email: " . $config['from_email'] . "\n";
echo "Test Mode: " . ($config['test_mode'] ? 'ON' : 'OFF') . "\n";
echo "Debug Mode: " . ($config['debug_mode'] ? 'ON' : 'OFF') . "\n";
echo "Queue Batch Size: " . $config['queue_batch_size'] . "\n";
echo "Rate Limiting: " . ($config['rate_limit_enabled'] ? 'ON' : 'OFF') . "\n";
echo "</pre>\n";

// Show queue statistics
echo "<h2>Current Queue Statistics</h2>\n";
echo "<pre>\n";
try {
    $queue = new NotificationQueue();
    $stats = $queue->getQueueStatistics();
    
    foreach ($stats as $key => $value) {
        echo ucfirst(str_replace('_', ' ', $key)) . ": $value\n";
    }
} catch (Exception $e) {
    echo "Error getting queue stats: " . $e->getMessage() . "\n";
}
echo "</pre>\n";
?>
