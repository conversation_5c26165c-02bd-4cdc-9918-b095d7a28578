<?php
/**
 * CIG Realizace - Performance Monitor
 * System performance monitoring and alerting
 */

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../includes/session.php';

class PerformanceMonitor {
    private $pdo;
    private $config;
    private $alerts = [];
    
    public function __construct() {
        $this->config = require __DIR__ . '/../config/database.php';
        $this->pdo = new PDO(
            $this->config['dsn'],
            $this->config['username'],
            $this->config['password'],
            $this->config['options']
        );
    }
    
    /**
     * Run complete performance monitoring
     */
    public function runMonitoring() {
        echo "Starting Performance Monitoring...\n";
        echo "Timestamp: " . date('Y-m-d H:i:s') . "\n\n";
        
        $this->checkDatabasePerformance();
        $this->checkSystemResources();
        $this->checkApplicationHealth();
        $this->checkLogFiles();
        $this->checkEmailQueue();
        
        $this->generateReport();
        $this->sendAlerts();
        
        return $this->alerts;
    }
    
    /**
     * Check database performance
     */
    private function checkDatabasePerformance() {
        echo "Checking database performance...\n";
        
        try {
            // Check connection time
            $start = microtime(true);
            $this->pdo->query("SELECT 1");
            $connectionTime = (microtime(true) - $start) * 1000;
            
            if ($connectionTime > 1000) { // > 1 second
                $this->addAlert('DATABASE', 'Slow database connection', [
                    'connection_time' => $connectionTime . 'ms'
                ]);
            }
            
            // Check slow queries
            if ($this->config['driver'] === 'mysql') {
                $stmt = $this->pdo->query("SHOW PROCESSLIST");
                $processes = $stmt->fetchAll();
                
                foreach ($processes as $process) {
                    if ($process['Time'] > 30 && $process['Command'] !== 'Sleep') {
                        $this->addAlert('DATABASE', 'Long running query detected', [
                            'query_time' => $process['Time'] . 's',
                            'query' => substr($process['Info'], 0, 100)
                        ]);
                    }
                }
                
                // Check table sizes
                $stmt = $this->pdo->query("
                    SELECT table_name, 
                           ROUND(((data_length + index_length) / 1024 / 1024), 2) AS size_mb
                    FROM information_schema.tables 
                    WHERE table_schema = DATABASE()
                    ORDER BY size_mb DESC
                ");
                
                $tables = $stmt->fetchAll();
                foreach ($tables as $table) {
                    if ($table['size_mb'] > 1000) { // > 1GB
                        $this->addAlert('DATABASE', 'Large table detected', [
                            'table' => $table['table_name'],
                            'size' => $table['size_mb'] . 'MB'
                        ]);
                    }
                }
            }
            
            // Check record counts
            $stmt = $this->pdo->query("SELECT COUNT(*) as count FROM orders");
            $orderCount = $stmt->fetch()['count'];
            
            $stmt = $this->pdo->query("SELECT COUNT(*) as count FROM order_history");
            $historyCount = $stmt->fetch()['count'];
            
            echo "  Orders: $orderCount\n";
            echo "  History records: $historyCount\n";
            echo "  Connection time: " . number_format($connectionTime, 2) . "ms\n";
            
            if ($historyCount > 100000) {
                $this->addAlert('DATABASE', 'Large history table', [
                    'records' => $historyCount
                ]);
            }
            
        } catch (Exception $e) {
            $this->addAlert('DATABASE', 'Database error', [
                'error' => $e->getMessage()
            ]);
        }
        
        echo "Database check completed.\n\n";
    }
    
    /**
     * Check system resources
     */
    private function checkSystemResources() {
        echo "Checking system resources...\n";
        
        // Check disk space
        $diskFree = disk_free_space('.');
        $diskTotal = disk_total_space('.');
        $diskUsagePercent = (($diskTotal - $diskFree) / $diskTotal) * 100;
        
        echo "  Disk usage: " . number_format($diskUsagePercent, 1) . "%\n";
        
        if ($diskUsagePercent > 90) {
            $this->addAlert('SYSTEM', 'High disk usage', [
                'usage_percent' => number_format($diskUsagePercent, 1) . '%'
            ]);
        }
        
        // Check memory usage
        if (function_exists('memory_get_usage')) {
            $memoryUsage = memory_get_usage(true);
            $memoryLimit = ini_get('memory_limit');
            
            echo "  Memory usage: " . $this->formatBytes($memoryUsage) . "\n";
            echo "  Memory limit: $memoryLimit\n";
            
            if ($memoryUsage > $this->parseBytes($memoryLimit) * 0.8) {
                $this->addAlert('SYSTEM', 'High memory usage', [
                    'usage' => $this->formatBytes($memoryUsage),
                    'limit' => $memoryLimit
                ]);
            }
        }
        
        // Check load average (Linux only)
        if (function_exists('sys_getloadavg')) {
            $load = sys_getloadavg();
            echo "  Load average: " . implode(', ', array_map(function($l) { 
                return number_format($l, 2); 
            }, $load)) . "\n";
            
            if ($load[0] > 5.0) {
                $this->addAlert('SYSTEM', 'High system load', [
                    'load_1min' => number_format($load[0], 2)
                ]);
            }
        }
        
        echo "System resources check completed.\n\n";
    }
    
    /**
     * Check application health
     */
    private function checkApplicationHealth() {
        echo "Checking application health...\n";
        
        // Check if application is responding
        $start = microtime(true);
        $context = stream_context_create([
            'http' => [
                'timeout' => 10,
                'method' => 'GET'
            ]
        ]);
        
        $baseUrl = 'http://localhost:8000';
        $response = @file_get_contents($baseUrl, false, $context);
        $responseTime = (microtime(true) - $start) * 1000;
        
        echo "  Response time: " . number_format($responseTime, 2) . "ms\n";
        
        if ($response === false) {
            $this->addAlert('APPLICATION', 'Application not responding', [
                'url' => $baseUrl
            ]);
        } elseif ($responseTime > 5000) { // > 5 seconds
            $this->addAlert('APPLICATION', 'Slow application response', [
                'response_time' => number_format($responseTime, 2) . 'ms'
            ]);
        }
        
        // Check session files
        $sessionPath = session_save_path() ?: sys_get_temp_dir();
        $sessionFiles = glob($sessionPath . '/sess_*');
        $sessionCount = count($sessionFiles);
        
        echo "  Active sessions: $sessionCount\n";
        
        if ($sessionCount > 100) {
            $this->addAlert('APPLICATION', 'High number of active sessions', [
                'session_count' => $sessionCount
            ]);
        }
        
        echo "Application health check completed.\n\n";
    }
    
    /**
     * Check log files
     */
    private function checkLogFiles() {
        echo "Checking log files...\n";
        
        $logDir = __DIR__ . '/../logs';
        $logFiles = [
            'php_errors.log',
            'email_cron.log',
            'security.log',
            'application.log'
        ];
        
        foreach ($logFiles as $logFile) {
            $logPath = $logDir . '/' . $logFile;
            
            if (file_exists($logPath)) {
                $size = filesize($logPath);
                $sizeFormatted = $this->formatBytes($size);
                
                echo "  $logFile: $sizeFormatted\n";
                
                // Check for large log files
                if ($size > 50 * 1024 * 1024) { // > 50MB
                    $this->addAlert('LOGS', 'Large log file', [
                        'file' => $logFile,
                        'size' => $sizeFormatted
                    ]);
                }
                
                // Check for recent errors
                if ($logFile === 'php_errors.log') {
                    $recentErrors = $this->checkRecentErrors($logPath);
                    if ($recentErrors > 10) {
                        $this->addAlert('LOGS', 'High error rate', [
                            'file' => $logFile,
                            'recent_errors' => $recentErrors
                        ]);
                    }
                }
            } else {
                echo "  $logFile: Not found\n";
            }
        }
        
        echo "Log files check completed.\n\n";
    }
    
    /**
     * Check email queue
     */
    private function checkEmailQueue() {
        echo "Checking email queue...\n";
        
        try {
            $stmt = $this->pdo->query("
                SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
                    SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed
                FROM email_queue
            ");
            
            $stats = $stmt->fetch();
            
            echo "  Total emails: {$stats['total']}\n";
            echo "  Pending: {$stats['pending']}\n";
            echo "  Failed: {$stats['failed']}\n";
            
            if ($stats['pending'] > 100) {
                $this->addAlert('EMAIL', 'Large email queue', [
                    'pending_count' => $stats['pending']
                ]);
            }
            
            if ($stats['failed'] > 50) {
                $this->addAlert('EMAIL', 'High email failure rate', [
                    'failed_count' => $stats['failed']
                ]);
            }
            
        } catch (Exception $e) {
            echo "  Error checking email queue: " . $e->getMessage() . "\n";
        }
        
        echo "Email queue check completed.\n\n";
    }
    
    /**
     * Check recent errors in log file
     */
    private function checkRecentErrors($logPath) {
        $errorCount = 0;
        $oneDayAgo = time() - 86400; // 24 hours ago
        
        if (file_exists($logPath)) {
            $lines = file($logPath, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
            $recentLines = array_slice($lines, -1000); // Check last 1000 lines
            
            foreach ($recentLines as $line) {
                if (preg_match('/\[(.*?)\]/', $line, $matches)) {
                    $timestamp = strtotime($matches[1]);
                    if ($timestamp > $oneDayAgo && 
                        (strpos($line, 'ERROR') !== false || strpos($line, 'FATAL') !== false)) {
                        $errorCount++;
                    }
                }
            }
        }
        
        return $errorCount;
    }
    
    /**
     * Add alert to collection
     */
    private function addAlert($category, $message, $details = []) {
        $this->alerts[] = [
            'timestamp' => date('Y-m-d H:i:s'),
            'category' => $category,
            'message' => $message,
            'details' => $details,
            'severity' => $this->getSeverity($category, $message)
        ];
    }
    
    /**
     * Get alert severity
     */
    private function getSeverity($category, $message) {
        $criticalKeywords = ['not responding', 'database error', 'high disk usage'];
        $warningKeywords = ['slow', 'large', 'high'];
        
        foreach ($criticalKeywords as $keyword) {
            if (stripos($message, $keyword) !== false) {
                return 'CRITICAL';
            }
        }
        
        foreach ($warningKeywords as $keyword) {
            if (stripos($message, $keyword) !== false) {
                return 'WARNING';
            }
        }
        
        return 'INFO';
    }
    
    /**
     * Generate monitoring report
     */
    private function generateReport() {
        $reportFile = __DIR__ . '/../logs/performance_' . date('Y-m-d') . '.log';
        
        $report = "=== Performance Monitoring Report ===\n";
        $report .= "Generated: " . date('Y-m-d H:i:s') . "\n\n";
        
        if (empty($this->alerts)) {
            $report .= "No issues detected.\n";
        } else {
            $report .= "Issues detected:\n\n";
            
            foreach ($this->alerts as $alert) {
                $report .= "[{$alert['severity']}] {$alert['category']}: {$alert['message']}\n";
                if (!empty($alert['details'])) {
                    foreach ($alert['details'] as $key => $value) {
                        $report .= "  $key: $value\n";
                    }
                }
                $report .= "\n";
            }
        }
        
        file_put_contents($reportFile, $report, FILE_APPEND | LOCK_EX);
        echo "Report saved to: $reportFile\n";
    }
    
    /**
     * Send alerts via email
     */
    private function sendAlerts() {
        $criticalAlerts = array_filter($this->alerts, function($alert) {
            return $alert['severity'] === 'CRITICAL';
        });
        
        if (!empty($criticalAlerts)) {
            $subject = 'CIG Realizace - Critical System Alert';
            $message = "Critical issues detected in CIG Realizace system:\n\n";
            
            foreach ($criticalAlerts as $alert) {
                $message .= "- {$alert['message']}\n";
            }
            
            $message .= "\nPlease check the system immediately.";
            
            // Send email (implement based on your email system)
            error_log("CRITICAL ALERT: $subject - $message");
        }
    }
    
    /**
     * Format bytes to human readable format
     */
    private function formatBytes($bytes, $precision = 2) {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
    
    /**
     * Parse bytes from string (e.g., "128M" -> bytes)
     */
    private function parseBytes($str) {
        $str = trim($str);
        $last = strtolower($str[strlen($str)-1]);
        $num = (int) $str;
        
        switch($last) {
            case 'g': $num *= 1024;
            case 'm': $num *= 1024;
            case 'k': $num *= 1024;
        }
        
        return $num;
    }
}

// Run monitoring if called directly
if (php_sapi_name() === 'cli') {
    $monitor = new PerformanceMonitor();
    $alerts = $monitor->runMonitoring();
    
    // Exit with error code if critical alerts
    $criticalCount = count(array_filter($alerts, function($alert) {
        return $alert['severity'] === 'CRITICAL';
    }));
    
    exit($criticalCount > 0 ? 1 : 0);
}
?>
