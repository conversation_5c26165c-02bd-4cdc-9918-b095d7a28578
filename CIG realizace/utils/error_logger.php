<?php
/**
 * CIG Realizace - Enhanced Error Logger
 * Advanced error logging and monitoring system
 */

class ErrorLogger {
    private static $logPath;
    private static $maxFileSize = 50 * 1024 * 1024; // 50MB
    private static $maxFiles = 10;
    private static $initialized = false;
    
    /**
     * Initialize error logger
     */
    public static function init($logPath = null) {
        if (self::$initialized) {
            return;
        }
        
        self::$logPath = $logPath ?: __DIR__ . '/../logs/';
        
        // Create log directory if it doesn't exist
        if (!is_dir(self::$logPath)) {
            mkdir(self::$logPath, 0755, true);
        }
        
        // Set custom error handler
        set_error_handler([self::class, 'handleError']);
        set_exception_handler([self::class, 'handleException']);
        register_shutdown_function([self::class, 'handleShutdown']);
        
        self::$initialized = true;
    }
    
    /**
     * Custom error handler
     */
    public static function handleError($severity, $message, $file, $line) {
        // Don't log suppressed errors
        if (!(error_reporting() & $severity)) {
            return false;
        }
        
        $errorType = self::getErrorType($severity);
        
        $context = [
            'type' => $errorType,
            'severity' => $severity,
            'file' => $file,
            'line' => $line,
            'trace' => debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 10)
        ];
        
        self::logError($message, $context);
        
        // Don't execute PHP internal error handler
        return true;
    }
    
    /**
     * Custom exception handler
     */
    public static function handleException($exception) {
        $context = [
            'type' => 'EXCEPTION',
            'class' => get_class($exception),
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'trace' => $exception->getTrace(),
            'previous' => $exception->getPrevious()
        ];
        
        self::logError($exception->getMessage(), $context);
    }
    
    /**
     * Handle fatal errors on shutdown
     */
    public static function handleShutdown() {
        $error = error_get_last();
        
        if ($error && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR])) {
            $context = [
                'type' => 'FATAL',
                'severity' => $error['type'],
                'file' => $error['file'],
                'line' => $error['line']
            ];
            
            self::logError($error['message'], $context);
        }
    }
    
    /**
     * Log error with context
     */
    public static function logError($message, $context = []) {
        if (!self::$initialized) {
            self::init();
        }
        
        $timestamp = date('Y-m-d H:i:s');
        $level = $context['type'] ?? 'ERROR';
        $file = $context['file'] ?? 'unknown';
        $line = $context['line'] ?? 0;
        
        // Create log entry
        $logEntry = [
            'timestamp' => $timestamp,
            'level' => $level,
            'message' => $message,
            'file' => basename($file),
            'line' => $line,
            'full_path' => $file,
            'context' => $context,
            'request' => self::getRequestContext(),
            'system' => self::getSystemContext()
        ];
        
        // Write to appropriate log file
        $logFile = self::getLogFile($level);
        self::writeLogEntry($logFile, $logEntry);
        
        // Send critical alerts
        if (in_array($level, ['FATAL', 'EXCEPTION']) || 
            (isset($context['severity']) && $context['severity'] === E_ERROR)) {
            self::sendCriticalAlert($logEntry);
        }
    }
    
    /**
     * Log application events
     */
    public static function logEvent($level, $message, $context = []) {
        $logEntry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'level' => strtoupper($level),
            'message' => $message,
            'context' => $context,
            'request' => self::getRequestContext(),
            'system' => self::getSystemContext()
        ];
        
        $logFile = self::getLogFile('APPLICATION');
        self::writeLogEntry($logFile, $logEntry);
    }
    
    /**
     * Log security events
     */
    public static function logSecurity($event, $details = []) {
        $logEntry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'level' => 'SECURITY',
            'event' => $event,
            'details' => $details,
            'request' => self::getRequestContext(),
            'system' => self::getSystemContext()
        ];
        
        $logFile = self::getLogFile('SECURITY');
        self::writeLogEntry($logFile, $logEntry);
        
        // Send security alerts for critical events
        $criticalEvents = ['login_bruteforce', 'sql_injection_attempt', 'file_upload_attack'];
        if (in_array($event, $criticalEvents)) {
            self::sendSecurityAlert($logEntry);
        }
    }
    
    /**
     * Get appropriate log file for level
     */
    private static function getLogFile($level) {
        $fileMap = [
            'FATAL' => 'fatal_errors.log',
            'ERROR' => 'php_errors.log',
            'WARNING' => 'warnings.log',
            'NOTICE' => 'notices.log',
            'EXCEPTION' => 'exceptions.log',
            'APPLICATION' => 'application.log',
            'SECURITY' => 'security.log'
        ];
        
        $filename = $fileMap[$level] ?? 'general.log';
        return self::$logPath . $filename;
    }
    
    /**
     * Write log entry to file
     */
    private static function writeLogEntry($logFile, $logEntry) {
        // Rotate log file if too large
        self::rotateLogFile($logFile);
        
        // Format log entry
        $formattedEntry = self::formatLogEntry($logEntry);
        
        // Write to file
        file_put_contents($logFile, $formattedEntry . "\n", FILE_APPEND | LOCK_EX);
        
        // Also write to system log for critical entries
        if (in_array($logEntry['level'], ['FATAL', 'EXCEPTION', 'SECURITY'])) {
            error_log("CIG-{$logEntry['level']}: {$logEntry['message']}");
        }
    }
    
    /**
     * Format log entry for output
     */
    private static function formatLogEntry($logEntry) {
        // JSON format for structured logging
        return json_encode($logEntry, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    }
    
    /**
     * Rotate log file if it's too large
     */
    private static function rotateLogFile($logFile) {
        if (!file_exists($logFile) || filesize($logFile) < self::$maxFileSize) {
            return;
        }
        
        $pathInfo = pathinfo($logFile);
        $baseName = $pathInfo['filename'];
        $extension = $pathInfo['extension'];
        $directory = $pathInfo['dirname'];
        
        // Rotate existing files
        for ($i = self::$maxFiles - 1; $i > 0; $i--) {
            $oldFile = "$directory/$baseName.$i.$extension";
            $newFile = "$directory/$baseName." . ($i + 1) . ".$extension";
            
            if (file_exists($oldFile)) {
                if ($i === self::$maxFiles - 1) {
                    unlink($oldFile); // Delete oldest file
                } else {
                    rename($oldFile, $newFile);
                }
            }
        }
        
        // Move current file to .1
        rename($logFile, "$directory/$baseName.1.$extension");
    }
    
    /**
     * Get request context information
     */
    private static function getRequestContext() {
        return [
            'method' => $_SERVER['REQUEST_METHOD'] ?? 'CLI',
            'uri' => $_SERVER['REQUEST_URI'] ?? 'N/A',
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'referer' => $_SERVER['HTTP_REFERER'] ?? '',
            'user_id' => $_SESSION['user_id'] ?? null,
            'username' => $_SESSION['username'] ?? null,
            'session_id' => session_id() ?: null
        ];
    }
    
    /**
     * Get system context information
     */
    private static function getSystemContext() {
        return [
            'php_version' => PHP_VERSION,
            'memory_usage' => memory_get_usage(true),
            'memory_peak' => memory_get_peak_usage(true),
            'execution_time' => microtime(true) - ($_SERVER['REQUEST_TIME_FLOAT'] ?? microtime(true)),
            'server_name' => $_SERVER['SERVER_NAME'] ?? gethostname(),
            'process_id' => getmypid()
        ];
    }
    
    /**
     * Get error type string from severity
     */
    private static function getErrorType($severity) {
        $errorTypes = [
            E_ERROR => 'ERROR',
            E_WARNING => 'WARNING',
            E_PARSE => 'PARSE',
            E_NOTICE => 'NOTICE',
            E_CORE_ERROR => 'CORE_ERROR',
            E_CORE_WARNING => 'CORE_WARNING',
            E_COMPILE_ERROR => 'COMPILE_ERROR',
            E_COMPILE_WARNING => 'COMPILE_WARNING',
            E_USER_ERROR => 'USER_ERROR',
            E_USER_WARNING => 'USER_WARNING',
            E_USER_NOTICE => 'USER_NOTICE',
            E_STRICT => 'STRICT',
            E_RECOVERABLE_ERROR => 'RECOVERABLE_ERROR',
            E_DEPRECATED => 'DEPRECATED',
            E_USER_DEPRECATED => 'USER_DEPRECATED'
        ];
        
        return $errorTypes[$severity] ?? 'UNKNOWN';
    }
    
    /**
     * Send critical alert
     */
    private static function sendCriticalAlert($logEntry) {
        $subject = "CIG Realizace - Critical Error Alert";
        $message = "Critical error detected:\n\n";
        $message .= "Time: {$logEntry['timestamp']}\n";
        $message .= "Level: {$logEntry['level']}\n";
        $message .= "Message: {$logEntry['message']}\n";
        $message .= "File: {$logEntry['full_path']}:{$logEntry['line']}\n";
        $message .= "User: " . ($logEntry['request']['username'] ?? 'anonymous') . "\n";
        $message .= "IP: {$logEntry['request']['ip']}\n";
        
        // Log to system for immediate attention
        error_log("CRITICAL ALERT: $subject - $message");
        
        // TODO: Implement email notification
        // mail('<EMAIL>', $subject, $message);
    }
    
    /**
     * Send security alert
     */
    private static function sendSecurityAlert($logEntry) {
        $subject = "CIG Realizace - Security Alert";
        $message = "Security event detected:\n\n";
        $message .= "Time: {$logEntry['timestamp']}\n";
        $message .= "Event: {$logEntry['event']}\n";
        $message .= "IP: {$logEntry['request']['ip']}\n";
        $message .= "User Agent: {$logEntry['request']['user_agent']}\n";
        $message .= "Details: " . json_encode($logEntry['details']) . "\n";
        
        // Log to system for immediate attention
        error_log("SECURITY ALERT: $subject - $message");
        
        // TODO: Implement email notification
        // mail('<EMAIL>', $subject, $message);
    }
    
    /**
     * Get error statistics
     */
    public static function getErrorStats($hours = 24) {
        $stats = [
            'total' => 0,
            'by_level' => [],
            'by_file' => [],
            'recent_errors' => []
        ];
        
        $logFiles = glob(self::$logPath . '*.log');
        $cutoffTime = time() - ($hours * 3600);
        
        foreach ($logFiles as $logFile) {
            if (!file_exists($logFile)) continue;
            
            $lines = file($logFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
            
            foreach ($lines as $line) {
                $entry = json_decode($line, true);
                if (!$entry || !isset($entry['timestamp'])) continue;
                
                $entryTime = strtotime($entry['timestamp']);
                if ($entryTime < $cutoffTime) continue;
                
                $stats['total']++;
                
                $level = $entry['level'] ?? 'UNKNOWN';
                $stats['by_level'][$level] = ($stats['by_level'][$level] ?? 0) + 1;
                
                $file = $entry['file'] ?? 'unknown';
                $stats['by_file'][$file] = ($stats['by_file'][$file] ?? 0) + 1;
                
                if (count($stats['recent_errors']) < 10) {
                    $stats['recent_errors'][] = [
                        'timestamp' => $entry['timestamp'],
                        'level' => $level,
                        'message' => $entry['message'] ?? '',
                        'file' => $file
                    ];
                }
            }
        }
        
        // Sort by count
        arsort($stats['by_level']);
        arsort($stats['by_file']);
        
        return $stats;
    }
    
    /**
     * Clean old log files
     */
    public static function cleanOldLogs($days = 30) {
        $cutoffTime = time() - ($days * 24 * 3600);
        $logFiles = glob(self::$logPath . '*.log*');
        $deletedCount = 0;
        
        foreach ($logFiles as $logFile) {
            if (filemtime($logFile) < $cutoffTime) {
                unlink($logFile);
                $deletedCount++;
            }
        }
        
        return $deletedCount;
    }
}

// Initialize error logger
ErrorLogger::init();

// Convenience functions
function log_error($message, $context = []) {
    ErrorLogger::logError($message, $context);
}

function log_info($message, $context = []) {
    ErrorLogger::logEvent('INFO', $message, $context);
}

function log_warning($message, $context = []) {
    ErrorLogger::logEvent('WARNING', $message, $context);
}

function log_security($event, $details = []) {
    ErrorLogger::logSecurity($event, $details);
}

// CLI usage
if (php_sapi_name() === 'cli' && isset($argv[1])) {
    switch ($argv[1]) {
        case 'stats':
            $hours = $argv[2] ?? 24;
            $stats = ErrorLogger::getErrorStats($hours);
            echo "Error Statistics (last $hours hours):\n";
            echo "Total errors: {$stats['total']}\n\n";
            
            echo "By level:\n";
            foreach ($stats['by_level'] as $level => $count) {
                echo "  $level: $count\n";
            }
            
            echo "\nBy file:\n";
            foreach ($stats['by_file'] as $file => $count) {
                echo "  $file: $count\n";
            }
            break;
            
        case 'clean':
            $days = $argv[2] ?? 30;
            $deleted = ErrorLogger::cleanOldLogs($days);
            echo "Deleted $deleted old log files (older than $days days)\n";
            break;
            
        default:
            echo "Usage: php error_logger.php [stats|clean] [hours|days]\n";
            break;
    }
}
?>
