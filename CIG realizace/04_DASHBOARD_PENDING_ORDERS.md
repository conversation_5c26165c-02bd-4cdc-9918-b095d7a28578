# Phase 04 - Basic Dashboard and Pending Orders

## Objective
Create the main dashboard with overview of pending orders and basic navigation.

## Tasks

### 1. Dashboard Layout
- `dashboard.php` - Main dashboard page
- Navigation menu based on user role
- Statistics overview cards
- Quick action buttons

### 2. Pending Orders List
- `orders/pending.php` - List of pending orders
- Display orders where preview_status != 'approved'
- Show order code, sales rep, creation date, preview status, items count
- Sortable columns and pagination

### 3. Order Statistics
- Total pending orders
- Orders by sales representative
- Orders by preview status
- Recent activity summary

### 4. Navigation Structure
- Role-based menu items
- Breadcrumb navigation
- Quick access to main functions

## Files to Create

### Main Dashboard
1. `dashboard.php`
2. `includes/navigation.php`
3. `includes/header.php`
4. `includes/footer.php`

### Pending Orders
5. `orders/pending.php`
6. `orders/order_row.php` (reusable order row component)
7. `includes/pagination.php`

### Statistics
8. `includes/dashboard_stats.php`
9. `ajax/get_stats.php`

### Styling
10. `assets/css/dashboard.css`
11. `assets/css/orders.css`
12. `assets/js/dashboard.js`

## Dashboard Layout Structure

### Header Section
- Company logo/name
- User info and logout
- Main navigation menu

### Statistics Cards
```php
- Total Pending Orders: [count]
- Waiting for Preview: [count] 
- Sent to Client: [count]
- This Week's Orders: [count]
```

### Quick Actions
- Import new CSV
- Add manual order
- View calendar
- Generate reports

### Recent Activity
- Last 10 order status changes
- Recent imports
- System notifications

## Pending Orders Table

### Columns
1. **Order Code** - Clickable link to detail
2. **Sales Rep** - Name and prefix
3. **Creation Date** - Formatted date
4. **Preview Status** - Badge with color coding
5. **Items Count** - Number of order items
6. **Actions** - Quick action buttons

### Status Color Coding
```css
.status-not-created { background: #dc3545; } /* Red */
.status-sent-to-client { background: #ffc107; } /* Yellow */
.status-approved { background: #28a745; } /* Green */
```

### Sorting Options
- Order code (A-Z, Z-A)
- Creation date (newest, oldest)
- Sales representative
- Preview status

### Pagination
- 25 orders per page
- Page navigation
- Jump to page input
- Total count display

## Role-Based Navigation

### Admin
- Dashboard
- Pending Orders
- Calendar View
- Order Management
- User Management
- Import Data
- Reports

### Obchodník (Sales)
- Dashboard
- My Orders
- Pending Orders
- Calendar View
- Add Order

### Grafik (Designer)
- Dashboard
- Preview Tasks
- Calendar View
- Order Details

### Realizátor (Producer)
- Dashboard
- Production Queue
- Calendar View
- Completed Orders

## Database Queries

### Pending Orders Query
```sql
SELECT o.*, 
       COUNT(oi.id) as items_count,
       u.full_name as updated_by_name
FROM orders o
LEFT JOIN order_items oi ON o.id = oi.order_id
LEFT JOIN order_history oh ON o.id = oh.order_id
LEFT JOIN users u ON oh.user_id = u.id
WHERE o.preview_status != 'approved' 
  AND o.is_completed = FALSE
GROUP BY o.id
ORDER BY o.created_at DESC
```

### Statistics Queries
```sql
-- Total pending orders
SELECT COUNT(*) FROM orders WHERE preview_status != 'approved' AND is_completed = FALSE

-- Orders by status
SELECT preview_status, COUNT(*) FROM orders WHERE is_completed = FALSE GROUP BY preview_status

-- Orders by sales rep
SELECT sales_rep, sales_rep_name, COUNT(*) FROM orders WHERE is_completed = FALSE GROUP BY sales_rep
```

## Responsive Design
- Mobile-friendly layout
- Collapsible navigation
- Touch-friendly buttons
- Responsive tables

## Performance Considerations
- Efficient database queries
- Pagination for large datasets
- AJAX loading for statistics
- Caching for frequently accessed data

## Expected Outcome
- Functional dashboard with navigation
- Complete pending orders list
- Statistics overview
- Role-based access working
- Responsive design

## Next Phase
Phase 05 - Order Detail View and Status Management
