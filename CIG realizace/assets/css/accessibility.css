/* CIG Realizace - Accessibility Improvements */

/* Focus indicators */
button:focus,
input:focus,
select:focus,
textarea:focus,
a:focus,
.btn:focus {
    outline: 3px solid #007bff;
    outline-offset: 2px;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
}

/* Enhanced focus for interactive elements */
.order-row:focus,
.stat-card:focus,
.calendar-day:focus {
    outline: 2px solid #007bff;
    outline-offset: 1px;
}

/* Skip to main content link */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: #007bff;
    color: white;
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 9999;
    transition: top 0.3s;
}

.skip-link:focus {
    top: 6px;
    color: white;
    text-decoration: none;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .order-block {
        border: 2px solid #000;
        background: white;
        color: black;
    }
    
    .status-badge {
        border: 2px solid #000;
        font-weight: bold;
    }
    
    .btn {
        border: 2px solid #000;
        font-weight: bold;
    }
    
    .stat-card {
        border: 2px solid #000;
        background: white;
        color: black;
    }
    
    .navbar {
        border-bottom: 2px solid #000;
    }
    
    .table th,
    .table td {
        border: 1px solid #000;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
    
    .loading-spinner {
        animation: none;
    }
    
    .notification {
        transition: none;
    }
}

/* Screen reader only content */
.sr-only {
    position: absolute !important;
    width: 1px !important;
    height: 1px !important;
    padding: 0 !important;
    margin: -1px !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    white-space: nowrap !important;
    border: 0 !important;
}

.sr-only-focusable:focus {
    position: static !important;
    width: auto !important;
    height: auto !important;
    padding: inherit !important;
    margin: inherit !important;
    overflow: visible !important;
    clip: auto !important;
    white-space: normal !important;
}

/* Enhanced color contrast */
.text-muted {
    color: #495057 !important; /* Darker than default Bootstrap */
}

.text-secondary {
    color: #495057 !important;
}

/* Better link visibility */
a {
    text-decoration: underline;
}

a:hover {
    text-decoration: none;
}

/* Form accessibility improvements */
.form-label {
    font-weight: 600;
    margin-bottom: 5px;
}

.form-control:invalid {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.form-control:valid {
    border-color: #28a745;
}

/* Error message styling */
.error-message {
    color: #dc3545;
    font-size: 0.875em;
    margin-top: 5px;
    display: flex;
    align-items: center;
}

.error-message::before {
    content: "⚠";
    margin-right: 5px;
    font-weight: bold;
}

/* Success message styling */
.success-message {
    color: #28a745;
    font-size: 0.875em;
    margin-top: 5px;
    display: flex;
    align-items: center;
}

.success-message::before {
    content: "✓";
    margin-right: 5px;
    font-weight: bold;
}

/* Loading state accessibility */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-message {
    margin-top: 10px;
    font-weight: 600;
    color: #495057;
}

/* Keyboard navigation improvements */
.keyboard-nav {
    position: relative;
}

.keyboard-nav:focus-within::after {
    content: "";
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border: 2px solid #007bff;
    border-radius: 4px;
    pointer-events: none;
}

/* Table accessibility */
.table th {
    position: relative;
}

.table th[aria-sort]::after {
    content: "";
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 0;
}

.table th[aria-sort="ascending"]::after {
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-bottom: 4px solid #495057;
}

.table th[aria-sort="descending"]::after {
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-top: 4px solid #495057;
}

/* Modal accessibility */
.modal {
    display: none;
}

.modal.show {
    display: block;
}

.modal-backdrop {
    background-color: rgba(0, 0, 0, 0.5);
}

/* Notification accessibility */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1060;
    min-width: 300px;
    max-width: 500px;
    padding: 15px;
    border-radius: 4px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    role: alert;
    aria-live: polite;
}

.notification-success {
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.notification-error {
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.notification-warning {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
}

.notification-info {
    background-color: #d1ecf1;
    border: 1px solid #bee5eb;
    color: #0c5460;
}

/* Touch target improvements */
@media (max-width: 768px) {
    .btn,
    .form-control,
    .form-select,
    a {
        min-height: 44px;
        min-width: 44px;
    }
    
    .pagination .page-link {
        padding: 12px 16px;
    }
    
    .dropdown-item {
        padding: 12px 16px;
    }
}

/* Color blind friendly status indicators */
.status-not-created {
    background-color: #6c757d;
    position: relative;
}

.status-not-created::before {
    content: "●";
    position: absolute;
    left: 5px;
    top: 50%;
    transform: translateY(-50%);
}

.status-sent-to-client {
    background-color: #ffc107;
    position: relative;
}

.status-sent-to-client::before {
    content: "▲";
    position: absolute;
    left: 5px;
    top: 50%;
    transform: translateY(-50%);
}

.status-approved {
    background-color: #28a745;
    position: relative;
}

.status-approved::before {
    content: "✓";
    position: absolute;
    left: 5px;
    top: 50%;
    transform: translateY(-50%);
    color: white;
    font-weight: bold;
}
