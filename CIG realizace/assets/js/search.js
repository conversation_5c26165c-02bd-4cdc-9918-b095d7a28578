/**
 * Advanced Search and Filter JavaScript
 * CIG Realizace - Phase 09
 */

class AdvancedSearch {
    constructor() {
        this.searchTimeout = null;
        this.suggestionsTimeout = null;
        this.currentFilters = {};
        this.savedFilters = [];
        this.filterPresets = [];
        
        this.init();
    }
    
    init() {
        this.initGlobalSearch();
        this.initAdvancedFilters();
        this.initSavedFilters();
        this.initFilterPresets();
        this.initExportButtons();
        this.loadUserFilters();
        this.loadFilterPresets();
    }
    
    // Global Search Functionality
    initGlobalSearch() {
        const searchInput = document.getElementById('global-search');
        const suggestionsContainer = document.getElementById('search-suggestions');
        
        if (!searchInput) return;
        
        // Search input event
        searchInput.addEventListener('input', (e) => {
            const query = e.target.value.trim();
            
            clearTimeout(this.suggestionsTimeout);
            
            if (query.length >= 2) {
                this.suggestionsTimeout = setTimeout(() => {
                    this.fetchSuggestions(query);
                }, 300);
            } else {
                this.hideSuggestions();
            }
        });
        
        // Hide suggestions when clicking outside
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.global-search')) {
                this.hideSuggestions();
            }
        });
        
        // Handle Enter key
        searchInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                this.performGlobalSearch(searchInput.value);
            }
        });
        
        // Search button click
        const searchBtn = document.getElementById('search-btn');
        if (searchBtn) {
            searchBtn.addEventListener('click', () => {
                this.performGlobalSearch(searchInput.value);
            });
        }
        
        // Advanced search button
        const advancedBtn = document.getElementById('advanced-search-btn');
        if (advancedBtn) {
            advancedBtn.addEventListener('click', () => {
                this.toggleAdvancedFilters();
            });
        }
    }
    
    async fetchSuggestions(query) {
        try {
            const response = await fetch(`../ajax/search_suggestions.php?q=${encodeURIComponent(query)}&type=all`);
            const suggestions = await response.json();
            
            this.displaySuggestions(suggestions);
        } catch (error) {
            console.error('Error fetching suggestions:', error);
        }
    }
    
    displaySuggestions(suggestions) {
        const container = document.getElementById('search-suggestions');
        if (!container) return;
        
        let html = '';
        
        // Orders suggestions
        if (suggestions.orders && suggestions.orders.length > 0) {
            html += '<div class="suggestion-group">';
            html += '<div class="suggestion-header">Objednávky</div>';
            suggestions.orders.forEach(item => {
                html += `
                    <div class="suggestion-item" data-type="order" data-id="${item.id}">
                        <div class="suggestion-text">${item.text}</div>
                        <div class="suggestion-subtitle">${item.subtitle}</div>
                        <span class="suggestion-type order">Objednávka</span>
                    </div>
                `;
            });
            html += '</div>';
        }
        
        // Catalog suggestions
        if (suggestions.catalog && suggestions.catalog.length > 0) {
            html += '<div class="suggestion-group">';
            html += '<div class="suggestion-header">Katalogové kódy</div>';
            suggestions.catalog.forEach(item => {
                html += `
                    <div class="suggestion-item" data-type="catalog" data-code="${item.text}">
                        <div class="suggestion-text">${item.text}</div>
                        <div class="suggestion-subtitle">${item.subtitle}</div>
                        <span class="suggestion-type catalog">Katalog</span>
                    </div>
                `;
            });
            html += '</div>';
        }
        
        // Technology suggestions
        if (suggestions.technology && suggestions.technology.length > 0) {
            html += '<div class="suggestion-group">';
            html += '<div class="suggestion-header">Technologie</div>';
            suggestions.technology.forEach(item => {
                html += `
                    <div class="suggestion-item" data-type="technology" data-tech="${item.text}">
                        <div class="suggestion-text">${item.text}</div>
                        <div class="suggestion-subtitle">${item.subtitle}</div>
                        <span class="suggestion-type technology">Technologie</span>
                    </div>
                `;
            });
            html += '</div>';
        }
        
        if (html) {
            container.innerHTML = html;
            container.classList.add('show');
            
            // Add click handlers to suggestions
            container.querySelectorAll('.suggestion-item').forEach(item => {
                item.addEventListener('click', (e) => {
                    this.handleSuggestionClick(e.currentTarget);
                });
            });
        } else {
            this.hideSuggestions();
        }
    }
    
    handleSuggestionClick(item) {
        const type = item.dataset.type;
        
        switch (type) {
            case 'order':
                const orderId = item.dataset.id;
                window.location.href = `../orders/detail.php?id=${orderId}`;
                break;
                
            case 'catalog':
                const catalogCode = item.dataset.code;
                this.applyFilter('search', catalogCode);
                break;
                
            case 'technology':
                const technology = item.dataset.tech;
                this.applyFilter('technology', technology);
                break;
        }
        
        this.hideSuggestions();
    }
    
    hideSuggestions() {
        const container = document.getElementById('search-suggestions');
        if (container) {
            container.classList.remove('show');
        }
    }
    
    performGlobalSearch(query) {
        if (query.trim()) {
            this.applyFilter('search', query);
        }
    }
    
    // Advanced Filters
    initAdvancedFilters() {
        const filterForm = document.getElementById('advanced-filters-form');
        if (!filterForm) return;
        
        // Auto-submit on filter change
        const filterInputs = filterForm.querySelectorAll('select, input[type="date"], input[type="checkbox"]');
        filterInputs.forEach(input => {
            input.addEventListener('change', () => {
                clearTimeout(this.searchTimeout);
                this.searchTimeout = setTimeout(() => {
                    this.applyCurrentFilters();
                }, 500);
            });
        });
        
        // Technology filter with suggestions
        const techInput = document.getElementById('technology-filter');
        if (techInput) {
            techInput.addEventListener('input', (e) => {
                this.fetchTechnologySuggestions(e.target.value);
            });
        }
        
        // Clear filters button
        const clearBtn = document.getElementById('clear-filters');
        if (clearBtn) {
            clearBtn.addEventListener('click', () => {
                this.clearAllFilters();
            });
        }
        
        // Save filter button
        const saveBtn = document.getElementById('save-filter');
        if (saveBtn) {
            saveBtn.addEventListener('click', () => {
                this.showSaveFilterModal();
            });
        }
    }
    
    async fetchTechnologySuggestions(query) {
        if (query.length < 2) {
            this.hideTechnologySuggestions();
            return;
        }
        
        try {
            const response = await fetch(`../ajax/search_suggestions.php?q=${encodeURIComponent(query)}&type=technology`);
            const suggestions = await response.json();
            
            this.displayTechnologySuggestions(suggestions);
        } catch (error) {
            console.error('Error fetching technology suggestions:', error);
        }
    }
    
    displayTechnologySuggestions(suggestions) {
        const container = document.getElementById('technology-suggestions');
        if (!container) return;
        
        if (suggestions.length > 0) {
            let html = '';
            suggestions.forEach(item => {
                html += `<div class="technology-suggestion" data-tech="${item.text}">${item.text}</div>`;
            });
            
            container.innerHTML = html;
            container.classList.add('show');
            
            // Add click handlers
            container.querySelectorAll('.technology-suggestion').forEach(item => {
                item.addEventListener('click', () => {
                    document.getElementById('technology-filter').value = item.dataset.tech;
                    this.hideTechnologySuggestions();
                    this.applyCurrentFilters();
                });
            });
        } else {
            this.hideTechnologySuggestions();
        }
    }
    
    hideTechnologySuggestions() {
        const container = document.getElementById('technology-suggestions');
        if (container) {
            container.classList.remove('show');
        }
    }
    
    applyCurrentFilters() {
        const form = document.getElementById('advanced-filters-form');
        if (!form) return;
        
        const formData = new FormData(form);
        const filters = {};
        
        for (let [key, value] of formData.entries()) {
            if (value) {
                filters[key] = value;
            }
        }
        
        this.currentFilters = filters;
        this.redirectWithFilters(filters);
    }
    
    applyFilter(key, value) {
        this.currentFilters[key] = value;
        this.redirectWithFilters(this.currentFilters);
    }
    
    redirectWithFilters(filters) {
        const url = new URL(window.location);
        
        // Clear existing filter parameters
        const filterKeys = ['search', 'sales_rep', 'preview_status', 'status', 'date_from', 'date_to', 'technology', 'inventory_status', 'is_completed', 'overdue_only'];
        filterKeys.forEach(key => url.searchParams.delete(key));
        
        // Add new filters
        Object.entries(filters).forEach(([key, value]) => {
            if (value) {
                url.searchParams.set(key, value);
            }
        });
        
        // Reset to first page
        url.searchParams.set('page', '1');
        
        window.location.href = url.toString();
    }
    
    clearAllFilters() {
        const form = document.getElementById('advanced-filters-form');
        if (form) {
            form.reset();
        }
        
        const globalSearch = document.getElementById('global-search');
        if (globalSearch) {
            globalSearch.value = '';
        }
        
        this.currentFilters = {};
        this.redirectWithFilters({});
    }
    
    toggleAdvancedFilters() {
        const panel = document.getElementById('advanced-filters-panel');
        if (panel) {
            panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
        }
    }
    
    // Saved Filters
    async loadUserFilters() {
        try {
            const response = await fetch('../ajax/load_filter.php?action=get_filters');
            this.savedFilters = await response.json();
            this.displaySavedFilters();
        } catch (error) {
            console.error('Error loading user filters:', error);
        }
    }
    
    async loadFilterPresets() {
        try {
            const response = await fetch('../ajax/load_filter.php?action=get_presets');
            this.filterPresets = await response.json();
            this.displayFilterPresets();
        } catch (error) {
            console.error('Error loading filter presets:', error);
        }
    }
    
    displaySavedFilters() {
        const container = document.getElementById('saved-filters-list');
        if (!container) return;
        
        let html = '';
        this.savedFilters.forEach(filter => {
            html += `
                <div class="filter-item">
                    <span class="filter-name">${filter.name}</span>
                    <div class="filter-item-actions">
                        <button class="btn btn-sm btn-outline-primary load-filter" data-id="${filter.id}">
                            <i class="fas fa-play"></i> Načíst
                        </button>
                        <button class="btn btn-sm btn-outline-danger delete-filter" data-id="${filter.id}">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            `;
        });
        
        container.innerHTML = html;
        
        // Add event handlers
        container.querySelectorAll('.load-filter').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.loadFilter(e.target.dataset.id);
            });
        });
        
        container.querySelectorAll('.delete-filter').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.deleteFilter(e.target.dataset.id);
            });
        });
    }
    
    displayFilterPresets() {
        const container = document.getElementById('filter-presets');
        if (!container) return;
        
        let html = '';
        this.filterPresets.forEach(preset => {
            html += `
                <button class="preset-btn" data-filters='${JSON.stringify(preset.filters)}'>
                    ${preset.name}
                </button>
            `;
        });
        
        container.innerHTML = html;
        
        // Add event handlers
        container.querySelectorAll('.preset-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const filters = JSON.parse(e.target.dataset.filters);
                this.redirectWithFilters(filters);
            });
        });
    }
    
    async loadFilter(filterId) {
        try {
            const response = await fetch(`../ajax/load_filter.php?action=load_filter&id=${filterId}`);
            const filter = await response.json();
            
            if (filter.filters) {
                this.redirectWithFilters(filter.filters);
            }
        } catch (error) {
            console.error('Error loading filter:', error);
            this.showNotification('Chyba při načítání filtru', 'error');
        }
    }
    
    async deleteFilter(filterId) {
        if (!confirm('Opravdu chcete smazat tento filtr?')) return;
        
        try {
            const response = await fetch('../ajax/load_filter.php?action=delete_filter', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `id=${filterId}`
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showNotification('Filtr byl smazán', 'success');
                this.loadUserFilters(); // Reload the list
            } else {
                this.showNotification('Chyba při mazání filtru', 'error');
            }
        } catch (error) {
            console.error('Error deleting filter:', error);
            this.showNotification('Chyba při mazání filtru', 'error');
        }
    }
    
    showSaveFilterModal() {
        const name = prompt('Zadejte název filtru:');
        if (name) {
            this.saveCurrentFilter(name);
        }
    }
    
    async saveCurrentFilter(name) {
        try {
            const response = await fetch('../ajax/save_filter.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    name: name,
                    filters: this.currentFilters,
                    is_public: false
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showNotification('Filtr byl uložen', 'success');
                this.loadUserFilters(); // Reload the list
            } else {
                this.showNotification('Chyba při ukládání filtru', 'error');
            }
        } catch (error) {
            console.error('Error saving filter:', error);
            this.showNotification('Chyba při ukládání filtru', 'error');
        }
    }
    
    // Export functionality
    initExportButtons() {
        const exportButtons = document.querySelectorAll('.export-btn');
        exportButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                const format = e.target.dataset.format;
                this.exportFilteredData(format);
            });
        });
    }
    
    exportFilteredData(format) {
        const url = new URL('../ajax/export_filtered.php', window.location);
        url.searchParams.set('format', format);
        
        // Add current filters
        Object.entries(this.currentFilters).forEach(([key, value]) => {
            url.searchParams.set(key, value);
        });
        
        // Open in new window to trigger download
        window.open(url.toString(), '_blank');
    }
    
    // Utility functions
    showNotification(message, type = 'info') {
        // Simple notification - can be enhanced with a proper notification library
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show`;
        notification.style.position = 'fixed';
        notification.style.top = '20px';
        notification.style.right = '20px';
        notification.style.zIndex = '9999';
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 5000);
    }
    
    initSavedFilters() {
        // Initialize saved filters functionality
        // This will be called from init()
    }
    
    initFilterPresets() {
        // Initialize filter presets functionality
        // This will be called from init()
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new AdvancedSearch();
});
