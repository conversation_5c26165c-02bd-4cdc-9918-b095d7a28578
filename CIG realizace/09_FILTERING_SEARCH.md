# Phase 09 - Filtering and Search Functionality

## Objective
Implement comprehensive filtering and search capabilities across all order views to improve user productivity and data accessibility.

## Tasks

### 1. Advanced Search System
- Global search across orders, items, and catalog codes
- Auto-complete suggestions
- Search history and saved searches
- Full-text search capabilities

### 2. Multi-Level Filtering
- Filter by sales representative
- Filter by order status and dates
- Filter by technology assignments
- Filter by inventory status

### 3. Saved Filters and Views
- User-specific saved filter combinations
- Quick filter presets
- Export filtered results
- Share filter configurations

### 4. Search Analytics
- Track popular search terms
- Search performance optimization
- User search behavior analysis

## Files to Create

### Search Core
1. `classes/SearchEngine.php`
2. `classes/FilterManager.php`
3. `includes/search_bar.php`
4. `includes/filter_panel.php`

### Search Interface
5. `search/index.php`
6. `search/results.php`
7. `search/advanced.php`
8. `ajax/search_suggestions.php`

### Filter Components
9. `ajax/apply_filters.php`
10. `ajax/save_filter.php`
11. `ajax/load_filter.php`
12. `user/saved_filters.php`

### Assets
13. `assets/css/search.css`
14. `assets/js/search.js`
15. `assets/js/filters.js`

## Search Implementation

### Global Search Bar
```html
<div class="global-search">
    <div class="search-input-group">
        <input type="text" 
               id="global-search" 
               placeholder="Search orders, catalog codes, technologies..."
               autocomplete="off">
        <button type="button" id="search-btn">
            <i class="fas fa-search"></i>
        </button>
        <button type="button" id="advanced-search-btn">
            <i class="fas fa-cog"></i>
        </button>
    </div>
    <div id="search-suggestions" class="search-suggestions"></div>
</div>
```

### SearchEngine Class
```php
class SearchEngine {
    public function globalSearch($query, $filters = [], $limit = 50) {
        $results = [];
        
        // Search in orders
        $results['orders'] = $this->searchOrders($query, $filters, $limit);
        
        // Search in order items
        $results['items'] = $this->searchOrderItems($query, $filters, $limit);
        
        // Search in catalog codes
        $results['catalog'] = $this->searchCatalogCodes($query, $limit);
        
        return $results;
    }
    
    private function searchOrders($query, $filters, $limit) {
        $sql = "SELECT o.*, 
                       MATCH(o.order_code) AGAINST(? IN BOOLEAN MODE) as relevance
                FROM orders o 
                WHERE MATCH(o.order_code) AGAINST(? IN BOOLEAN MODE)";
        
        // Apply filters
        $sql .= $this->buildFilterConditions($filters);
        $sql .= " ORDER BY relevance DESC, o.created_at DESC LIMIT ?";
        
        return $this->executeSearch($sql, [$query, $query, $limit]);
    }
}
```

## Advanced Filtering System

### Filter Panel Interface
```html
<div class="filter-panel">
    <div class="filter-header">
        <h3>Filters</h3>
        <div class="filter-actions">
            <button id="clear-filters">Clear All</button>
            <button id="save-filter">Save Filter</button>
        </div>
    </div>
    
    <div class="filter-groups">
        <!-- Sales Representative Filter -->
        <div class="filter-group">
            <label>Sales Representative</label>
            <select multiple id="sales-rep-filter" class="multi-select">
                <option value="VP">Vláďa (VP)</option>
                <option value="J">Jirka (J)</option>
                <option value="NK">Nikol (NK)</option>
                <option value="MR">Mirka (MR)</option>
                <option value="D">Daniela (D)</option>
                <option value="CI">Czech Image (CI)</option>
            </select>
        </div>
        
        <!-- Order Status Filter -->
        <div class="filter-group">
            <label>Preview Status</label>
            <div class="checkbox-group">
                <label><input type="checkbox" value="not_created"> Not Created</label>
                <label><input type="checkbox" value="sent_to_client"> Sent to Client</label>
                <label><input type="checkbox" value="approved"> Approved</label>
            </div>
        </div>
        
        <!-- Date Range Filter -->
        <div class="filter-group">
            <label>Order Date Range</label>
            <div class="date-range">
                <input type="date" id="date-from" placeholder="From">
                <input type="date" id="date-to" placeholder="To">
            </div>
        </div>
        
        <!-- Technology Filter -->
        <div class="filter-group">
            <label>Technology</label>
            <input type="text" id="technology-filter" placeholder="Search technologies...">
            <div id="technology-suggestions" class="suggestions-list"></div>
        </div>
        
        <!-- Inventory Status Filter -->
        <div class="filter-group">
            <label>Inventory Status</label>
            <div class="checkbox-group">
                <label><input type="checkbox" value="not_in_stock"> Not in Stock</label>
                <label><input type="checkbox" value="ordered"> Ordered</label>
                <label><input type="checkbox" value="in_stock"> In Stock</label>
            </div>
        </div>
    </div>
    
    <div class="filter-footer">
        <button id="apply-filters" class="btn-primary">Apply Filters</button>
    </div>
</div>
```

### FilterManager Class
```php
class FilterManager {
    public function buildQuery($baseQuery, $filters) {
        $conditions = [];
        $params = [];
        
        // Sales representative filter
        if (!empty($filters['sales_rep'])) {
            $placeholders = str_repeat('?,', count($filters['sales_rep']) - 1) . '?';
            $conditions[] = "o.sales_rep IN ($placeholders)";
            $params = array_merge($params, $filters['sales_rep']);
        }
        
        // Preview status filter
        if (!empty($filters['preview_status'])) {
            $placeholders = str_repeat('?,', count($filters['preview_status']) - 1) . '?';
            $conditions[] = "o.preview_status IN ($placeholders)";
            $params = array_merge($params, $filters['preview_status']);
        }
        
        // Date range filter
        if (!empty($filters['date_from'])) {
            $conditions[] = "o.order_date >= ?";
            $params[] = $filters['date_from'];
        }
        if (!empty($filters['date_to'])) {
            $conditions[] = "o.order_date <= ?";
            $params[] = $filters['date_to'];
        }
        
        // Technology filter
        if (!empty($filters['technology'])) {
            $conditions[] = "EXISTS (SELECT 1 FROM order_items oi WHERE oi.order_id = o.id AND oi.technology_assignment LIKE ?)";
            $params[] = '%' . $filters['technology'] . '%';
        }
        
        if (!empty($conditions)) {
            $baseQuery .= " WHERE " . implode(" AND ", $conditions);
        }
        
        return ['query' => $baseQuery, 'params' => $params];
    }
    
    public function saveFilter($userId, $name, $filters) {
        $sql = "INSERT INTO saved_filters (user_id, name, filters, created_at) 
                VALUES (?, ?, ?, NOW())
                ON DUPLICATE KEY UPDATE filters = ?, updated_at = NOW()";
        
        $filtersJson = json_encode($filters);
        return $this->db->execute($sql, [$userId, $name, $filtersJson, $filtersJson]);
    }
}
```

## Auto-Complete and Suggestions

### Search Suggestions
```javascript
$('#global-search').on('input', function() {
    var query = $(this).val();
    
    if (query.length >= 2) {
        $.ajax({
            url: '../ajax/search_suggestions.php',
            data: {q: query},
            success: function(response) {
                displaySuggestions(response);
            }
        });
    } else {
        hideSuggestions();
    }
});

function displaySuggestions(suggestions) {
    var html = '';
    
    // Order code suggestions
    if (suggestions.orders.length > 0) {
        html += '<div class="suggestion-group">';
        html += '<div class="suggestion-header">Orders</div>';
        suggestions.orders.forEach(function(order) {
            html += '<div class="suggestion-item" data-type="order" data-id="' + order.id + '">';
            html += '<strong>' + order.order_code + '</strong> - ' + order.sales_rep_name;
            html += '</div>';
        });
        html += '</div>';
    }
    
    // Catalog code suggestions
    if (suggestions.catalog.length > 0) {
        html += '<div class="suggestion-group">';
        html += '<div class="suggestion-header">Catalog Codes</div>';
        suggestions.catalog.forEach(function(item) {
            html += '<div class="suggestion-item" data-type="catalog" data-code="' + item.catalog_code + '">';
            html += item.catalog_code;
            html += '</div>';
        });
        html += '</div>';
    }
    
    $('#search-suggestions').html(html).show();
}
```

### Technology Auto-Complete
```php
// ajax/search_suggestions.php
if (isset($_GET['type']) && $_GET['type'] === 'technology') {
    $query = $_GET['q'];
    
    $sql = "SELECT DISTINCT technology_assignment 
            FROM order_items 
            WHERE technology_assignment LIKE ? 
              AND technology_assignment IS NOT NULL 
              AND technology_assignment != ''
            ORDER BY technology_assignment 
            LIMIT 10";
    
    $results = $db->query($sql, ['%' . $query . '%']);
    echo json_encode($results);
}
```

## Saved Filters and Quick Presets

### Saved Filters Interface
```html
<div class="saved-filters">
    <h4>Saved Filters</h4>
    <div class="filter-list">
        <div class="filter-item">
            <span class="filter-name">My Pending Orders</span>
            <div class="filter-actions">
                <button class="load-filter" data-filter-id="1">Load</button>
                <button class="edit-filter" data-filter-id="1">Edit</button>
                <button class="delete-filter" data-filter-id="1">Delete</button>
            </div>
        </div>
    </div>
    
    <div class="quick-presets">
        <h4>Quick Presets</h4>
        <button class="preset-btn" data-preset="today">Today's Orders</button>
        <button class="preset-btn" data-preset="this-week">This Week</button>
        <button class="preset-btn" data-preset="pending-previews">Pending Previews</button>
        <button class="preset-btn" data-preset="overdue">Overdue Orders</button>
    </div>
</div>
```

### Quick Preset Definitions
```javascript
var quickPresets = {
    'today': {
        'date_from': getCurrentDate(),
        'date_to': getCurrentDate()
    },
    'this-week': {
        'date_from': getWeekStart(),
        'date_to': getWeekEnd()
    },
    'pending-previews': {
        'preview_status': ['not_created', 'sent_to_client']
    },
    'overdue': {
        'overdue_only': true
    }
};
```

## Search Performance Optimization

### Database Indexes
```sql
-- Full-text search indexes
ALTER TABLE orders ADD FULLTEXT(order_code);
ALTER TABLE order_items ADD FULLTEXT(catalog_code, technology_assignment);

-- Composite indexes for filtering
CREATE INDEX idx_orders_status_date ON orders(preview_status, order_date);
CREATE INDEX idx_orders_sales_rep_date ON orders(sales_rep, order_date);
CREATE INDEX idx_items_technology ON order_items(technology_assignment);
```

### Search Caching
```php
class SearchCache {
    private $redis;
    
    public function getCachedResults($cacheKey) {
        return $this->redis->get($cacheKey);
    }
    
    public function cacheResults($cacheKey, $results, $ttl = 300) {
        $this->redis->setex($cacheKey, $ttl, json_encode($results));
    }
    
    public function generateCacheKey($query, $filters) {
        return 'search:' . md5($query . serialize($filters));
    }
}
```

## Export Functionality

### Export Filtered Results
```php
// export/filtered_results.php
function exportFilteredResults($filters, $format = 'csv') {
    $filterManager = new FilterManager();
    $query = "SELECT o.*, oi.catalog_code, oi.quantity, oi.technology_assignment 
              FROM orders o 
              LEFT JOIN order_items oi ON o.id = oi.order_id";
    
    $result = $filterManager->buildQuery($query, $filters);
    $data = $db->query($result['query'], $result['params']);
    
    switch ($format) {
        case 'csv':
            exportToCSV($data);
            break;
        case 'excel':
            exportToExcel($data);
            break;
        case 'pdf':
            exportToPDF($data);
            break;
    }
}
```

## Expected Outcome
- Comprehensive search functionality
- Advanced filtering system
- Auto-complete suggestions
- Saved filters and quick presets
- Export capabilities
- Optimized search performance

## Next Phase
Phase 10 - UI/UX Improvements and Final Testing
