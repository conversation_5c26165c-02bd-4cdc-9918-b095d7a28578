<?php
/**
 * User Notification Settings
 * CIG Realizace - Phase 08
 */

require_once '../includes/auth_check.php';
require_once '../includes/email_functions.php';

$current_user = getCurrentUser();
$page_title = 'Nastavení notifikací';

// Handle form submission
$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'update_preferences') {
        $preferences = [
            'order_completion' => isset($_POST['order_completion']) ? 1 : 0,
            'preview_status_change' => isset($_POST['preview_status_change']) ? 1 : 0,
            'overdue_alerts' => isset($_POST['overdue_alerts']) ? 1 : 0,
            'daily_summary' => isset($_POST['daily_summary']) ? 1 : 0,
            'weekly_summary' => isset($_POST['weekly_summary']) ? 1 : 0,
            'email_format' => $_POST['email_format'] ?? 'html',
            'frequency' => $_POST['frequency'] ?? 'immediate'
        ];
        
        if (updateUserNotificationPreferences($current_user['id'], $preferences)) {
            $message = 'Nastavení notifikací bylo úspěšně uloženo.';
        } else {
            $error = 'Nepodařilo se uložit nastavení notifikací.';
        }
    }
}

// Get current preferences
$preferences = getUserNotificationPreferences($current_user['id']);
if (!$preferences) {
    $error = 'Nepodařilo se načíst nastavení notifikací.';
    $preferences = [
        'order_completion' => 1,
        'preview_status_change' => 1,
        'overdue_alerts' => 1,
        'daily_summary' => 0,
        'weekly_summary' => 1,
        'email_format' => 'html',
        'frequency' => 'immediate'
    ];
}
?>

<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($page_title) ?> - CIG Realizace</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .settings-card {
            border: none;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            border-radius: 15px;
            overflow: hidden;
        }
        .settings-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .settings-body {
            padding: 30px;
        }
        .notification-group {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #667eea;
        }
        .notification-group h5 {
            color: #2c3e50;
            margin-bottom: 15px;
        }
        .form-check {
            margin-bottom: 15px;
        }
        .form-check-input:checked {
            background-color: #667eea;
            border-color: #667eea;
        }
        .form-check-label {
            font-weight: 500;
        }
        .help-text {
            font-size: 0.875rem;
            color: #6c757d;
            margin-top: 5px;
        }
        .save-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .save-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .frequency-info {
            background: #e7f3ff;
            border: 1px solid #b3d7ff;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
        }
    </style>
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="../dashboard.php">
                <i class="fas fa-cog"></i> CIG Realizace
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="../dashboard.php">
                    <i class="fas fa-arrow-left"></i> Zpět na dashboard
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="settings-card">
                    <div class="settings-header">
                        <h1 class="mb-0">
                            <i class="fas fa-bell"></i>
                            <?= htmlspecialchars($page_title) ?>
                        </h1>
                        <p class="mb-0 mt-2 opacity-75">
                            Nastavte si, jaké emailové notifikace chcete dostávat
                        </p>
                    </div>
                    
                    <div class="settings-body">
                        <!-- Messages -->
                        <?php if ($message): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle"></i> <?= htmlspecialchars($message) ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        <?php endif; ?>

                        <?php if ($error): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle"></i> <?= htmlspecialchars($error) ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        <?php endif; ?>

                        <form method="post">
                            <input type="hidden" name="action" value="update_preferences">
                            
                            <!-- Order Notifications -->
                            <div class="notification-group">
                                <h5><i class="fas fa-shopping-cart text-primary"></i> Notifikace objednávek</h5>
                                
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="order_completion" 
                                           id="order_completion" <?= $preferences['order_completion'] ? 'checked' : '' ?>>
                                    <label class="form-check-label" for="order_completion">
                                        Dokončení objednávky
                                    </label>
                                    <div class="help-text">
                                        Dostanete email, když bude Vaše objednávka dokončena a připravena k dodání.
                                    </div>
                                </div>
                                
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="preview_status_change" 
                                           id="preview_status_change" <?= $preferences['preview_status_change'] ? 'checked' : '' ?>>
                                    <label class="form-check-label" for="preview_status_change">
                                        Změny stavu náhledu
                                    </label>
                                    <div class="help-text">
                                        Dostanete email při změně stavu náhledu (schváleno, zamítnuto, atd.).
                                    </div>
                                </div>
                                
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="overdue_alerts" 
                                           id="overdue_alerts" <?= $preferences['overdue_alerts'] ? 'checked' : '' ?>>
                                    <label class="form-check-label" for="overdue_alerts">
                                        Upozornění na zpoždění
                                    </label>
                                    <div class="help-text">
                                        Dostanete email, když objednávka překročí plánovaný termín dodání.
                                    </div>
                                </div>
                            </div>

                            <!-- Summary Reports -->
                            <div class="notification-group">
                                <h5><i class="fas fa-chart-bar text-success"></i> Souhrnné reporty</h5>
                                
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="daily_summary" 
                                           id="daily_summary" <?= $preferences['daily_summary'] ? 'checked' : '' ?>>
                                    <label class="form-check-label" for="daily_summary">
                                        Denní souhrn
                                    </label>
                                    <div class="help-text">
                                        Dostanete denní email se souhrnem Vaší aktivity a stavu objednávek.
                                    </div>
                                </div>
                                
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="weekly_summary" 
                                           id="weekly_summary" <?= $preferences['weekly_summary'] ? 'checked' : '' ?>>
                                    <label class="form-check-label" for="weekly_summary">
                                        Týdenní souhrn
                                    </label>
                                    <div class="help-text">
                                        Dostanete týdenní email se souhrnem aktivity za celý týden.
                                    </div>
                                </div>
                            </div>

                            <!-- Email Format -->
                            <div class="notification-group">
                                <h5><i class="fas fa-envelope text-info"></i> Formát emailů</h5>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <label for="email_format" class="form-label">Formát emailů</label>
                                        <select name="email_format" id="email_format" class="form-select">
                                            <option value="html" <?= $preferences['email_format'] === 'html' ? 'selected' : '' ?>>
                                                HTML (s obrázky a formátováním)
                                            </option>
                                            <option value="text" <?= $preferences['email_format'] === 'text' ? 'selected' : '' ?>>
                                                Prostý text
                                            </option>
                                        </select>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <label for="frequency" class="form-label">Frekvence odesílání</label>
                                        <select name="frequency" id="frequency" class="form-select">
                                            <option value="immediate" <?= $preferences['frequency'] === 'immediate' ? 'selected' : '' ?>>
                                                Okamžitě
                                            </option>
                                            <option value="daily" <?= $preferences['frequency'] === 'daily' ? 'selected' : '' ?>>
                                                Denní souhrn
                                            </option>
                                            <option value="weekly" <?= $preferences['frequency'] === 'weekly' ? 'selected' : '' ?>>
                                                Týdenní souhrn
                                            </option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <!-- Frequency Info -->
                            <div class="frequency-info">
                                <h6><i class="fas fa-info-circle text-info"></i> Informace o frekvenci</h6>
                                <ul class="mb-0">
                                    <li><strong>Okamžitě:</strong> Notifikace se odesílají ihned po události</li>
                                    <li><strong>Denní souhrn:</strong> Všechny notifikace za den se odešlou v jednom emailu</li>
                                    <li><strong>Týdenní souhrn:</strong> Všechny notifikace za týden se odešlou v jednom emailu</li>
                                </ul>
                            </div>

                            <!-- Save Button -->
                            <div class="text-center mt-4">
                                <button type="submit" class="btn btn-primary save-button">
                                    <i class="fas fa-save"></i> Uložit nastavení
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- Additional Info -->
                <div class="card mt-4">
                    <div class="card-body">
                        <h6><i class="fas fa-shield-alt text-success"></i> Ochrana soukromí</h6>
                        <p class="mb-2">
                            Vaše emailová adresa: <strong><?= htmlspecialchars($current_user['email']) ?></strong>
                        </p>
                        <p class="text-muted mb-0">
                            Vaše emailová adresa nebude sdílena s třetími stranami. 
                            Můžete se kdykoli odhlásit z notifikací pomocí odkazu v patičce každého emailu.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Update frequency info based on selection
        document.getElementById('frequency').addEventListener('change', function() {
            const frequency = this.value;
            const checkboxes = document.querySelectorAll('input[type="checkbox"]');
            
            if (frequency === 'daily' || frequency === 'weekly') {
                // Show info about batched notifications
                console.log('Frequency changed to:', frequency);
            }
        });
        
        // Auto-save indication
        const form = document.querySelector('form');
        const saveButton = document.querySelector('.save-button');
        
        form.addEventListener('change', function() {
            saveButton.innerHTML = '<i class="fas fa-save"></i> Uložit změny';
            saveButton.classList.add('btn-warning');
            saveButton.classList.remove('btn-primary');
        });
    </script>
</body>
</html>
