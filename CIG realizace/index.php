<?php
// Start session and check if user is already logged in
session_start();

// Include database configuration
require_once 'config/database.php';

// If user is already logged in, redirect to dashboard
if (isset($_SESSION['user_id'])) {
    header('Location: dashboard.php');
    exit;
}

// Test database connection
$dbConnected = false;
$dbError = '';
try {
    $pdo = getDbConnection();
    $dbConnected = true;
} catch (PDOException $e) {
    $dbError = $e->getMessage();
}

// Handle login form submission
$loginError = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['login'])) {
    $username = trim($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';

    if (empty($username) || empty($password)) {
        $loginError = 'Prosím vyplňte všechna pole.';
    } else if ($dbConnected) {
        try {
            $stmt = $pdo->prepare("SELECT id, username, password, role, full_name FROM users WHERE username = ? AND is_active = 1");
            $stmt->execute([$username]);
            $user = $stmt->fetch();

            if ($user && password_verify($password, $user['password'])) {
                // Login successful
                session_regenerate_id(true);
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['role'] = $user['role'];
                $_SESSION['full_name'] = $user['full_name'];
                $_SESSION['login_time'] = time();

                header('Location: dashboard.php');
                exit;
            } else {
                $loginError = 'Neplatné přihlašovací údaje.';
            }
        } catch (PDOException $e) {
            $loginError = 'Chyba při přihlašování. Zkuste to znovu.';
        }
    } else {
        $loginError = 'Databáze není dostupná.';
    }
}
?>
<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CIG Realizace - Přihlášení</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/login.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="login-container">
        <div class="login-card">
            <div class="text-center mb-4">
                <i class="fas fa-clipboard-list fa-3x text-primary mb-3"></i>
                <h2 class="fw-bold">CIG Realizace</h2>
                <p class="text-muted">Systém pro správu objednávek</p>
            </div>

            <?php if (!$dbConnected): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>Databáze není dostupná!</strong><br>
                <?= htmlspecialchars($dbError) ?>
                <hr>
                <a href="install.php" class="btn btn-warning btn-sm">
                    <i class="fas fa-database me-1"></i>Spustit instalaci
                </a>
            </div>
            <?php else: ?>

            <?php if (isset($_SESSION['logout_message'])): ?>
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                <?= htmlspecialchars($_SESSION['logout_message']) ?>
            </div>
            <?php unset($_SESSION['logout_message']); ?>
            <?php endif; ?>

            <?php if ($loginError): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle me-2"></i>
                <?= htmlspecialchars($loginError) ?>
            </div>
            <?php endif; ?>

            <form method="POST" action="">
                <div class="mb-3">
                    <label for="username" class="form-label">
                        <i class="fas fa-user me-1"></i>Uživatelské jméno
                    </label>
                    <input type="text" class="form-control" id="username" name="username"
                           value="<?= htmlspecialchars($_POST['username'] ?? '') ?>" required>
                </div>

                <div class="mb-3">
                    <label for="password" class="form-label">
                        <i class="fas fa-lock me-1"></i>Heslo
                    </label>
                    <input type="password" class="form-control" id="password" name="password" required>
                </div>

                <button type="submit" name="login" class="btn btn-primary w-100">
                    <i class="fas fa-sign-in-alt me-2"></i>Přihlásit se
                </button>
            </form>

            <div class="mt-4 text-center">
                <small class="text-muted">
                    <strong>Testovací účty:</strong><br>
                    Admin: admin / admin123<br>
                    Obchodník: sales_test / test123<br>
                    Grafik: designer_test / test123<br>
                    Realizátor: producer_test / test123
                </small>
            </div>

            <?php endif; ?>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
