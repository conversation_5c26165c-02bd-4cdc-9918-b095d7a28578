# Phase 05 - Order Detail View and Status Management

## Objective
Create detailed order view with ability to manage preview status, inventory status, and order items.

## Tasks

### 1. Order Detail Page
- `orders/detail.php` - Complete order information
- Order header with basic info
- Items list with technology assignment
- Status management forms
- Action buttons based on user role

### 2. Status Management
- Preview status updates with timestamp tracking
- Inventory status management for individual items
- Technology assignment for order items
- Item relevance toggle (delete irrelevant items)

### 3. Order Items Management
- Display all order items in table
- Edit technology assignments
- Mark items as irrelevant
- Update inventory status
- Quantity and catalog code display

### 4. Form Processing
- AJAX forms for status updates
- Validation and error handling
- Success/error notifications
- Real-time updates without page reload

## Files to Create

### Order Detail
1. `orders/detail.php`
2. `orders/order_header.php`
3. `orders/order_items_table.php`
4. `orders/status_forms.php`

### AJAX Processing
5. `ajax/update_preview_status.php`
6. `ajax/update_item_status.php`
7. `ajax/update_technology.php`
8. `ajax/toggle_item_relevance.php`

### Components
9. `includes/status_badges.php`
10. `includes/technology_selector.php`

### Styling
11. `assets/css/order_detail.css`
12. `assets/js/order_detail.js`

## Order Detail Layout

### Order Header Section
```php
- Order Code: [25VP-00001]
- Sales Representative: [Vláďa (VP)]
- Creation Date: [02.01.2025]
- Preview Status: [Badge with color]
- Last Status Change: [Date + User]
- Expected Delivery: [Date if approved]
```

### Preview Status Management
```html
<form id="preview-status-form">
    <select name="preview_status">
        <option value="not_created">Není vytvořen</option>
        <option value="sent_to_client">Odesláno na klienta</option>
        <option value="approved">Schváleno</option>
    </select>
    <button type="submit">Update Status</button>
</form>
```

### Order Items Table
| Catalog Code | Quantity | Technology | Inventory Status | Ordered Date | Stocked Date | Actions |
|--------------|----------|------------|------------------|--------------|--------------|---------|
| 13030683-03 | 1500.0000 | [Dropdown] | [Badge] | 03.01.2025 | 06.01.2025 | [Edit][Delete] |

### Technology Assignment
- Dropdown with available technologies
- Manual text input option
- Save button for each item
- Bulk assignment option

## Status Management Logic

### Preview Status Flow
1. **not_created** → **sent_to_client** → **approved**
2. When changed to "approved":
   - Set `preview_approved_date` to current date
   - Calculate `expected_delivery_date` (+14 days)
   - Move order to calendar view

### Inventory Status Logic
```php
// Auto-determine based on dates
if ($goods_stocked_date) {
    $status = 'in_stock';
} elseif ($goods_ordered_date) {
    $status = 'ordered';  
} else {
    $status = 'not_in_stock';
}
```

### Item Relevance
- Toggle button to mark items as irrelevant
- Irrelevant items hidden from main views
- Soft delete (is_relevant = FALSE)

## User Role Permissions

### Admin
- Full access to all functions
- Can change any status
- Can delete items
- Can assign technologies

### Obchodník (Sales)
- Can update preview status
- Can assign technologies
- Can mark items as irrelevant
- Cannot change inventory status

### Grafik (Designer)
- Can update preview status
- Can assign technologies
- Read-only for other fields

### Realizátor (Producer)
- Can update inventory status
- Can assign technologies
- Read-only for preview status

## AJAX Implementation

### Status Update
```javascript
$('#preview-status-form').on('submit', function(e) {
    e.preventDefault();
    $.ajax({
        url: '../ajax/update_preview_status.php',
        method: 'POST',
        data: $(this).serialize(),
        success: function(response) {
            // Update UI and show notification
        }
    });
});
```

### Technology Assignment
```javascript
$('.technology-select').on('change', function() {
    var itemId = $(this).data('item-id');
    var technology = $(this).val();
    
    $.ajax({
        url: '../ajax/update_technology.php',
        method: 'POST',
        data: {item_id: itemId, technology: technology},
        success: function(response) {
            // Show success message
        }
    });
});
```

## Database Updates

### Order History Tracking
```sql
INSERT INTO order_history (order_id, user_id, action_type, old_value, new_value, description)
VALUES (?, ?, 'preview_status_change', ?, ?, 'Preview status changed from X to Y')
```

### Status Update Queries
```sql
-- Update preview status
UPDATE orders 
SET preview_status = ?, 
    preview_status_date = NOW(),
    preview_approved_date = CASE WHEN ? = 'approved' THEN CURDATE() ELSE preview_approved_date END,
    expected_delivery_date = CASE WHEN ? = 'approved' THEN DATE_ADD(CURDATE(), INTERVAL 14 DAY) ELSE expected_delivery_date END
WHERE id = ?

-- Update item technology
UPDATE order_items 
SET technology_assignment = ?, updated_at = NOW() 
WHERE id = ?
```

## Validation Rules
- Preview status must be valid enum value
- Technology assignment max 100 characters
- Only authorized users can make changes
- Order must exist and be accessible

## Expected Outcome
- Complete order detail view
- Working status management
- Technology assignment functionality
- Item relevance management
- AJAX-powered updates
- Role-based permissions working

## Next Phase
Phase 06 - Calendar View for Approved Orders
