<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CIG Realizace - Instalace databáze</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .install-container { max-width: 800px; margin: 50px auto; }
        .step { margin: 20px 0; padding: 15px; border-radius: 5px; }
        .step.success { background-color: #d4edda; border: 1px solid #c3e6cb; }
        .step.error { background-color: #f8d7da; border: 1px solid #f5c6cb; }
        .step.pending { background-color: #fff3cd; border: 1px solid #ffeaa7; }
    </style>
</head>
<body>
    <div class="container install-container">
        <h1 class="text-center mb-4">CIG Realizace - Instalace systému</h1>
        
        <?php
        // Include database configuration
        require_once 'config/database.php';
        
        $steps = [];
        $hasErrors = false;
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['install'])) {
            
            // Step 1: Test database connection
            try {
                if ($db_config['type'] === 'sqlite') {
                    // For SQLite, just test if we can create the database file
                    $dataDir = dirname($db_config['sqlite_path']);
                    if (!is_dir($dataDir)) {
                        mkdir($dataDir, 0755, true);
                    }
                    $dsn = "sqlite:" . $db_config['sqlite_path'];
                    $pdo = new PDO($dsn, null, null, $db_config['options']);
                    $steps[] = ['title' => 'Připojení k SQLite databázi', 'status' => 'success', 'message' => 'Úspěšně připojeno'];
                } else {
                    // MySQL connection
                    $temp_config = $db_config;
                    unset($temp_config['dbname']);
                    $dsn = "mysql:host={$temp_config['host']};charset={$temp_config['charset']}";
                    $pdo = new PDO($dsn, $temp_config['username'], $temp_config['password'], $temp_config['options']);
                    $steps[] = ['title' => 'Připojení k MySQL serveru', 'status' => 'success', 'message' => 'Úspěšně připojeno'];
                }
            } catch (PDOException $e) {
                $steps[] = ['title' => 'Připojení k databázi', 'status' => 'error', 'message' => 'Chyba: ' . $e->getMessage()];
                $hasErrors = true;
            }
            
            if (!$hasErrors) {
                // Step 2: Create database (only for MySQL)
                try {
                    if ($db_config['type'] === 'mysql') {
                        if (file_exists('sql/01_create_database.sql')) {
                            $sql = file_get_contents('sql/01_create_database.sql');
                            $statements = array_filter(array_map('trim', explode(';', $sql)));

                            foreach ($statements as $statement) {
                                if (!empty($statement)) {
                                    $pdo->exec($statement);
                                }
                            }
                            $steps[] = ['title' => 'Vytvoření databáze', 'status' => 'success', 'message' => 'Databáze "cig_realizace" byla vytvořena'];
                        } else {
                            throw new Exception('SQL soubor 01_create_database.sql nebyl nalezen');
                        }
                    } else {
                        // SQLite doesn't need separate database creation
                        $steps[] = ['title' => 'Vytvoření databáze', 'status' => 'success', 'message' => 'SQLite databáze je připravena'];
                    }
                } catch (Exception $e) {
                    $steps[] = ['title' => 'Vytvoření databáze', 'status' => 'error', 'message' => 'Chyba: ' . $e->getMessage()];
                    $hasErrors = true;
                }
            }
            
            if (!$hasErrors) {
                // Step 3: Create tables
                try {
                    if (file_exists('sql/02_create_tables.sql')) {
                        $pdo_db = getDbConnection(); // Now connect to specific database
                        $sql = file_get_contents('sql/02_create_tables.sql');
                        $statements = array_filter(array_map('trim', explode(';', $sql)));
                        
                        foreach ($statements as $statement) {
                            if (!empty($statement)) {
                                $pdo_db->exec($statement);
                            }
                        }
                        $steps[] = ['title' => 'Vytvoření tabulek', 'status' => 'success', 'message' => 'Všechny tabulky byly vytvořeny'];
                    } else {
                        throw new Exception('SQL soubor 02_create_tables.sql nebyl nalezen');
                    }
                } catch (Exception $e) {
                    $steps[] = ['title' => 'Vytvoření tabulek', 'status' => 'error', 'message' => 'Chyba: ' . $e->getMessage()];
                    $hasErrors = true;
                }
            }
            
            if (!$hasErrors) {
                // Step 4: Insert default data
                try {
                    if (file_exists('sql/03_insert_default_data.sql')) {
                        $pdo_db = getDbConnection();
                        $sql = file_get_contents('sql/03_insert_default_data.sql');
                        $statements = array_filter(array_map('trim', explode(';', $sql)));
                        
                        foreach ($statements as $statement) {
                            if (!empty($statement)) {
                                $pdo_db->exec($statement);
                            }
                        }
                        $steps[] = ['title' => 'Vložení výchozích dat', 'status' => 'success', 'message' => 'Výchozí uživatelé a technologie byly vytvořeny'];
                    } else {
                        throw new Exception('SQL soubor 03_insert_default_data.sql nebyl nalezen');
                    }
                } catch (Exception $e) {
                    $steps[] = ['title' => 'Vložení výchozích dat', 'status' => 'error', 'message' => 'Chyba: ' . $e->getMessage()];
                    $hasErrors = true;
                }
            }
            
            if (!$hasErrors) {
                // Step 5: Create indexes
                try {
                    if (file_exists('sql/04_indexes.sql')) {
                        $pdo_db = getDbConnection();
                        $sql = file_get_contents('sql/04_indexes.sql');
                        $statements = array_filter(array_map('trim', explode(';', $sql)));
                        
                        foreach ($statements as $statement) {
                            if (!empty($statement)) {
                                $pdo_db->exec($statement);
                            }
                        }
                        $steps[] = ['title' => 'Vytvoření indexů', 'status' => 'success', 'message' => 'Indexy pro optimalizaci výkonu byly vytvořeny'];
                    } else {
                        throw new Exception('SQL soubor 04_indexes.sql nebyl nalezen');
                    }
                } catch (Exception $e) {
                    $steps[] = ['title' => 'Vytvoření indexů', 'status' => 'error', 'message' => 'Chyba: ' . $e->getMessage()];
                    $hasErrors = true;
                }
            }
            
            if (!$hasErrors) {
                $steps[] = ['title' => 'Instalace dokončena', 'status' => 'success', 'message' => 'Systém je připraven k použití. Výchozí přihlašovací údaje: admin / admin123'];
            }
        }
        ?>
        
        <?php if (empty($steps)): ?>
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">Instalace databáze</h5>
                <p class="card-text">
                    Tento script vytvoří databázovou strukturu pro systém CIG Realizace.
                    Ujistěte se, že máte správně nakonfigurované připojení k databázi v souboru <code>config/database.php</code>.
                </p>
                
                <div class="alert alert-info">
                    <h6>Co bude provedeno:</h6>
                    <ul>
                        <li>Vytvoření databáze <strong>cig_realizace</strong></li>
                        <li>Vytvoření tabulek (users, orders, order_items, order_history, technologies)</li>
                        <li>Vložení výchozích uživatelů a dat</li>
                        <li>Vytvoření indexů pro optimalizaci výkonu</li>
                    </ul>
                </div>
                
                <form method="post">
                    <button type="submit" name="install" class="btn btn-primary btn-lg">Spustit instalaci</button>
                </form>
            </div>
        </div>
        <?php else: ?>
        
        <div class="installation-results">
            <?php foreach ($steps as $step): ?>
            <div class="step <?= $step['status'] ?>">
                <h5><?= htmlspecialchars($step['title']) ?></h5>
                <p><?= htmlspecialchars($step['message']) ?></p>
            </div>
            <?php endforeach; ?>
            
            <?php if (!$hasErrors): ?>
            <div class="text-center mt-4">
                <a href="index.php" class="btn btn-success btn-lg">Přejít do systému</a>
            </div>
            <?php else: ?>
            <div class="text-center mt-4">
                <a href="install.php" class="btn btn-warning">Zkusit znovu</a>
            </div>
            <?php endif; ?>
        </div>
        
        <?php endif; ?>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
