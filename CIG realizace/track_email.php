<?php
/**
 * Email Tracking Pixel
 * CIG Realizace - Phase 08
 * 
 * This script tracks when emails are opened by users.
 * It returns a 1x1 transparent pixel image.
 */

require_once 'includes/email_functions.php';

// Get tracking ID from URL
$trackingId = intval($_GET['id'] ?? 0);

if ($trackingId > 0) {
    // Get user agent and IP address
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? null;
    $ipAddress = $_SERVER['REMOTE_ADDR'] ?? null;
    
    // Track the email open
    trackEmailOpen($trackingId, $userAgent, $ipAddress);
}

// Return 1x1 transparent pixel
header('Content-Type: image/gif');
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

// 1x1 transparent GIF in base64
echo base64_decode('R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7');
exit;
?>
