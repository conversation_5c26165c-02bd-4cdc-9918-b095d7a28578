# Phase 08 - Email Notifications System

## Objective
Implement email notification system to inform sales representatives and other users about order status changes and completions.

## Tasks

### 1. Email Configuration
- SMTP configuration for Webglobe hosting
- Email templates system
- Queue management for bulk emails
- Error handling and retry logic

### 2. Notification Triggers
- Order completion notifications to sales reps
- Preview status change notifications
- Overdue order alerts
- Daily/weekly summary reports

### 3. Email Templates
- HTML email templates with company branding
- Plain text alternatives
- Dynamic content insertion
- Responsive design for mobile

### 4. Notification Management
- User notification preferences
- Email delivery tracking
- Unsubscribe functionality
- Admin notification controls

## Files to Create

### Email Core
1. `classes/EmailManager.php`
2. `classes/NotificationQueue.php`
3. `config/email.php`
4. `includes/email_functions.php`

### Email Templates
5. `templates/email/order_completed.php`
6. `templates/email/preview_status_changed.php`
7. `templates/email/overdue_alert.php`
8. `templates/email/daily_summary.php`

### Notification System
9. `admin/notifications.php`
10. `admin/email_queue.php`
11. `cron/send_notifications.php`
12. `cron/daily_reports.php`

### User Preferences
13. `user/notification_settings.php`
14. `ajax/update_notification_prefs.php`

## Email Configuration

### SMTP Settings for Webglobe
```php
// config/email.php
$email_config = [
    'smtp_host' => 'smtp.webglobe.com',
    'smtp_port' => 587,
    'smtp_username' => '<EMAIL>',
    'smtp_password' => 'your-password',
    'smtp_encryption' => 'tls',
    'from_email' => '<EMAIL>',
    'from_name' => 'CIG Realizace System'
];
```

### EmailManager Class
```php
class EmailManager {
    public function sendOrderCompletionNotification($orderId, $salesRepEmail) {
        // Load order data
        // Load email template
        // Send email with order details
    }
    
    public function sendPreviewStatusNotification($orderId, $oldStatus, $newStatus) {
        // Notify relevant users about status change
    }
    
    public function sendOverdueAlert($overdueOrders) {
        // Send alert about overdue orders
    }
    
    public function queueEmail($to, $subject, $template, $data) {
        // Add email to queue for batch processing
    }
}
```

## Email Templates

### Order Completion Template
```html
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Order Completed - {{order_code}}</title>
    <style>
        body { font-family: Arial, sans-serif; }
        .header { background: #2c3e50; color: white; padding: 20px; }
        .content { padding: 20px; }
        .order-details { background: #f8f9fa; padding: 15px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="header">
        <h1>CIG Realizace</h1>
        <h2>Order Completed</h2>
    </div>
    
    <div class="content">
        <p>Dear {{sales_rep_name}},</p>
        
        <p>Your order <strong>{{order_code}}</strong> has been completed and is ready for delivery.</p>
        
        <div class="order-details">
            <h3>Order Details:</h3>
            <ul>
                <li><strong>Order Code:</strong> {{order_code}}</li>
                <li><strong>Creation Date:</strong> {{order_date}}</li>
                <li><strong>Completion Date:</strong> {{completion_date}}</li>
                <li><strong>Items Count:</strong> {{items_count}}</li>
                <li><strong>Technologies:</strong> {{technologies}}</li>
            </ul>
        </div>
        
        <p>You can view the complete order details <a href="{{order_url}}">here</a>.</p>
        
        <p>Best regards,<br>CIG Realizace Team</p>
    </div>
</body>
</html>
```

### Preview Status Change Template
```html
<div class="content">
    <p>Dear {{recipient_name}},</p>
    
    <p>The preview status for order <strong>{{order_code}}</strong> has been changed.</p>
    
    <div class="status-change">
        <p><strong>Previous Status:</strong> {{old_status_text}}</p>
        <p><strong>New Status:</strong> {{new_status_text}}</p>
        <p><strong>Changed By:</strong> {{changed_by}}</p>
        <p><strong>Change Date:</strong> {{change_date}}</p>
    </div>
    
    {{#if approved}}
    <p><strong>Expected Delivery Date:</strong> {{delivery_date}}</p>
    {{/if}}
    
    <p><a href="{{order_url}}">View Order Details</a></p>
</div>
```

## Notification Triggers

### Order Completion
```php
// In order completion process
function completeOrder($orderId, $userId) {
    // Update order status
    $order = updateOrderStatus($orderId, true);
    
    // Send notification to sales rep
    $emailManager = new EmailManager();
    $emailManager->sendOrderCompletionNotification($orderId, $order['sales_rep_email']);
    
    // Log history
    HistoryLogger::log($orderId, $userId, 'order_completed', null, 'completed', 'Order marked as completed');
}
```

### Preview Status Change
```php
// In status update process
function updatePreviewStatus($orderId, $newStatus, $userId) {
    $oldStatus = getCurrentStatus($orderId);
    
    // Update status
    updateStatus($orderId, $newStatus);
    
    // Send notification if significant change
    if ($newStatus === 'approved') {
        $emailManager = new EmailManager();
        $emailManager->sendPreviewStatusNotification($orderId, $oldStatus, $newStatus);
    }
}
```

## Notification Queue System

### Queue Table Structure
```sql
CREATE TABLE notification_queue (
    id INT PRIMARY KEY AUTO_INCREMENT,
    recipient_email VARCHAR(255),
    subject VARCHAR(255),
    template VARCHAR(100),
    template_data JSON,
    priority INT DEFAULT 5,
    attempts INT DEFAULT 0,
    max_attempts INT DEFAULT 3,
    status ENUM('pending', 'sent', 'failed') DEFAULT 'pending',
    scheduled_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    sent_at TIMESTAMP NULL,
    error_message TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Queue Processing
```php
// cron/send_notifications.php
class NotificationQueue {
    public function processPendingEmails() {
        $emails = $this->getPendingEmails();
        
        foreach ($emails as $email) {
            try {
                $this->sendEmail($email);
                $this->markAsSent($email['id']);
            } catch (Exception $e) {
                $this->handleFailure($email['id'], $e->getMessage());
            }
        }
    }
    
    private function handleFailure($emailId, $error) {
        $this->incrementAttempts($emailId);
        $this->logError($emailId, $error);
        
        // Mark as failed if max attempts reached
        if ($this->getAttempts($emailId) >= $this->getMaxAttempts($emailId)) {
            $this->markAsFailed($emailId);
        }
    }
}
```

## User Notification Preferences

### Preferences Table
```sql
CREATE TABLE user_notification_preferences (
    user_id INT PRIMARY KEY,
    order_completion BOOLEAN DEFAULT TRUE,
    preview_status_change BOOLEAN DEFAULT TRUE,
    overdue_alerts BOOLEAN DEFAULT TRUE,
    daily_summary BOOLEAN DEFAULT FALSE,
    weekly_summary BOOLEAN DEFAULT TRUE,
    email_format ENUM('html', 'text') DEFAULT 'html',
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

### Preferences Interface
```html
<form id="notification-preferences">
    <h3>Email Notifications</h3>
    
    <div class="preference-group">
        <label>
            <input type="checkbox" name="order_completion" {{checked_if_enabled}}>
            Order completion notifications
        </label>
    </div>
    
    <div class="preference-group">
        <label>
            <input type="checkbox" name="preview_status_change" {{checked_if_enabled}}>
            Preview status change notifications
        </label>
    </div>
    
    <div class="preference-group">
        <label>
            <input type="checkbox" name="overdue_alerts" {{checked_if_enabled}}>
            Overdue order alerts
        </label>
    </div>
    
    <div class="preference-group">
        <label>
            <input type="checkbox" name="daily_summary" {{checked_if_enabled}}>
            Daily summary reports
        </label>
    </div>
    
    <button type="submit">Save Preferences</button>
</form>
```

## Automated Reports

### Daily Summary Report
```php
// cron/daily_reports.php
function sendDailySummary() {
    $users = getUsersWithDailySummaryEnabled();
    
    foreach ($users as $user) {
        $data = [
            'new_orders' => getNewOrdersCount($user['id']),
            'completed_orders' => getCompletedOrdersCount($user['id']),
            'pending_previews' => getPendingPreviewsCount($user['id']),
            'overdue_orders' => getOverdueOrdersCount($user['id'])
        ];
        
        $emailManager = new EmailManager();
        $emailManager->queueEmail(
            $user['email'],
            'Daily Summary - ' . date('d.m.Y'),
            'daily_summary',
            $data
        );
    }
}
```

### Overdue Alerts
```php
// Check for overdue orders daily
function checkOverdueOrders() {
    $overdueOrders = getOrdersPastDeliveryDate();
    
    if (!empty($overdueOrders)) {
        $adminUsers = getAdminUsers();
        
        foreach ($adminUsers as $admin) {
            $emailManager = new EmailManager();
            $emailManager->sendOverdueAlert($overdueOrders, $admin['email']);
        }
    }
}
```

## Email Delivery Tracking

### Tracking Implementation
```php
// Add tracking pixel to emails
$trackingPixel = '<img src="' . $baseUrl . '/track_email.php?id=' . $emailId . '" width="1" height="1" style="display:none;">';

// track_email.php
if (isset($_GET['id'])) {
    $emailId = (int)$_GET['id'];
    markEmailAsOpened($emailId);
    
    // Return 1x1 transparent pixel
    header('Content-Type: image/gif');
    echo base64_decode('R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7');
}
```

## Expected Outcome
- Functional email notification system
- Automated order completion notifications
- User notification preferences
- Email queue management
- Daily/weekly summary reports
- Delivery tracking and analytics

## Next Phase
Phase 09 - Filtering and Search Functionality
