<?php
/**
 * Email Unsubscribe Page
 * CIG Realizace - Phase 08
 */

require_once 'config/email.php';
require_once 'includes/email_functions.php';

$message = '';
$error = '';
$success = false;

// Get parameters
$userId = intval($_GET['user'] ?? 0);
$token = $_GET['token'] ?? '';

// Validate token
if ($userId > 0 && !empty($token)) {
    if (validateUnsubscribeToken($userId, $token)) {
        // Handle unsubscribe action
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $action = $_POST['action'] ?? '';
            
            if ($action === 'unsubscribe_all') {
                // Unsubscribe from all notifications
                $preferences = [
                    'order_completion' => 0,
                    'preview_status_change' => 0,
                    'overdue_alerts' => 0,
                    'daily_summary' => 0,
                    'weekly_summary' => 0,
                    'email_format' => 'html',
                    'frequency' => 'immediate'
                ];
                
                if (updateUserNotificationPreferences($userId, $preferences)) {
                    $success = true;
                    $message = 'Byli jste úspěšně odhlášeni ze všech emailových notifikací.';
                } else {
                    $error = 'Nepodařilo se odhlásit z notifikací. Zkuste to prosím později.';
                }
            } elseif ($action === 'unsubscribe_type') {
                // Unsubscribe from specific notification type
                $notificationType = $_POST['notification_type'] ?? '';
                
                $currentPrefs = getUserNotificationPreferences($userId);
                if ($currentPrefs && isset($currentPrefs[$notificationType])) {
                    $currentPrefs[$notificationType] = 0;
                    
                    if (updateUserNotificationPreferences($userId, $currentPrefs)) {
                        $success = true;
                        $message = 'Byli jste odhlášeni z vybraného typu notifikací.';
                    } else {
                        $error = 'Nepodařilo se odhlásit z notifikací. Zkuste to prosím později.';
                    }
                } else {
                    $error = 'Neplatný typ notifikace.';
                }
            }
        }
        
        // Get user info and current preferences
        try {
            $pdo = getDbConnection();
            $stmt = $pdo->prepare("SELECT email, full_name FROM users WHERE id = ?");
            $stmt->execute([$userId]);
            $user = $stmt->fetch();
            
            if (!$user) {
                $error = 'Uživatel nebyl nalezen.';
            } else {
                $preferences = getUserNotificationPreferences($userId);
            }
        } catch (Exception $e) {
            $error = 'Došlo k chybě při načítání údajů.';
        }
    } else {
        $error = 'Neplatný nebo vypršelý odkaz pro odhlášení.';
    }
} else {
    $error = 'Neplatné parametry pro odhlášení.';
}
?>

<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Odhlášení z notifikací - CIG Realizace</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
        }
        .unsubscribe-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .card-header {
            background: linear-gradient(135deg, #dc3545, #fd7e14);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .notification-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #dc3545;
        }
        .notification-item.disabled {
            opacity: 0.5;
            border-left-color: #6c757d;
        }
        .btn-unsubscribe {
            background: linear-gradient(135deg, #dc3545, #fd7e14);
            border: none;
            color: white;
            padding: 10px 20px;
            border-radius: 20px;
            transition: all 0.3s ease;
        }
        .btn-unsubscribe:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(220, 53, 69, 0.4);
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-6">
                <div class="unsubscribe-card">
                    <div class="card-header">
                        <h1 class="mb-0">
                            <i class="fas fa-envelope-open-text"></i>
                            Odhlášení z notifikací
                        </h1>
                        <p class="mb-0 mt-2 opacity-75">
                            CIG Realizace - Systém pro správu objednávek
                        </p>
                    </div>
                    
                    <div class="card-body p-4">
                        <?php if ($error): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle"></i> <?= htmlspecialchars($error) ?>
                        </div>
                        
                        <div class="text-center">
                            <p>Pokud máte problémy s odhlášením, kontaktujte nás prosím přímo.</p>
                            <a href="mailto:<EMAIL>" class="btn btn-outline-primary">
                                <i class="fas fa-envelope"></i> Kontaktovat podporu
                            </a>
                        </div>
                        
                        <?php elseif ($success): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle"></i> <?= htmlspecialchars($message) ?>
                        </div>
                        
                        <div class="text-center">
                            <p>Pokud si to rozmyslíte, můžete se kdykoli znovu přihlásit k notifikacím v nastavení svého účtu.</p>
                            <a href="user/notification_settings.php" class="btn btn-outline-primary">
                                <i class="fas fa-cog"></i> Nastavení notifikací
                            </a>
                        </div>
                        
                        <?php elseif (isset($user) && isset($preferences)): ?>
                        <div class="mb-4">
                            <h5>Dobrý den <?= htmlspecialchars($user['full_name']) ?>,</h5>
                            <p class="text-muted">
                                Emailová adresa: <strong><?= htmlspecialchars($user['email']) ?></strong>
                            </p>
                            <p>
                                Zde můžete spravovat své emailové notifikace. Můžete se odhlásit ze všech notifikací 
                                nebo pouze z vybraných typů.
                            </p>
                        </div>
                        
                        <h6>Současné nastavení notifikací:</h6>
                        
                        <div class="notification-item <?= $preferences['order_completion'] ? '' : 'disabled' ?>">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <strong>Dokončení objednávky</strong>
                                    <div class="small text-muted">Notifikace při dokončení objednávky</div>
                                </div>
                                <div>
                                    <?php if ($preferences['order_completion']): ?>
                                    <span class="badge bg-success">Aktivní</span>
                                    <form method="post" class="d-inline ms-2">
                                        <input type="hidden" name="action" value="unsubscribe_type">
                                        <input type="hidden" name="notification_type" value="order_completion">
                                        <button type="submit" class="btn btn-sm btn-outline-danger">Odhlásit</button>
                                    </form>
                                    <?php else: ?>
                                    <span class="badge bg-secondary">Neaktivní</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        
                        <div class="notification-item <?= $preferences['preview_status_change'] ? '' : 'disabled' ?>">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <strong>Změny stavu náhledu</strong>
                                    <div class="small text-muted">Notifikace při změně stavu náhledu</div>
                                </div>
                                <div>
                                    <?php if ($preferences['preview_status_change']): ?>
                                    <span class="badge bg-success">Aktivní</span>
                                    <form method="post" class="d-inline ms-2">
                                        <input type="hidden" name="action" value="unsubscribe_type">
                                        <input type="hidden" name="notification_type" value="preview_status_change">
                                        <button type="submit" class="btn btn-sm btn-outline-danger">Odhlásit</button>
                                    </form>
                                    <?php else: ?>
                                    <span class="badge bg-secondary">Neaktivní</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        
                        <div class="notification-item <?= $preferences['overdue_alerts'] ? '' : 'disabled' ?>">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <strong>Upozornění na zpoždění</strong>
                                    <div class="small text-muted">Notifikace při zpožděných objednávkách</div>
                                </div>
                                <div>
                                    <?php if ($preferences['overdue_alerts']): ?>
                                    <span class="badge bg-success">Aktivní</span>
                                    <form method="post" class="d-inline ms-2">
                                        <input type="hidden" name="action" value="unsubscribe_type">
                                        <input type="hidden" name="notification_type" value="overdue_alerts">
                                        <button type="submit" class="btn btn-sm btn-outline-danger">Odhlásit</button>
                                    </form>
                                    <?php else: ?>
                                    <span class="badge bg-secondary">Neaktivní</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        
                        <div class="notification-item <?= $preferences['daily_summary'] ? '' : 'disabled' ?>">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <strong>Denní souhrn</strong>
                                    <div class="small text-muted">Denní souhrn aktivity</div>
                                </div>
                                <div>
                                    <?php if ($preferences['daily_summary']): ?>
                                    <span class="badge bg-success">Aktivní</span>
                                    <form method="post" class="d-inline ms-2">
                                        <input type="hidden" name="action" value="unsubscribe_type">
                                        <input type="hidden" name="notification_type" value="daily_summary">
                                        <button type="submit" class="btn btn-sm btn-outline-danger">Odhlásit</button>
                                    </form>
                                    <?php else: ?>
                                    <span class="badge bg-secondary">Neaktivní</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        
                        <div class="notification-item <?= $preferences['weekly_summary'] ? '' : 'disabled' ?>">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <strong>Týdenní souhrn</strong>
                                    <div class="small text-muted">Týdenní souhrn aktivity</div>
                                </div>
                                <div>
                                    <?php if ($preferences['weekly_summary']): ?>
                                    <span class="badge bg-success">Aktivní</span>
                                    <form method="post" class="d-inline ms-2">
                                        <input type="hidden" name="action" value="unsubscribe_type">
                                        <input type="hidden" name="notification_type" value="weekly_summary">
                                        <button type="submit" class="btn btn-sm btn-outline-danger">Odhlásit</button>
                                    </form>
                                    <?php else: ?>
                                    <span class="badge bg-secondary">Neaktivní</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        
                        <div class="text-center mt-4">
                            <form method="post" onsubmit="return confirm('Opravdu se chcete odhlásit ze všech emailových notifikací?')">
                                <input type="hidden" name="action" value="unsubscribe_all">
                                <button type="submit" class="btn btn-unsubscribe">
                                    <i class="fas fa-times-circle"></i> Odhlásit ze všech notifikací
                                </button>
                            </form>
                        </div>
                        
                        <div class="text-center mt-3">
                            <small class="text-muted">
                                Pokud si to rozmyslíte, můžete se kdykoli znovu přihlásit k notifikacím 
                                v nastavení svého účtu v systému CIG Realizace.
                            </small>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
