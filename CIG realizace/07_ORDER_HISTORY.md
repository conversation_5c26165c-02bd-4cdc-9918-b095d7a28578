# Phase 07 - Order History Tracking

## Objective
Implement comprehensive order history tracking to monitor all changes and provide audit trail.

## Tasks

### 1. History Tracking System
- Automatic logging of all order changes
- User attribution for all actions
- Timestamp tracking with timezone support
- Detailed change descriptions

### 2. History Display
- `orders/history.php` - Order history view
- Timeline visualization of changes
- Filter by action type and user
- Export history reports

### 3. Activity Logging
- Preview status changes
- Delivery date modifications
- Technology assignments
- Item relevance changes
- Order completion

### 4. System Activity Dashboard
- Recent activity feed
- User activity statistics
- System-wide change log
- Performance metrics

## Files to Create

### History Core
1. `classes/HistoryLogger.php`
2. `orders/history.php`
3. `includes/history_timeline.php`
4. `includes/activity_feed.php`

### History Views
5. `admin/system_activity.php`
6. `admin/user_activity.php`
7. `reports/activity_report.php`

### AJAX Handlers
8. `ajax/get_order_history.php`
9. `ajax/get_activity_feed.php`

### Styling
10. `assets/css/history.css`
11. `assets/js/history.js`

## History Logging Implementation

### HistoryLogger Class
```php
class HistoryLogger {
    public static function log($orderId, $userId, $actionType, $oldValue, $newValue, $description) {
        // Insert into order_history table
        // Include IP address and user agent
        // Format values for display
    }
    
    public static function logPreviewStatusChange($orderId, $userId, $oldStatus, $newStatus) {
        // Specific method for preview status changes
    }
    
    public static function logDeliveryDateChange($orderId, $userId, $oldDate, $newDate) {
        // Specific method for delivery date changes
    }
}
```

### Automatic Logging Integration
```php
// In order update functions
$oldStatus = $order['preview_status'];
// Update order...
HistoryLogger::logPreviewStatusChange($orderId, $_SESSION['user_id'], $oldStatus, $newStatus);
```

## History Display Layout

### Order History Page
```html
<div class="order-history">
    <div class="history-header">
        <h2>Order History: 25VP-00001</h2>
        <div class="history-filters">
            <select id="action-filter">
                <option value="">All Actions</option>
                <option value="preview_status_change">Preview Status</option>
                <option value="delivery_date_change">Delivery Date</option>
                <option value="technology_assignment">Technology</option>
            </select>
            <select id="user-filter">
                <option value="">All Users</option>
                <!-- Dynamic user list -->
            </select>
        </div>
    </div>
    
    <div class="history-timeline">
        <!-- Timeline items here -->
    </div>
</div>
```

### Timeline Item Structure
```html
<div class="timeline-item" data-action="preview_status_change">
    <div class="timeline-marker"></div>
    <div class="timeline-content">
        <div class="timeline-header">
            <span class="action-type">Preview Status Changed</span>
            <span class="timestamp">15.01.2025 14:30</span>
        </div>
        <div class="timeline-body">
            <p><strong>User:</strong> admin (Admin User)</p>
            <p><strong>Change:</strong> not_created → sent_to_client</p>
            <p><strong>Description:</strong> Preview status updated by admin</p>
        </div>
    </div>
</div>
```

## Action Types and Descriptions

### Preview Status Changes
```php
$descriptions = [
    'not_created' => 'Preview not yet created',
    'sent_to_client' => 'Preview sent to client for approval',
    'approved' => 'Preview approved by client'
];
```

### Technology Assignments
```php
// Log format: "Technology assigned to item [catalog_code]: [technology]"
"Technology assigned to item 13030683-03: TA1 99 1"
```

### Delivery Date Changes
```php
// Log format: "Delivery date changed from [old_date] to [new_date]"
"Delivery date changed from 29.01.2025 to 02.02.2025"
```

### Item Relevance Changes
```php
// Log format: "Item [catalog_code] marked as [relevant/irrelevant]"
"Item PVY marked as irrelevant"
```

## Database Queries

### Get Order History
```sql
SELECT oh.*, u.username, u.full_name, u.role
FROM order_history oh
LEFT JOIN users u ON oh.user_id = u.id
WHERE oh.order_id = ?
ORDER BY oh.created_at DESC
```

### Recent Activity Feed
```sql
SELECT oh.*, o.order_code, u.username, u.full_name
FROM order_history oh
LEFT JOIN orders o ON oh.order_id = o.id
LEFT JOIN users u ON oh.user_id = u.id
ORDER BY oh.created_at DESC
LIMIT 50
```

### User Activity Statistics
```sql
SELECT u.username, u.full_name, 
       COUNT(oh.id) as total_actions,
       COUNT(CASE WHEN oh.action_type = 'preview_status_change' THEN 1 END) as status_changes,
       MAX(oh.created_at) as last_activity
FROM users u
LEFT JOIN order_history oh ON u.id = oh.user_id
WHERE oh.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY u.id
ORDER BY total_actions DESC
```

## Activity Feed Component

### Dashboard Activity Widget
```html
<div class="activity-feed">
    <h3>Recent Activity</h3>
    <div class="activity-list">
        <div class="activity-item">
            <div class="activity-icon status-change"></div>
            <div class="activity-content">
                <p><strong>admin</strong> changed preview status for <a href="orders/detail.php?id=123">25VP-00001</a></p>
                <span class="activity-time">2 minutes ago</span>
            </div>
        </div>
        <!-- More activity items -->
    </div>
    <a href="admin/system_activity.php" class="view-all">View All Activity</a>
</div>
```

### Real-time Updates
```javascript
// Auto-refresh activity feed every 30 seconds
setInterval(function() {
    $.ajax({
        url: '../ajax/get_activity_feed.php',
        success: function(response) {
            $('#activity-feed').html(response);
        }
    });
}, 30000);
```

## History Export Features

### Export Options
- PDF report with order history
- CSV export for data analysis
- Email history summary
- Print-friendly view

### Report Generation
```php
// Generate PDF history report
$pdf = new TCPDF();
$pdf->AddPage();
$pdf->writeHTML($historyHTML);
$pdf->Output('order_history_' . $orderCode . '.pdf', 'D');
```

## Performance Considerations

### Database Optimization
- Index on order_id and created_at
- Partition large history tables by date
- Archive old history data

### Caching Strategy
- Cache recent activity feed
- Cache user activity statistics
- Use Redis for real-time updates

## Privacy and Security

### Data Retention
- Keep history for 2 years minimum
- Archive older data
- Comply with data protection regulations

### Access Control
- Users can only see history for orders they have access to
- Admin can see all history
- Audit log for history access

## Expected Outcome
- Complete order change tracking
- Visual timeline of order history
- Activity feed on dashboard
- User activity monitoring
- Export and reporting capabilities

## Next Phase
Phase 08 - Email Notifications System
