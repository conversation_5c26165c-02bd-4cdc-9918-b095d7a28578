# Phase 02 - User Authentication System

## Objective
Implement secure user authentication with role-based access control.

## Tasks

### 1. Create Authentication Classes
- `classes/User.php` - User management class
- `classes/Auth.php` - Authentication handling
- `includes/session.php` - Session management

### 2. Login System
- `index.php` - Login page (main entry point)
- `login_process.php` - Login form processing
- `logout.php` - Logout functionality
- Password hashing and verification
- Session security measures

### 3. Role-Based Access Control
- Middleware for checking user roles
- Route protection based on permissions
- Role-specific navigation menus

### 4. User Management (Admin only)
- `admin/users.php` - User list and management
- `admin/user_add.php` - Add new user
- `admin/user_edit.php` - Edit existing user
- `admin/user_delete.php` - Delete user

### 5. Security Features
- CSRF protection
- Session timeout
- Password strength requirements
- Login attempt limiting
- Secure password reset (future enhancement)

## Files to Create

### Core Authentication
1. `classes/User.php`
2. `classes/Auth.php`
3. `includes/session.php`
4. `includes/auth_check.php`

### Login Interface
5. `index.php` (login page)
6. `login_process.php`
7. `logout.php`
8. `dashboard.php` (main dashboard after login)

### Admin User Management
9. `admin/users.php`
10. `admin/user_add.php`
11. `admin/user_edit.php`
12. `admin/user_delete.php`

### Styling
13. `assets/css/login.css`
14. `assets/css/admin.css`

## User Roles and Permissions

### Admin
- Full system access
- User management
- System configuration
- All order operations

### Obchodník (Sales)
- View all orders
- Edit order details
- Manage preview status
- View calendar

### Grafik (Designer)
- View orders assigned to graphics
- Update preview status
- View calendar

### Realizátor (Producer)
- View approved orders
- Update production status
- Mark orders as completed
- View calendar

## Security Considerations
- All passwords hashed with PHP's `password_hash()`
- Session regeneration on login
- HTTPS enforcement (production)
- Input validation and sanitization
- SQL injection prevention

## Default Users to Create
```php
// Admin user
username: admin
password: admin123 (change on first login)
role: admin

// Test users for each role
username: sales_test
password: test123
role: obchodnik

username: designer_test
password: test123
role: grafik

username: producer_test
password: test123
role: realizator
```

## Expected Outcome
- Secure login system
- Role-based access control
- User management interface
- Protected dashboard access

## Next Phase
Phase 03 - CSV Data Import Functionality
