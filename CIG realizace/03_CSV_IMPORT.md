# Phase 03 - CSV Data Import Functionality

## Objective
Create a system to import order data from CSV files and populate the database.

## Tasks

### 1. CSV Parser Class
- `classes/CSVImporter.php` - Main import functionality
- Handle Czech character encoding (UTF-8, Windows-1250)
- Parse CSV structure and validate data
- Error handling and logging

### 2. Data Processing Logic
- Group CSV rows by order code (`<PERSON><PERSON><PERSON> dokla<PERSON>`)
- Extract sales representative from order prefix
- Determine inventory status based on dates
- Handle empty/invalid data gracefully

### 3. Import Interface
- `admin/import.php` - CSV upload interface
- `admin/import_process.php` - Process uploaded file
- Progress tracking for large files
- Import history and logs

### 4. Data Validation
- Validate order codes format
- Check for duplicate orders
- Validate dates and quantities
- Handle special catalog items (PVY, Poštovné, Dopravné)

### 5. Sales Representative Mapping
```php
$sales_reps = [
    'VP' => 'Vláďa',
    'J' => 'Ji<PERSON>', 
    'NK' => 'Nikol',
    'MR' => '<PERSON><PERSON>',
    'D' => '<PERSON><PERSON>',
    'CI' => 'Czech Image'
];
```

## Files to Create

### Core Import System
1. `classes/CSVImporter.php`
2. `classes/OrderProcessor.php`
3. `includes/import_functions.php`

### Admin Interface
4. `admin/import.php`
5. `admin/import_process.php`
6. `admin/import_history.php`
7. `admin/import_logs.php`

### Utilities
8. `utils/test_import.php` (for testing)
9. `logs/import_log.txt` (log file)

## CSV Column Mapping
```php
$csv_mapping = [
    'order_code' => 'Číslo dokladu',
    'catalog' => 'Katalog', 
    'quantity' => 'Množ.hlavní',
    'order_date' => 'Datum vystavení',
    'goods_ordered_date' => 'Datum vystavení OV',
    'goods_stocked_date' => 'Datum vytvoření DLP'
];
```

## Data Processing Rules

### Inventory Status Logic
```php
if (!empty($goods_stocked_date)) {
    $status = 'in_stock';
} elseif (!empty($goods_ordered_date)) {
    $status = 'ordered';
} else {
    $status = 'not_in_stock';
}
```

### Items to Ignore
- Catalog items: "Poštovné", "Dopravné", "PVY"
- Empty catalog codes
- Zero quantities

### Order Grouping
- Group all items with same order code
- Create single order record
- Multiple order_items records

## Import Process Flow
1. **Upload CSV** - Admin uploads file
2. **Parse File** - Read and validate CSV structure
3. **Preview Data** - Show what will be imported
4. **Process Import** - Insert data into database
5. **Generate Report** - Show success/error summary
6. **Log Results** - Save import log for review

## Error Handling
- Invalid date formats
- Missing required fields
- Duplicate order codes
- Database connection errors
- File encoding issues

## Import Statistics
Track and display:
- Total rows processed
- Orders created
- Items imported
- Errors encountered
- Processing time

## Testing Data
Use the provided CSV file:
- `Objednavka prijata CSV - IMAGE CZECH.CSV`
- Contains 5151 rows
- Multiple order codes and sales reps
- Various date combinations

## Expected Outcome
- Functional CSV import system
- All order data imported correctly
- Error logging and reporting
- Admin interface for managing imports

## Next Phase
Phase 04 - Basic Dashboard and Pending Orders
